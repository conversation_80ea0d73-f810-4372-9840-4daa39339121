<script setup lang="ts">
import { watch, ref } from 'vue';
import { calcGroup, getToolbox } from './index';
import { type Measuredata } from '@/utils/spline-interpolate';
import Chart from '../chart.vue';
import type { Specification } from '@/views/metrology-management/interface';
import { quantileSeq } from 'mathjs';
import { colorList, formatterY } from '@futurefab/ui-sdk-ees-charts';
const props = defineProps<{
    data: Measuredata[];
    valueKeys: string[];
    groupKey: string;
    groupNumber: number;
    specification: Specification;
}>();
const options = ref<any>(null);
const getMultiBoxplot = (
    data: Measuredata[],
    valueKeys: string[],
    groupKey: string,
    groupNumber: number,
    specification: Specification,
) => {
    let maxV = -Infinity;
    let minV = Infinity;
    type GroupList = number[];
    const groupLists: GroupList[] = [];
    data.forEach((measure: Measuredata) => {
        let groupList: GroupList;
        const xIndex = measure.header.findIndex(item => item === specification.jsonObj.xField);
        const yIndex = measure.header.findIndex(item => item === specification.jsonObj.yField);
        if (groupKey !== 'Radius') {
            const groupIndex = measure.header.findIndex(item => item === groupKey);
            groupList = measure.data.map(item => Number(item[groupIndex]));
        } else {
            groupList = measure.data.map(item =>
                Math.sqrt(Number(item[xIndex]) ** 2 + Number(item[yIndex]) ** 2),
            );
        }
        groupLists.push(groupList);
        const tempMax = Math.max(...groupList);
        if (tempMax > maxV) {
            maxV = tempMax;
        }
        const tempMin = Math.min(...groupList);
        if (tempMin < minV) {
            minV = tempMin;
        }
    });

    const gap = (maxV - minV) / groupNumber;

    // 按区间分组的对象
    const groupRes = valueKeys.map(valueKey =>
        new Array(groupNumber).fill(null).map((_: null, index: number) => ({
            start: minV + gap * index,
            end: index === groupNumber - 1 ? maxV : minV + gap * (index + 1),
            data: [] as number[],
            count: 0,
            valueKey,
        })),
    );
    valueKeys.forEach((valueKey, keyIndex) => {
        groupLists.forEach((groupList, gIndex) => {
            const vIndex = data[gIndex].header.findIndex(item => item === valueKey);
            groupList.forEach((groupValue: number, index: number) => {
                const groupI = calcGroup(groupValue, maxV, minV, gap, groupNumber);
                const value = data[gIndex].data[index][vIndex];
                groupRes[keyIndex][groupI].data.push(Number(value));
            });
        });
    });

    groupRes.forEach(keyGroup => {
        keyGroup.forEach(item => {
            if (!item.data.length) {
                return;
            }
            item.count = item.data.length;
            // 将数据转换为 boxplot需要的格式  [min,  Q1,  median (or Q2),  Q3,  max]
            const q = quantileSeq(item.data, [0, 0.25, 0.5, 0.75, 1]) as number[];
            const iqr = q[3] - q[1];
            const upper = q[3] + 1.5 * iqr;
            const lower = q[1] - 1.5 * iqr;
            for (let i = 0; i < q.length; i++) {
                if (q[i] < lower) {
                    q[i] = lower;
                } else {
                    break;
                }
            }
            for (let i = q.length - 1; i >= 0; i++) {
                if (q[i] > upper) {
                    q[i] = upper;
                } else {
                    break;
                }
            }
            item.data = q;
        });
    });
    return {
        color: colorList,
        grid: {
            left: 80,
            top: 30,
            right: 20,
            bottom: 30,
        },
        toolbox: getToolbox(),
        xAxis: {
            show: true,
            type: 'category',
            data: new Array(groupNumber).fill(null).map((item, index) => index + 1),
            splitArea: {
                show: true,
            },
        },
        yAxis: {
            show: true,
            scale: true,
            splitArea: {
                show: false,
            },
        },
        legend: {
            show: true,
        },
        tooltip: {
            formatter: (event: any) => {
                if (event?.data?.info) {
                    const info = event.data.info;
                    return [
                        `Range: ${formatterY(info.start)} ~ ${formatterY(info.end)}`,
                        `Count: ${info.count}`,
                        `Max: ${event.data.value[5]}`,
                        `Q3: ${event.data.value[4]}`,
                        `Median: ${event.data.value[3]}`,
                        `Q1: ${event.data.value[2]}`,
                        `Min: ${event.data.value[1]}`
                    ].join('<br/>');
                }
            },
            appendToBody: true, 
        },
        series: valueKeys.map((valueKey: string, index: number) => ({
            name: valueKey,
            type: 'boxplot',
            data: groupRes[index].map(item => ({
                value: item.data,
                info: item,
            })),

        })),
    };
};
watch(
    [
        () => props.data,
        () => props.valueKeys,
        () => props.groupKey,
        () => props.groupNumber,
        () => props.specification,
    ],
    v => {
        if (v) {
            options.value = getMultiBoxplot(
                props.data,
                props.valueKeys,
                props.groupKey,
                props.groupNumber,
                props.specification,
            );
        }
    },
    { immediate: true },
);
</script>
<template>
    <div class="common-chart-container">
        <Chart :options="options" />
    </div>
</template>
<style scoped lang="less">
.common-chart-container {
    width: 100%;
    height: 100%;
}
</style>
