<template>
  <svg style="outline: 0" class="svg-icon" :style="{ color: color, width: width, height: height }">
    <use :xlink:href="symbolId" />
  </svg>
</template>

<script setup>
  import { computed } from 'vue';

  const props = defineProps({
    name: {
      type: String,
      required: true
    },
    color: {
      type: String,
      default: '#4E5969'
    },
    width: {
      type: String,
      default: '16px'
    },
    height: {
      type: String,
      default: '16px'
    }
  });

  const symbolId = computed(() => `#icon-${props.name}`);
</script>

<style scoped>
  .hover-container {
    width: 32px;
    height: 32px;

    &:hover {
      border-radius: 8px;
      background: #f5f7f9;
    }
  }
</style>
