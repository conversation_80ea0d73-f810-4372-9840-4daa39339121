<template>
  <div ref="container" :class="baseClass + 'outer-box'" @dragover="dragOver">
    <div
      :class="[baseClass + 'box', 'cm-tree-table', dragging ? 'dragging' : '']"
      :style="{ height: totalHeight + 'px', width: totolWidth + 'px' }"
    >
      <div :class="baseClass + 'header'" :style="{ height: headerHight + 'px' }">
        <div
          v-for="(col, index) in visibleColumn"
          :key="index"
          :class="[
            index === 0 ? baseClass + 'first-col' : '',
            scrollLeft ? baseClass + 'has-shadow' : '',
            baseClass + 'header-item',
            headerClass?.({ row: null, col, columnIndex: col.visibleColIndex })
          ]"
          :style="{
            width: (index === 0 ? firstColWidth : otherColWidth) + 'px',
            marginLeft: index === 1 ? fisrtItemColLeft + 'px' : 0
          }"
        >
          <div
            v-if="index === 0"
            :class="[baseClass + 'header-drag-line', dragging ? 'dragging' : '']"
            :style="{
              left: firstColWidth - 1 + 'px',
              height: totalHeight + 'px'
            }"
            :draggable="true"
            @dragstart="dragstart"
            @drag="drag"
            @dragend="dragend"
          ></div>
          <slot name="header" :col="col" :index="col.visibleColIndex">
            {{ col?.title }}
          </slot>
        </div>
      </div>

      <ul :class="baseClass + 'visual-box'" :style="{ marginTop: boxMarginTop + 'px' }">
        <template v-for="treeParent in visibleData" :key="treeParent[props.treeKey]">
          <li
            v-if="treeParent.hideParent !== true"
            :style="{ height: itemHeight + 'px' }"
            :class="[
              treeParent[treeKey] === activeRowId ? baseClass + 'avtive-row' : '',
              baseClass + 'row-item'
            ]"
          >
            <div
              v-for="(col, index) in visibleColumn"
              :key="col.visibleColIndex"
              :class="[
                baseClass + 'tree-cell-box',
                baseClass + (index === 0 ? 'first-col' : 'no-first-col'),
                scrollLeft ? baseClass + 'has-shadow' : ''
              ]"
              :style="{
                width: (index === 0 ? firstColWidth : otherColWidth) + 'px',
                marginLeft: index === 1 ? fisrtItemColLeft + 'px' : 0
              }"
              @click="cellClick(treeParent, col, col.visibleColIndex, true)"
            >
              <div
                :class="[
                  cellClass?.({
                    row: treeParent,
                    columnIndex: col.visibleColIndex,
                    col
                  }),
                  baseClass + 'tree-cell'
                ]"
                :style="{
                  width: (index === 0 ? firstColWidth : otherColWidth) + 'px'
                }"
              >
                <span
                  v-if="index === 0 && treeParent.children"
                  :class="baseClass + 'tree-cell-span'"
                >
                  <i
                    v-if="!treeParent.expand"
                    class="iconfont chart_icon_icon zoom-icon icon-arrow-bold-right"
                  />
                  <i v-else class="iconfont chart_icon_icon zoom-icon icon-arrow-bold-down" />
                </span>
                <slot
                  name="default"
                  :col="col"
                  :column-index="col.visibleColIndex"
                  :row="getOriginRow(treeParent)"
                  style="width: 100%"
                >
                  <!-- {{ treeParent[col.field] }} -->
                </slot>
              </div>
            </div>
          </li>
          <ul v-if="treeParent.expand">
            <li
              v-for="treeChild in treeParent.children"
              :key="treeChild[props.treeKey]"
              :style="{ height: itemHeight + 'px' }"
              :class="[
                treeChild[treeKey] === activeRowId ? baseClass + 'avtive-row' : '',
                baseClass + 'row-item'
              ]"
            >
              <div
                v-for="(col, index) in visibleColumn"
                :key="col.visibleColIndex"
                :class="[
                  scrollLeft ? baseClass + 'has-shadow' : '',
                  baseClass + 'tree-cell-box',
                  baseClass + (index === 0 ? 'first-col' : 'no-first-col')
                ]"
                :style="{
                  width: (index === 0 ? firstColWidth : otherColWidth) + 'px',
                  marginLeft: index === 1 ? fisrtItemColLeft + 'px' : 0
                }"
                @click="cellClick(treeChild, col, col.visibleColIndex, false)"
              >
                <div
                  :class="[
                    cellClass?.({
                      row: treeChild,
                      columnIndex: col.visibleColIndex,
                      col
                    }),
                    baseClass + 'tree-cell'
                  ]"
                  :style="{
                    width: (index === 0 ? firstColWidth : otherColWidth) + 'px',
                    paddingLeft: index === 0 ? '40px' : ''
                  }"
                >
                  <slot
                    name="default"
                    :col="col"
                    :column-index="col.visibleColIndex"
                    :row="treeChild"
                    style="width: 100%"
                  >
                    <!-- {{ treeParent[col.field] }} -->
                  </slot>
                </div>
              </div>
            </li>
          </ul>
        </template>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { inject, onBeforeUnmount, onMounted, ref, watch, type Ref } from 'vue';
import { debounce } from 'lodash-es';
import { useRowVirtualScroll, useColVirtualScroll, useDragFirstCol } from './config';
const baseClass = 'cm-tree-table-';
const props = withDefaults(
  defineProps<{
    headerHight: number; // 表格头部高度
    itemHeight: number; // 表格正文每行高度
    columns: any[];
    treeData: any[];
    cellClass: (param: any) => string;
    headerClass: (param: any) => string;
    treeKey?: string;
    otherColWidth?: number;
  }>(),
  {
    headerHight: 100,
    itemHeight: 25,
    columns: () => [],
    treeData: () => [],
    treeKey: 'treeKey',
    otherColWidth: 220,
    cellClass: () => '',
    headerClass: () => ''
  }
);
const emits = defineEmits(['cellClick']);
const firstColWidth = defineModel<number>('firstColWidth', { default: 300 });
const activeRowId = ref<any>(null);
watch(
  () => props.treeData,
  () => {
    // 将第一有数据的展开，而且设置为active
    const temp = props.treeData.find((item) => item.children && item.children.length);
    if (temp) {
      temp.expand = true;
      activeRowId.value = temp.children[0][props.treeKey];
    }
  },
  { immediate: true, deep: false }
);

const tableCaptrueMode = inject<Ref<boolean>>('tableCaptrueMode', ref(false));
watch(
  () => tableCaptrueMode.value,
  (newValue) => {
    if (newValue) {
      props.treeData.forEach((item) => {
        if (item.children && item.children.length) {
          item.expand = true; // 仅展开有子节点的行
        }
      });
    }
  }
);
function getOriginRow(row: any) {
  return props.treeData.find((item) => item[props.treeKey] === row[props.treeKey]);
}
const container = ref();
const boxMarginTop = ref(0);
const { visibleData, updateRowVisibleData, totalHeight } = useRowVirtualScroll(
  props,
  container,
  boxMarginTop
);
const { visibleColumn, totolWidth, fisrtItemColLeft, updateColumn, scrollLeft } =
  useColVirtualScroll(props, container, firstColWidth);

const { drag, dragstart, dragend, dragging } = useDragFirstCol(firstColWidth, container);
function cellClick(row: any, col: any, columnIndex: number, isParent: boolean) {
  activeRowId.value = row[props.treeKey];
  if (isParent) {
    changeExpand(row);
    emits('cellClick', { row: getOriginRow(row), col, columnIndex });
    console.timeEnd('cellClick');
  } else {
    emits('cellClick', { row, col, columnIndex });
  }
}
const tempScrollFn = () => {
  updateRowVisibleData(true);
  updateColumn(true);
};
const updateRowVisibleDataDebounce = debounce(() => {
  tempScrollFn();
}, 10);

const changeExpand = (row: any) => {
  props.treeData.find((item) => item[props.treeKey] === row[props.treeKey])!.expand = !row.expand;
  updateRowVisibleData(); // Update visible data when expanding/collapsing
};
const dragOver = (e: any) => {
  e.preventDefault(); // 允许放置
};
// 展开/收起所有有子节点的行
const expandOrFoldAll = (isExpand: boolean) => {
  props.treeData.forEach((item) => {
    if (item.children && item.children.length) {
      item.expand = isExpand; // 仅展开有子节点的行
    }
  });
  updateRowVisibleData();
};
const observer = new ResizeObserver(updateRowVisibleDataDebounce);
onMounted(() => {
  updateRowVisibleData();
  updateColumn();
  observer.observe(container.value);
  container.value.addEventListener('scroll', updateRowVisibleDataDebounce);
});
onBeforeUnmount(() => {
  observer.disconnect();
  container.value.removeEventListener('scroll', updateRowVisibleDataDebounce);
});

defineExpose({
  cellClick,
  expandOrFoldAll
});
</script>

<style scoped lang="less">
@import url('@/assets/style/variable.less');
.cm-tree-table-outer-box {
  height: 100%;
  width: 100%;
  overflow: auto;
  position: relative;
  // 显示滚动条
  &:hover {
    &::-webkit-scrollbar-thumb {
      background-color: @bg-scroll-color;
    }
  }
  &::-webkit-scrollbar-corner {
    background-color: @bg-scroll-color;
  }
}
.cm-tree-table {
  &.dragging {
    cursor: col-resize !important;
  }
  &-visual-box {
    padding: 0;
    margin: 0;
    li.cm-tree-table-row-item {
      display: flex;
      flex-wrap: nowrap;
    }
  }
  &-header {
    position: sticky;
    top: 0; /* 添加此行以固定在顶部 */
    z-index: 3; /* 确保 header 在其他内容之上 */
    display: flex;
    flex-wrap: nowrap;
  }

  &-header &-header-item {
    display: inline-block;
    border: 1px solid @border-color;
    height: 100%;
    flex-shrink: 0;
    color: @th-font-color;
    border-top: 0px;
    border-left: 0px;
    background: @bg-block-color;
  }
  &-tree-cell {
    width: 100%;
    height: 100%;
    display: inline-block;
    border: 1px solid @border-color;
    color: @td-font-color;
    flex-shrink: 0;
    display: flex;
    flex-wrap: nowrap;
    position: relative;
    padding: 0 0 0 10px;
    overflow: hidden;
    text-overflow: ellipsis;
    align-items: center;
    &-span {
      margin-right: 5px;
      .zoom-icon {
        width: 16px;
        height: 16px;
        display: inline-flex;
        justify-content: center;
        align-items: center;
        &:hover {
          cursor: pointer;
          color: @text-weak-text;
          background-color: @bg-hover-2-color;
        }
      }
    }
    & > div {
      width: 100%;
      flex-shrink: 0;
    }
    border-left: 0px;
    border-top: 0px;
  }
  &-tree-cell&-no-first-col > div {
    width: 100% !important;
    height: 100%;
  }
  &-first-col {
    position: sticky; /* 添加此行以固定第一列 */
    left: 0; /* 添加此行以固定在左侧 */
    z-index: 2; /* 确保第一列在其他内容之上 */
    background: @bg-block-color;
    &.cm-tree-table-has-shadow {
      box-shadow: 2px 0px 2px 0 @shadow-color;
    }
  }
  &-header-item {
    &.cm-tree-table-first-col {
      overflow: visible !important;
    }
    // padding: 7px 4px;
    overflow: hidden;
    text-overflow: ellipsis;
    .cm-tree-table-header-drag-line {
      width: 3px;
      top: 0;
      position: absolute;
      z-index: 30;
      opacity: 0;
      background-color: @primary-color;
      &.dragging {
        opacity: 1;
      }
      &:hover {
        cursor: col-resize;
        opacity: 1;
      }
    }
  }

  &-avtive-row &-tree-cell {
    background-color: @primary-color-column-select !important;
  }
  &-tree-cell:hover {
    background-color: @primary-color-row-select;
  }
}
</style>
