<script setup lang="ts">
import { reactive, ref, nextTick } from 'vue';
import { getInitFormValue, getScheduleValidationMsg } from './config';
import dayjs from 'dayjs';
import { cloneDeep } from 'lodash-es';
import {
  type AddScheduleItem,
  cmInsertScheduleJob,
  updateScheduleJob
} from '@futurefab/ui-sdk-api';
import { successInfo } from '@futurefab/ui-sdk-ees-basic';
import { type AddScheduleForm, getEndTime, getTriggerTime } from '@/utils/schedule';
import { eqpDataSources as buttonIds } from '@/log-config';

const props = withDefaults(
  defineProps<{
    isEdit: boolean;
    dataTypeStr: string;
    eqpId: string;
    schdJobInfo: any;
  }>(),
  {
    isEdit: false,
    dataTypeStr: '',
    eqpId: '',
    schdJobInfo: null
  }
);
const emits = defineEmits(['refresh']);
const headFormRef = ref();
const showModel = ref(false);
const handleHide = () => {
  showModel.value = false;
};

const loading = ref(false);
const formState = ref<AddScheduleForm>(getInitFormValue({ isEdit: props.isEdit, form: null }));
const errorMsgList = ref<string[]>([]);

const startDateChange = (events: any) => {
  console.log(events, 'startDateChange');
  if (events?.$d) {
    formState.value.endDate = dayjs(events?.$d).add(1, 'day');
  }
};
const disabledEndDate = (time: any) => {
  if (formState.value.startDate?.$d) {
    return dayjs(time.$d).endOf('day').toDate().getTime() < formState.value.startDate?.$d.getTime();
  }
  return false;
};

const getJobParams = (form: AddScheduleForm) => {
  const timeFormat = 'YYYY-MM-DD HH:mm:ss';
  const startTime = form.startDate.$d;
  const startStr = dayjs(startTime).format(timeFormat);
  const isDay = form.recurrenceBy === 'day';
  const interval = isDay ? form.dayInterval : form.hourInterval;
  const startSecond = dayjs(startTime).second();
  const startMinute = dayjs(startTime).minute();
  const startHour = dayjs(startTime).hour();
  const startDay = dayjs(startTime).date();
  const startMonth = dayjs(startTime).month() + 1;
  const hour = 1000 * 60 * 60;
  // 只执行一次
  const executeOnce =
    formState.value.recurrenceBy === 'never' ||
    (formState.value.endType === 'after' && formState.value.afterOccurrences === 1);

  const params: AddScheduleItem = {
    applicationName: 'cm',
    serverUrl: 'ScheduleExecEqpDataCollectionService',
    description: JSON.stringify({
      ...formState.value,
      startDate: new Date(formState.value.startDate).getTime(),
      endDate: formState.value.endDate && new Date(formState.value.endDate).getTime()
    }),
    applicationData: props.eqpId,
    eqpId: props.eqpId,
    jobName: form.jobName as string,
    isEnable: 1,
    triggerTime: getTriggerTime(
      executeOnce,
      startMinute,
      startSecond,
      startHour,
      startDay,
      startMonth,
      isDay,
      interval
    ),
    jobEnableDtts: dayjs(startTime).subtract(0.5, 'hour').format(timeFormat),
    jobDisableDtts: getEndTime(
      timeFormat,
      executeOnce,
      form,
      startTime,
      startDay,
      interval,
      isDay,
      startHour,
      hour,
      startMinute
    ) as string
  };
  return params;
};
const apply = async () => {
  const result = getScheduleValidationMsg(formState.value, props.isEdit);
  errorMsgList.value = result;
  if (!result.length) {
    try {
      loading.value = true;
      const params = getJobParams(formState.value);
      const headerParams = {
        log: 'Y',
        page: buttonIds.pageId,
        action: buttonIds.schedule
      };
      let res: any;
      if (props.isEdit) {
        res = await updateScheduleJob({
          bodyParams: [{ ...params, rawId: props.schdJobInfo!.rawId }],
          headerParams
        });
      } else {
        res = await cmInsertScheduleJob({
          bodyParams: [params],
          headerParams
        });
      }
      if (res.status === 'SUCCESS') {
        successInfo('common.tip.actionSuccess');
        handleHide();
        emits('refresh');
      }
    } catch (e) {
      console.error(e);
    } finally {
      loading.value = false;
    }
  }
};
defineExpose({
  show: () => {
    nextTick(() => {
      showModel.value = true;
      errorMsgList.value = [];
      headFormRef.value?.clearValidate();
      if (props.isEdit) {
        const temp = JSON.parse(props.schdJobInfo.description);
        formState.value = getInitFormValue({
          isEdit: props.isEdit,
          form: temp
        });
      } else {
        formState.value = getInitFormValue({
          isEdit: props.isEdit,
          form: null,
          eqpId: props.eqpId
        });
      }
    });
  },

  hide: handleHide
});
const baseClass = 'eqp-data-sources-schedule';
</script>
<template>
  <Teleport to="body">
    <vxe-modal
      v-model="showModel"
      :title="$t(isEdit ? 'cm.title.modifySchedule' : 'cm.title.scheduleJob')"
      :mask-closable="false"
      :width="'650px'"
      show-footer
      @hide="handleHide"
      @close="handleHide"
    >
      <div :class="baseClass">
        <ul v-if="errorMsgList.length" :class="baseClass + '-error-list'">
          <li v-for="msg in errorMsgList" :key="msg">{{ msg }}</li>
        </ul>
        <div :class="baseClass + '-header'">
          <div :class="baseClass + '-data-type'">
            <div :class="baseClass + '-data-type-label'">{{ 'Data Type: ' }}</div>
            <div :class="baseClass + '-data-type-value'">{{ dataTypeStr }}</div>
          </div>
          <a-form
            ref="headFormRef"
            :class="baseClass + '-form'"
            name="advanced_search"
            :label-col="{ style: { width: '147px', 'text-align': 'left' } }"
            :model="formState"
            :layout="'horizontal'"
          >
            <a-form-item
              :name="'jobName'"
              :label="$t('cm.label.jobName')"
              :rules="[{ required: true, message: 'Please input Job Name!' }]"
            >
              <a-input
                v-model:value="formState.jobName"
                :placeholder="$t('cm.label.jobName')"
                :disabled="isEdit"
                style="width: 240px"
              />
            </a-form-item>
            <a-form-item
              :name="'startDate'"
              :label="$t('cm.label.startDate')"
              :rules="[{ required: true, message: 'Please input Start Date!' }]"
            >
              <a-date-picker
                v-model:value="formState.startDate"
                show-time
                style="width: 240px"
                format="YYYY-MM-DD HH:mm:ss"
                :disabled="isEdit"
                @change="startDateChange"
              ></a-date-picker>
            </a-form-item>
            <a-form-item :name="'recurrence'" :label="$t('cm.label.recurrence')">
              <a-radio-group v-model:value="formState.recurrenceBy">
                <a-radio :value="'never'">{{ $t('cm.label.never') }}</a-radio>
                <a-radio :value="'hour'">{{ $t('cm.label.hourly') }}</a-radio>
                <a-radio :value="'day'">{{ $t('cm.label.daily') }}</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-form>
        </div>
        <div
          v-if="formState.recurrenceBy !== 'never'"
          :class="[baseClass + '-recurrence', baseClass + '-panel']"
        >
          <div :class="baseClass + '-recurrence-by-option'">
            <div :class="baseClass + '-recurrence-by-option-daily-interval'">
              <span>{{ $t('cm.label.every') }}</span>
              <template v-if="formState.recurrenceBy === 'day'">
                <a-input-number
                  v-model:value="formState.dayInterval"
                  style="width: 160px; margin: 0 4px"
                  :min="1"
                  :max="15"
                />
                <span>{{ $t('cm.label.day') }}</span>
              </template>
              <template v-else>
                <a-input-number
                  v-model:value="formState.hourInterval"
                  style="width: 160px; margin: 0 4px"
                  :min="1"
                  :max="12"
                />
                <span>{{ $t('cm.label.hour') }}</span>
              </template>
            </div>

            <div :class="baseClass + '-recurrence-by-option-daily-end'">
              <a-radio-group v-model:value="formState.endType">
                <a-radio :style="{ display: 'flex' }" :value="'noEnd'"
                  ><span>{{ $t('cm.label.noEndDate') }}</span></a-radio
                >
                <a-radio :style="{ display: 'flex', marginTop: '10px' }" :value="'after'">
                  <div :class="baseClass + '-recurrence-by-option-daily-end-inline-block'">
                    <div class="flex-temp-box">
                      <span>{{ $t('cm.label.after') }}</span>
                      <a-input-number
                        v-model:value="formState.afterOccurrences"
                        style="width: 160px; margin: 0 4px"
                        :disabled="formState.endType !== 'after'"
                        :min="1"
                        :max="999"
                      />
                      <span>{{ $t('cm.label.occurrences') }}</span>
                    </div>
                  </div>
                </a-radio>
                <a-radio :style="{ display: 'flex', marginTop: '10px' }" :value="'endBy'">
                  <div :class="baseClass + '-recurrence-by-option-daily-end-inline-block'">
                    <div class="flex-temp-box">
                      <span>{{ $t('cm.label.endBy') }}</span>
                      <a-date-picker
                        v-model:value="formState.endDate"
                        show-time
                        style="width: 240px; margin: 0 4px"
                        :disabled="formState.endType !== 'endBy'"
                        format="YYYY-MM-DD HH:mm:ss"
                        :disabled-date="disabledEndDate"
                      ></a-date-picker>
                    </div>
                  </div>
                </a-radio>
              </a-radio-group>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <a-button type="primary" style="margin-right: 10px" @click="apply">{{
          $t('common.btn.apply')
        }}</a-button>
        <a-button @click="handleHide">{{ $t('common.btn.cancel') }}</a-button>
      </template>
    </vxe-modal>
  </Teleport>
</template>
<style scoped lang="less">
@import url('@/assets/style/variable.less');
.eqp-data-sources-schedule {
  min-height: 550px;
  max-height: calc(100vh - 114px);
  overflow-x: hidden;
  &-error-list {
    border-radius: @border-circle;
    padding: @module-padding;
    border: 1px solid @card-default-br-color;
    li {
      font-size: 14px;
      &:before {
        content: '';
        display: inline-block;
        height: 5px;
        width: 5px;
        border-radius: 50%;
        background-color: red;
        margin: 0px 5px 2px 5px;
      }
    }
    margin-bottom: 10px;
  }
  &-data-type {
    height: 48px;
    padding: 14px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    flex-wrap: nowrap;
    background-color: @bg-group-color;
    &-label,
    &-value {
      font-size: 14px;
      font-weight: bold;
      line-height: 20px;
      height: 20px;
    }
    &-label {
      color: @text-hint-color;
      margin-right: 4px;
    }
    &-value {
      color: @text-subtitle-color;
    }
  }

  :deep(.ant-picker.ant-picker-disabled) {
    background-color: @bg-disabled-color;
  }
  :deep(.ant-form-inline .ant-form-item-with-help) {
    margin-bottom: 0;
  }
  :deep(.ant-form-item-label label) {
    color: @text-subtitle-color;
    font-size: 14px;
    font-weight: bold;
    &::after {
      content: none;
    }
  }
  :deep(.ant-form > .ant-form-item:last-child) {
    margin-bottom: 0px;
  }
  height: 100%;
  overflow-y: auto;
  &-panel {
    background-color: @bg-group-color;
    padding: 14px;
  }
  &-recurrence {
    margin-top: 5px;
    border-radius: 4px;
    border: 1px solid @border-form-color;
    &-by-option {
      &-daily-interval {
        display: flex;
        flex-wrap: nowrap;
        height: 32px;
        align-items: center;
        margin-bottom: 10px;
        span {
          color: @text-sub-color;
        }
      }
      &-daily-end {
        &-inline-block {
          display: inline-block;
          .flex-temp-box {
            display: flex;
            align-items: center;
            flex-wrap: nowrap;
            .ant-radio-wrapper {
              span {
                display: inline-block;
                margin: 3px;
                color: @text-sub-color;
              }
            }
          }
        }
      }
    }
  }
}
</style>
