import dayjs from 'dayjs';
import * as echarts from '@xdatav/echarts';
import type {
    GroupValue,
    GroupTableValue,
    MarkAreaData,
    CommentHistoryItem,
    LegendItem,
} from './interface';
import { cloneDeep, max, min } from 'lodash-es';
import {
    formatterY,
    setColor,
    binarySearch,
    getSpecColor,
} from '@futurefab/ui-sdk-ees-basic';
import { getChartPointSize } from '@futurefab/ui-sdk-ees-charts';
import { minBy } from 'lodash-es';
import { fill } from 'lodash-es';
import { customColor } from '@/utils/custom-color';
import { useCmstore } from '@futurefab/ui-sdk-stores';

const cmStore = useCmstore();
const { slColor, clColor, faultColor, warningColor, limitColor, calcCLColor, calcSLColor } =
    getSpecColor();
const { scatterPointSize, linePointSize, linePointWidth, lineWidth } = getChartPointSize();
export const serieConfig = ({ isShowPoint }: { isShowPoint: boolean }) => {
    return {
        showSymbol: isShowPoint,
        showAllSymbol: isShowPoint,
        symbolSize: isShowPoint ? linePointSize : scatterPointSize,
        smooth: false,
        animationThreshold: 1000,
        emphasis: {
            disabled: true,
            scale: isShowPoint,
        },
    };
};
//处理markArea
export const getMarkArea = ({
    startKey,
    endKey,
    index,
    data,
    markAreaList,
    color,
    startXAxis,
}: {
    startKey: string;
    endKey: string;
    index: number;
    data: Record<string, any[]>;
    markAreaList: MarkAreaData[][];
    color?: string;
    startXAxis?: number; // 处理deleteBlank
}) => {
    const val1 = isValue(data[startKey][index]) ? undefined : data[startKey][index];
    const val2 = isValue(data[endKey][index]) ? undefined : data[endKey][index];
    if (val1 === undefined && val2 === undefined) return false;
    const obj = [
        {
            xAxis: startXAxis != null ? startXAxis : data['timestamp'][index],
            yAxis: val1,
            itemStyle: {
                color: color ? color : slColor,
            },
        },
        {
            xAxis: startXAxis != null ? startXAxis + 1 : data['timestamp'][index + 1],
            yAxis: val2,
        },
    ];
    if (markAreaList.length == 0) {
        markAreaList.push(obj);
    } else {
        const currStart = data[startKey][index];
        const currEnd = data[endKey][index];
        const prevStart = data[startKey][index - 1];
        const prevEnd = data[endKey][index - 1];
        if (currStart == prevStart && currEnd == prevEnd) {
            markAreaList[markAreaList.length - 1][1]['xAxis'] =
                startXAxis != null ? startXAxis : data['timestamp'][index];
        } else {
            markAreaList.push(obj);
        }
    }
};
function getSpecMarkArea({
    data,
    index,
    oneScale,
    markAreaList,
    startXAxis,
}: {
    data: Record<string, any[]>;
    index: number;
    oneScale: number;
    markAreaList: MarkAreaData[][];
    startXAxis?: number; // 处理deleteBlank
}) {
    //!顺序不能变，相同时，后面的覆盖前面的
    const keys = ['ucl', 'lcl', 'lsl', 'usl'];
    keys.forEach(key => {
        if (data[key][index] !== '') {
            const obj = [
                {
                    yAxis: data[key][index] - 2 / oneScale,
                    xAxis: startXAxis != null ? startXAxis : data['timestamp'][index],
                    itemStyle: {
                        color: key.includes('cl') ? calcCLColor : calcSLColor,
                    },
                    opacity: 0.991,
                },
                {
                    yAxis: data[key][index],
                    xAxis: startXAxis != null ? startXAxis + 1 : data['timestamp'][index + 1],
                },
            ];
            markAreaList.push(obj);
        }
    });
}
const getStatusMarkArea = ({
    data,
    index,
    maxY,
    oneScale,
    markAreaList,
    startXAxis,
}: {
    data: Record<string, any[]>;
    index: number;
    maxY: number;
    oneScale: number;
    markAreaList: MarkAreaData[][];
    startXAxis?: number; // 处理deleteBlank
}) => {
    const faultList = ['F'];
    const currColor = faultList.includes(data['faultType'][index])
        ? faultColor
        : ['W', 'R'].includes(data['faultType'][index])
        ? warningColor
        : limitColor;
    // const isSpec =
    //     data.lsl[index] != '' ||
    //     data.usl[index] != '' ||
    //     data.lcl[index] != '' ||
    //     data.ucl[index] != '';
    // if (isSpec) {
    if (data['faultType'][index] != '') {
        markAreaList.push([
            {
                yAxis: maxY - 2 / oneScale,
                xAxis: startXAxis != null ? startXAxis : data['timestamp'][index],
                itemStyle: { color: currColor },
                opacity: 0.99,
            },
            {
                yAxis: maxY,
                xAxis: startXAxis != null ? startXAxis + 1 : data['timestamp'][index + 1],
            },
        ]);
    }

    // }
};
const isValue = (val: any) => {
    return val === undefined || val === '' || val === null;
};
export const getChartLegendList = ({
    list,
    colorIndex,
    isScatter,
}: {
    list: any[];
    colorIndex?: number | null;
    isScatter?: boolean;
}) => {
    const legendDataList = [] as LegendItem[];
    list.forEach((data: any, i: number) => {
        let color = data['paramInfo']['color'] || setColor(i);
        if (typeof colorIndex == 'number') {
            color = setColor(colorIndex);
        }

        legendDataList.push({
            name: data['paramInfo']['legend'],
            color,
            type: 'legendSelect',
        });
    });
    return legendDataList;
};
//把管控数据外的点标红
export const pointSetColor = (point: [number, number], faultType: 'C' | 'R' | 'F' | 'W') => {
    return {
        value: point,
        itemStyle: {
            color: faultType === 'F' ? faultColor : warningColor,
        },
    };
};
//获取chart的series数据
export const getSeries = ({
    list,
    paramName,
    isDeleteBlank,
    totalTimeList,
    isShowPoint,
    colorIndex,
    currentSpecParam,
    maxY,
    oneScale,
    markLineList,
    isSplit,
}: {
    list: any[];
    paramName: { x: string; y: string };
    isDeleteBlank?: boolean;
    totalTimeList?: number[];
    isShowPoint?: boolean;
    colorIndex?: number | null;
    currentSpecParam?: string | null;
    maxY?: number | null;
    oneScale?: number;
    markLineList?: CommentHistoryItem[];
    isSplit?: boolean;
}) => {
    const series = [] as echarts.SeriesOption[];
    const sMarkArea = [] as MarkAreaData[][];
    const cMarkArea = [] as MarkAreaData[][];
    const statusMarkArea = [] as MarkAreaData[][];
    const specLineMarkArea = [] as MarkAreaData[][];
    const markLineData = [] as { xAxis: number }[];
    console.log('chartData', list);
    list.forEach((data: any, i: number) => {
        // 当前参数是不是需要超出spec的标红
        let isSpecParam =
            currentSpecParam && data['paramInfo']['sumParamAlias'] == currentSpecParam;
        if (isSplit) {
            isSpecParam = !!currentSpecParam;
        }

        const seriesData = [] as any[];
        const existEqpIdList = markLineList
            ? markLineList.filter(
                  (item: CommentHistoryItem) => item.eqpId === data['paramInfo']['eqpId'],
              )
            : [];
        if (data[paramName.x] && data[paramName.x].length > 0) {
            data[paramName.x].forEach((val: any, index: number) => {
                const hasLcl = data.lcl[index] !== '';
                const hasUcl = data.ucl[index] !== '';
                const hasLsl = data.lsl[index] !== '';
                const hasUsl = data.usl[index] !== '';
                if (isDeleteBlank) {
                    let currIndex = index;
                    if (totalTimeList && totalTimeList.length > 0) {
                        const idx = binarySearch(totalTimeList, val);
                        currIndex = idx!;
                    }
                    if (isSpecParam && ['F', 'R', 'W'].includes(data['faultType'][index])) {
                        seriesData.push(
                            pointSetColor(
                                [currIndex, data[paramName.y][index]],
                                data['faultType'][index],
                            ),
                        );
                    } else {
                        seriesData.push([currIndex, data[paramName.y][index]]);
                    }
                    if (existEqpIdList.length > 0) {
                        existEqpIdList.forEach((item1: CommentHistoryItem) => {
                            const timestamp = dayjs(item1.eventDtts).valueOf();
                            if (timestamp === val) {
                                markLineData.push({
                                    xAxis: currIndex,
                                });
                            }
                        });
                    }
                    if (
                        currentSpecParam &&
                        data['paramInfo']['sumParamAlias'] == currentSpecParam
                    ) {
                        if (index == data['timestamp'].length) return;
                        // getMarkArea({
                        //     startKey: 'lsl',
                        //     endKey: 'usl',
                        //     index,
                        //     data,
                        //     markAreaList: sMarkArea,
                        //     startXAxis: currIndex,
                        // });
                        // getMarkArea({
                        //     startKey: !hasLcl && hasUcl ? 'lsl' : 'lcl',
                        //     endKey: hasLcl && !hasUcl ? 'usl' : 'ucl',
                        //     index,
                        //     data,
                        //     markAreaList: cMarkArea,
                        //     color: clColor,
                        //     startXAxis: currIndex,
                        // });
                        getStatusMarkArea({
                            data,
                            index,
                            maxY: maxY!,
                            oneScale: oneScale!,
                            markAreaList: statusMarkArea,
                            startXAxis: currIndex,
                        });
                        //绘制specLineMarkArea
                        getSpecMarkArea({
                            data,
                            index,
                            oneScale: oneScale!,
                            markAreaList: specLineMarkArea,
                        });
                    }
                } else {
                    if (isSpecParam && ['F', 'R', 'W'].includes(data['faultType'][index])) {
                        seriesData.push(
                            pointSetColor(
                                [val, data[paramName.y][index]],
                                data['faultType'][index],
                            ),
                        );
                    } else {
                        seriesData.push([val, data[paramName.y][index]]);
                    }
                    //处理markArea
                    if (
                        currentSpecParam &&
                        data['paramInfo']['sumParamAlias'] == currentSpecParam
                    ) {
                        if (index == data['timestamp'].length) return;
                        // getMarkArea({
                        //     startKey: 'lsl',
                        //     endKey: 'usl',
                        //     index,
                        //     data,
                        //     markAreaList: sMarkArea,
                        // });
                        // getMarkArea({
                        //     startKey: !hasLcl && hasUcl ? 'lsl' : 'lcl',
                        //     endKey: hasLcl && !hasUcl ? 'usl' : 'ucl',
                        //     index,
                        //     data,
                        //     markAreaList: cMarkArea,
                        //     color: clColor,
                        // });
                        getStatusMarkArea({
                            data,
                            index,
                            maxY: maxY!,
                            oneScale: oneScale!,
                            markAreaList: statusMarkArea,
                        });
                        //绘制SPEC MarkArea
                        getSpecMarkArea({
                            data,
                            index,
                            oneScale: oneScale!,
                            markAreaList: specLineMarkArea,
                        });
                    }
                    if (existEqpIdList.length > 0) {
                        existEqpIdList.forEach((item1: CommentHistoryItem) => {
                            const timestamp = dayjs(item1.eventDtts).valueOf();
                            if (timestamp === val) {
                                markLineData.push({
                                    xAxis: val,
                                });
                            }
                        });
                    }
                }
            });
        }
        let color = data['paramInfo']['color'] || setColor(i);
        if (typeof colorIndex == 'number') {
            color = setColor(colorIndex);
        }
        const markArea = {
            silent: true,
            label: {
                show: false,
            },
            emphasis: {
                disabled: true,
            },
            data: [] as any[],
        };
        if (currentSpecParam && data['paramInfo']['sumParamAlias'] == currentSpecParam) {
            markArea.data = [...sMarkArea, ...cMarkArea, ...statusMarkArea, ...specLineMarkArea];
        }
        series.push({
            id: `${data['paramInfo']['legend']}-${i}`,
            type: 'line',
            name: data['paramInfo']['legend'],
            color,
            yAxisIndex: data['paramInfo']['yAxisIndex'],
            lineStyle: {
                width: isShowPoint ? linePointWidth : lineWidth,
            },
            data: seriesData,
            markArea,
            markLine: {
                silent: false,
                symbol: ['none', 'circle'],
                label: {
                    show: false,
                },
                emphasis: {
                    disabled: true,
                },
                lineStyle: {
                    type: 'solid',
                    color: 'red',
                    width: 1,
                },
                data: markLineData,
            },
            ...serieConfig({ isShowPoint: !!isShowPoint || data['sumValue'].length == 1 }),
        });
    });
    return series;
};

//获取chart单条数据的data
export const getSignalSeries = ({
    data,
    paramName,
    isDeleteBlank,
}: {
    data: any;
    paramName: { x: string; y: string };
    isDeleteBlank?: boolean;
}) => {
    const seriesData = [] as any[];
    data[paramName.x].forEach((val: any, index: number) => {
        if (isDeleteBlank) {
            seriesData.push([index, data[paramName.y][index]]);
        } else {
            seriesData.push([val, data[paramName.y][index]]);
        }
    });
    return seriesData;
};

//获取legend
export const getLegend = (list: any, isSumAliasSplit?: boolean) => {
    const legend = [] as string[];
    if (Array.isArray(list)) {
        list &&
            list.forEach((item: any) => {
                const name = item['paramInfo']['legend'];
                if (legend.indexOf(name) > -1) return false;
                if (isSumAliasSplit) {
                    legend.push(name.split(`${item['paramInfo']['sumParamAlias']}^`)[1]);
                } else {
                    legend.push(name);
                }
            });
    } else {
        if (isSumAliasSplit) {
            legend.push(
                list['paramInfo']['legend'].split(`${list['paramInfo']['sumParamAlias']}^`)[1],
            );
        } else {
            legend.push(list['paramInfo']['legend']);
        }
    }

    return legend;
};
//获取小数点后保留两位的百分比
export const getPercentage = (val: number) => {
    const num = val * 100;
    return num.toFixed(2);
};
//获取当天的日期 时间戳形式
export const getToday = () => {
    return {
        start: dayjs().startOf('day'),
        end: dayjs().endOf('day'),
    };
};

export const formatterDate = (timestamp: number | string) => {
    return dayjs(Number(timestamp)).format('YYYY-MM-DD HH:mm:ss.SSS');
};
//获取tooltip
export const getTooltip = ({
    value,
    dataIndex,
    specList,
}: {
    value: any;
    dataIndex: number;
    specList?: string[];
}) => {
    if (!value) return '';
    const paramInfo = value['paramInfo'];
    let tip = '';
    tip += `EQP ID: ${paramInfo['eqpId'] || ''}</br>`;
    tip += `Module Alias: ${paramInfo['moduleAlias'] || ''}</br>`;
    tip += `Lot ID: ${value['lotid'] ? value['lotid'][dataIndex] : ''}</br>`;
    tip += `Substrate ID: ${value['substrateid'] ? value['substrateid'][dataIndex] : ''}</br>`;
    tip += `Slot ID: ${value['slot'] ? value['slot'][dataIndex] : ''}</br>`;
    tip += `Recipe ID: ${value['recipe'] ? value['recipe'][dataIndex] : ''}</br>`;
    tip += `Product ID: ${value['product'] ? value['product'][dataIndex] : ''}</br>`;
    tip += `Step ID: ${value['step'] ? value['step'][dataIndex] : ''}</br>`;
    tip += `Step Name: ${value['step_name'] ? value['step_name'][dataIndex] : ''}</br>`;
    tip += `Time: ${value['timestamp'] ? formatterDate(value['timestamp'][dataIndex]) : ''}</br>`;
    // tip += `Param Alias: ${paramInfo['sumParamAlias'] || ''}</br>`;
    tip += `${paramInfo['sumParamAlias'] || ''}: ${
        value['sumValue'] ? value['sumValue'][dataIndex] : ''
    }</br>`;
    if (paramInfo['yName'] && paramInfo['yName'] != '') {
        const name = paramInfo['yName'].split('^')[0];
        tip += `${name || ''}: ${value['ySumVal'] ? value['ySumVal'][dataIndex] : ''}</br>`;
    }
    // if (
    //     specList &&
    //     specList.length > 0 &&
    //     paramInfo['sumParamAlias'] &&
    //     specList.includes(paramInfo['sumParamAlias'])
    // ) {
    tip += `USL: ${value['usl'][dataIndex]}</br>`;
    tip += `UCL: ${value['ucl'][dataIndex]}</br>`;
    tip += `Target: ${value['target'][dataIndex]}</br>`;
    tip += `LCL: ${value['lcl'][dataIndex]}</br>`;
    tip += `LSL: ${value['lsl'][dataIndex]}</br>`;
    // }
    const keys = Object.keys(value);
    keys.forEach((item: string) => {
        if (
            item.indexOf('^') > -1 &&
            item.indexOf('RSD_') > -1 &&
            value[item][dataIndex] != undefined
        ) {
            const arr = item.split('^');
            const name = arr[1];
            tip += `${name}: ${value[item][dataIndex]}</br>`;
        }
    });
    return tip;
};
export const getCommentTooltipStr = ({
    value,
    eventName,
    eventDtts,
    comment,
    moduleAlias,
}: {
    value: any;
    eventName: string;
    eventDtts: string;
    comment: string;
    moduleAlias?: string;
}) => {
    let tip = 'Category:FDCCHARTLOG</br>';
    const paramInfo = value['paramInfo'];
    tip += `EQP ID: ${paramInfo['eqpId'] || ''}</br>`;
    tip += `Module Alias: ${moduleAlias ? moduleAlias : paramInfo['moduleAlias']}</br>`;
    tip += `EVENT_NAME: ${eventName || ''}</br>`;
    tip += `EVENT_DTTS: ${formatterDate(eventDtts) || ''}</br>`;
    tip += `COMMENT: ${comment || ''}</br>`;
    return tip;
};
//echarts 基础配置
export const setOptions = (config: any) => {
    let yAxis = {} as any;
    if (config.yAxis && Array.isArray(config.yAxis)) {
        const list = [] as any[];
        config.yAxis.forEach((item: any) => {
            const val = {
                type: 'value',
                axisLine: {
                    show: true,
                    onZero: false,
                },
                axisLabel: {
                    formatter: (value: any) => {
                        const newValue = formatterY(value);
                        return newValue;
                    },
                },
                // alignTicks: true,
                ...item,
            };
            list.push(val);
        });
        yAxis = list;
    } else {
        yAxis = {
            type: 'value',
            axisLine: {
                show: true,
                onZero: false,
            },
            alignTicks: true,
            ...config.yAxis,
        };
    }
    const options: echarts.EChartsOption = {
        ...config,
        grid: {
            top: 20,
            left: 80,
            right: 80,
            ...config.grid,
        },
        xAxis: {
            type: 'time',
            ...config.xAxis,
            // boundaryGap: ['1%',0],
            axisLine: { onZero: false },
            axisLabel: {
                formatter: '{MM}-{dd} {HH}:{mm}:{ss}',
                color: customColor.realtime_xAxisLabel,
                width: 70,
                overflow: 'break',
                ...(config.xAxis && config.xAxis.axisLabel),
            },
            axisPointer: {
                snap: true,
                type: 'line',
                triggerTooltip: false,
                lineStyle: {
                    color: customColor.realtime_xAxisPointer,
                    width: 2,
                    type: 'solid',
                },
                handle: {
                    show: true,
                    color: customColor.realtime_xAxisPointer,
                },
                triggerOn: 'mousemove',
                ...(config.xAxis && config.xAxis.axisPointer),
            },
        },
        axisPointer: {
            show: true,
            type: 'line',
            snap: true,
            label: {
                show: true,
            },
            ...config.axisPointer,
        },
        yAxis: yAxis,
        legend: {
            type: 'scroll',
            top: 'bottom',
            show: false,
            textStyle: {
                color: customColor.realtime_xAxisLabel,
            },
            ...config.legend,
        },
        tooltip: {
            borderColor: customColor.realtime_tooltip_borderColor,
            textStyle: {
                fontSize: 12,
                color: customColor.realtime_tooltip_text,
            },
            appendToBody: false, //在chart内部显示tooltip
            className: 'echarts-tooltip-hide',
            show: cmStore.isChartHoverTooltip === false ? false : true,
            ...config.tooltip,
        },
        toolbox: {
            show: true,
            itemSize: 0,
            feature: {
                dataZoom: {
                    enableBrushBack: true,
                },
                brush: {
                    type: ['rect'],
                },
            },
            iconStyle: {
                opacity: 0,
            },
            showTitle: false,
        },
        brush: {
            xAxisIndex: 'all',
            yAxisIndex: 'all',
            brushLink: 'none',
            seriesIndex: 'all',
            brushType: 'rect',
            brushMode: 'multiple',
            throttleType: 'debounce',
            transformable: false,
        },
        animation: false,
        animationThreshold: 0,
        animationDurationUpdate: 0,
        animationDuration: 0,
    };
    return options;
};
export const getLegendConfig = (legendList: string[], layout: number) => {
    return {
        type: legendList.length > 1 ? 'scroll' : 'plain',
        data: legendList,
        formatter: (name: string) => {
            if (layout == 2) {
                return echarts.format.truncateText(name, 400, '12px', '…', {});
            } else if (layout > 2) {
                return echarts.format.truncateText(name, 200, '12px', '…', {});
            } else {
                return name;
            }
        },
        tooltip: {
            show: layout > 0 ? true : false,
        },
    };
};
export const dispatchBrush = (myChart: echarts.ECharts) => {
    myChart.dispatchAction({
        type: 'takeGlobalCursor',
        key: 'brush',
        brushOption: {
            brushType: 'rect',
            brushMode: 'multiple',
        },
    });
};
export const removeBrush = (myChart: echarts.ECharts) => {
    myChart.dispatchAction({
        type: 'brush', //选择action行为
        areas: [], //areas表示选框的集合，此时为空即可。
    });
};
export const dispatchZoom = (myChart: echarts.ECharts) => {
    myChart.dispatchAction({
        type: 'takeGlobalCursor',
        key: 'dataZoomSelect',
        dataZoomSelectActive: true,
    });
};
export const initChartEvents = ({
    myChart,
    filterLegendFun,
    mutilTooltipFun,
    getSelectData,
    finishedFun,
    drawChartFun,
}: {
    myChart: echarts.ECharts;
    filterLegendFun?: (arg0: object) => void | null;
    mutilTooltipFun?: (arg0: object) => void;
    getSelectData?: (arg0: any[]) => void;
    finishedFun?: () => void;
    drawChartFun?: () => undefined | boolean;
}) => {
    dispatchZoom(myChart);
    removeBrush(myChart);
    let moveX = 0;
    let isClickBaseLine = false;
    myChart.getZr().on('mousedown', (event: any) => {
        moveX = event.offsetX;
        isClickBaseLine = event.target && event.target.draggable;
    });
    myChart.getZr().on('mouseup', (event: any) => {
        if (event.offsetX < moveX && !isClickBaseLine) {
            if (drawChartFun) {
                drawChartFun();
            }
            dispatchZoom(myChart);
        }
    });
    myChart.on('click', (params: any) => {
        mutilTooltipFun && mutilTooltipFun({ type: 'show', value: params });
    });
    myChart.on('datazoom', () => {
        mutilTooltipFun && mutilTooltipFun({ type: 'hide' });
    });
    myChart.on('mouseout', () => {
        const dom = document.getElementsByClassName('echarts-tooltip-hide');
        Array.from(dom).forEach((element: any) => {
            element.style.display = 'none';
        });
    });
    myChart.on('legendselectchanged', (argus: any) => {
        const { selected, name } = argus;
        filterLegendFun && filterLegendFun({ selected, clickName: name });
    });
    myChart.on('rendered', function () {
        finishedFun && finishedFun();
    });
    myChart.on('brushend', (params: any) => {
        //brushType 为 'rect' range 和 coordRange 的格式为：[[minX, maxX], [minY, maxY]]
        const areas = params.areas;
        const selectedSeries = [] as any;
        areas.forEach((item: any) => {
            item.coordRanges.forEach((coordRange: any) => {
                const min = [coordRange[0][0], coordRange[1][0]];
                const max = [coordRange[0][1], coordRange[1][1]];
                selectedSeries.push({
                    min,
                    max,
                });
            });
        });
        const options = myChart.getOption();
        const series = options.series as any[];
        const legend = options.legend as any[];
        const legendData = legend[0].selected;
        const findData = [] as any[];
        series.forEach((item: any, index: number) => {
            if (item.type == 'scatter') {
                if (legendData[item.name] !== undefined && !legendData[item.name]) {
                    return false;
                }
            } else {
                if (!legendData[item.name]) return false;
            }
            item.data.forEach((val: any, idx: number) => {
                const xVal = val[0];
                const yVal = val[1];
                selectedSeries.forEach((value: any) => {
                    const min = value.min;
                    const max = value.max;
                    if (xVal >= min[0] && xVal <= max[0] && yVal >= min[1] && yVal <= max[1]) {
                        findData.push({ serieIndex: index, dataIndex: idx });
                    }
                });
            });
        });
        getSelectData && getSelectData(findData);
    });
};

//设置分组
export const setSeriesGroup = (i: number) => {
    let group = '';
    if (i < 26) {
        group = String.fromCharCode('A'.charCodeAt(0) + i);
    } else {
        const num = Math.floor(i / 26) + 1;
        const rest = i % 26;
        let str = '';
        for (let ind = 0; ind < num; ind++) {
            str += String.fromCharCode('A'.charCodeAt(0) + rest);
        }
        group = str;
    }
    return group;
};
//移除tooltip
export const clearMutilTooltip = (dom: Element) => {
    if (dom.children.length == 0) return false;
    const marksList = document.getElementsByClassName('xchart-marker__item xchart-marker');
    const lineList = document.getElementsByClassName('xchart-marker__line');
    marksList.length > 0 &&
        Array.from(marksList).forEach((item: Element) => {
            dom && dom.removeChild(item);
        });
    lineList.length > 0 &&
        Array.from(lineList).forEach((item: Element) => {
            dom && dom.removeChild(item);
        });
};
// 过滤数据
const filterFun = ({
    data,
    type,
    value,
}: {
    data: any;
    type: string; //key
    value: any[] | string | number; // 要找的值
}) => {
    const keys: string[] = Object.keys(data);
    const values: any = Object.values(data);
    const obj = {} as any;
    const setKey = (i: number) => {
        keys.forEach((key: string, index: number) => {
            if (key == 'paramInfo') {
                obj[key] = cloneDeep(values[index]);
            } else {
                if (obj[key] && Array.isArray(obj[key])) {
                    obj[key].push(values[index][i]);
                } else {
                    obj[key] = [values[index][i]];
                }
            }
        });
    };
    data[type].forEach((val: string, index: number) => {
        if (Array.isArray(value)) {
            if (value.includes(val)) {
                setKey(index);
            }
        } else {
            if (value === val) {
                setKey(index);
            }
        }
    });
    return obj;
};

export const handleFilterChartData = ({
    data,
    name,
    value,
}: {
    data: any[];
    name: string;
    value: string[];
}) => {
    const newData = [] as any;
    data.forEach((item: any) => {
        let obj = {} as any;
        switch (name) {
            case 'eqp':
                if (value.includes(item['paramInfo']['eqpId'])) {
                    obj = item;
                }
                break;
            case 'moduleAlias':
                if (value.includes(item['paramInfo']['moduleName'])) {
                    obj = item;
                }
                break;
            case 'recipe':
                obj = filterFun({ data: item, type: 'recipe', value });
                break;
            case 'product':
                obj = filterFun({ data: item, type: 'product', value });
                break;
            case 'step':
                obj = filterFun({ data: item, type: 'step', value });
                break;
            default:
                break;
        }
        if (Object.keys(obj).length > 0 && obj.sumValue.length > 0) {
            newData.push(obj);
        }
    });
    newData.forEach((item: any) => {
        item['paramInfo']['step'] = [...new Set(item['step'])];
        item['paramInfo']['recipe'] = [...new Set(item['recipe'])];
        item['paramInfo']['product'] = [...new Set(item['product'])];
    });
    return newData;
};

const groupFun = ({ data, type }: { data: any; type: string }) => {
    const newData = [] as any[];
    data['paramInfo'][type] &&
        data['paramInfo'][type].forEach((item: string) => {
            const obj = filterFun({ data, type, value: item });
            if (Object.keys(obj).length > 0 && obj.sumValue.length > 0) {
                obj['paramInfo'][type] = [item];
                newData.push(obj);
            }
        });
    return newData;
};

export const handleGroupChartData = ({ data, groupingMap }: { data: any[]; groupingMap: any }) => {
    const keys = Object.keys(groupingMap);
    let myData = data as any[];
    if (keys.includes('recipe') || keys.includes('step') || keys.includes('product')) {
        const loopFun = (chartData: any, type: string) => {
            const newData = [] as any[];
            chartData.forEach((item: any) => {
                const list = groupFun({
                    data: item,
                    type,
                });
                newData.push(...list);
            });
            return newData;
        };

        for (const key in groupingMap) {
            switch (key) {
                case 'eqp':
                case 'moduleAlias':
                    break;
                case 'recipe':
                case 'product':
                case 'step':
                    myData = loopFun(myData, key);
                    break;
                default:
                    break;
            }
        }
    }
    const groupList = [] as any[];
    const values = Object.values(groupingMap) as any[];
    const notNullValue = values.filter((item: any[]) => item.length > 0);
    const loopGroup = (key: string, index: number, name: string, group: string) => {
        groupingMap[key].forEach((item: GroupValue) => {
            const myName = `${name}^${item.name}`;
            const myGroup = `${group}${item.group}`;
            if (index < keys.length - 1) {
                loopGroup(keys[index + 1], index + 1, myName, myGroup);
            } else {
                if (name.split('^').length != notNullValue.length) return false;
                groupList.push({
                    name: myName,
                    group: myGroup,
                });
            }
        });
    };

    if (notNullValue.length > 1) {
        keys.forEach((key: string, index: number) => {
            if (groupingMap[key].length > 0) {
                loopGroup(key, index, '', '');
            }
        });
    } else {
        notNullValue.length > 0 &&
            notNullValue[0].forEach((item: any) => {
                groupList.push({
                    name: `^${item.name}`,
                    group: item.group,
                });
            });
    }
    let groupNameList = [] as string[];
    groupList.forEach((item: any) => {
        groupNameList.push(item.group);
    });
    groupNameList = [...new Set(groupNameList)] as string[];
    const colorMap = {} as any;
    groupNameList.forEach((name: string, index: number) => {
        colorMap[name] = setColor(index);
    });
    myData.forEach((item: any) => {
        let legendStr = `${item['paramInfo']['sumParamAlias']}`;
        if (keys.includes('eqp') || keys.includes('moduleAlias')) {
            legendStr += `^${item['paramInfo']['moduleName']}`;
        }
        keys.forEach((val: string) => {
            if (val == 'recipe') {
                legendStr += `^${item['paramInfo']['recipe'][0]}`;
            } else if (val == 'product') {
                legendStr += `^${item['paramInfo']['product'][0]}`;
            } else if (val == 'step') {
                legendStr += `^${item['paramInfo']['step'][0]}`;
            }
        });
        item['paramInfo']['legend'] = legendStr;
        groupList.forEach((val: any) => {
            let currentLegend = item['paramInfo']['sumParamAlias'];
            if (
                (keys.includes('eqp') || keys.includes('moduleAlias')) &&
                !(val.name.indexOf(item['paramInfo']['moduleName']) > -1)
            ) {
                currentLegend += `^${item['paramInfo']['moduleName']}`;
            }
            if (`${currentLegend}${val.name}` == legendStr) {
                item['paramInfo']['color'] = colorMap[val.group];
            }
        });
    });
    return myData;
};

const loopScatterData = ({
    xItem,
    yItem,
    type,
    xName,
    yName,
    isStep,
}: {
    xItem: any;
    yItem: any;
    type: string;
    xName: string;
    yName: string;
    isStep?: boolean;
}) => {
    const obj = {} as any;
    const keys = Object.keys(xItem);
    xItem[type].forEach((nxValue: string, nxIndex: number) => {
        yItem[type].forEach((nyValue: string, nyIndex: number) => {
            let flag = false;
            if (type == 'lotid') {
                flag = nxValue == nyValue;
            } else if (type == 'substrateid') {
                flag = nxValue == nyValue && xItem.lotid[nxIndex] == yItem.lotid[nyIndex];
            } else if (type == 'step') {
                flag =
                    nxValue == nyValue &&
                    xItem.lotid[nxIndex] == yItem.lotid[nyIndex] &&
                    xItem.substrateid[nxIndex] == yItem.substrateid[nyIndex];
            }
            if (flag) {
                keys.forEach((name: string) => {
                    if (name == 'paramInfo') {
                        obj['paramInfo'] = xItem.paramInfo;
                        if (isStep) {
                            obj[
                                'paramInfo'
                            ].xName = `${xItem.paramInfo.sumParamAlias}_${xItem.paramInfo.step[0]}^${xItem.paramInfo.moduleName}`;
                            obj[
                                'paramInfo'
                            ].yName = `${yItem.paramInfo.sumParamAlias}_${yItem.paramInfo.step[0]}^${yItem.paramInfo.moduleName}`;
                        } else {
                            obj['paramInfo'].xName = xName;
                            obj['paramInfo'].yName = yName;
                        }

                        return false;
                    }
                    if (obj[name]) {
                        obj[name].push(xItem[name][nxIndex]);
                    } else {
                        obj[name] = [xItem[name][nxIndex]];
                    }
                });
                if (obj['yTimestamp']) {
                    obj['yTimestamp'].push(yItem.timestamp[nyIndex]);
                } else {
                    obj['yTimestamp'] = [yItem.timestamp[nyIndex]];
                }
                if (obj['ySumVal']) {
                    obj['ySumVal'].push(yItem.sumValue[nyIndex]);
                } else {
                    obj['ySumVal'] = [yItem.sumValue[nyIndex]];
                }
                if (obj['xyValue']) {
                    obj['xyValue'].push([xItem.sumValue[nxIndex], yItem.sumValue[nyIndex]]);
                } else {
                    obj['xyValue'] = [[xItem.sumValue[nxIndex], yItem.sumValue[nyIndex]]];
                }
            }
        });
    });
    return obj;
};

export const getScatterData = ({
    chartItem,
    data,
    isStepGroup,
    chartType,
}: {
    chartItem: { x: string[]; y: string[] };
    data: any[];
    isStepGroup?: false;
    chartType: number;
}) => {
    let scatterChartData = [] as any[];
    chartItem.x.forEach((x: string) => {
        chartItem.y.forEach((y: string) => {
            //x与y相同的sumModuleName，不显示scatter
            if (x == y) return false;
            const xData = data.filter((item: any) => item.paramInfo.sumModuleName == x);
            const yData = data.filter((item: any) => item.paramInfo.sumModuleName == y);
            xData.forEach((xItem: any) => {
                yData.forEach((yItem: any) => {
                    if (xItem.paramInfo.sumCategory == yItem.paramInfo.sumCategory) {
                        let type = '';
                        if (!isStepGroup) {
                            if (
                                xItem.paramInfo.sumCategory === 'LOT' ||
                                xItem.paramInfo.sumCategory === 'TIME'
                            ) {
                                //仅需lot相等
                                type = 'lotid';
                            } else if (xItem.paramInfo.sumCategory === 'SUBSTRATE') {
                                //lot&substrate相等
                                type = 'substrateid';
                            } else if (xItem.paramInfo.sumCategory === 'STEP') {
                                //lot&substrate&step相等
                                type = 'step';
                            }
                            if (
                                type == '' &&
                                xItem.substrateid &&
                                xItem.substrateid.length > 0 &&
                                yItem.substrateid &&
                                yItem.substrateid.length > 0
                            ) {
                                //event Sum数据， sumCategory为空，需要判断数据中是否有substrateid是否为空或
                                type = 'substrateid';
                            }
                        } else {
                            //包含step的grouping,lot&substrate两个都要相等
                            type = 'substrateid';
                        }
                        if (type == '') return false;
                        const obj = loopScatterData({
                            xItem,
                            yItem,
                            type,
                            xName: x,
                            yName: y,
                            isStep: true,
                        });
                        if (Object.keys(obj).length > 0) {
                            scatterChartData.push(obj);
                        }
                    }
                });
            });
        });
    });
    if (chartType == 1) {
        const newSData = [] as any[];
        scatterChartData.forEach((item: any) => {
            const exist = newSData.filter(
                (value: any) =>
                    value[0].paramInfo.xName == item.paramInfo.xName &&
                    value[0].paramInfo.yName == item.paramInfo.yName,
            );
            if (exist.length == 0) {
                newSData.push([item]);
            } else {
                newSData.forEach((value: any) => {
                    if (
                        value[0].paramInfo.xName == item.paramInfo.xName &&
                        value[0].paramInfo.yName == item.paramInfo.yName
                    ) {
                        value.push(item);
                    }
                });
            }
        });
        scatterChartData = newSData;
    }
    return scatterChartData;
};

export const exportFun = (data: any[]) => {
    console.time('export handle data');
    const tableData = [] as any[];
    //LOTID, SUBSTRATEID, RECIPE, OPERATION, PRODUCT, SLOT, STEP, STEP_NAME, CONTEXT KEY, EQP_ID, MODULE_ID, MODULE ALIAS, PARAM ALIAS, SUM_VALUE
    data.forEach((item: any, idx: number) => {
        const dataList = [] as any[];
        item.sumValue &&
            item.sumValue.forEach((value: string, index: number) => {
                const obj: any = {
                    LOTID: item.lotid[index],
                    SUBSTRATEID: item.substrateid[index],
                    RECIPE: item.recipe[index],
                    OPERATION: item.operation[index],
                    PRODUCT: item.product[index],
                    SLOT: item.slot[index],
                    STEP: item.step[index],
                    STEP_NAME: item.step_name[index],
                    CONTEXT: item.CONTEXT_KEY[index],
                    EQP_ID: item.paramInfo.eqpId,
                    MODULE_ID: item.paramInfo.eqpModuleId,
                    'MODULE ALIAS': item.paramInfo.moduleAlias,
                    'PARAM ALIAS': item.paramInfo.sumParamAlias,
                    TIME: formatterDate(item.timestamp[index]),
                    SUM_VALUE: item.sumValue[index],
                    LSL: item.lsl[index],
                    LCL: item.lcl[index],
                    TARGET: item.target[index],
                    UCL: item.ucl[index],
                    USL: item.usl[index],
                };
                const keys = Object.keys(item);
                keys.forEach((key: string) => {
                    if (key.indexOf('^') > -1 && key.indexOf('RSD_') > -1) {
                        const arr = key.split('^');
                        const name = arr[1];
                        obj[name] = item[key][index];
                    }
                });
                dataList.push(obj);
            });
        let paramName = item.paramInfo.eqpParamAlias;
        const count = 30;
        const numLength = String(idx).length;
        if (paramName.length > count - numLength) {
            paramName = paramName.slice(0, count - numLength);
        }
        tableData[idx] = {
            name: `${paramName}_${idx + 1}`,
            data: dataList,
        };
    });
    console.timeEnd('export handle data');
    return tableData;
};

export const getLegendTree = (data: any[], groupingMap?: GroupTableValue) => {
    const paramList = ['param', 'eqpId', 'moduleAlias', 'recipe', 'product', 'step'];
    const legendList = [] as any;
    paramList.forEach((item: string) => {
        legendList.push({ label: item, children: [] });
    });
    data.forEach((item: any) => {
        legendList[0]['children'].push(item.paramInfo.sumParamAlias);
        if (groupingMap) {
            const keys = Object.keys(groupingMap);
            keys.includes('moduleAlias') &&
                legendList[2]['children'].push(item.paramInfo.moduleName);
            keys.includes('eqpId') && legendList[1]['children'].push(item.paramInfo.eqpId);
            keys.includes('recipe') && legendList[3]['children'].push(...item.paramInfo.recipe);
            keys.includes('product') && legendList[4]['children'].push(...item.paramInfo.product);
            keys.includes('step') && legendList[5]['children'].push(...item.paramInfo.step);
        } else {
            legendList[2]['children'].push(item.paramInfo.moduleName);
        }
    });
    legendList.forEach((item: any, index: number) => {
        if (item.children.length > 0) {
            item.children = [...new Set(item.children)];
            if (index == 5) {
                const strList = item.children.filter(
                    (item: string) => item == '' || isNaN(Number(item)),
                );
                const numList = item.children.filter(
                    (item: string) => item != '' && !isNaN(Number(item)),
                );
                const newList = [
                    ...numList.sort((a: string, b: string) => Number(a) - Number(b)),
                    ...strList.sort(),
                ];
                item.children = newList;
            }
        }
    });
    return legendList.filter((item: any) => item.children.length > 0);
};

export const handleData = ({
    data,
    totalData,
    // chartData,
    totalCount,
    rightParamList,
    sumModuleNameList,
}: {
    data: any;
    totalData: any;
    // chartData:any,
    totalCount: number;
    rightParamList: string[];
    sumModuleNameList: string[];
}) => {
    const key = Object.keys(data)[0];
    const value: any = Object.values(data)[0];
    value['paramInfo']['sumModuleName'] = key;
    value['paramInfo']['legend'] = key;
    value['paramInfo'][
        'moduleName'
    ] = `${value['paramInfo']['eqpId']}:${value['paramInfo']['moduleAlias']}`;
    value['paramInfo']['yAxisIndex'] = 0;
    if (totalData[key]) {
        const keys = Object.keys(value);
        totalCount += value.sumValue ? value.sumValue.length : 0;
        keys.forEach(str => {
            if (str !== 'paramInfo') {
                const values = totalData[key][str]
                    ? [...totalData[key][str], ...value[str]]
                    : [...value[str]];
                totalData[key][str] = values;
            }
        });
        totalData[key]['paramInfo']['step'] = [...new Set(totalData[key]['step'])];
        totalData[key]['paramInfo']['recipe'] = [...new Set(totalData[key]['recipe'])];
        totalData[key]['paramInfo']['product'] = [...new Set(totalData[key]['product'])];
        totalData[key]['eqpId'] = fill(
            new Array(totalData[key]['timestamp'].length),
            totalData[key]['paramInfo']['eqpId'],
        );
        totalData[key]['moduleAlias'] = fill(
            new Array(totalData[key]['timestamp'].length),
            totalData[key]['paramInfo']['moduleAlias'],
        );
    } else {
        totalData[key] = value;
        totalCount += value.sumValue ? value.sumValue.length : 0;
        rightParamList.push(value['paramInfo']['sumParamAlias']);
        sumModuleNameList.push(value['paramInfo']['sumModuleName']);
        if (value['timestamp'] && value['timestamp'].length > 0) {
            totalData[key]['eqpId'] = fill(
                new Array(value['timestamp'].length),
                value['paramInfo']['eqpId'],
            );
            totalData[key]['moduleAlias'] = fill(
                new Array(value['timestamp'].length),
                value['paramInfo']['moduleAlias'],
            );
        } else {
            totalData[key]['eqpId'] = [];
            totalData[key]['moduleAlias'] = [];
        }
    }
    return {
        totalData,
        totalCount,
        rightParamList,
        sumModuleNameList,
    };
};
export const findMinMax = (arr: string[]) => {
    const filteredArray = arr.filter(item => item !== '').map(Number);
    const maxValue = max(filteredArray);
    const minValue = min(filteredArray);
    return {
        max: maxValue,
        min: minValue,
    };
};
//获取留白后的最大最小值
export const getMinMaxY = ({
    minY,
    maxY,
}: {
    minY: undefined | string;
    maxY: undefined | string;
}) => {
    if (maxY !== undefined && minY !== undefined) {
        const gapVal = Math.abs(
            maxY === minY ? parseFloat(maxY) * 0.1 : (parseFloat(maxY) - parseFloat(minY)) * 0.1,
        );
        if (maxY !== undefined) {
            maxY = (parseFloat(maxY) + gapVal).toString(); //顶部留白
        }
        if (minY !== undefined) {
            minY = (parseFloat(minY) - gapVal).toString(); //底部留白
        }
    }
    return {
        minY,
        maxY,
    };
};

export const findPointsByXValue = ({
    target,
    targetXValue,
    offsetY,
}: {
    targetXValue: number;
    offsetY: number;
    target: any;
}) => {
    const matches: any[] = []; // 存储匹配项
    const offsetYList: number[] = []; // 存储匹配项的 Y 轴定位
    const option = target.getOption();
    // 遍历所有的 series
    option.series.forEach((series: any, index: number) => {
        const seriesData = series.data; // 获取 series 的数据
        // 遍历数据项
        seriesData.forEach((dataItem: any, dataIndex: number) => {
            // 检查 X 轴值是否匹配
            if (targetXValue == dataItem[0]) {
                const pixData = target.convertToPixel(
                    { yAxisIndex: series.yAxisIndex, xAxisIndex: 0 },
                    [targetXValue, dataItem[1]],
                );
                matches.push({
                    seriesIndex: index,
                    dataIndex,
                    color: series.color,
                    event: {
                        offsetY: pixData[1],
                        offsetX: pixData[0],
                    },
                });
                offsetYList.push(pixData[1]);
            }
        });
    });

    if (matches.length > 0) {
        const minvalue = minBy(offsetYList, (num: number) => Math.abs(num - offsetY));
        if (Math.abs(offsetY - minvalue) < 4) {
            const index = offsetYList.indexOf(minvalue);
            return matches[index];
        }
    }
    return null;
};
export const getConditionWidth = (text: string, fontSize?: number) => {
    const _span = document.createElement('span');
    // 放入文本
    _span.innerText = text;
    // 设置文字大小
    _span.style.fontSize = (fontSize ? fontSize : 14) + 'px';
    // span元素转块级
    _span.style.position = 'absolute';
    // span放入body中
    document.body.appendChild(_span);
    // 获取span的宽度
    const width = _span.offsetWidth;
    // 从body中删除该span
    document.body.removeChild(_span);
    // 返回span宽度
    return width;
};
