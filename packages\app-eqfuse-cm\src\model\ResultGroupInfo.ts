export class ResultGroupInfo {
  public groupConfigId: number | null;
  public unmatchingCount: number;
  public warningCount: number;
  public matchingCount: number;
  public missingCount: number;
  public totalCount: number;
  public parameterName: string;
  public name: string;
  public id: number | null;
  public groupId: number | null;
  public parentId: number | null;
  public parentGroupId: number | null;
  public isGroup: boolean;
  public isAnalysisResultRow: boolean;
  public results: any[];
  public unMatchingTop5: any[];
  public resultRequestId: string;
  public categoryOrder: number;

  constructor() {
    this.groupConfigId = null;
    this.unmatchingCount = 0;
    this.warningCount = 0;
    this.matchingCount = 0;
    this.missingCount = 0;
    this.totalCount = 0;
    this.parameterName = '';
    this.name = '';
    this.id = null;
    this.groupId = null;
    this.parentId = null;
    this.parentGroupId = null;
    this.isGroup = false;
    this.isAnalysisResultRow = false;
    this.results = [];
    this.unMatchingTop5 = [];
    this.resultRequestId = '';
    this.categoryOrder = 0;
  }

  getValue(): string {
    return this.totalCount < 1
      ? 'N/A'
      : this.unmatchingCount +
          this.warningCount +
          '/' +
          this.totalCount +
          ' | ' +
          this.getUnmatchingWarningRatio() +
          '%';
  }

  getMatchValue(): string {
    return this.totalCount < 1
      ? 'N/A'
      : this.matchingCount + '/' + this.totalCount + ' | ' + this.getMatchingRatio() + '%';
  }

  getUnmatchingRatio(): string {
    return this.totalCount < 1 ? '0' : ((this.unmatchingCount / this.totalCount) * 100).toFixed(2);
  }

  getWarningRatio(): string {
    return this.totalCount < 1 ? '0' : ((this.warningCount / this.totalCount) * 100).toFixed(2);
  }

  getUnmatchingWarningRatio(): string {
    return this.totalCount < 1
      ? '0'
      : (((this.unmatchingCount + this.warningCount) / this.totalCount) * 100).toFixed(2);
  }

  getMatchingRatio(): string {
    return this.totalCount < 1 ? '0' : ((this.matchingCount / this.totalCount) * 100).toFixed(2);
  }

  getMissingRatio(): string {
    return this.totalCount < 1 ? '0' : ((this.missingCount / this.totalCount) * 100).toFixed(2);
  }

  // 判断这行是不是最差的top5
  isUnMatchingTop5(resultRow: any) {
    return this.unMatchingTop5.findIndex((e) => e.parameterName == resultRow.parameterName) != -1;
  }

  getUnMatchingTop5ParameterNames() {
    return this.unMatchingTop5.map((e) => e.parameterName);
  }

  // 获取结果集最最差匹配的前5，规则是按匹配百分比从小到大排序，按没有匹配率降序，按警告降序
  // 这里决定了默认排序顺序*sort
  setUnMatchingTop5() {
    this.unMatchingTop5 = this.results
      .sort((r1, r2) => {
        // 首先处理 notSelected 状态，notSelected 为 true 的排在最后
        if (r1.notSelected && !r2.notSelected) return 1;
        if (!r1.notSelected && r2.notSelected) return -1;
        // 其次处理 inActive 状态，inActive 为 true 的排在倒数第二
        if (r1.inActive && !r2.inActive) return 1;
        if (!r1.inActive && r2.inActive) return -1;
        // 其他按照原来的排序逻辑
        const matchingPercentageCmp = r1.matchingPercentage - r2.matchingPercentage;
        if (matchingPercentageCmp === 0) {
          const unmatchingRcpCntCmp =
            r2.unmatchingRecipeSteps.length - r1.unmatchingRecipeSteps.length;
          if (unmatchingRcpCntCmp === 0) {
            const warningRcpCntCmp = r2.warningRecipeSteps.length - r1.warningRecipeSteps.length;
            if (warningRcpCntCmp === 0) {
              return r1.parameterName < r2.parameterName ? -1 : 1;
            } else {
              return warningRcpCntCmp;
            }
          } else {
            return unmatchingRcpCntCmp;
          }
        }
        return matchingPercentageCmp;
      })
      .slice(0, 5);
  }
}
