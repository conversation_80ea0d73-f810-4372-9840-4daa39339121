<script setup lang="ts">
import { ref, computed } from 'vue';
const listData = defineModel<any>('listData');

const ITEM_HEIGHT = 32; // 每项高度(px)
const VISIBLE_COUNT = 12; // 可视区显示项数
const scrollTop = ref(0);
const totalCount = computed(() => listData.value.length);

// 优化startIndex和endIndex，避免滚动到最底部或最顶部时留白
const startIndex = computed(() => {
    const raw = Math.floor(scrollTop.value / ITEM_HEIGHT);
    // 只有数据多于一屏时才需要回退，否则为0
    if (totalCount.value <= VISIBLE_COUNT) return 0;
    if (raw > totalCount.value - VISIBLE_COUNT) {
        return totalCount.value - VISIBLE_COUNT;
    }
    return raw;
});
const endIndex = computed(() => {
    if (totalCount.value <= VISIBLE_COUNT) return totalCount.value;
    return Math.min(startIndex.value + VISIBLE_COUNT, totalCount.value);
});
const visibleOptions = computed(() => listData.value.slice(startIndex.value, endIndex.value));
const onScroll = (e: Event) => {
    scrollTop.value = (e.target as HTMLElement).scrollTop;
};
</script>

<template>
    <div class="virtual-scroll-parameter" @scroll="onScroll">
        <a-row v-for="(rowData, i) in listData" :key="i" class="virtual-row">
            <slot name="virtual-row" :row-data="rowData"></slot>
        </a-row>
        <!-- <div
            class="virtual-scroll-list"
            :style="{
                height: Math.max(totalCount, VISIBLE_COUNT) * ITEM_HEIGHT + 'px',
            }"
        >
            <a-row
                v-for="(rowData, i) in visibleOptions"
                :key="startIndex + i"
                class="virtual-row"
                :style="{
                    top: (startIndex + i) * ITEM_HEIGHT + 'px',
                    height: ITEM_HEIGHT + 'px',
                }"
            >
                <slot name="virtual-row" :row-data="rowData"></slot>
            </a-row>
        </div> -->
    </div>
</template>

<style lang="less" scoped>
@import url('@/assets/style/variable.less');
.virtual-scroll-parameter {
    max-height: 380px;
    width: max-content;
    overflow-y: auto;
    // overflow-x: hidden;
    box-sizing: border-box;
    position: relative;
    padding: 0;
    // 显示滚动条
    &:hover {
        &::-webkit-scrollbar-thumb {
            background-color: @bg-scroll-color;
        }
    }
    &::-webkit-scrollbar-corner {
        background-color: @bg-scroll-color;
    }
}
.virtual-scroll-list {
    width: max-content;
    position: relative;
    .virtual-row {
        position: absolute;
        left: 0;
        right: 0;
        display: flex;
        align-items: center;
        width: max-content;
    }
}
</style>
