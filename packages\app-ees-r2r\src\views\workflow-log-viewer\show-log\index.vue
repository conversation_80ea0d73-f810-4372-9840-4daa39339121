<script lang="ts" setup>
import { onMounted, reactive, watch } from 'vue';
import config from './config';
import { getLogs } from '@futurefab/ui-sdk-api';
import { cloneDeep } from 'lodash-es';
import { workflow<PERSON>og<PERSON>iewer as buttonIds } from '@/log-config/index';
import { EesJsonPretty } from '@futurefab/ui-sdk-ees-workflow';

const props = withDefaults(
  defineProps<{
    showLog?: boolean;
    logs: any[];
    bodyParams?: { tid: string; methodName: string } | null;
  }>(),
  {
    showLog: false,
    bodyParams: null,
    logs: () => []
  }
);
const params = reactive({
  config: {} as { [key: string]: any },
  list: [] as any[],
  currentCell: {
    title: '',
    val: '',
    visible: false
  }
});
onMounted(() => {
  params.config = cloneDeep(config);
});
const requestLogs = () => {
  params.config.loading = true;
  getLogs({
    headerParams: {
      log: 'Y',
      page: buttonIds.pageId,
      action: buttonIds.showLogs
    },
    bodyParams: {
      ...props.bodyParams
    }
  }).then((result: any) => {
    if (result.status == 'SUCCESS') {
      if (result.data) {
        result.data.dataList &&
          Array.isArray(result.data.dataList) &&
          result.data.dataList.forEach((item: any) => {
            if (item.Input) {
              item.Input = JSON.stringify(item.Input);
            }
          });
        params.list = result.data.dataList;
      }
      params.config.loading = false;
    }
  });
};
const events = {
  cellDblclick: ({ column, row }: { column: { title: string; field: string }; row: any }) => {
    const list = ['Input', 'CustomLog', 'Output', 'ErrorLog'];
    if (list.includes(column.field) && (row[column.field] || row[column.field] === false)) {
      params.currentCell = {
        title: column.title,
        val:
          column.field === 'Input' && row[column.field]
            ? JSON.parse(row[column.field])
            : row[column.field],
        visible: true
      };
    }
  },
  toolbarToolClick: ({ code }: { code: string }) => {
    if (code == 'refresh') {
      requestLogs();
    }
  }
};
watch(
  () => props.showLog,
  (val: boolean) => {
    if (val) {
      if (props.bodyParams?.tid) {
        requestLogs();
      }
    }
  },
  { immediate: true }
);
</script>
<template>
  <vxe-grid :data="bodyParams ? params.list : logs" v-bind="params.config" v-on="events" />
  <ees-json-pretty
    :json-val="params.currentCell.val"
    :title="params.currentCell.title"
    v-model:visible="params.currentCell.visible"
  ></ees-json-pretty>
</template>
