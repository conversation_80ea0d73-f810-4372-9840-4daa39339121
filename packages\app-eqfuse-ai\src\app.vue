<template>
  <router-view v-slot="{ Component }">
    <keep-alive>
      <component :is="Component" v-if="route?.meta.keepAlive" :key="route.path" />
    </keep-alive>
    <component :is="Component" v-if="!route?.meta.keepAlive" :key="route.path" />
  </router-view>
</template>

<script setup lang="ts">
import { RouterView } from 'vue-router';
import { useRoutes } from '@futurefab/ui-framework';
import { routes } from './router/index';
import { useRoute } from 'vue-router';
const route = useRoute();

useRoutes(routes);
</script>

<style scoped></style>

