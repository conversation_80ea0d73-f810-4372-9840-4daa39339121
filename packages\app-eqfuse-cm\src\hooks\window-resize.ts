import { ref, onUnmounted, nextTick } from 'vue';

function useWindowResize() {
    const searchConditionHeight = ref(0);
    let dom = null as HTMLElement | null;
    const handleResize = () => {
        if (dom) {
            searchConditionHeight.value = dom.offsetHeight;
        }
    };
    const bindResize = (searchConditionRef: HTMLElement) => {
        setTimeout(() => {
            dom = searchConditionRef;
            getSearchConditionHeight();
        }, 50);
        window.addEventListener('resize', handleResize);
    };
    const getSearchConditionHeight = () => {
        nextTick(() => {
            if (dom) searchConditionHeight.value = dom.offsetHeight;
        });
    };
    onUnmounted(() => {
        window.removeEventListener('resize', handleResize);
    });
    return { searchConditionHeight, bindResize, getSearchConditionHeight };
}

export default useWindowResize;
