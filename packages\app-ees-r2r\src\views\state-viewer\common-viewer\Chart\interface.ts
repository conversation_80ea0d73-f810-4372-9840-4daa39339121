export interface Parameter {
  category: string;
  dataType: string;
  eqpAlias: string;
  param: string;
  priority: string;
  unit: string | null;
}

export interface Value {
  code: string;
  name: string;
}
export interface GroupValue extends Value {
  group: string;
  filter: boolean;
}
export interface GroupTableValue extends Value {
  itemName: string;
  isGrouping: boolean;
  isFilter: boolean;
  filterList: GroupValue[];
  checked: boolean;
}
export type SumType = Value;
export type Category = Value;
export type StepList = Value;
export interface Eqp {
  alias: string;
  eqpId: string;
  moduleId: string;
  rawId: number;
  name?: string;
}

export interface LeftFilterType {
  eqpList: Eqp[];
  categoryList: Category[];
  typeList: SumType[];
  stepList: StepList[];
}

export interface ChartDataType {
  type: number;
  layout: number;
  kind: string;
  isSplitParam: boolean;
  paramList?: string[];
  x: string[];
  y: string[];
}

export interface LegendTree {
  label: string;
  children: string[];
}

export interface DataIndex {
  seriesIndex: number;
  dataIndex: number;
}
export interface ButtonAuthority {
  allButtonFlag: boolean;
  buttonList: string[];
}
