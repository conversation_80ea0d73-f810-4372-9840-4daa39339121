<script lang="ts" setup>
import type { TabListType } from './interface';
defineProps<{ activeTab: string | number; tabList: Array<TabListType> }>();
const emit = defineEmits<{
    (e: 'handleTab', val: TabListType): void;
}>();
const handleTab = (val: TabListType) => emit('handleTab', val);
</script>
<template>
    <div class="eqp_tab">
        <div class="eqp_tab_item" @click="handleTab(item)" v-for="(item, i) in tabList" :key="i">
            <span class="eqp_tab_item_text" :class="{ active: activeTab === item.value }">{{
                item.label
            }}</span>
        </div>
    </div>
</template>

<style scoped lang="less">
@import '@/assets/style/variable.less';
.eqp_tab {
    display: flex;
    flex-wrap: wrap;
    &_item {
        cursor: pointer;
        display: flex;
        &_text {
            padding: 0 @padding-basis;
            height: 24px;
            line-height: 24px;
            box-sizing: border-box;
            display: inline-block;
            border: 1px solid @border-color;
            border-left: none;
            color: @text-subtitle-color;
            &.active {
                border: 1px solid @primary-color;
                border-left: none;
                font-weight: 600;
                color: @primary-color;
                font-size: @font-size-basis;
            }
        }
        &:first-child {
            .eqp_tab_item_text {
                border-left: 1px solid @border-color;
                border-radius: @border-radius-basis 0 0 @border-radius-basis;
                &.active {
                    border-left: 1px solid @primary-color;
                }
            }
        }
        &:last-child {
            .eqp_tab_item_text {
                border-left: 1px solid @border-color;
                border-radius: 0 @border-radius-basis @border-radius-basis 0;
                &.active {
                    border-left: 1px solid @primary-color;
                }
            }
        }
        &_popover {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background-color: @error-color;
            color: @color-default;
            margin-left: 4px;
            line-height: 20px;
            text-align: center;
        }
        &_line {
            width: 1px;
            height: 20px;
            background-color: @border-color;
            margin: 0 10px;
        }
    }
}
</style>
