import { matrix, lusolve } from 'mathjs';
export interface Measuredata {
    data: string[][];
    header: string[];
}
export interface SamplePoint {
    x: number;
    y: number;
    value: number;
}
export function interpolate(
    x: number,
    y: number,
    points: SamplePoint[],
    weights: number[],
    c: number[],
) {
    let result = c[0] + c[1] * x + c[2] * y;
    for (let i = 0; i < points.length; i++) {
        const dx = x - points[i].x;
        const dy = y - points[i].y;
        const r = Math.sqrt(dx ** 2 + dy ** 2);
        result += weights[i] * (r === 0 ? 0 : r ** 2 * Math.log(r));
    }
    return result;
}
export function thinPlateSplineInterpolate(points: SamplePoint[]) {
    const n = points.length;
    const matrixSize = n + 3;
    const A = matrix().resize([matrixSize, matrixSize]);
    const b = matrix().resize([matrixSize]);

    // 填充K和P矩阵
    for (let i = 0; i < n; i++) {
        for (let j = 0; j < n; j++) {
            const dx = points[i].x - points[j].x;
            const dy = points[i].y - points[j].y;
            const r = Math.sqrt(dx ** 2 + dy ** 2);
            A.set([i, j], r === 0 ? 0 : r ** 2 * Math.log(r));
        }
        A.set([i, n], 1);
        A.set([i, n + 1], points[i].x);
        A.set([i, n + 2], points[i].y);
        b.set([i], points[i].value);
    }

    // 填充约束条件
    for (let j = 0; j < n; j++) {
        A.set([n, j], 1);
        A.set([n + 1, j], points[j].x);
        A.set([n + 2, j], points[j].y);
    }

    // 求解线性方程组
    const x = lusolve(A, b);
    return {
        weights: (x as any)._data.slice(0, n).map((item: any[]) => item[0]),
        c: (x as any)._data.slice(n, n + 3).map((item: any[]) => item[0]),
    };
}
