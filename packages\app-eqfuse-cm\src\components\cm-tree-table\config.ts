import { debounce } from 'lodash-es';
import { computed, ref, type Ref, watch } from 'vue';

export const useRowVirtualScroll = (props: any, container: Ref<any>, boxMarginTop: Ref<number>) => {
    const expendCount = 10; // 前后顺延的行数
    const visibleData = ref<any[]>([]);
    const totalHeight = ref(0);
    const startIndex = ref(0);
    const endIndex = ref(0);
    const scrollTop = ref(0);

    let beforeStart = 0;
    let beforeEnd = 0;
    watch(
        () => props.treeData,
        () => {
            updateRowVisibleData();
        },
        { deep: false },
    );
    const totalItems = computed(() =>
        props.treeData.reduce(
            (total: number, item: any) => (total += item.expand ? item.children.length + 1 : 1),
            0,
        ),
    );
    function updateRowVisibleData(isScroll = false) {
        if (!container.value) return;
        // console.time('useRowVirtualScroll');

        // 计算全部高度
        const totalItemHeight = totalItems.value * props.itemHeight;
        totalHeight.value = totalItemHeight + props.headerHight;
        // 滚动条高度
        scrollTop.value = container.value.scrollTop;
        const shouldStart = Math.floor(scrollTop.value / props.itemHeight);
        startIndex.value = Math.max(shouldStart - expendCount, 0);
        boxMarginTop.value =
            scrollTop.value -
            (scrollTop.value % props.itemHeight) -
            (shouldStart - startIndex.value) * props.itemHeight;
        // 可视区域的高度 - header区域的高度
        const viewHeigth = container.value.clientHeight - props.headerHight;
        const itemsInView = Math.ceil(viewHeigth / props.itemHeight + 1);

        endIndex.value = Math.min(totalItems.value, shouldStart + itemsInView + expendCount);
        console.log(startIndex.value, endIndex.value, beforeStart, beforeEnd);

        if (isScroll && startIndex.value === beforeStart && endIndex.value === beforeEnd) {
            // 触底或轻微滚动 或 横向滚动
            // console.timeEnd('useRowVirtualScroll');
            return;
        }
        const temp = [];
        let currentIndex = 0;

        for (let i = 0; i < props.treeData.length; i++) {
            const treeParent = props.treeData[i];
            if (currentIndex >= startIndex.value && currentIndex < endIndex.value) {
                temp.push({ ...treeParent, children: [] });
            } else if (
                treeParent.expand &&
                currentIndex < startIndex.value &&
                currentIndex + treeParent.children.length >= startIndex.value
            ) {
                temp.push({ ...treeParent, children: [], hideParent: true });
            }
            currentIndex++;
            if (currentIndex > endIndex.value) {
                break;
            }
            if (treeParent.expand) {
                for (let j = 0; j < treeParent.children.length; j++) {
                    const treeChild = treeParent.children[j];
                    if (currentIndex >= startIndex.value && currentIndex < endIndex.value) {
                        temp[temp.length - 1].children.push(treeChild);
                    }
                    currentIndex++;
                    if (currentIndex > endIndex.value) {
                        break;
                    }
                }
            }
            if (currentIndex > endIndex.value) {
                break;
            }
        }
        beforeStart = startIndex.value;
        beforeEnd = endIndex.value;
        visibleData.value = temp;
        // console.timeEnd('useRowVirtualScroll');
    }
    return { visibleData, updateRowVisibleData, totalHeight };
};

export const useColVirtualScroll = (props: any, container: Ref<any>, firstColWidth: Ref<number>) => {
    const expendCount = 2; // 前后各顺延的列数
    const visibleColumn = ref<any[]>();
    const totolWidth = computed(
        () => firstColWidth.value + (props.columns.length - 1) * props.otherColWidth,
    );
    const startIndex = ref(0);
    const endIndex = ref(0);
    const scrollLeft = ref(0);
    let beforeStart = 0;
    let beforeEnd = 0;
    const fisrtItemColLeft = ref(0);
    watch(
        () => props.columns,
        () => {
            updateColumn();
        },
    );
    const updateColumn = (isScroll = false) => {
        // console.time('updateColumn');
        // 滚动条左侧距离
        scrollLeft.value = container.value.scrollLeft;
        const shouldStart = Math.floor(scrollLeft.value / props.otherColWidth);
        startIndex.value = Math.max(shouldStart - expendCount, 0);
        // 第一个可视区域的 col 的 margin left
        fisrtItemColLeft.value = startIndex.value * props.otherColWidth;
        // 可视区域的宽度 - 第一列的宽度
        const viewHeigth = container.value.clientWidth - firstColWidth.value;
        const itemsInView = Math.ceil(viewHeigth / props.otherColWidth + 2);
        endIndex.value = Math.min(
            props.columns.length - 1,
            shouldStart + itemsInView + expendCount,
        );

        if (isScroll && startIndex.value === beforeStart && endIndex.value === beforeEnd) {
            // 触底或轻微滚动 或 纵向滚动
            // console.timeEnd('updateColumn');
            return;
        }
        visibleColumn.value = [
            {
                ...props.columns[0],
                visibleColIndex: 0,
            },
            ...props.columns
                .slice(startIndex.value + 1, endIndex.value + 1)
                .map((item: any, index: number) => {
                    item.visibleColIndex = index + startIndex.value + 1;
                    return item;
                }),
        ];
        beforeStart = startIndex.value;
        beforeEnd = endIndex.value;
        // console.timeEnd('updateColumn');
    };
    return {
        visibleColumn,
        totolWidth,
        fisrtItemColLeft,
        scrollLeft,
        updateColumn,
    };
};

export const useDragFirstCol = (firstColWidth: Ref<number>, container: Ref<HTMLElement>) => {
    let startOffset: null | number = null;
    let width: null | number = null;
    let maxWidth: null | number = null;
    const dragging = ref(false);
    const dragstart = (event: DragEvent) => {
        startOffset = event.clientX;
        width = firstColWidth.value;
        maxWidth = container.value.clientWidth;
        dragging.value = true;
    }
    const drag = debounce((event: DragEvent) => {
        if (startOffset === null || width === null || maxWidth === null) {
            return;
        }
        const cur = event.clientX;
        if(!cur) {
            return;
        }
        const gap = cur - startOffset!;
        let res = width! + gap;
        if (res < 0) {
            res = 0;
        }
        if (res > maxWidth!) {
            res =  maxWidth!;
        }
        firstColWidth.value = res;
    }, 10)
    const dragend = debounce((event: DragEvent) => {
        startOffset = null;
        width = null;
        dragging.value = false;
    }, 20)
    return {
        drag,
        dragstart,
        dragend,
        dragging
    }
}