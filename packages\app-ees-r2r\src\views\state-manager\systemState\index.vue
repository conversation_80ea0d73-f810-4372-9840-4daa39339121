<script lang="ts" setup>
import { ref, reactive, provide, onBeforeMount } from 'vue';
import { getSystemStateList, getUserDefineStateItemList } from '@futurefab/ui-sdk-api';
import { xConfig, dConfig } from './config';
import { Splitpanes, Pane } from 'splitpanes';
import { stateCnText, stateUsText } from '@/docs/state-manager/index';
import { EesTabs } from '@futurefab/ui-sdk-ees-basic';
const xGrid = ref();
const dGrid = ref();
const xTable = reactive({
  data: [] as any[],
  options: xConfig,
  currentRow: {},
  loading: false
});
const dTable = reactive({
  data: [] as any[],
  options: dConfig,
  loading: false
});
const gridEvents = {
  currentChange: ({ row }: { row: any }) => {
    console.log(row, !!row.children);
    // if (row.children) {
    if (!row.rawId) {
      xGrid.value?.setCurrentRow(xTable.currentRow);
    } else {
      xTable.currentRow = xGrid.value?.getCurrentRecord();
    }
  }
};
const typeOptions: string[] = ['Controller', 'Predictor', 'Optimizer', 'Deadband'];
const getList = async () => {
  const res = await getSystemStateList({ bodyParams: {} });
  if (res.status !== 'SUCCESS') return false;
  xTable.data = res.data;
  dTable.data = [];
  let tree: any = [];
  res.data.forEach((item: any) => {
    if (!item.rawId) {
      tree.push({ ...item, children: [] });
    } else {
      tree.find((v: any) => v.id === item.pid)?.children?.push(item);
    }
  });
  console.log('systemTree', tree);
  xTable.data = tree;
};
const getDetail = async (rawId: number) => {
  if (!rawId) return;
  const res = await getUserDefineStateItemList({ bodyParams: { rawId } });
  if (res.status !== 'SUCCESS') return;
  dTable.data = res.data;
};
onBeforeMount(async () => {
  getList();
});
const getContent = () => {
  const lang = localStorage.getItem('language');
  return lang == 'en-US' ? stateUsText : stateCnText;
};
</script>

<template>
  <div class="r2r-state-system">
    <splitpanes class="default-theme" style="height: 100%; width: 100%">
      <pane class="cardItem1" size="30">
        <vxe-grid
          ref="xGrid"
          border="none"
          class="left-grid"
          :show-header="false"
          :data="xTable.data"
          v-bind="xTable.options"
          highlight-current-row
          v-on="gridEvents"
        >
          <template #item="{ row }">
            <div
              :class="[row.children ? 'cursor-default' : 'cursor-pointer']"
              @click="getDetail(row.rawId)"
            >
              {{ row.name }}
            </div>
          </template>
        </vxe-grid>
      </pane>
      <pane class="cardItem2 pane-border-radius-top-right pane-border-radius-bottom-right" size="70">
        <vxe-grid
          ref="dGrid"
          class="vxe-toolbar-top-right-radius-lg vxe-table-bottom-right-radius-lg"
          :show-header="false"
          :data="dTable.data"
          v-bind="dTable.options"
        >
          <template #item="{ row }">
            <span>
              {{ row.itemName }}
              <span v-show="row.description" class="desc"> - {{ row.description }} </span>
            </span>
          </template>
          <template #beforeTools>
            <vxe-tooltip
              :use-h-t-m-l="true"
              :enterable="true"
              theme="light"
              :content="getContent()"
            >
              <button class="vxe-button type--button icon-type--svgButton">
                <svg class="icon" aria-hidden="true">
                  <use xlink:href="#icon-btn-help"></use>
                </svg>
              </button>
            </vxe-tooltip>
          </template>
        </vxe-grid>
      </pane>
    </splitpanes>
  </div>
</template>

<style lang="less" scoped>
@import url('@/assets/style/variable.less');
@ns: r2r-state-system;
.@{ns} {
  height: 100%;
  .cardItem1 {
    border-radius: @border-radius-lg 0 0 @border-radius-lg;
  }
  :deep(.splitpanes__pane) {
    display: block !important;
  }
  :deep(.ees-tabChange-wrapper .ees-tabChange-horizontal) {
    padding: @padding-xs;
  }
  .cursor-default {
    cursor: default;
    font-weight: @font-weight-lg;
  }
  .cursor-pointer {
    width: 100%;
    cursor: pointer;
    height: 32px;
    line-height: 32px;
    padding-left: @padding-xs - 4px;
    &:hover {
      background-color: @bg-hover-1-color;
      color: @primary-color;
      border-radius: @border-radius-xs;
    }
  }
  .desc {
    font-style: italic;
  }
  .left-grid {
    :deep(.vxe-toolbar .vxe-gird-table-name--wrapper .vxe-table-name-text) {
      line-height: 32px;
    }
    :deep(.vxe-table--render-default .vxe-body--row.row--current) {
      background-color: @bg-block-color;
      color: @primary-color;
      font-weight: @font-weight-lg;
    }
    :deep(.vxe-table--render-default .vxe-body--column:not(.col--ellipsis)) {
      padding: 0;
    }
  }
}
</style>
