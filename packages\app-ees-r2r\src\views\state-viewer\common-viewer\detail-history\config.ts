import { VXETable, type VxeGridProps, t } from '@futurefab/vxe-table';
import { stateViewer as buttonIds } from '@/log-config/index';
import { tableDefaultConfig } from '@futurefab/ui-sdk-ees-basic';
export const xConfig = tableDefaultConfig({
  id: 'StateDetailHistoryKey',
  borderOutLine: [1, 1, 1, 1],
  columns: [],
  checkboxConfig: {
    showHeader: true
  }
} as VxeGridProps);
export const dGetConfig = (type: any) => {
  return tableDefaultConfig({
    id: 'StateDetailHistory',
    borderOutLine: [1, 1, 1, 1],
    toolbarConfig: {
      tableName: 'common.title.dataView',
      import: false,
      export: false,
      refresh: false,
      custom: false,
      zoom: false,
      border: true,
      tools: [
        ...VXETable.tableFun.getToolsButton([
          {
            name: 'common.btn.exportData',
            icon: 'icon-btn-export',
            id: buttonIds[type].export,
            visible: false
          }
        ])
      ]
    },
    columns: [],
    checkboxConfig: {
      showHeader: true
    },
    columnCheckboxConfig: {
      checkMethod: () => false
    }
  } as VxeGridProps);
};
