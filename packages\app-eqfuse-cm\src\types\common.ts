import type { Dayjs } from "dayjs";

export interface UnknownData {
    [propName: string]: any;
}
export interface ResultData {
    code: string;
    count: string;
    data: Array<UnknownData> | UnknownData;
    msg: string | null;
    page: string;
    status: string;
}

export interface SearchConditionInputData {
    site: string | null;
    fab: string | null;
    line: string | null;
    lineRawId: string | null;
    location: string;
    locationRawId: string | null;
    area: string;
    areaRawId: string | null;
    model: string;
    modelRawId: string | null;
    eqp: string;
    eqpRawId: string | null;
    modelId: string | null;
}

export interface SearchConditionTreeInputData {
    locationID: string | number | null;
    areaID: string | number | null;
    modelID: string | number | null;
    eqpName: string[] | number[] | null;
    eqpRawId: string[] | number[] | null;
    modelName: string | null;
    contextResult?: ContextResult;
}

export type RangeValue = [Dayjs, Dayjs];

export interface ContextResult {
    lotId: string | number | undefined;
    lotIdsFlag: boolean;
    product: string | number | undefined;
    productIdsFlag: boolean;
    recipeId: string | number | undefined;
    recipeIdsFlag: boolean;
    substrateId: string | number | undefined;
    substrateIdsFlag: boolean;
}
