/* 主要存放公共的样式 */
@import './variable.less';
@import './border-radius.less';
@import './flex.scss';
.full-modal,
.modal-max-height {
    .ant-modal {
        max-width: 100%;
        top: 0;
        padding-bottom: 0;
        margin: 0;
    }
    .ant-modal-content {
        display: flex;
        flex-direction: column;
        height: calc(100vh);
    }
    .ant-modal-body {
        flex: 1;
    }
    .ant-modal-close {
        // color: @primary-font-color-3;
        color: var(--vxe-ui-table-icon-color);
        :hover {
            color: var(--primary-color);
        }
    }
    .ant-modal-close-icon {
        font-size: 16px !important;
    }
}
.ant-popover {
    z-index: 99999 !important;
}
.ant-picker-dropdown,
.ant-select-dropdown {
    z-index: 99999 !important;
}
.vxe-button.type--button.icon-type--iconButton,
.vxe-button.type--button.icon-type--svgButton {
    color: @primary-color;
}
.ant-input-borderless {
    background-color: @border-special-color !important;
    .ant-input-disabled {
        background-color: @border-special-color !important;
    }
}
// searchbar里面的Condition小圆角
.search-br-xs {
    border-radius: @border-radius-basis !important;
}
.sidebar-no-padding {
    .ant-drawer-body {
        padding: 0 !important;
    }
}

//在弹窗中，vxe-table的标题的左padding为0
.vxe-toolbar-padding-left-zero {
    .vxe-toolbar {
        padding-left: 0 !important;
    }
}
