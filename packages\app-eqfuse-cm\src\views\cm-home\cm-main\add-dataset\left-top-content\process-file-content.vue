<script lang="ts" setup>
import { ref, reactive } from 'vue';
import type { UploadChangeParam } from '@futurefab/ant-design-vue';
import { uploadCsvXmlFile, getTxtAnalysisResult } from '@futurefab/ui-sdk-api';
import { showWarning, uuid } from '@futurefab/ui-sdk-ees-basic';
import { cmMainInfo as buttonIds } from '@/log-config';
import { createZipFromFiles } from '@futurefab/ui-sdk-eqfuse-common';

const runByModel = defineModel('runByModel');
const groupLoading = defineModel('groupLoading');
const emits = defineEmits(['setWaferData', 'setFileNames']);

const processFileTips = `Only TXT format is supported.`;

const defaultFileInfo = {
  fileList: [],
  names: new Map(),
  headers: {
    authorization: 'authorization-text'
  }
};
const processFileInfo = reactive(defaultFileInfo);

const deRepeatFile = () => {
  let arr: any = [];
  processFileInfo.fileList.forEach((e: any) => {
    let found = arr.findIndex((e2: any) => e2.name === e.name) > -1;
    if (!found) arr.push(e);
  });
  processFileInfo.fileList = arr;
};
const customUpload = () => {
  // console.log('自定义上传d.fileList:', processFileInfo.fileList);
};
let handleDrop = (e: DragEvent) => {
  // console.log(e);
};
const handleChange = (info: UploadChangeParam) => {
  const status = info.file.status;
  if (status !== 'uploading') {
    console.log(info.file, info.fileList);
  }
  deRepeatFile();
};
const handleUploadClearBox = (e: any) => {
  e.stopPropagation();
};
const handleUploadClear = (e: any) => {
  e.stopPropagation();
  processFileInfo.fileList = [];
  processFileInfo.names = new Map();
};
// 解析txt文件
const getProcessFileWaferData = async () => {
  groupLoading.value = true;
  const isLoop = runByModel.value === 'loop';
  let promiseArray: any = [];
  let errorMsg: any = [];
  for(let i = 0; i < processFileInfo.fileList.length; i++) {
    const file: any = processFileInfo.fileList[i];
    let zipFile: any = file.originFileObj;
    try {
      zipFile = await createZipFromFiles([file.originFileObj], uuid() + new Date().getTime());
    } catch (e) {
    }
    let promise = uploadCsvXmlFile({
      bodyParams: {
        fileInfo: zipFile
      },
      headerParams: {
        routeFrom: 'wees-gateway-cm',
        log: 'Y'
      }
    });
    promise.then((res: any) => {
      if (res?.status === 'FAIL') {
        errorMsg.push(res.msg);
      }
      promiseArray.push(res);
    });
    promiseArray.push(promise);
  }

  if (!promiseArray.length) {
    groupLoading.value = false;
    return showWarning('cm.tips.noFile');
  }
  if (errorMsg.length > 0) return showWarning(errorMsg?.join('，'), false);
  // 清空文件名，防止数据混乱
  processFileInfo.names = new Map();
  Promise.all(promiseArray)
    .then(async () => {
      processFileInfo.fileList.forEach(({ name }) => {
        if (processFileInfo.names.get(runByModel.value)?.size > 0) {
          const tempData = processFileInfo.names.get(runByModel.value);
          tempData.add(name);
          processFileInfo.names.set(runByModel.value, tempData);
        } else {
          const tempSet = new Set();
          tempSet.add(name);
          processFileInfo.names.set(runByModel.value, tempSet);
        }
      });
      const fileNames = [...processFileInfo.names.get(runByModel.value)];
      const params = { loop: isLoop, fileNames };
      await getTxtAnalysisResult({
        bodyParams: params,
        headerParams: {
          log: 'Y',
          page: buttonIds.pageId,
          action: 'get-wafer-list-by-csv'
        }
      })
        .then((response: any) => {
          if (response.status === 'SUCCESS') {
            response.data.data = response.data?.data?.map((item: any) => ({
              ...item,
              wafer: item?.waferId?.split(';')?.[0],
              fileName: item?.waferId?.split(';')?.[1]
            }));
            const tempData = response.data;
            emits('setWaferData', tempData);
            // 设置fileNames
            emits('setFileNames', [...processFileInfo.names.get(runByModel.value)].flat());
          }
          groupLoading.value = false;
        })
        .catch(() => (groupLoading.value = false));
    })
    .catch(() => (groupLoading.value = false));
};

defineExpose({ getProcessFileWaferData });
</script>

<template>
  <div class="process-file-content">
    <div class="process-file-content-upload-box">
      <span class="label">{{ $t('cm.title.csvFileImport') }}</span>
      <a-upload
        v-model:file-list="processFileInfo.fileList"
        accept=".txt"
        :multiple="true"
        name="file"
        :custom-request="customUpload"
        :headers="processFileInfo.headers"
        :before-upload="() => false"
        :show-upload-list="{ showRemoveIcon: true }"
        @change="handleChange"
        @drop="handleDrop"
      >
        <div class="upload-header">
          <a-button class="upload-header-left">
            <i class="iconfont icon-upload" />
            {{ $t('cm.btn.addFiles') }}
          </a-button>
          <div
            v-if="processFileInfo.fileList.length > 0"
            class="upload-header-right"
            @click="handleUploadClearBox"
          >
            <span class="count-text">
              <span class="hint-text">{{ $t('cm.title.fileCount') }}:</span>
              {{ processFileInfo.fileList.length }}
            </span>
            <a @click="handleUploadClear" class="clear-btn">
              <i class="iconfont icon-clear hint-text" />{{ $t('cm.btn.clearFiles') }}
            </a>
          </div>
        </div>
        <a-typography-paragraph class="fileTips">
          {{ processFileTips }}
        </a-typography-paragraph>
      </a-upload>
    </div>
  </div>
</template>

<style lang="less" scoped>
@import url('@/assets/style/variable.less');
.process-file-content {
  flex: 1;
  display: flex;
  overflow: hidden;
  &-upload-box {
    flex: 1;
    display: flex;
    overflow: hidden;
    flex-direction: row;
    align-items: flex-start;
    margin-bottom: 10px;
    .label {
      min-width: 106px;
      height: 32px;
      width: max-content;
      color: @text-title-color;
      font-weight: bold;
      display: flex;
      align-items: center;
    }
    .hint-text {
      color: @text-hint-color;
    }
    .upload-header {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      &-left {
        height: 32px;
        border-radius: 4px;
        border: 1px solid @border-color;
        color: @text-subtitle-color;
        .icon-upload {
          margin-right: 8px;
        }
      }
      &-right {
        height: 32px;
        display: flex;
        flex: 1;
        justify-content: flex-end;
        align-items: center;
        .count-text {
          font-weight: bold;
          margin-right: 10px;
          color: @text-subtitle-color;
        }
        .clear-btn {
          color: @text-subtitle-color;
          padding: 0 8px;
          .hint-text {
            margin-right: 4px;
          }
          &:hover {
            border-radius: 4px;
            background-color: @bg-active-1-color;
          }
        }
      }
    }
    .fileTips {
      margin: 4px 0 0 0;
      font-size: 12px;
    }
    // 右侧选择文件区域
    :deep(.ant-upload-wrapper) {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      height: 100%;
      .ant-upload {
        width: 100%;
        flex: 0 0 auto;
      }
      .ant-upload-list {
        flex: 1;
        overflow-y: auto;
        // 显示滚动条
        &:hover {
          &::-webkit-scrollbar-thumb {
            background-color: @bg-scroll-color;
          }
        }
        &::-webkit-scrollbar-corner {
          background-color: @bg-scroll-color;
        }
      }
    }
  }
}
</style>
