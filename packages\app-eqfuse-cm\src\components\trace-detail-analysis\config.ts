import { VXETable } from '@futurefab/vxe-table';
import { paramAliasUsText, paramAliasCnText } from '@/docs/eqp-status/eqp-report';

export function getHelpTip() {
    const lang = localStorage.getItem('language');
    return lang === 'en-US' ? paramAliasUsText : paramAliasCnText;
}

const dealFilters = (filterType: string) => {
    if (filterType == 'N') {
        return [
            {
                data: {
                    list: [
                        {
                            type: 'eq',
                            name: '',
                            val: '',
                        },
                    ],
                    condition: 'and',
                },
            },
        ];
    }
    return [{ data: '' }];
};
const dealFilterRender = (filterType: string) => {
    if (filterType == 'I') {
        return {
            name: '$input',
            events: {
                keyup: (argus: any, event: any) => {
                    const { $panel } = argus;
                    if (event.code == 'Enter') {
                        $panel.confirmFilter();
                    }
                },
            },
        };
    } else if (filterType == 'N') {
        return {
            name: '$number-filter',
        };
    }
};
const dealFilterMethod = (filterType: string, columnName: string) => {
    if (filterType == 'I') {
        return ({ option, row }: any) => {
            // 自定义插槽实现模糊搜索
            const str = `\S*${option.data}\S*`;
            const reg = new RegExp(str, 'i');
            return reg.test(row[columnName]);
        };
    }
};

export const paramOptions = VXETable.tableFun.tableDefaultConfig({
    resizable: true,
    showHeaderOverflow: true,
    showOverflow: true,
    highlightHoverRow: true,
    keepSource: true,
    height: 'auto',
    stripe: false,
    loading: false,
    checkboxConfig: {
        showHeader: false,
    },
    mouseConfig: {
        selected: true,
        area: true,
    },
    columns: [
        { type: 'checkbox', width: 40, align: 'center', headerClassName: 'sel' },
        {
            field: 'paramAlias',
            sortable: true,
            title: 'common.field.paramAlias',
            slots: { default: 'param_slot', header: 'paramAliasHelp' },
            filters: dealFilters('I'),
            filterRender: dealFilterRender('I'),
            filterMethod: dealFilterMethod('I', 'paramAlias'),
            minWidth: 220,
        },
        {
            field: 'priority',
            sortable: true,
            title: 'common.field.priority',
            filters: dealFilters('S'),
            filterRender: dealFilterRender('S'),
            filterMethod: dealFilterMethod('S', 'priority'),
        },
        {
            field: 'unit',
            sortable: true,
            title: 'common.field.unit',
            filters: dealFilters('S'),
            filterRender: dealFilterRender('S'),
            filterMethod: dealFilterMethod('S', 'unit'),
        },
    ],
});

/**
 * grouping 去重
 * @param list 源数据
 * @param key 去重的key
 * @returns
 */
export const removeRepeat = (list: any[], key: string) => {
    if (!key) return list;
    if (!list || !list.length) return [];
    list = list.filter((item: any, index: number, self: any[]) => {
        const selfIndex: number = self.findIndex((el: any) => {
            return item[key] === el[key];
        });
        return selfIndex === index;
    });
    return list;
};

// 侧边栏表格option
export const sidebarOptions = (checkRowKeys: string[]) => {
    return VXETable.tableFun.tableDefaultConfig({
        id: 'trace-detail-chart-sidebar-table',
        toolbarConfig: {
            tableName: 'common.title.lotList',
            export: false,
            import: false,
            tools: [
                ...VXETable.tableFun.getToolsButton([
                    { name: 'common.btn.return', icon: 'icon-btn-rollback' },
                    { name: 'common.btn.drawChart', icon: 'icon-btn-chart-draw' },
                    { name: 'common.btn.delete', icon: 'icon-btn-delete' },
                ]),
            ],
        },
        mouseConfig: {
            area: true,
        },
        rowConfig: {
            keyField: 'rawId',
        },
        checkboxConfig: {
            reserve: true,
            checkRowKeys,
        },
        columns: [
            {
                field: 'rawId',
                type: 'checkbox',
                width: '40px',
                align: 'center',
            },
            {
                field: 'eqpAlias',
                width: '160px',
                title: 'common.field.eqpAlias',
                sortable: true,
                filters: dealFilters('S'),
                filterRender: dealFilterRender('S'),
                filterMethod: dealFilterMethod('S', 'eqpAlias'),
            },
            {
                field: 'moduleAlias',
                width: '190px',
                title: 'common.field.moduleAlias',
                sortable: true,
                filters: dealFilters('S'),
                filterRender: dealFilterRender('S'),
                filterMethod: dealFilterMethod('S', 'moduleAlias'),
            },
            {
                field: 'lotId',
                width: '130px',
                title: 'common.field.lotId',
                sortable: true,
                filters: dealFilters('S'),
                filterRender: dealFilterRender('S'),
                filterMethod: dealFilterMethod('S', 'lotId'),
            },
            {
                field: 'substrateId',
                width: '140px',
                title: 'common.field.substrateId',
                sortable: true,
                filters: dealFilters('S'),
                filterRender: dealFilterRender('S'),
                filterMethod: dealFilterMethod('S', 'substrateId'),
            },
            {
                field: 'slot',
                width: '80px',
                title: 'common.field.slot',
                sortable: true,
                filters: dealFilters('S'),
                filterRender: dealFilterRender('S'),
                filterMethod: dealFilterMethod('S', 'slot'),
            },
            {
                field: 'recipeId',
                width: '140px',
                title: 'common.field.recipeId',
                sortable: true,
                filters: dealFilters('S'),
                filterRender: dealFilterRender('S'),
                filterMethod: dealFilterMethod('S', 'recipeId'),
            },
            {
                field: 'startTime',
                width: '180px',
                title: 'common.field.startTime',
                sortable: true,
                filters: dealFilters('I'),
                filterRender: dealFilterRender('I'),
                filterMethod: dealFilterMethod('I', 'startTime'),
            },
            {
                field: 'endTime',
                width: '180px',
                title: 'common.field.endTime',
                sortable: true,
                filters: dealFilters('I'),
                filterRender: dealFilterRender('I'),
                filterMethod: dealFilterMethod('I', 'endTime'),
            },
            {
                field: 'elapse',
                width: '130px',
                title: 'traceDetail.field.elapse',
                sortable: true,
                filters: dealFilters('I'),
                filterRender: dealFilterRender('I'),
                filterMethod: dealFilterMethod('I', 'elapse'),
            },
            {
                field: 'sampleCount',
                width: '160px',
                title: 'traceDetail.field.sampleCount',
                sortable: true,
                align: 'right',
                filters: dealFilters('N'),
                filterRender: dealFilterRender('N'),
                filterMethod: dealFilterMethod('N', 'sampleCount'),
            },
            {
                field: 'faultCount',
                width: '150px',
                title: 'traceDetail.field.faultCount',
                sortable: true,
                align: 'right',
                className: 'css-fault',
                filters: dealFilters('N'),
                filterRender: dealFilterRender('N'),
                filterMethod: dealFilterMethod('N', 'faultCount'),
            },
            {
                field: 'warningCount',
                width: '170px',
                title: 'traceDetail.field.warningCount',
                sortable: true,
                align: 'right',
                className: 'css-warning',
                filters: dealFilters('N'),
                filterRender: dealFilterRender('N'),
                filterMethod: dealFilterMethod('N', 'warningCount'),
            },
            {
                field: 'dataQuality',
                width: '170px',
                title: 'traceDetail.field.dataQuality',
                sortable: true,
                align: 'right',
                className: 'css-warning',
                filters: dealFilters('N'),
                filterRender: dealFilterRender('N'),
                filterMethod: dealFilterMethod('N', 'dataQuality'),
            },
        ],
    });
};
