import { subtract, ceil, isEmpty } from 'lodash-es';
import { saveAnalysisConfigs, getAnalysisConfigs } from '@futurefab/ui-sdk-api';

const isNotEmptyObject = (obj: any) =>
  !isEmpty(obj) && typeof obj === 'object' && !Array.isArray(obj);
export class CmAnalysisOptions {
  visible: boolean;

  similarityThreshold: number;
  criticalThreshold: number;
  warningThreshold: number;
  traceMatchingType: string;
  tempOptions: {
    criticalThreshold: number;
    warningThreshold: number;
    sensitivity: number;
    // match 状态: ['ALL', 'MATCHING'. 'UN_MATCHING']
    traceMatchingType: string;
  };

  constructor() {
    this.visible = false;

    this.similarityThreshold = 0.9;
    this.criticalThreshold = 15;
    this.warningThreshold = 50;
    this.traceMatchingType = 'ALL';
    this.tempOptions = {
      sensitivity: 0.1,
      criticalThreshold: 15,
      warningThreshold: 50,
      traceMatchingType: 'ALL'
    };
  }

  resetTempOptions(): void {
    this.readHistoryOptions();
  }

  cancelThreshold(): void {
    this.tempOptions.sensitivity = ceil(subtract(1, this.similarityThreshold), 1);

    this.tempOptions.criticalThreshold = this.criticalThreshold;
    this.tempOptions.warningThreshold = this.warningThreshold;
    this.tempOptions.traceMatchingType = this.traceMatchingType;
  }

  setAnalysisOptions(options: any): void {
    this.similarityThreshold = options.similarityThreshold;
    this.tempOptions.sensitivity = ceil(subtract(1, options.similarityThreshold), 1);

    this.criticalThreshold = options.criticalThreshold;
    this.tempOptions.criticalThreshold = options.criticalThreshold;

    this.warningThreshold = options.warningThreshold;
    this.tempOptions.warningThreshold = options.warningThreshold;
  }

  async applyAnalysisOption() {
    this.similarityThreshold = ceil(subtract(1, this.tempOptions.sensitivity), 1);
    this.criticalThreshold = this.tempOptions.criticalThreshold;
    this.warningThreshold = this.tempOptions.warningThreshold;
    this.traceMatchingType = this.tempOptions.traceMatchingType;

    // 保存数据到DB
    await saveAnalysisConfigs({
      bodyParams: {
        analysisConfig: {
          similarityThreshold: ceil(subtract(1, this.tempOptions.sensitivity), 1),
          criticalThreshold: this.tempOptions.criticalThreshold,
          warningThreshold: this.tempOptions.warningThreshold,
          traceMatchingType: this.tempOptions.traceMatchingType
        }
      }
    });
  }

  async readHistoryOptions() {
    const dataCacheRes = await getAnalysisConfigs({
      bodyParams: {}
    });
    if (
      dataCacheRes.status === 'SUCCESS' &&
      isNotEmptyObject(dataCacheRes.data.analysisConfigJson)
    ) {
      this.setAnalysisOptions(dataCacheRes.data.analysisConfigJson);
    } else {
      this.tempOptions.sensitivity = 0.1;
      this.tempOptions.criticalThreshold = 15;
      this.tempOptions.warningThreshold = 50;
      this.tempOptions.traceMatchingType = 'ALL';
    }
  }

  closeAnalysisOption(isOpen: any): void {
    if (!isOpen) {
      this.cancelThreshold();
    }
  }
}
