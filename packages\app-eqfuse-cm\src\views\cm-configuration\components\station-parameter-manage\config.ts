import { VxeGridProps, VXETable } from '@futurefab/vxe-table';
import { stationParameterInfo as buttonIds } from '@/log-config';
import { type Ref } from 'vue';
import { t } from '@futurefab/vxe-table';
import type { StationItem } from '../../interface';
import { DATA_GROUP } from '@/constant/charts';
export const getStationParameterOptions = () => {
    return VXETable.tableFun.tableDefaultConfig({
        toolbarConfig: {
            tableName: 'cm.title.stationParameter',
            import: false,
            export: false,
            refresh: true,
            custom: false,
            tools: [
                ...VXETable.tableFun.getToolsButton([
                    {
                        id: buttonIds.add,
                        name: 'common.btn.add',
                        icon: 'icon-btn-add',
                        visible: true,
                    },
                    {
                        name: 'common.btn.save',
                        icon: 'icon-btn-save',
                        visible: true,
                        id: buttonIds.save,
                    },
                    {
                        id: buttonIds.delete,
                        name: 'common.btn.delete',
                        icon: 'icon-btn-delete',
                        visible: true,
                    },
                    {
                        name: 'common.btn.importData',
                        icon: 'icon-btn-import',
                        id: buttonIds.import,
                        visible: false,
                    },
                ]),
            ],
        },
        border: true,
        columns: [
            {
                type: 'checkbox',
            },
            // {
            //     field: 'eqpId',
            //     title: 'common.field.eqpId',
            //     sortable: false,
            // },
            // {
            //     field: 'chamberStr',
            //     title: 'cm.field.chamberName',
            //     sortable: true,
            //     filterRender: { name: '$input' },
            //     filters: [{ data: '' }],
            //     editRender: { name: '$input' },
            // },
            {
                field: 'paramName',
                title: 'cm.field.parameterName',
                sortable: true,
                filterRender: { name: '$input' },
                filters: [{ data: '' }],
                editRender: { name: '$input' },
            },
            {
                field: 'paramAlias',
                title: 'common.field.alias',
                sortable: true,
                filterRender: { name: '$input' },
                filters: [{ data: '' }],
                editRender: { name: '$input' },
            },
            {
                field: 'stationName',
                title: 'cm.field.stationName',
                sortable: true,
                filterRender: { name: '$input' },
                filters: [{ data: '' }],
                editRender: {
                    name: '$select',
                    options: DATA_GROUP,
                },
            },
            {
                field: 'createDtts',
                title: 'common.field.createdTime',
                sortable: true,
                filterRender: { name: '$input' },
                filters: [{ data: '' }],
            },
            {
                field: 'createBy',
                title: 'common.field.createdBy',
                sortable: true,
                filterRender: { name: '$input' },
            },
        ],
        exportConfig: {
            filename: 'station_parameter.xlsx',
        },
    }) as typeof VxeGridProps;
};

export const getStationValidRules = (xGrid: Ref<any>) => {
    return {
        paramName: [
            {
                required: true,
                message: t('common.tip.required', { name: t('cm.field.parameterName') }),
            },
            {
                validator({ row }: { row: StationItem }) {
                    const data = xGrid.value.getTableData().fullData;
                    if (
                        row.paramName &&
                        data.filter((item: StationItem) => item.paramName === row.paramName)
                            ?.length > 1
                    ) {
                        return new Error(
                            t('common.tip.nameRepeated', {
                                name: `${t('cm.field.parameterName')}:${row.paramName}`,
                            }),
                        );
                    }
                },
            },
        ],
        paramAlias: [
            {
                required: true,
                message: t('common.tip.required', { name: t('common.field.paramAlias') }),
            },
        ],
        stationName: [
            {
                required: true,
                message: t('common.tip.required', { name: t('cm.field.stationName') }),
            },
        ],
    };
};
export const stationValidate = (validList: StationItem[], totalList: StationItem[]) => {
    const ukMap: Record<string, { row: StationItem; paramName: string }> = {};
    for (let i = 0; i < validList.length; i++) {
        const row = validList[i];
        let msg = '';
        if (!row.paramName || !row.paramAlias || !row.stationName) {
            if (!row.paramName) {
                msg = t('common.tip.required', { name: t('cm.field.parameterName') });
            } else if (!row.paramAlias) {
                msg = t('common.tip.required', { name: t('common.field.paramAlias') });
            } else {
                msg = t('common.tip.required', { name: t('cm.field.stationName') });
            }
            //必填项
            return { row, msg };
        }
        const key = row.paramName;
        if (ukMap.hasOwnProperty(key) && ukMap[key].row !== row) {
            // 判定 重复
            const msg = t('common.tip.nameRepeated', {
                name: `${t('cm.field.parameterName')}: ${row.paramName}`,
            });
            return { row: ukMap[key].row, msg };
        } else {
            ukMap[key] = {
                paramName: row.paramName,
                row,
            };
        }
    }

    for (let i = 0; i < totalList.length; i++) {
        const item = totalList[i];
        const key = item.chamberStr + item.paramName;
        if (
            ukMap.hasOwnProperty(key) &&
            ukMap[key].row.isAdd && // add 的才能编辑，才校验重复
            ukMap[key].row !== item
        ) {
            const msg = t('common.tip.nameRepeated', {
                name: ` ${t('cm.field.parameterName')}: ${item.paramName}`,
            });
            return { row: ukMap[key].row, msg };
        }
    }
};
