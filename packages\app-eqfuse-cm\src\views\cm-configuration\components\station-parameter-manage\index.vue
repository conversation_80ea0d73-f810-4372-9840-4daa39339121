<script setup lang="ts">
import { onMounted, reactive, ref, watch } from 'vue';
import { getStationParameterOptions, getStationValidRules, stationValidate } from './config';
import {
  showConfirm,
  successInfo,
  showWarning,
  getPermissionButton,
  EesImportExcel
} from '@futurefab/ui-sdk-ees-basic';
import {
  deleteStationParamList,
  getStationParamList,
  saveStationParamList
} from '@futurefab/ui-sdk-api';
import { useAuthority } from '@/utils/use-authority';
import { stationParameterInfo as buttonIds } from '@/log-config';
import type { StationItem } from '../../interface';
import { t } from '@futurefab/vxe-table';

const baseClass = 'cm-station-parameter-manage';
const options = ref(getStationParameterOptions());
const data = ref<StationItem[]>([]);
const loading = ref(false);
const xGrid = ref();
const cmStationParameterAuthorityInfo = ref({} as any);
const importInfo = reactive({
  show: false,
  initTableColumnConfig: {
    url: 'system/initImport',
    bodyParams: {
      moduleName: 'station_parameter'
    },
    crossDomainAppName: 'cm'
  },
  importParams: {},
  handleFinish: () => {
    getData();
  }
});
const validRules = ref(getStationValidRules(xGrid) as any);
const events = {
  toolbarToolClick: ({ code }: { code: string }) => {
    const checkboxRecords = xGrid.value && xGrid.value.getCheckboxRecords();
    xGrid.value?.clearValidate();
    switch (code) {
      case 'refresh':
        refresh();
        break;
      case 'add':
        dealAdd();
        break;
      case 'save':
        dealSave();
        break;
      case 'delete':
        dealDelete(checkboxRecords);
        break;
      case 'importData':
        importInfo.show = true;
        break;
    }
  }
};

async function getData() {
  try {
    loading.value = true;
    const res = await getStationParamList({
      bodyParams: {},
      headerParams: {
        log: 'Y',
        page: 'cm-cm-config',
        action: 'getS-station-param-list'
      }
    });
    data.value = res?.data || [];
  } catch {
  } finally {
    loading.value = false;
  }
}
const refresh = async () => {
  await getData();
};
async function dealAdd() {
  xGrid.value.insert({ isAdd: true });
}
async function dealSave() {
  const { updateRecords, insertRecords } = xGrid.value.getRecordset();
  const { fullData } = xGrid.value.getTableData();
  const temp = [...updateRecords, ...insertRecords];
  if (!temp.length) {
    return showWarning('common.tip.saveTip');
  }
  const errorItem = stationValidate(temp, fullData);
  if (errorItem) {
    xGrid.value.validate(errorItem.row).then((error: any) => {
      if (!error) {
        showWarning(errorItem.msg);
        xGrid.value.scrollToRow(errorItem.row);
      }
    });
    return;
  }
  const params = {
    insertList: insertRecords,
    updateList: updateRecords
  };
  try {
    loading.value = true;
    const res = await saveStationParamList({
      bodyParams: params,
      headerParams: {
        log: 'Y',
        page: buttonIds.pageId,
        action: 'save-station-param-list'
      }
    });
    if (res.status === 'SUCCESS') {
      successInfo('common.tip.actionSuccess');
      await refresh();
    }
  } catch {
  } finally {
    loading.value = false;
  }
}
async function dealDelete(checkboxRecords: StationItem[]) {
  if (checkboxRecords.length) {
    // 二次确认弹窗
    const type = await showConfirm({
      msg: t('common.tip.deleteConfirm', {
        total: checkboxRecords.length as any
      })
    });
    if (type !== 'confirm') return;
    const addRows: StationItem[] = []; // 新增的直接删除
    const existRows: StationItem[] = []; // 请求接口删除
    checkboxRecords.forEach((row) => {
      if (row.isAdd) {
        addRows.push(row);
        xGrid.value.remove(row);
      } else {
        existRows.push(row);
      }
    });
    if (existRows.length) {
      try {
        loading.value = true;
        const res = await deleteStationParamList({
          bodyParams: existRows.map((item) => item.paramName),
          headerParams: {
            log: 'Y',
            page: 'cm-cm-config',
            action: 'delete-station-param-list'
          }
        });
        if (res.status === 'SUCCESS') {
          xGrid.value.remove(existRows);
          successInfo('common.tip.actionSuccess');
        }
      } finally {
        loading.value = false;
      }
    } else {
      successInfo('common.tip.actionSuccess');
    }
    xGrid.value.remove(addRows);
  } else {
    showWarning('common.tip.selectData');
  }
}
// function search() {
//     beforeSearchCondition = eqpDetail.value.alias;
//     getData(beforeSearchCondition);
// }
useAuthority(xGrid, options, cmStationParameterAuthorityInfo, buttonIds, true);
onMounted(() => {
  getPermissionButton('/cm/cm-config').then((res: any) => {
    cmStationParameterAuthorityInfo.value = res;
  });
  getData();
});
const activeMethod = ({ row, column }: any) => {
  return row?.isAdd || ['stationName', 'paramAlias'].includes(column.field);
};
watch(
  () => importInfo.show,
  () => {
    importInfo.initTableColumnConfig = {
      url: 'system/initImport',
      bodyParams: {
        moduleName: 'station_parameter'
      },
      crossDomainAppName: 'cm'
    };
  }
);
</script>
<template>
  <div :class="[baseClass, 'b-radius']">
    <vxe-grid
      :id="baseClass + '-grid'"
      ref="xGrid"
      :class="[baseClass + '-grid', 'table-b-radius']"
      v-bind="options"
      :data="data"
      :loading="loading"
      :edit-rules="validRules"
      :edit-config="{
        trigger: 'dblclick',
        mode: 'cell',
        showStatus: false,
        showStatusBorder: true,
        activeMethod
      }"
      v-on="events"
    ></vxe-grid>
    <Teleport to="body">
      <EesImportExcel
        v-model:visible="importInfo.show"
        module-name="station_parameter"
        :app="'cm-api' as any"
        import-url="ImportStationParamService"
        verify-url="ValidateStationParamImportService"
        :upload-file-config="{
          url: 'system/uploadImportFile',
          bodyParams: { moduleName: 'station_parameter' }
        }"
        :import-params="importInfo.importParams"
        :init-table-column-config="importInfo.initTableColumnConfig"
        :show-verify="true"
        :page-id="'station_parameter'"
        :action-id="`station_parameter_import`"
        show-preview-limit
        :is-socket="false"
        :socket-service="'rms'"
        @finish="importInfo.handleFinish"
      />
    </Teleport>
  </div>
</template>
<style scoped lang="less">
@import url('@/assets/style/variable.less');
.cm-station-parameter-manage {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  height: 100%;
  &-grid-content {
    flex-grow: 1;
    height: 500px;
  }
}
</style>
