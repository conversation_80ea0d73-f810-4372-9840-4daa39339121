<script lang="ts" setup>
import { PROJECTNAME } from '@/log-config/constant';
import { onMounted, reactive, ref, nextTick } from 'vue';
import dayjs, { Dayjs } from 'dayjs';
import { getAreaValue, getTooltipText } from '@/utils/tools';
import { cnText, usText } from '@/docs/workflow-log-viewer/index';
import {
  getWorkflowAreaList,
  getWorkflowEqpList,
  getWorkflowList,
  exportWorkflowLogs,
  apiConfig
} from '@futurefab/ui-sdk-api';
import { APP_NAME } from '@/utils/constant';
import type { ConditionData, TableData } from './interface';
import config from './config';
import {
  exportToExcel,
  getPermissionButton,
  setHeaderSelectFilter,
  showWarning,
  successInfo,
  EesCustomTime,
  EesAddContext,
  EesSearchConditionLayer,
  useFavoritePageCondition
} from '@futurefab/ui-sdk-ees-basic';
import ExecuteFlow from './execute-flow/index.vue';
import { workflow<PERSON>ogViewer as buttonIds } from '@/log-config/index';
import type { ButtonAuthority } from '../setup-data-manager/interface';
import { t } from '@futurefab/vxe-table';
import { EesJsonPretty } from '@futurefab/ui-sdk-ees-workflow';

const { getFavoritePageCondition } = useFavoritePageCondition();
const pageId = `${PROJECTNAME}workflow-log-viewer`;
const initDate = [dayjs().startOf('day'), dayjs().endOf('day')];
const params = reactive({
  startDate: initDate[0].format('YYYY-MM-DD HH:mm:ss'),
  endDate: initDate[1].format('YYYY-MM-DD HH:mm:ss'),
  timeAreaBind: [...initDate] as [Dayjs, Dayjs],
  area: '',
  eqp: '',
  method: '',
  areaList: [] as string[],
  eqpList: [] as string[],
  methodList: [] as string[],
  tableData: [] as TableData[],
  config,
  isShow: false,
  curRow: null as any,
  totalPage: 0,
  currentPage: 1,
  pageSize: 100,
  currentCell: {
    title: '',
    val: '' as string | Record<string, any>,
    visible: false
  },
  buttonAuthority: {} as ButtonAuthority
});
const contextResult = ref({
  lotId: '',
  lotIdsFlag: false,
  recipeId: '',
  recipeIdsFlag: false
});
const xGrid = ref();
const addContextRef = ref();
const timePop = (val: Dayjs[]) => {
  params.startDate = val[0] ? dayjs(val[0]).format('YYYY-MM-DD HH:mm:ss') : '';
  params.endDate = val[1] ? dayjs(val[1]).format('YYYY-MM-DD HH:mm:ss') : '';
};
const setConditionVal = () => {
  return {
    startDate: params.startDate,
    endDate: params.endDate,
    area: params.area,
    eqp: params.eqp,
    method: params.method,
    lotId: contextResult.value.lotId,
    lotFlag: contextResult.value.lotIdsFlag,
    recipeId: contextResult.value.recipeId,
    recipeFlag: contextResult.value.recipeIdsFlag
  };
};
let conditionVal: {
  startDate: string;
  endDate: string;
  area: string;
  eqp: string;
  method: string;
  lotId: string;
  recipeId: string;
  lotFlag: boolean;
  recipeFlag: boolean;
} = { ...setConditionVal() };
const handleChangeTime = () => {
  requestArea(true);
};
const requestEqp = () => {
  getWorkflowEqpList({
    headerParams: {
      log: 'Y'
    },
    bodyParams: {
      startDate: params.startDate,
      endDate: params.endDate,
      area: params.area,
      routePath: `/${APP_NAME}/workflow-log-viewer`
    }
  }).then((result: any) => {
    if (result.status == 'SUCCESS') {
      if (result.data) {
        params.eqpList = result.data.eqpList;
        params.methodList = result.data.methodList;
        if (params.eqp && !params.eqpList.includes(params.eqp)) {
          params.eqp = '';
        }
        if (params.method && !params.methodList.includes(params.method)) {
          params.method = '';
        }
      }
    }
  });
};
const requestArea = async (isChangeTime?: boolean) => {
  await getWorkflowAreaList({
    headerParams: {
      log: 'Y'
    },
    bodyParams: {
      startDate: params.startDate,
      endDate: params.endDate,
      routePath: `/${APP_NAME}/workflow-log-viewer`
    }
  }).then((result: any) => {
    if (result.status == 'SUCCESS') {
      params.areaList = result.data;
      if (isChangeTime) {
        if (params.area && params.areaList.includes(params.area)) {
          requestEqp();
        } else {
          params.area = '';
          params.eqp = '';
          params.method = '';
          params.methodList = [];
          params.eqpList = [];
        }
      }
    }
  });
};
const requestList = () => {
  if (!conditionVal.area) {
    return false;
  }
  params.config.loading = true;
  getWorkflowList({
    headerParams: {
      log: 'Y',
      page: buttonIds.pageId,
      action: buttonIds.search
    },
    bodyParams: {
      startDate: conditionVal.startDate,
      endDate: conditionVal.endDate,
      area: conditionVal.area,
      eqpId: conditionVal.eqp,
      methodName: conditionVal.method,
      lotId: conditionVal.lotId,
      lotIdsFlag: conditionVal.lotFlag,
      recipeId: conditionVal.recipeId,
      recipeIdsFlag: conditionVal.recipeFlag,
      routePath: `/${APP_NAME}/workflow-log-viewer`,
      page: params.currentPage,
      limit: params.pageSize
    }
  }).then((result: any) => {
    if (result.status == 'SUCCESS') {
      params.tableData = [...result.data];
      params.totalPage = result.count;
      setHeaderSelectFilter({
        xGrid: xGrid,
        columnFieldList: [
          'area',
          'eqpId',
          'recipeId',
          'methodName',
          'errorYn',
          'fid',
          'mid',
          'lotId',
          'substrateId',
          'modelGroupWorkflow',
          'modelWorkflow'
        ],
        tableData: params.tableData,
        splitTypeMap: {
          substrateId: ';'
        }
      });
    }
    params.config.loading = false;
  });
};
const handleChangeArea = () => {
  if (params.area) {
    requestEqp();
  } else {
    params.eqpList = [];
  }
};
const reset = () => {
  params.startDate = initDate[0].format('YYYY-MM-DD HH:mm:ss');
  params.endDate = initDate[1].format('YYYY-MM-DD HH:mm:ss');
  params.timeAreaBind = [...initDate] as unknown as [Dayjs, Dayjs];
  params.area = '';
  requestArea(true);
  addContextRef.value && addContextRef.value.handleReset();
};
const search = () => {
  params.currentPage = 1;
  setTimeout(() => {
    conditionVal = { ...setConditionVal() };
    requestList();
  }, 100);
};
const isFirstRender = ref(true);
const setInitialValue = async (data: ConditionData, isAuto?: boolean, isClickTag?: boolean) => {
  const {
    startDate,
    endDate,
    area,
    eqp,
    method,
    timeAreaBind,
    lotId,
    lotFlag,
    recipeId,
    recipeFlag
  } = data;
  if (startDate && endDate) {
    params.startDate = startDate.replace(/\+/g, ' ');
    params.endDate = endDate.replace(/\+/g, ' ');
    await requestArea(true);
    params.area = area;
    params.eqp = eqp;
    params.method = method;
    params.timeAreaBind = [
      dayjs(startDate.replace(/\+/g, ' ')),
      dayjs(endDate.replace(/\+/g, ' '))
    ];
  } else {
    const areaArr = getAreaValue(params.areaList);
    params.area = areaArr.length ? areaArr[0] : '';
  }
  if (params.area) {
    requestEqp();
  } else {
    params.eqpList = [];
  }
  contextResult.value = {
    lotId: lotId ? lotId : '',
    lotIdsFlag: !!lotFlag,
    recipeIdsFlag: !!recipeFlag,
    recipeId: recipeId ? recipeId : ''
  };
  if ((isAuto && isFirstRender.value) || isClickTag) {
    search();
    isFirstRender.value = false;
  }
};
const handleOtherChange = (searchCondition: any) => {
  searchCondition &&
    Array.isArray(searchCondition) &&
    searchCondition.forEach((item: { label: string; isExclude: boolean; value: string }) => {
      if (item.label == 'LOT ID') {
        contextResult.value.lotId = item.value;
        contextResult.value.lotIdsFlag = item.isExclude;
      }
      if (item.label == 'Recipe ID') {
        contextResult.value.recipeId = item.value;
        contextResult.value.recipeIdsFlag = item.isExclude;
      }
    });
};
const handleOtherReset = () => {
  contextResult.value = {
    lotId: '',
    lotIdsFlag: false,
    recipeId: '',
    recipeIdsFlag: false
  };
};
const events = {
  toolbarToolClick: ({ code }: { code: string }) => {
    const currRow = xGrid.value.getCurrentRecord();
    if (code == 'executeFlow') {
      if (!currRow) {
        showWarning('common.tip.selectData');
        return false;
      }
      params.curRow = currRow;
      params.isShow = true;
    } else if (code == 'backendExport') {
      if (params.tableData.length == 0) {
        return false;
      } else {
        exportWorkflowLogs({
          headerParams: {
            log: 'Y',
            page: buttonIds.pageId,
            action: buttonIds.backendExport
          },
          bodyParams: {
            startDate: conditionVal.startDate,
            endDate: conditionVal.endDate,
            area: conditionVal.area,
            eqpId: conditionVal.eqp,
            methodName: conditionVal.method,
            routePath: `/${APP_NAME}/workflow-log-viewer`,
            exportFileName: 'WorkflowLogViewer'
          }
        }).then((result: any) => {
          if (result.status == 'SUCCESS') {
            successInfo('common.tip.exportSuccess');
            exportToExcel(result.data);
          }
        });
      }
    } else if (code == 'refresh') {
      requestList();
    }
  },
  menuClick: ({ menu, row }: { menu: { code: string }; row: TableData }) => {
    if (menu && menu.code == 'executeFlow') {
      params.curRow = row;
      params.isShow = true;
    }
  },
  pageChange: async ({
    type,
    currentPage,
    pageSize,
    $event
  }: {
    type: string;
    currentPage: number;
    pageSize: number;
    $event: any;
  }) => {
    params.pageSize = pageSize;
    params.currentPage = currentPage;
    requestList();
  },
  cellDblclick: ({ column, row }: { column: { title: string; field: string }; row: any }) => {
    if (column.field === 'message') {
      params.currentCell = {
        title: column.title,
        val: row[column.field],
        visible: true
      };
    }
  }
};
onMounted(async () => {
  /** 读取favoritePageCondition并赋值给条件表单 */
  const favoritePageCondition = getFavoritePageCondition();
  if (favoritePageCondition && Object.keys(favoritePageCondition).length > 0) {
    setInitialValue(favoritePageCondition, true);
  }
  requestArea();
  params.buttonAuthority = (await getPermissionButton(
    '/r2r/workflow-log-viewer'
  )) as ButtonAuthority;
  nextTick(() => {
    setTimeout(() => {
      xGrid.value &&
        xGrid.value.setVisibleTools({
          authorityList: params.buttonAuthority.buttonList,
          flag: params.buttonAuthority.allButtonFlag
        });
      xGrid.value &&
        xGrid.value.setVisibleMenuBody({
          authorityList: params.buttonAuthority.buttonList,
          menuList: localStorage.getItem('permissionPageList'),
          allButtonFlag: params.buttonAuthority.allButtonFlag
        });
    }, 100);
  });
});
</script>
<template>
  <div :id="buttonIds.pageId" class="r2r-workflow-log-viewer-wrapper">
    <vxe-grid
      ref="xGrid"
      class="vxe-toolbar-top-left-radius-lg vxe-toolbar-top-right-radius-lg vxe-pager-bottom-left-radius-lg vxe-pager-bottom-right-radius-lg"
      v-bind="params.config"
      :data="params.tableData"
      :pager-config="{
        layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes', 'FullJump'],
        align: 'right',
        pageSizes: [100, 150, 200, 250, 300],
        pageSize: params.pageSize,
        total: params.totalPage,
        currentPage: params.currentPage
      }"
      v-on="events"
    >
      <template #search-condition>
        <ees-search-condition-layer
          :page-id="buttonIds.pageId"
          :is-disabled-search="!params.area"
          :input-data="{
            ...setConditionVal()
          }"
          @on-apply="search"
          @on-reset="reset"
          @set-condition="(v: any, isAuto?: boolean, isClickTag?: boolean) => setInitialValue(v, isAuto, isClickTag)"
        >
          <template #search-form>
            <div ref="searchCondition" class="r2r-workflow-log-viewer-search-condition-content">
              <div class="search-item">
                <ees-custom-time
                  ref="refTime"
                  class="search-item-date"
                  :time-area="params.timeAreaBind"
                  :api-config="apiConfig"
                  @time-pop="timePop"
                  @time-start-end-change="handleChangeTime"
                />
              </div>
              <div class="search-item">
                <span class="title">{{ t('common.title.area') }}</span>
                <a-select
                  v-model:value="params.area"
                  :not-found-content="t('eesBasic.tips.noData')"
                  :bordered="false"
                  @change="handleChangeArea"
                >
                  <a-select-option
                    v-for="area in params.areaList"
                    :key="area"
                    :label="area"
                    :value="area"
                  />
                </a-select>
              </div>
              <div class="search-item">
                <span class="title">{{ t('common.title.eqp') }}</span>
                <a-select
                  v-model:value="params.eqp"
                  :not-found-content="t('eesBasic.tips.noData')"
                  :bordered="false"
                  allow-clear
                >
                  <a-select-option
                    v-for="eqp in params.eqpList"
                    :key="eqp"
                    :label="eqp"
                    :value="eqp"
                  />
                </a-select>
              </div>
              <div class="search-item">
                <span class="title">{{ t('workflowLogViewer.title.method') }}</span>
                <a-select
                  v-model:value="params.method"
                  :not-found-content="t('eesBasic.tips.noData')"
                  :bordered="false"
                  allow-clear
                >
                  <a-select-option
                    v-for="methodName in params.methodList"
                    :key="methodName"
                    :label="methodName"
                    :value="methodName"
                  />
                </a-select>
              </div>
              <div class="search-item">
                <ees-add-context
                  ref="addContextRef"
                  :show-recent="false"
                  :self-defined-list="['LOT ID', 'Recipe ID']"
                  :context-result="contextResult"
                  :popover-class="'r2r-workflow-log-viewer-add-context'"
                  :add-context-width="'100%'"
                  :api-config="apiConfig"
                  @reset="handleOtherReset"
                  @change="handleOtherChange"
                >
                </ees-add-context>
              </div>
            </div>
          </template>
        </ees-search-condition-layer>
      </template>
      <template #help>
        <vxe-tooltip
          class="setup-schema-tip"
          :use-h-t-m-l="true"
          :enterable="true"
          theme="light"
          :content="getTooltipText(cnText, usText)"
        >
          <button class="vxe-button type--button icon-type--svgButton">
            <svg class="icon" aria-hidden="true">
              <use xlink:href="#icon-btn-help"></use>
            </svg>
          </button>
        </vxe-tooltip>
      </template>
    </vxe-grid>
  </div>
  <execute-flow
    :show="params.isShow"
    :current-row="params.curRow"
    @hide-sidebar="() => (params.isShow = false)"
  />
  <ees-json-pretty
    v-model:visible="params.currentCell.visible"
    :json-val="params.currentCell.val"
    :title="params.currentCell.title"
  ></ees-json-pretty>
</template>
<style lang="less" scoped>
@import url('@/assets/style/variable.less');
.r2r-workflow-log-viewer-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  :deep(.error-red-row) {
    background-color: @error-color-light;
  }
}
.r2r-workflow-log-viewer-search-condition-content {
  // display: block;
  .search-item {
    padding-top: @padding-basis + 2;
    display: flex;
    flex-direction: column;

    .title {
      font-weight: @font-weight-lg;
      font-size: @font-size-base;
      padding-bottom: @padding-xss;
    }

    .search-item-date {
      width: 100%;
    }

    .add-context-wrap {
      :deep(.search-condition-item) {
        padding: 0;
      }
      :deep(.add-search-box) {
        .select-item {
          padding: @padding-basis + 2 0 0 0;
          .title {
            font-weight: @font-weight-lg;
            font-size: @font-size-base;
            padding-bottom: @padding-xss;
          }
        }
      }
    }
  }
}
</style>
