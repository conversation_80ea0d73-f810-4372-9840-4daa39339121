<script setup lang="ts">
import { ref, watch, shallowRef } from 'vue';
import { cloneDeep } from 'lodash-es';
import { filterCategoryAndParameter } from './config';
import { showWarning } from '@futurefab/ui-sdk-ees-basic';

const props = withDefaults(
  defineProps<{
    columns: any;
    tableData: any[];
  }>(),
  {
    columns: () => [],
    tableData: () => []
  }
);
const searchByResult = defineModel<boolean>('searchByResult');
const columnData = defineModel<any>('columnData');
const emits = defineEmits(['setSortByCategory', 'updateInfo']);

const showSearch = ref<boolean>(false);
// 排序
const setSortByCategory = (isChecked: boolean) => {
  emits('setSortByCategory', isChecked);
};
// search category
const searchCategoryValue = ref<string[]>([]);
const searchCategoryOptions = ref<any[]>([]);
const inputRef = ref<any>(null);
const searchContent = ref<string>('');
const handleShowSearch = () => {
  showSearch.value = true;
  setTimeout(() => {
    inputRef.value?.focus();
  }, 200);
};
// 参数列表数据
const totalData = shallowRef([]);
// 获取到Category数据
const formatCategory = (originData: any) => {
  const uniqueCategories: string[] = [];
  // 不需要筛选groupConfigId，其可能为0
  return originData
    .map((item: any) => ({
      categoryCodeId: item.categoryCodeId,
      color: item.categoryColor,
      category: item.categoryName,
      categoryOrder: item.categoryOrder
    }))
    .filter((item: any) => {
      if (uniqueCategories.indexOf(item.category) === -1 && item.category) {
        uniqueCategories.push(item.category);
        return true;
      }
      return false;
    })
    .sort((a: any, b: any) => a.categoryOrder - b.categoryOrder);
};
const isCheckAll = ref<boolean>(false);
const changeCheckAll = (event: any) => {
  if (event.target.checked) {
    searchCategoryValue.value = searchCategoryOptions.value.map((item: any) => item.categoryCodeId);
  } else {
    searchCategoryValue.value = [];
  }
};
// 下拉选择变化
const categoryChange = () => {
  isCheckAll.value = searchCategoryValue.value.length === searchCategoryOptions.value.length;
};
// 关闭弹窗
const handleCancel = (visible?: boolean) => {
  if (!visible) {
    showSearch.value = false;
  }
};
// 搜索
const handleIconSearch = () => {
  if (searchCategoryValue.value.length === 0) {
    showWarning('cm.tips.noCategory');
    return;
  }
  // 根据categoryCodeId和searchContent过滤数据
  const { newTableData, newColumns } = filterCategoryAndParameter(
    props.columns,
    props.tableData,
    searchCategoryValue.value,
    searchContent.value
  );
  columnData.value = newColumns;
  emits('updateInfo', newTableData);
};
const handleSearch = () => {
  handleIconSearch();
  handleCancel();
};
// 重置，默认全选，输入框为空
const handleReset = () => {
  searchCategoryValue.value = searchCategoryOptions.value.map((item: any) => item.categoryCodeId);
  searchContent.value = '';
  // 还原result内容
  columnData.value = props.columns;
  emits('updateInfo', props.tableData);
};
watch(
  () => props.tableData,
  () => {
    if (props.tableData.length > 3 && props.tableData.length > totalData.value.length) {
      totalData.value = cloneDeep(props.tableData);
      searchCategoryOptions.value = formatCategory(totalData.value);
      // 默认全选
      isCheckAll.value = true;
      searchCategoryValue.value = searchCategoryOptions.value.map(
        (item: any) => item.categoryCodeId
      );
    }
  },
  { immediate: true }
);
</script>

<template>
  <div class="parameter-search-box">
    <div class="parameter-search-box-sort">
      <span class="parameter-search-box-sort-text">Sort by Result</span>
      <a-switch
        v-model:checked="searchByResult"
        size="middle"
        :disabled="tableData.length < 3"
        @change="setSortByCategory"
      />
    </div>
    <a-popover
      v-model:open="showSearch"
      placement="bottomLeft"
      trigger="click"
      @openChange="handleCancel"
    >
      <template #content>
        <div class="search-box">
          <a-select
            mode="multiple"
            v-model:value="searchCategoryValue"
            show-search
            allow-clear
            style="width: 100%"
            :default-active-first-option="true"
            max-tag-count="responsive"
            :show-arrow="true"
            :not-found-content="$t('eesBasic.tips.noData')"
            @change="categoryChange"
          >
            <template #maxTagPlaceholder="omittedValues">
              <a-tooltip>
                <template #title>
                  <div
                    class="tooltip-content"
                    v-for="item in omittedValues"
                    :key="item.option.label"
                  >
                    <!-- 有颜色的正方形块 -->
                    <span
                      class="color-square"
                      :style="{ backgroundColor: item.option.color }"
                    ></span>
                    <span>{{ item.option.label }}</span>
                  </div>
                </template>
                <span>+ {{ omittedValues.length }} ...</span>
              </a-tooltip>
            </template>
            <template #dropdownRender="{ menuNode: menu }">
              <div style="padding: 6px" v-if="searchCategoryOptions.length > 0">
                <a-checkbox v-model:checked="isCheckAll" @change="changeCheckAll">
                  {{
                    searchCategoryValue.length === searchCategoryOptions.length
                      ? 'Uncheck All'
                      : 'Check All'
                  }}
                </a-checkbox>
              </div>
              <component :is="menu"></component>
            </template>
            <a-select-option
              v-for="item in searchCategoryOptions"
              :key="item.value"
              :label="item.category"
              :value="item.categoryCodeId"
              :title="item.category"
              :color="item.color"
            >
              <span
                style="
                  border-radius: 2px;
                  width: 12px;
                  height: 12px;
                  display: inline-block;
                  margin-top: 5px;
                "
                :style="{ backgroundColor: item.color }"
              />
              {{ item.category }}
            </a-select-option>
          </a-select>
          <a-input
            ref="inputRef"
            v-model:value="searchContent"
            placeholder="Search"
            class="search-input"
            allowClear
            @pressEnter="handleIconSearch"
          >
            <template #suffix>
              <i class="iconfont icon-search" @click="handleIconSearch" />
            </template>
          </a-input>
          <div class="btn-group">
            <a-button type="primary" size="small" @click="handleSearch">
              {{ $t('common.btn.confirm') }}
            </a-button>
            <a-button size="small" class="reset-btn" @click="handleReset">
              {{ $t('common.btn.reset') }}
            </a-button>
          </div>
        </div>
      </template>
      <a-tooltip>
        <template #title>
          <span>{{ $t('common.btn.search') }}</span>
        </template>
        <a-button
          :class="'parameter-search-box-btn ' + (showSearch || searchContent ? 'active' : '')"
          @click="handleShowSearch"
        >
          <template #icon><i class="iconfont icon-search" /></template>
        </a-button>
      </a-tooltip>
    </a-popover>
  </div>
</template>

<style scoped lang="less">
@import url('@/assets/style/variable.less');
.parameter-search-box {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  padding: 8px 10px;
  &-sort {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    &-text {
      font-weight: 700;
      margin-right: 4px;
    }
  }
  &-btn {
    width: 24px;
    height: 24px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: @primary-color;
  }
  .active {
    background-color: @primary-color-row-select;
  }
}
// search 弹窗
.search-box {
  // margin: -4px;
  width: 160px;
  .search-input {
    margin: 10px 0;
    :deep(.icon-search) {
      color: @text-hint-color;
      &:hover {
        cursor: pointer;
        background-color: @bg-hover-2-color;
      }
    }
  }
  .btn-group {
    display: flex;
    justify-content: flex-end;
    margin-top: 10px;

    .reset-btn {
      margin-left: 10px;
    }
  }
}
// 超出的省略内容
.tooltip-content {
  display: flex;
  align-items: center;
  font-size: 14px;
  .color-square {
    display: inline-block;
    width: 12px;
    height: 12px;
    margin-right: 8px;
    border-radius: 2px;
  }
}
</style>
