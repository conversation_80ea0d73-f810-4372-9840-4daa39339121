import { VXETable, type VxeGridProps, t } from '@futurefab/vxe-table';
// import { filterMethod } from '@/utils/tools';
import { tableDefaultConfig } from '@futurefab/ui-sdk-ees-basic';
export const xConfig = tableDefaultConfig({
  id: 'StateManagerHistory',
  borderOutLine: [1, 0, 0, 0],
  toolbarConfig: {
    tableName: 'common.title.history',
    import: false,
    export: true,
    border: false,
    // refresh: false,
    // custom: false,
    // zoom: false,
    tools: [...VXETable.tableFun.getToolsButton([])]
  },
  columns: [
    {
      field: 'version',
      minWidth: 120,
      title: 'common.field.version',
      align: 'center',
      sortable: true,
      filters: []
    },
    {
      field: 'operateType',
      minWidth: 120,
      title: 'common.field.inputType',
      align: 'left',
      sortable: true,
      filters: []
    },
    {
      field: 'activityFunctionName',
      minWidth: 150,
      title: 'stateManager.field.function',
      align: 'left',
      sortable: true,
      filters: []
    },
    // {
    //     field: 'areaName',
    //     minWidth: 150,
    //     title: 'common.field.area',
    //     align: 'left',
    //     sortable: true,
    //     filters: [{ data: '' }],
    //     filterMethod,
    // },
    // {
    //     field: 'eqpModelName',
    //     minWidth: 150,
    //     title: 'common.field.eqpModel',
    //     align: 'left',
    //     sortable: true,
    //     filters: [{ data: '' }],
    //     filterMethod,
    // },
    {
      field: 'activityClassName',
      minWidth: 150,
      title: 'stateManager.field.stateType',
      align: 'left',
      sortable: true,
      filters: []
    },
    {
      field: 'lastUpdateBy',
      minWidth: 150,
      title: 'common.field.lastUpdateBy',
      align: 'left',
      sortable: true,
      filters: []
    },
    {
      field: 'lastUpdateDtts',
      minWidth: 200,
      title: 'common.field.lastUpdateDtts',
      align: 'left',
      sortable: true,
      filters: [{ data: '' }],
      filterRender: {
        name: '$input'
      }
    },
    {
      field: 'createBy',
      minWidth: 150,
      title: 'common.field.createBy',
      align: 'left',
      sortable: true,
      filters: []
    },
    {
      field: 'createDtts',
      minWidth: 200,
      title: 'common.field.createDtts',
      align: 'left',
      sortable: true,
      filters: [{ data: '' }],
      filterRender: {
        name: '$input'
      }
    }
  ],
  checkboxConfig: {
    showHeader: true
  },
  columnCheckboxConfig: {
    checkMethod: ({ column: any }) => {
      return false;
    }
  }
} as VxeGridProps);
