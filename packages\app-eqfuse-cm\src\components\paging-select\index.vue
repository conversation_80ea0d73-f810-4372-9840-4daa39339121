<template>
  <a-select
    v-model:value="innerValue"
    :mode="mode"
    :placeholder="placeholder"
    :loading="loading"
    :filter-option="false"
    :not-found-content="loading ? undefined : '暂无数据'"
    show-search
    @search="handleSearch"
    @popupScroll="handleScroll"
    @dropdownVisibleChange="handleDropdownChange"
    @change="handleChange"
  >
    <a-select-option
      v-for="item in options"
      :key="item.value"
      :value="item.value"
      :disabled="item.disabled"
    >
      {{ item.label }}
    </a-select-option>
    
    <a-select-option
      v-if="hasMore && !loading && options.length > 0"
      key="load-more"
      value="load-more"
      disabled
      style="text-align: center; color: #999;"
    >
      滚动加载更多...
    </a-select-option>
    
    <a-select-option
      v-if="loading && options.length > 0"
      key="loading"
      value="loading"
      disabled
      style="text-align: center; color: #999;"
    >
      加载中...
    </a-select-option>
  </a-select>
</template>

<script setup>
import { ref, watch, onMounted, defineProps, defineEmits } from 'vue'

const props = defineProps({
  modelValue: {
    type: [String, Number, Array],
    default: undefined
  },
  mode: {
    type: String,
    default: undefined
  },
  placeholder: {
    type: String,
    default: '请选择'
  },
  pageSize: {
    type: Number,
    default: 20
  }
})

const emit = defineEmits(['update:modelValue'])

const innerValue = ref(props.modelValue)
const options = ref([])
const loading = ref(false)
const hasMore = ref(true)
const currentPage = ref(1)
const searchKeyword = ref('')
let searchTimer = null

// 模拟API
const mockApi = async (params) => {
  await new Promise(resolve => setTimeout(resolve, 500))
  
  const { page, pageSize, keyword = '' } = params
  const totalItems = 200
  
  const allData = Array.from({ length: totalItems }, (_, index) => ({
    value: `item_${index + 1}`,
    label: keyword 
      ? `${keyword}相关选项 ${index + 1}` 
      : `选项 ${index + 1}`,
    disabled: Math.random() > 0.95
  }))
  
  const filteredData = keyword
    ? allData.filter(item => 
        item.label.toLowerCase().includes(keyword.toLowerCase())
      )
    : allData
  
  const startIndex = (page - 1) * pageSize
  const endIndex = startIndex + pageSize
  const pageData = filteredData.slice(startIndex, endIndex)
  
  return {
    data: pageData,
    total: filteredData.length,
    hasMore: endIndex < filteredData.length
  }
}

const loadData = async (reset = false) => {
  if (loading.value) return
  
  loading.value = true
  
  try {
    const response = await mockApi({
      page: currentPage.value,
      pageSize: props.pageSize,
      keyword: searchKeyword.value
    })
    
    if (reset) {
      options.value = response.data
    } else {
      options.value = [...options.value, ...response.data]
    }
    
    hasMore.value = response.hasMore
  } catch (error) {
    console.error('加载数据失败:', error)
  } finally {
    loading.value = false
  }
}

const handleSearch = (value) => {
  if (searchTimer) {
    clearTimeout(searchTimer)
  }
  
  searchTimer = setTimeout(() => {
    searchKeyword.value = value
    currentPage.value = 1
    hasMore.value = true
    loadData(true)
  }, 300)
}

const handleScroll = (e) => {
  const { target } = e
  const { scrollTop, scrollHeight, clientHeight } = target
  
  if (scrollTop + clientHeight >= scrollHeight - 10 && hasMore.value && !loading.value) {
    currentPage.value++
    loadData()
  }
}

const handleDropdownChange = (open) => {
  if (open && options.value.length === 0) {
    loadData(true)
  } else if (!open && searchKeyword.value) {
    searchKeyword.value = ''
    currentPage.value = 1
    hasMore.value = true
    loadData(true)
  }
}

const handleChange = (value) => {
  innerValue.value = value
  emit('update:modelValue', value)
}

watch(() => props.modelValue, (newValue) => {
  innerValue.value = newValue
})

onMounted(() => {
  loadData(true)
})
</script>
