<script lang="ts" setup>
import type { CmLegend } from './interface';
const props = withDefaults(
  defineProps<{
    legend: CmLegend[];
    type?: 'circle' | 'default';
    title?: string;
    showCheckbox?: boolean;
  }>(),
  {
    type: 'default'
  }
);
const emit = defineEmits(['legendChange']);
const changeLegendShow = (item: CmLegend) => {
  item.show = !item.show;
  emit('legendChange', item);
};

const handleCheckboxChange = (item: CmLegend, newValue: boolean) => {
  item.show = newValue;
  emit('legendChange', item);
};
const baseClass = 'cm-group-legend';
</script>
<template>
  <div :class="baseClass">
    <div :class="baseClass + '-chart-box'">
      <div
        v-if="title"
        :class="[baseClass + '-chart-box-title', 'single-line-ellipsis']"
        :title="title"
      >
        {{ title }}
      </div>
      <div :class="[baseClass + '-chart-box-content', title ? 'has-title' : 'no-title']">
        <slot name="chart"></slot>
      </div>
    </div>
    <div :class="baseClass + '-legend'">
      <div
        v-for="item in legend"
        :key="item.id + item.color + item.name"
        :class="[baseClass + '-legend-item', item.show ? '' : 'disabled']"
        @click="changeLegendShow(item)"
      >
        <vxe-checkbox
          v-if="showCheckbox"
          :model-value="item.show"
          @update:model-value="(newValue: boolean) => handleCheckboxChange(item, newValue)"
          @click.stop
        />
        <div
          :class="[baseClass + '-legend-item-sample', props.type]"
          :style="{ backgroundColor: item.color }"
        ></div>
        <div :class="baseClass + '-legend-item-name'">{{ item.name }}</div>
      </div>
    </div>
  </div>
</template>
<style scoped lang="less">
@import url('@/assets/style/variable.less');
.cm-group-legend {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  &-chart-box {
    flex: 1;
    min-height: 0;
    width: 100%;
    padding-top: 5px;
    display: flex;
    flex-direction: column;
    &-title {
      width: 100%;
      height: 24px;
      line-height: 24px;
      text-align: center;
      font-size: 14px;
      font-weight: bold;
      color: @text-title-color;
      flex-shrink: 0;
    }
    &-content {
      flex: 1;
      width: 100%;
      min-height: 0;
    }
  }
  &-legend {
    max-height: 100px; 
    min-height: 24px; // 保持最小高度
    // border: 1px solid @border-group-color;
    display: flex;
    flex-wrap: wrap;
    width: 100%;
    justify-content: center;
    gap: 10px;
    z-index: 1;
    position: relative;
    overflow-y: auto; 
    overflow-x: hidden; 
    padding: 3px 10px;
    
    // 自定义滚动条样式
    &::-webkit-scrollbar {
      width: 6px;
    }
    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }
    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;
      &:hover {
        background: #a8a8a8;
      }
    }
    &-item {
      display: flex;
      flex-wrap: nowrap;
      align-items: center;
      height: 18px;
      cursor: pointer;
      &-sample {
        margin-right: 4px;
      }
      &.disabled &-sample {
        background: @bg-disabled-color !important;
      }
      &-sample.default {
        height: 4px;
        width: 14px;
        border-radius: 1px;
      }
      &-sample.circle {
        height: 12px;
        width: 12px;
        border-radius: 50%;
      }
      &-name {
        font-size: 12px;
        color: @text-sub-color;
      }
      &.disabled &-name {
        color: @text-disabled-color !important;
      }
    }
  }
}
</style>
