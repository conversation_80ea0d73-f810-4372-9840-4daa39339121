<script setup lang="ts">
import { watch, ref } from 'vue';
import type { Measuredata } from '@/utils/spline-interpolate';
import Chart from '../chart.vue';
import { visualMapColors } from '@/utils/custom-color';
import type { Specification } from '@/views/metrology-management/interface';
import { getPicecs, getToolbox } from './index';
const props = defineProps<{
    data?: Measuredata;
    size: number;
    valueKey: string;
    specification?: Specification;
}>();
const options = ref<any>(null);
const getOriginWafer = (
    data: Measuredata,
    visualColors: string[],
    valueKey = 'T1',
    specification: Specification,
) => {
    const xIndex = data.header.findIndex(item => item === specification.jsonObj.xField);
    const yIndex = data.header.findIndex(item => item === specification.jsonObj.yField);
    const tIndex = data.header.findIndex(item => item === valueKey);
    const valueList = data.data.map(item => Number(item[tIndex]));

    return {
        tooltip: {
            position: 'top',
        },
        grid: {
            left: 30,
            top: 30,
            right: 80,
            bottom: 30,
        },
        xAxis: {
            show: true,
            splitArea: {
                show: true,
            },
        },
        yAxis: {
            show: true,

            splitArea: {
                show: true,
            },
        },
        toolbox: getToolbox(),
        visualMap: {
            type: 'piecewise',
            show: false,
            calculable: true,
            orient: 'vertical',
            right: 10,
            top: 10,
            align: 'bottom',
            pieces: getPicecs(Math.min(...valueList), Math.max(...valueList), visualColors),
        },
        series: [
            {
                name: 'wafer chart',
                type: 'scatter',
                data: data.data.map(item => ({
                    value: [item[xIndex], item[yIndex], item[tIndex]],
                    info: item,
                })),
                label: {
                    show: false,
                },
                symbolSize: 5,
                emphasis: {
                    itemStyle: {
                        scale: 1.05,
                    },
                },
                itemStyle: {
                    borderWidth: 1,
                    borderColor: 'gray'
                },
                tooltip: {
                    formatter: (event: any) => {
                        if ((event?.value?.[2] ?? false) !== false) {
                            return [
                                `${specification.jsonObj.xField}: ${event.value[0]}`,
                                `${specification.jsonObj.yField}: ${event.value[1]}`,
                                `${valueKey}: ${event.value[2]}`,
                            ].join('<br/>');
                        }
                    },
                    appendToBody: true, 
                },
            },
        ],
    };
};
watch(
    [() => props.data, () => props.valueKey, () => props.specification],
    v => {
        if (v) {
            options.value = getOriginWafer(
                props.data!,
                visualMapColors,
                props.valueKey,
                props.specification!,
            );
        }
    },
    { immediate: true },
);
</script>
<template>
    <div :style="{ height: size + 'px', width: size + 50 + 'px' }" class="common-chart-container">
        <Chart :options="options" />
    </div>
</template>
<style scoped lang="less">
.common-chart-container {
    display: inline-block;
    .chartBox {
        width: 100%;
        height: 100%;
    }
}
</style>
