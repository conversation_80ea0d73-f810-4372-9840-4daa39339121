<script setup lang="ts">
import { watch, ref } from 'vue';
import { type Measuredata } from '@/utils/spline-interpolate';
import Chart from '../chart.vue';
import type { Specification } from '@/views/metrology-management/interface';
import { calcGroup, getToolbox } from './index';
import { debounce } from 'lodash-es';
import { colorList } from '@futurefab/ui-sdk-ees-charts';
const props = defineProps<{
    data?: Measuredata;
    valueKey: string;
    groupKey: string;
    groupNumber: number;
    specification?: Specification;
}>();
const options = ref<any>({});
const getSingleScatter = (
    data: Measuredata,
    valueKey: string,
    groupKey: string,
    groupNumber: number,
    specification: Specification,
) => {
    let groupList: number[];
    const xIndex = data.header.findIndex(item => item === specification.jsonObj.xField);
    const yIndex = data.header.findIndex(item => item === specification.jsonObj.yField);
    const vIndex = data.header.findIndex(item => item === valueKey);
    const valueList = data.data.map(item => Number(item[vIndex]));
    if (groupKey !== 'Radius') {
        const groupIndex = data.header.findIndex(item => item === groupKey);
        groupList = data.data.map(item => Number(item[groupIndex]));
    } else {
        let center = [0, 0];
        const radius = specification.jsonObj.diameter / 2;
        switch(specification.jsonObj.originPosition) {
            case 'Bottom Left': 
                center = [radius, radius];
                break;
            case 'Bottom Right':
                center = [-radius, radius];
                break;
            case 'Top Left':
                center = [radius, -radius];
                break;
            case 'Top Right':
                center = [-radius, -radius];
                break;
        }
        groupList = data.data.map(item =>
            Math.sqrt((Number(item[xIndex]) - center[0]) ** 2 + (Number(item[yIndex]) - center[1] ) ** 2),
        );
    }

    const maxV = Math.max(...groupList);
    const minV = Math.min(...groupList);
    const gap = (maxV - minV) / groupNumber;

    // 按区间分组的对象
    const groupRes = new Array(groupNumber).fill(null).map((_: null, index: number) => ({
        start: minV + gap * index,
        end: index === groupNumber - 1 ? maxV : minV + gap * (index + 1),
        data: [] as {value: number; groupValue: number; x: number, y: number}[],
        count: 0,
    }));

    groupList.forEach((value: number, index: number) => {
        const groupI = calcGroup(value, maxV, minV, gap, groupNumber);
        groupRes[groupI].data.push({
            value: valueList[index],
            groupValue: groupList[index],
            x: Number(data.data[index][xIndex]),
            y: Number(data.data[index][yIndex]),
        });
    });
    const temp: any[] = [];
    groupRes.forEach((item, groupIndex: number) => {
        item.data.forEach((obj: any) => temp.push({ value: [groupIndex, obj.value], info: {...item, ...obj} }));
    });

    return {
        color: colorList,
        grid: {
            left: 80,
            top: 30,
            right: 20,
            bottom: 30,
        }, 
        xAxis: {
            show: true,
            type: 'category',
            data: new Array(groupNumber).fill(null).map((item, index) => index + 1),
            splitArea: {
                show: true,
            },
        },
        yAxis: {
            show: true,
            scale: true,
            splitArea: {
                show: false,
            },
        },
        toolbox: getToolbox(),
        tooltip: {
            formatter: (event: any) => {
                if (event?.data?.info) {
                    const info = event.data.info;
                    return [
                        `${groupKey}: ${info.groupValue}`,
                        `${specification.jsonObj.xField}: ${info.x}`,
                        `${specification.jsonObj.yField}: ${info.y}`,
                    ].join('<br/>');
                }
            },
            appendToBody: true, 
        },
        series: [
            {
                name: 'scatter',
                type: 'scatter',
                data: temp,
                symbolSize: 5,
                emphasis: {
                    itemStyle: {
                        scale: 1.05,
                    },
                },
                itemStyle: {
                    borderWidth: 1,
                    borderColor: 'gray'
                },
            },
        ],
    };
};
const tempFn = debounce(() => {
    options.value = getSingleScatter(
        props.data!,
        props.valueKey,
        props.groupKey,
        props.groupNumber,
        props.specification!,
    );
}, 100);
watch(
    [
        () => props.data,
        () => props.valueKey,
        () => props.groupKey,
        () => props.groupNumber,
        () => props.specification,
    ],
    v => {
        if (props.data) {
            tempFn();
        }
    },
    { immediate: true },
);
</script>
<template>
    <div class="common-chart-container">
        <Chart :options="options" />
    </div>
</template>
<style scoped lang="less">
.common-chart-container {
    width: 100%;
    height: 100%;
}
</style>
