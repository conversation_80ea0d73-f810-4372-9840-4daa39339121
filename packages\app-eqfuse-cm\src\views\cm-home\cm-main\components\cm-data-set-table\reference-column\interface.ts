export interface PercentageInfo {
  isUnMatchingTop5: boolean;
  matchingCount: number;
  matchingCountTotal: string;
  matchingPercentage: number;
  recipeStepId: string;
  totalCount: number;
}
export interface ParameterLineType {
  allColInfo?: any;
  groupConfigId: number;
  groupId: number;
  isAnalysisResultRow: boolean;
  isUnMatchingTop5: boolean;
  parameterName: string;
  parentGroupId: string;
  recipeStepSummaryItems?: PercentageInfo[];
  recipeText: string;
  // 不匹配step
  unmatchingRecipeSteps?: PercentageInfo[];
  unmatchingRecipes: string;
  // 警告step
  warningRecipeSteps?: PercentageInfo[];
  // 未分析step
  missingRecipeSteps?: PercentageInfo[];
}

export interface ParameterLinesType<T> extends ParameterLineType {
  children: T[];
}

export interface GroupDataType {
  recipeStepSummaryItems: PercentageInfo[];
  unmatchingRecipeSteps: PercentageInfo[];
  warningRecipeSteps: PercentageInfo[];
  // 未分析step
  missingRecipeSteps: PercentageInfo[];
}

export interface ParameterTableLine {
  isUnMatchingTop5: boolean;
  parameterName?: string;
  recipeStepId: string;
  matchingCountTotal: string;
  matchingPercentage: number;
}
