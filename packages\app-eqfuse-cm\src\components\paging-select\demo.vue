<template>
  <div style="padding: 20px;">
    <h2>无限滚动下拉选择器测试</h2>
    
    <div style="margin-bottom: 20px;">
      <h3>单选模式</h3>
      <PagingSelect
        v-model="singleValue"
        placeholder="请选择单个选项"
        style="width: 300px;"
      />
      <p>选中值: {{ singleValue }}</p>
    </div>
    
    <div style="margin-bottom: 20px;">
      <h3>多选模式</h3>
      <PagingSelect
        v-model="multipleValue"
        mode="multiple"
        placeholder="请选择多个选项"
        style="width: 300px;"
      />
      <p>选中值: {{ JSON.stringify(multipleValue) }}</p>
    </div>
    
    <div>
      <h3>功能说明</h3>
      <ul>
        <li>支持搜索（输入关键词试试）</li>
        <li>滚动到底部自动加载更多</li>
        <li>模拟200条数据，每页20条</li>
        <li>搜索防抖300ms</li>
        <li>关闭下拉框时重置搜索状态</li>
      </ul>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import PagingSelect from '@/components/paging-select/index.vue';

const singleValue = ref();
const multipleValue = ref([]);
</script>
