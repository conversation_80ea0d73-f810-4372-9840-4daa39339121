<script setup lang="ts">
import { ref } from 'vue';
import TrimConfig from './index.vue';
import type { TrimOriginData } from '../../interface';
const props = withDefaults(
    defineProps<{
        isHistory?: boolean;
    }>(),
    {
        isHistory: false,
    },
);
const showModel = ref(false);
const handleHide = () => {
    showModel.value = false;
};
const trimConfigRef = ref();
const emits = defineEmits(['apply']);
const apply = () => {
    emits('apply', trimConfigRef.value?.getCheckList() || []);
    showModel.value = false;
};
const clear = () => {
    trimConfigRef.value?.clear();
};
const historyTrimmingList = ref<TrimOriginData[]>([]);
defineExpose({
    show: (trimmingList?: TrimOriginData[]) => {
        showModel.value = true;
        historyTrimmingList.value = props.isHistory ? trimmingList || [] : [];
    },
    clear,
    refresh: () => trimConfigRef.value?.refresh(),
});
</script>

<template>
    <Teleport to="body">
        <vxe-modal
            v-model="showModel"
            :title="$t('cm.title.trimOption')"
            :mask-closable="false"
            :width="'90%'"
            :height="'90%'"
            resize
            show-footer
            @hide="handleHide"
            @close="handleHide"
        >
            <TrimConfig
                ref="trimConfigRef"
                :history-list="historyTrimmingList"
                :is-history="props.isHistory"
                :is-model="true"
            ></TrimConfig>
            <template #footer>
                <a-button type="primary" style="margin-right: 10px" @click="apply">{{
                    $t('common.btn.apply')
                }}</a-button>
                <a-button @click="handleHide">{{ $t('common.btn.cancel') }}</a-button>
            </template>
        </vxe-modal>
    </Teleport>
</template>
