<script lang="ts" setup>
import { onUnmounted, ref, watchEffect } from 'vue';
const props = withDefaults(
    defineProps<{ value: string[]; options: { label: string; value: string }[] }>(),
    {
        options: () => [],
        value: () => [],
    },
);
const emits = defineEmits(['update:value']);
const _value = ref<string[]>([]);
const _options = ref<{ label: string; value: string }[]>([]);
const _visibleOptions = ref<{ label: string; value: string }[]>([]);
const selectRef = ref();
watchEffect(() => {
    _value.value = props.value;
});
watchEffect(() => {
    _options.value = [...props.options];
    _visibleOptions.value = [...props.options];
});

let beforeSearch = '';
const events = {
    change: (events: string[]) => {
        emits('update:value', events);
        events.forEach(str => {
            if (!_options.value.find(item => item.label === str)) {
                _options.value.unshift({ label: str, value: str });
            }
        });
    },
    search: (event: string) => {
        beforeSearch = event;
        if (!_options.value.find(item => item.label === beforeSearch)) {
            const temp = [{ label: beforeSearch, value: beforeSearch }, ..._options.value];
            _visibleOptions.value = temp;
        }
    },
    inputKeyDown: (events: { code: string }) => {
        if (events.code === 'Enter') {
            if (beforeSearch) {
                if (!_options.value.find(item => item.label === beforeSearch)) {
                    const temp = [{ label: beforeSearch, value: beforeSearch }, ..._options.value];
                    _options.value = temp;
                }
                beforeSearch = '';
            }
        }
    },
};
onUnmounted(() => {
    beforeSearch = '';
});
</script>
<template>
    <a-select
        ref="selectRef"
        v-model:value="_value"
        mode="multiple"
        :show-arrow="true"
        :options="_visibleOptions"
        v-on="events"
    />
</template>
