<script setup lang="ts">
import { computed, ref, watch } from 'vue';
import Categorization from '@/views/cm-home/parameter-categorization/parameter-categorization.vue';
import Configuration from '@/views/cm-home/cm-configuration/cm-configuration.vue';
import TrimConfig from '@/views/cm-configuration/components/trim-config/index.vue';
import StationParameterManage from '@/views/cm-configuration/components/station-parameter-manage/index.vue';
import SchemaConfig from '@/views/cm-configuration/components/schema-config/index.vue';
import EqpDataSources from '@/views/cm-configuration/components/eqp-data-sources/index.vue';
import { EesTabs } from '@futurefab/ui-sdk-ees-basic';
const activeMenu = ref(0);

const component = computed(() => {
    switch (activeMenu.value) {
        case 4:
            return EqpDataSources;
        case 3:
            return StationParameterManage;
        case 2:
            return TrimConfig;
        case 1:
            return Configuration;
        default:
            return Categorization;
    }
});

const changeTab = ({ index }: { index: number }) => {
    activeMenu.value = index;
};
const baseClass = 'cm-configuration-home';
</script>
<template>
    <div :class="baseClass">
        <EesTabs
            :class="baseClass + '-tab'"
            :active-item="activeMenu"
            :is-large-font="true"
            :tab-list="([
                { name: $t('cm.title.parameterCategorization') },
                { name: $t('cm.title.cMConfiguration') },
                { name: $t('cm.title.trimConfig') },
                { name: $t('cm.title.stationParameter') },
                { name: $t('cm.title.eqpDataSources') },
                // { name: 'Schema Config' },
            ] as any)"
            @change-tab="changeTab"
        ></EesTabs>
        <div :class="[baseClass + '-content', 'b-radius']">
            <keep-alive>
                <component :is="component" />
            </keep-alive>
        </div>
    </div>
</template>

<style lang="less" scoped>
.cm-configuration-home {
    height: 100%;
    width: 100%;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    &-tab {
        flex-shrink: 0;
        width: 100%;
        margin-bottom: 8px;

        :deep(.ff-basic-tabChange-horizontal) {
            padding: 8px;
            display: flex;

            :deep(li) {
                float: left;
                height: 32px;
                line-height: 32px;
                cursor: pointer;
            }
        }
    }

    &-content {
        flex-grow: 1;
        overflow: hidden;
    }
}
</style>
