import { t, VXETable } from '@futurefab/vxe-table';
import { cmTrimingInfo as buttonIds } from '@/log-config/index';
export const getTrimDataRuleOptions = (
    contextList: { alias: string; column: string }[],
    isModel: boolean,
    isHistory: boolean,
) => {
    const temp = contextList.map(item => {
        return {
            field: `${item.column}`,
            title: item.alias,
            sortable: true,
            minWidth: 115,
            filters: [],
            editRender: isModel ? undefined : { name: '$input' },
        };
    });
    const checkboxCol = isHistory
        ? []
        : [
              {
                  type: 'checkbox',
              },
          ];
    return VXETable.tableFun.tableDefaultConfig({
        toolbarConfig: {
            tableName: 'cm.title.dataTrimmingRule',
            import: false,
            export: false,
            refresh: !isHistory,
            tools: [
                ...VXETable.tableFun.getToolsButton(
                    isModel
                        ? []
                        : [
                              {
                                  id: buttonIds.add,
                                  name: 'common.btn.add',
                                  icon: 'icon-btn-add',
                                  visible: true,
                              },
                              {
                                  name: 'common.btn.save',
                                  icon: 'icon-btn-save',
                                  visible: true,
                                  id: buttonIds.save,
                              },
                              {
                                  id: buttonIds.delete,
                                  name: 'common.btn.delete',
                                  icon: 'icon-btn-delete',
                                  visible: true,
                              },
                          ],
                ),
            ],
        },
        columns: [
            ...checkboxCol,
            {
                field: 'rawId',
                title: 'cm.field.ruleId',
                minWidth: 120,
                sortable: true,
                filters: [],
            },
            ...temp,
            {
                field: 'trimming',
                title: 'cm.field.trimming',
                sortable: false,
                slots: {
                    default: 'editRule',
                    edit: 'editRule',
                },
                className: 'trimming-rule-cell',
                params: {
                    isPaset: false,
                },
            },
        ],
        border: true,
    });
};

export const getValidRules = (contextList: { alias: string; column: string }[]) => {
    const result: Record<string, any[]> = {
        trimming: [
            {
                required: true,
                message: t('common.tip.required', { name: t('cm.field.trimming') }),
            },
        ],
    };
    contextList.forEach(item => {
        result[item.column] = [
            {
                required: true,
                message: t('common.tip.required', { name: item.alias }),
            },
        ];
    });
    return result;
};
