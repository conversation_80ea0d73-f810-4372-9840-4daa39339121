<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue';
import { useResizeObserver } from '@vueuse/core';

const props = withDefaults(defineProps<{
    isFullScreen: boolean,
}>(), {
    isFullScreen: false
});
const open = defineModel<boolean>('open', {default: true});
const activeKey = ref(open.value ? ['1'] : []);
const baseClass = 'cm-chart-collapsed';
const collapseRef = ref();
watch(() => props.isFullScreen, () => {
    if (props.isFullScreen && !activeKey.value.length) {
        activeKey.value = ['1'];
    }
});
const contentHeight = ref(0);
onMounted(() => {
    useResizeObserver(collapseRef.value, () => {
        contentHeight.value = Math.max(collapseRef.value?.clientHeight ? (collapseRef.value?.clientHeight - 53) : 0, 0);
    });
});

watch(() => activeKey, () => {
    open.value = !!activeKey.value.length;
}, { deep: true });
</script>

<template>
    <div ref="collapseRef" :class="baseClass">
        <a-collapse v-model:activeKey="activeKey" collapsible="icon" ghost>
            <a-collapse-panel key="1" :showArrow="!isFullScreen">
                <template #header>
                    <slot name="header"></slot>
                </template>
                <div :class="baseClass + '-content'" :style="{ height: contentHeight + 'px' }">
                    <slot name="content"></slot>
                </div>
            </a-collapse-panel>
        </a-collapse>
    </div>
</template>

<style scoped lang="less">
@import url('@/assets/style/variable.less');
.cm-chart-collapsed {
    height: 100%;
    width: 100%;
    :deep(.ant-collapse) {
        height: 100%;
        border-radius: 0px;
        .ant-collapse-header {
            height: 52px;
            border-bottom: 1px solid @border-color;
            border-radius: 0;
            align-items: center;
        }
        .ant-collapse-content-box {
            padding: 0;
        }
    }
}
</style>