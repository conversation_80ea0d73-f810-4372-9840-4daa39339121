[{"groupConfigId": 1, "matchingPercentage": 0, "parameterName": "TDS: Tool: Heater current phase c (amperes); SVID9560", "unmatchingRecipeSteps": [{"matchingCount": 0, "matchingPercentage": 0, "recipeStep": "21", "totalCount": 45}, {"matchingCount": 0, "matchingPercentage": 0, "recipeStep": "22", "totalCount": 45}, {"matchingCount": 0, "matchingPercentage": 0, "recipeStep": "23", "totalCount": 45}, {"matchingCount": 0, "matchingPercentage": 0, "recipeStep": "24", "totalCount": 45}, {"matchingCount": 0, "matchingPercentage": 0, "recipeStep": "7", "totalCount": 45}], "unmatchingRecipes": "Rcp. Step 22, 23, 24, 7, 21", "warningRecipeSteps": [], "warningRecipes": "", "parentId": 1, "isAnalysisResultRow": true, "isUnMatchingTop5": true, "children": [], "_X_ROW_CHILD": [], "_X_ROW_KEY": "row_21"}, {"groupConfigId": 1, "matchingPercentage": 0, "parameterName": "TDS: Tool: Heater current phase a (amperes); SVID9530", "unmatchingRecipeSteps": [{"matchingCount": 0, "matchingPercentage": 0, "recipeStep": "21", "totalCount": 45}, {"matchingCount": 0, "matchingPercentage": 0, "recipeStep": "23", "totalCount": 45}, {"matchingCount": 0, "matchingPercentage": 0, "recipeStep": "7", "totalCount": 45}, {"matchingCount": 6, "matchingPercentage": 13.33, "recipeStep": "22", "totalCount": 45}], "unmatchingRecipes": "Rcp. Step 22, 23, 7, 21", "warningRecipeSteps": [{"matchingCount": 11, "matchingPercentage": 24.44, "recipeStep": "24", "totalCount": 45}], "warningRecipes": "Rcp. Step 22, 23, 7, 21", "parentId": 1, "isAnalysisResultRow": true, "isUnMatchingTop5": true, "children": [], "_X_ROW_CHILD": [], "_X_ROW_KEY": "row_22"}, {"groupConfigId": 1, "matchingPercentage": 0, "parameterName": "TDS: Tool: Heater current phase b (amperes); SVID9545", "unmatchingRecipeSteps": [{"matchingCount": 0, "matchingPercentage": 0, "recipeStep": "4", "totalCount": 43}, {"matchingCount": 0, "matchingPercentage": 0, "recipeStep": "7", "totalCount": 45}, {"matchingCount": 1, "matchingPercentage": 2.33, "recipeStep": "3", "totalCount": 43}], "unmatchingRecipes": "Rcp. Step 3, 4, 7", "warningRecipeSteps": [], "warningRecipes": "", "parentId": 1, "isAnalysisResultRow": true, "isUnMatchingTop5": true, "children": [], "_X_ROW_CHILD": [], "_X_ROW_KEY": "row_23"}, {"groupConfigId": 1, "matchingPercentage": 0, "parameterName": "TDS: Tool: TEOS Flow (sccm); SVID2908", "unmatchingRecipeSteps": [{"matchingCount": 0, "matchingPercentage": 0, "recipeStep": "5", "totalCount": 44}, {"matchingCount": 0, "matchingPercentage": 0, "recipeStep": "7", "totalCount": 45}], "unmatchingRecipes": "Rcp. Step 5, 7", "warningRecipeSteps": [], "warningRecipes": "", "parentId": 1, "isAnalysisResultRow": true, "isUnMatchingTop5": true, "children": [], "_X_ROW_CHILD": [], "_X_ROW_KEY": "row_24"}, {"groupConfigId": 1, "matchingPercentage": 0, "parameterName": "TDS: Tool: TEB Flow (sccm); SVID2907", "unmatchingRecipeSteps": [{"matchingCount": 0, "matchingPercentage": 0, "recipeStep": "7", "totalCount": 45}], "unmatchingRecipes": "Rcp. Step 7", "warningRecipeSteps": [{"matchingCount": 15, "matchingPercentage": 34.09, "recipeStep": "6", "totalCount": 44}], "warningRecipes": "Rcp. Step 7", "parentId": 1, "isAnalysisResultRow": true, "isUnMatchingTop5": true, "children": [], "_X_ROW_CHILD": [], "_X_ROW_KEY": "row_25"}, {"groupConfigId": 1, "matchingPercentage": 0, "parameterName": "TDS: Tool: T<PERSON><PERSON>le Valve Position; SVID1340", "unmatchingRecipeSteps": [{"matchingCount": 0, "matchingPercentage": 0, "recipeStep": "7", "totalCount": 45}], "unmatchingRecipes": "Rcp. Step 7", "warningRecipeSteps": [{"matchingCount": 22, "matchingPercentage": 48.89, "recipeStep": "24", "totalCount": 45}], "warningRecipes": "Rcp. Step 7", "parentId": 1, "isAnalysisResultRow": true, "isUnMatchingTop5": false, "children": [], "_X_ROW_CHILD": [], "_X_ROW_KEY": "row_26"}, {"groupConfigId": 1, "matchingPercentage": 0, "parameterName": "TDS: Tool: TEPO Flow (sccm); SVID2909", "unmatchingRecipeSteps": [{"matchingCount": 0, "matchingPercentage": 0, "recipeStep": "7", "totalCount": 45}], "unmatchingRecipes": "Rcp. Step 7", "warningRecipeSteps": [], "warningRecipes": "", "parentId": 1, "isAnalysisResultRow": true, "isUnMatchingTop5": false, "children": [], "_X_ROW_CHILD": [], "_X_ROW_KEY": "row_27"}, {"groupConfigId": 1, "matchingPercentage": 100, "parameterName": "TDS: Tool: Chamber Pressure (Torr); SVID900", "unmatchingRecipeSteps": [], "unmatchingRecipes": "", "warningRecipeSteps": [], "warningRecipes": "", "parentId": 1, "isAnalysisResultRow": true, "isUnMatchingTop5": false, "children": [], "_X_ROW_CHILD": [], "_X_ROW_KEY": "row_28"}, {"groupConfigId": 1, "matchingPercentage": 100, "parameterName": "TDS: Tool: He HI Flow (sccm); SVID2905", "unmatchingRecipeSteps": [], "unmatchingRecipes": "", "warningRecipeSteps": [], "warningRecipes": "", "parentId": 1, "isAnalysisResultRow": true, "isUnMatchingTop5": false, "children": [], "_X_ROW_CHILD": [], "_X_ROW_KEY": "row_29"}, {"groupConfigId": 1, "matchingPercentage": 100, "parameterName": "TDS: Tool: <PERSON>er Temp (C); SVID1010", "unmatchingRecipeSteps": [], "unmatchingRecipes": "", "warningRecipeSteps": [], "warningRecipes": "", "parentId": 1, "isAnalysisResultRow": true, "isUnMatchingTop5": false, "children": [], "_X_ROW_CHILD": [], "_X_ROW_KEY": "row_30"}, {"groupConfigId": 1, "matchingPercentage": 100, "parameterName": "TDS: Tool: O2 Flow (sccm); SVID2900", "unmatchingRecipeSteps": [], "unmatchingRecipes": "", "warningRecipeSteps": [], "warningRecipes": "", "parentId": 1, "isAnalysisResultRow": true, "isUnMatchingTop5": false, "children": [], "_X_ROW_CHILD": [], "_X_ROW_KEY": "row_31"}, {"groupConfigId": 1, "matchingPercentage": 100, "parameterName": "TDS: Tool: O3 Flow (sccm); SVID2903", "unmatchingRecipeSteps": [], "unmatchingRecipes": "", "warningRecipeSteps": [], "warningRecipes": "", "parentId": 1, "isAnalysisResultRow": true, "isUnMatchingTop5": false, "children": [], "_X_ROW_CHILD": [], "_X_ROW_KEY": "row_32"}, {"groupConfigId": 1, "matchingPercentage": 100, "parameterName": "TDS: Tool: RF DC Bias (Volts); SVID1325", "unmatchingRecipeSteps": [], "unmatchingRecipes": "", "warningRecipeSteps": [], "warningRecipes": "", "parentId": 1, "isAnalysisResultRow": true, "isUnMatchingTop5": false, "children": [], "_X_ROW_CHILD": [], "_X_ROW_KEY": "row_33"}, {"groupConfigId": 1, "matchingPercentage": 100, "parameterName": "TDS: Tool: <PERSON><PERSON><PERSON> Temp (C); SVID20050", "unmatchingRecipeSteps": [], "unmatchingRecipes": "", "warningRecipeSteps": [], "warningRecipes": "", "parentId": 1, "isAnalysisResultRow": true, "isUnMatchingTop5": false, "children": [], "_X_ROW_CHILD": [], "_X_ROW_KEY": "row_34"}]