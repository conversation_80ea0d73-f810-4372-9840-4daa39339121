import {
    thinPlateSplineInterpolate,
    interpolate,
    type Measuredata,
    type SamplePoint,
} from '@/utils/spline-interpolate';
import type { Specification } from '@/views/metrology-management/interface';
import { getX, getY } from '../wafer-2d';
// 薄板样条插值函数（Thin Plate Spline, TPS）
function generateWaferGridArr(
    samplePoints: SamplePoint[],
    specification: Specification,
    gridSize: number,
) {
    let maxV = -Infinity;
    let minV = Infinity;
    const radius = specification.jsonObj.diameter / 2;
    const { weights, c } = thinPlateSplineInterpolate(samplePoints);
    const grid = [];
    const step = specification.jsonObj.diameter / (gridSize - 1);
    const center = getCenterPointer(specification, gridSize) as any;
    for (let i = 0; i < gridSize; i++) {
        const x = getX(specification, i, step) as number;
        const temp = [];
        for (let j = 0; j < gridSize; j++) {
            const y = getY(specification, j, step) as number;
            const dist = Math.sqrt((x - center[0]) ** 2 + (y - center[1]) ** 2);

            if (dist > radius) {
                temp.push(0);
            } else {
                const value = interpolate(x, y, samplePoints, weights, c);
                if (value > maxV) {
                    maxV = value;
                } else if (value < minV) {
                    minV = value;
                }
                temp.push(value);
                // grid.push({value: [x, y, interpolate(x, y, samplePoints, weights, c)]});
            }
        }
        grid.push(temp);
    }
    return { grid, maxV, minV };
}

export const getThreeQuadData = (
    data: Measuredata[],
    gridSize: number,
    visualColors: string[] = [],
    padding = 5,
    valueKey = 'T1',
    specification: Specification,
    offset = 5, // 向上的偏移量
    scale = 10, // 归一化后的缩放比
) => {
    const colorLen = visualColors.length;
    const total = gridSize + 1 + 2 * padding;
    const half = Math.round(total / 2);
    type Site = string;
    const temp: Record<Site, { x: number; y: number; value: number }> = {};
    data.forEach(item => {
        const xIndex = item.header.findIndex(item => item === specification.jsonObj.xField);
        const yIndex = item.header.findIndex(item => item === specification.jsonObj.yField);
        const vIndex = item.header.findIndex(item => item === valueKey);
        const siteIndex = item.header.findIndex(item => item === specification.jsonObj.siteField);
        item.data.forEach(dataRow => {
            const site = dataRow[siteIndex];
            const value = Number(dataRow[vIndex]);
            if (!temp[site]) {
                const x = Number(dataRow[xIndex]);
                const y = Number(dataRow[yIndex]);
                temp[site] = { x, y, value };
            } else {
                temp[site].value += value;
            }
        });
    });

    const samplePoints = Object.values(temp);
    const sampleV = samplePoints.map(item => item.value);
    const originMax = Math.max(...sampleV);
    const originMin = Math.min(...sampleV);
    const originGap = originMax - originMin;
    const { grid, maxV, minV } = generateWaferGridArr(samplePoints, specification, gridSize + 1);
    const gap = maxV - minV;
    // 归一化
    const norl = (v: number) => (v ? (v - minV) / gap : 0);
    const getColor = (originV: number) => {
        if (originV) {
            if (originV >= originMax) {
                return visualColors[colorLen - 1];
            } else if (originV <= originMin) {
                return visualColors[0];
            } else {
                return visualColors[Math.floor((originV - originMin) / (originGap / colorLen))];
            }
        } else {
            return 'white';
        }
    };
    const res = [];

    const getGridV = (i: number, j: number) => grid[i]?.[j] || 0;
    for (let i = 0 - padding; i < total - 1 - padding; i++) {
        for (let j = total - 1 - padding; j > 0 - padding; j--) {
            const originVList = [
                getGridV(i, j),
                getGridV(i + 1, j),
                getGridV(i + 1, j - 1),
                getGridV(i, j - 1),
            ];
            const letfTop = norl(originVList[0]);
            const rightTop = norl(originVList[1]);
            const rightBtm = norl(originVList[2]);
            const letfBtm = norl(originVList[3]);

            const temp = [];

            temp.push([
                i - half + padding,
                j - half + padding,
                originVList[0] ? scale * letfTop + offset : 0,
            ]);
            temp.push([
                i - half + 1 + padding,
                j - half + padding,
                originVList[1] ? scale * rightTop + offset : 0,
            ]);
            temp.push([
                i - half + 1 + padding,
                j - half - 1 + padding,
                originVList[2] ? scale * rightBtm + offset : 0,
            ]);
            temp.push([
                i - half + padding,
                j - half - 1 + padding,
                originVList[3] ? scale * letfBtm + offset : 0,
            ]);
            const zeroList = temp
                .map((item, index) => ({ item, index }))
                .filter(item => !item.item[2]);

            res.push({
                points: temp,
                color: zeroList.length ? 'white' : getColor(grid[i][j]),
            });
        }
    }

    return res;
};

export const getCenterPointer = (specification: Specification, gridSize: number) => {
    let x = 0,
        y = 0;
    const radius = specification.jsonObj.diameter / 2;
    switch (specification.jsonObj.originPosition) {
        case 'Bottom Left':
            x = radius;
            y = radius;
            break;
        case 'Bottom Right':
            x = -radius;
            y = radius;
            break;
        case 'Center':
            break;
        case 'Top Left':
            x = radius;
            y = -radius;
            break;
        case 'Top Right':
            x = -radius;
            y = -radius;
    }
    return [x, y, 0];
};
export const setNotch = (
    notchM: any,
    center: [number, number, number],
    specification: Specification,
    notchHight: number,
    gridSize: number,
    offset: number,
) => {
    let x = 0,
        y = 0;
    let rotate = 0;
    const radius = gridSize / 2;
    switch (specification.jsonObj.notchPosition) {
        case 'Bottom':
            y = -radius - notchHight / 2;
            break;
        case 'Right':
            x = radius + notchHight / 2;
            rotate = Math.PI / 2;
            break;
        case 'Top':
            y = radius + notchHight / 2;
            rotate = Math.PI;
            break;
        case 'Left':
            x = -radius - notchHight / 2;
            rotate = -Math.PI / 2;
            break;
    }
    notchM.position.z = offset;
    notchM.position.x = center[0] + x;
    notchM.position.y = center[1] + y;
    notchM.rotateZ(rotate);
};
