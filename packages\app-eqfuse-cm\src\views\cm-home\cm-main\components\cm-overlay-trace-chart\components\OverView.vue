<template>
  <div class="over-view-wrap">
    <!-- 新增弹窗 -->
    <vxe-modal
      v-model="isShow"
      width="98%"
      height="98%"
      :title="t('common.btn.overView')"
      show-close
      transfer
      destroy-on-close
      type="confirm"
      :before-hide-method="handleHideModal"
      @confirm="confirm"
      @cancel="cancel"
      @close="cancel"
    >
      <div class="over-view-box">
        <div class="flex-row flex-justify-between">
          <div class="flex-row flex-items-center">
            <span style="margin-right: 10px;font-size: 14px;font-weight: bold;">Layout</span>
            <a-input
              v-model:value="layout"
              style="width: 190px;"
              type="number"
              :min="1"
              :max="10"
              @input="handleInput"
            ></a-input>
          </div>

          <ees-button-tip
            :is-border="true"
            :margin-right="10"
            icon="#icon-btn-export"
            :text="$t('common.btn.export')"
            @click="confirm"
          />
        </div>
        <div class="img-box">
          <div v-if="loading" class="loading-container">
            <a-spin size="large" />
            <div class="loading-text">{{ $t('cm.tips.generatingChartPreview') }}</div>
          </div>
          <div v-else-if="imgList && imgList.length > 0">
            <img
              v-for="(item, index) in imgList"
              :key="index"
              :class="'layout-' + layout"
              :src="item"
            />
          </div>
          <div v-else style="text-align: center; color: #999; padding: 20px;">
            {{ $t('cm.tips.noImageData') }}
          </div>
        </div>
        <div style="width: 100%; height: 0px; overflow: hidden">
          <div id="over-view-charts" class="shadow-img-box" :class="'layout-' + layout">
            <img
              v-for="(item, index) in imgList"
              :key="index"
              :class="'layout-' + layout"
              :src="item"
            />
          </div>
        </div>
      </div>
    </vxe-modal>
  </div>
</template>

<script setup lang="ts">
import { t } from '@futurefab/vxe-table';
import { watch, ref } from 'vue';
import { htmlToImgUrl } from '@futurefab/ui-sdk-ees-charts';
import { EesButtonTip } from '@futurefab/ui-sdk-ees-basic';

// eslint-disable-next-line vue/require-prop-types
const props = defineProps(['isOverView', 'imgList', 'loadName', 'loading']);
const emit = defineEmits(['update:isOverView']);

const isShow = ref(false);
const imgList = ref(props.imgList);

watch(
  () => props.isOverView,
  (val) => {
    isShow.value = val;
    imgList.value = props.imgList;
  },
  {
    immediate: true
  }
);

watch(
  () => props.imgList,
  (newImgList) => {
    imgList.value = newImgList;
  },
  {
    immediate: true
  }
);
const layout = ref(2);
const handleHideModal = ({ type }: { type: string }): any => {
  if (type == 'confirm') {
    return new Error('');
  }
};
async function confirm() {
  const url = await htmlToImgUrl('over-view-charts');
  downloadFile(url, props.loadName);
  console.log('confirm');
}
const handleInput = ({ value }: { value: number }) => {
  if (value < 1) {
    layout.value = 1;
  } else if (value > 10) {
    layout.value = 10;
  }
};
function cancel() {
  emit('update:isOverView', false);
}

function downloadFile(url: string, name: string) {
  const a = document.createElement('a');
  a.href = url;
  a.download = name || 'chart';
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
}
</script>

<style lang="less" scoped>
.over-view-wrap {
  background-color: #787171;
}
.img-box {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  margin: 5px;
  padding: 5px;
  border: 1px dotted #000;
  overflow: auto;
  
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 500px;
    color: #666;
    
    .loading-text {
      margin-top: 16px;
      font-size: 14px;
    }
  }
  img {
    width: 49%;
    height: auto;
    box-shadow:
      rgba(50, 50, 105, 0.15) 0px 2px 5px 0px,
      rgba(0, 0, 0, 0.05) 0px 1px 1px 0px;
    margin: calc(1% / 2);
    .loop(@i) when (@i > 0) {
      @width: calc(99% / @i);
      &.layout-@{i} {
        width: @width;
        margin: calc(1% / (2 * @i));
      }
      .loop(@i - 1);
    }
    .loop(10);
  }
}
.shadow-img-box {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  border: 1px dotted #000;
  padding: 5px;
  .loop(@i) when (@i > 0) {
    @width: calc(100% * @i);
    &.layout-@{i} {
      width: @width;
    }
    .loop(@i - 1);
  }
  .loop(10);
  img {
    width: 49%;
    height: auto;
    box-shadow:
      rgba(50, 50, 105, 0.15) 0px 2px 5px 0px,
      rgba(0, 0, 0, 0.05) 0px 1px 1px 0px;
    margin: calc(1% / 2);
    .loop(@i) when (@i > 0) {
      @width: calc(99% / @i);
      &.layout-@{i} {
        width: @width;
        margin: calc(1% / (2 * @i));
      }
      .loop(@i - 1);
    }
    .loop(10);
  }
}
</style>
