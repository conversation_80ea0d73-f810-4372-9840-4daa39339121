<script lang="ts" setup>
import { ref, reactive, watch, computed, onBeforeMount, onUnmounted, nextTick } from 'vue';
import { t } from '@futurefab/vxe-table';
import {
  setHeaderSelectFilter,
  EesSidebar,
  type EESTheme,
  type EESLanguage
} from '@futurefab/ui-sdk-ees-basic';
import { getExecutionLog, getWorkflowInfo } from '@futurefab/ui-sdk-api';
import ShowLog from './../show-log/index.vue';
import type { TableData } from '../interface';
import { workflowLogViewer as buttonIds } from '@/log-config/index';
import infoOption from './config';
import { EesLogViewerFlow } from '@futurefab/ui-sdk-ees-workflow';
import type { backActivityType } from '@futurefab/ui-sdk-ees-workflow/dist-types/workflow/types';

const props = withDefaults(
  defineProps<{
    show: boolean;
    currentRow: null | TableData;
  }>(),
  {
    show: false,
    currentRow: null
  }
);

const emits = defineEmits(['hideSidebar']);
const xInfo = ref();
const initDataType = 'logList'; //展示flow
const params = reactive({
  showModal: false,
  isShowLog: false,
  logList: [] as Record<string, any>[],
  flowList: [] as { weesDesigntimeJson: string }[],
  dataType: initDataType,
  activityList: {} as Record<string, backActivityType[]>,
  isShowLoading: false,
  theme: localStorage.getItem('theme') as EESTheme,
  lang: localStorage.getItem('language') as EESLanguage,
  showInfo: false,
  infoList: [] as Record<string, any>[]
});
const requestFLow = () => {
  params.theme = localStorage.getItem('theme') as EESTheme;
  params.lang = localStorage.getItem('language') as EESLanguage;
  params.isShowLoading = true;
  getExecutionLog({
    headerParams: {
      log: 'Y',
      page: buttonIds.pageId,
      action: buttonIds.executeFlow
    },
    bodyParams: {
      tid: props.currentRow!.tid
      //   tid: '1731413057',
      // methodName: props.currentRow!.methodName,
    }
  }).then((result: any) => {
    if (result.status == 'SUCCESS') {
      if (result.data) {
        params.dataType = result.data.dataType || initDataType;
        if (params.dataType != 'logList') {
          params.logList = result.data.dataList || [];
        } else {
          params.flowList = result.data.dataList || [];
          params.activityList = result.data.activityList || {};
        }
      }
      params.isShowLoading = false;
    }
  });
};
const requestWorkflowInfo = () => {
  infoOption.loading = true;
  getWorkflowInfo({
    headerParams: {
      log: 'Y',
      page: buttonIds.pageId,
      action: buttonIds.flowInfo
    },
    bodyParams: {
      tid: props.currentRow!.tid
    }
  }).then((result: any) => {
    if (result.status == 'SUCCESS') {
      params.infoList = result.data && Array.isArray(result.data) ? result.data : [];
      setHeaderSelectFilter({
        xGrid: xInfo,
        columnFieldList: ['workflowType', 'workflowName', 'version'],
        tableData: params.infoList
      });
    }
    infoOption.loading = false;
  });
};
const handleClick = (type: any) => {
  emits('hideSidebar');
  params.dataType = '';
};
const handleShowLogs = () => {
  params.showInfo = false;
  params.isShowLog = true;
  params.showModal = true;
};
const handleShowInfo = () => {
  params.isShowLog = false;
  params.showInfo = true;
  params.showModal = true;
  requestWorkflowInfo();
};
watch(
  () => props.show,
  (val: boolean) => {
    if (val) {
      requestFLow();
    }
  }
);
</script>

<template>
  <ees-sidebar
    v-if="show"
    :title="t('workflowLogViewer.title.executeFlowViewer')"
    :button-list="[]"
    :class="{ 'execute-flow-remove-padding': params.dataType != 'logList' }"
    @clickbutton="handleClick"
  >
    <template #sidebar_content>
      <div class="execute-flow-wrapper">
        <div v-if="params.dataType == 'logList'" class="log-header">
          <div class="log-title">
            <span
              v-show="
                currentRow && currentRow.modelGroupWorkflow && currentRow.modelGroupWorkflow != ''
              "
              >{{ t('flowDesign.label.modelGroup') }}:
              <vxe-tooltip :content="currentRow ? currentRow.modelGroupWorkflow : ''" theme="light"
                ><em>{{ currentRow ? currentRow.modelGroupWorkflow : '' }}</em></vxe-tooltip
              ></span
            >
            <span v-show="currentRow && currentRow.modelWorkflow && currentRow.modelWorkflow != ''"
              >{{ t('stateViewer.title.model') }}:
              <vxe-tooltip theme="light" :content="currentRow ? currentRow.modelWorkflow : ''"
                ><em>{{ currentRow ? currentRow.modelWorkflow : '' }}</em></vxe-tooltip
              >
            </span>
          </div>
          <div class="log-btn">
            <a-button class="flow-info-btn" type="primary" @click="handleShowInfo">
              {{ t('workflowLogViewer.btn.flowInfo') }}
            </a-button>
            <a-button type="primary" @click="handleShowLogs">
              {{ t('workflowLogViewer.btn.showLogs') }}
            </a-button>
          </div>
        </div>
        <div
          v-isLoading="{
            isShow: params.isShowLoading
          }"
          class="flow-content"
          :class="params.dataType != 'logList' ? 'table' : ''"
        >
          <show-log
            v-if="params.dataType != 'logList'"
            :logs="params.logList"
            :body-params="{
              tid: props.currentRow!.tid,
              methodName: props.currentRow!.methodName
            }"
          />
          <ees-log-viewer-flow
            v-else
            :is-edit="false"
            :is-log-viewer="true"
            :workflow-list="params.flowList"
            :activity-list="params.activityList"
            :selected-row="{} as any"
            :theme="params.theme"
            :lang="params.lang"
          />
        </div>
      </div>
    </template>
  </ees-sidebar>
  <vxe-modal
    v-model="params.showModal"
    class-name="show-log-info-modal"
    :title="
      params.isShowLog ? 'workflowLogViewer.title.logsViewer' : 'workflowLogViewer.btn.flowInfo'
    "
    width="900"
    height="600"
    show-zoom
    resize
    @close="params.isShowLog = false"
  >
    <template #default>
      <show-log
        v-if="params.isShowLog"
        :show-log="params.isShowLog"
        :logs="params.logList"
        :body-params="{
          tid: props.currentRow!.tid,
          methodName: props.currentRow!.methodName
        }"
      />
      <vxe-grid
        v-if="params.showInfo"
        ref="xInfo"
        :data="params.infoList"
        v-bind="infoOption"
      ></vxe-grid>
    </template>
  </vxe-modal>
</template>
<style>
.show-log-info-modal,
.execute-flow-remove-padding {
  .vxe-modal--content {
    padding: 0;
  }
}
</style>
<style lang="less" scoped>
@import url('@/assets/style/variable.less');
.execute-flow-wrapper {
  width: 100%;
  height: 100%;
  .log-header {
    display: flex;
    padding-bottom: @padding-xs;
    justify-content: space-between;
    align-items: center;
    .log-title {
      flex: 1;
      span {
        display: inline-block;
        max-width: 50%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        box-sizing: border-box;
        font-weight: @font-weight-base;
        em {
          font-style: normal;
          font-weight: normal;
        }
        &:last-child {
          padding-left: @padding-inner;
        }
      }
    }
    .log-btn {
      width: auto;
      text-align: end;
    }
    .flow-info-btn {
      margin-right: @margin-btn;
    }
  }
  .flow-content {
    height: calc(100% - 40px);
    :deep(.xflow-editor .--hide) {
      border: none;
    }
    :deep(.x-config-provider) {
      border: 1px solid @border-color;
      border-radius: @border-radius-xs;
    }
    &.table {
      height: 100%;
    }
  }
}
</style>
