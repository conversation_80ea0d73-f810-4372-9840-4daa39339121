<script lang="ts" setup>
import { ref, reactive, provide, onBeforeMount } from 'vue';
import { stateManager as buttonIds } from '@/log-config/index';
import { 
  getPermissionButton, 
  judgeTabPermisson,
  EesLeftMenu,
  type EESMenuListType, 
  type EESTabItem
} from '@futurefab/ui-sdk-ees-basic';
import userDefineState from './userDefineState/index.vue';
import systemState from './systemState/index.vue';
import { useBaseStore } from '@futurefab/ui-sdk-stores';
const baseStore = useBaseStore();
const initTabList: EESMenuListType[] = [
  { name: 'stateManager.title.shareState', id: buttonIds.userDefineStateTab },
  { name: 'stateManager.title.state', id: buttonIds.systemStateTab }
];
const params = reactive({
  tabList: [] as any,
  currentTabId: '',
  currentTabIdx: 0,
  buttonAuthority: {} as any
});
const getButtonAuthority = () => {
  return {
    buttonAuthority: () => params.buttonAuthority
  };
};
provide('getButtonAuthority', getButtonAuthority);
const handleChange = ({ tab }: { tab: { name: string; id: string } }) => {
  params.currentTabId = tab.id;
};
onBeforeMount(async () => {
  params.buttonAuthority = await getPermissionButton('/r2r/state-manager');
  params.tabList = judgeTabPermisson(initTabList as EESTabItem[], params.buttonAuthority);
  if (params.tabList.length) params.currentTabId = params.tabList[0].id;
});
</script>

<template>
  <div class="r2r-state-manager">
    <EesLeftMenu
      v-model:activeKey="params.currentTabId"
      :menu-title-info="baseStore.findMenuTitleInfo()"
      :menu-list="params.tabList"
    >
      <template #right-content>
        <user-define-state v-if="params.currentTabId == buttonIds.userDefineStateTab" />
        <system-state v-if="params.currentTabId == buttonIds.systemStateTab" />
      </template>
    </EesLeftMenu>
  </div>
</template>

<style lang="less" scoped>
@import url('@/assets/style/variable.less');
@ns: r2r-state-manager;
.@{ns} {
  height: 100%;
  width: 100%;
}
</style>
