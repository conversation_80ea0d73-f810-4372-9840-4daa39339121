<script lang="ts" setup>
import {
  defineProps,
  nextTick,
  reactive,
  ref,
  onBeforeUnmount,
  onMounted,
  shallowReactive,
  computed
} from 'vue';
import * as echarts from '@xdatav/echarts/core';
import { LineChart, ScatterChart } from '@xdatav/echarts/charts';
import { summaryInfo as buttonIds } from '@/log-config/index';
import {
  TitleComponent,
  // 组件类型的定义后缀都为 ComponentOption
  TooltipComponent,
  GridComponent,
  // 数据集组件
  DatasetComponent,
  // 内置数据转换器组件 (filter, sort)
  TransformComponent,
  ToolboxComponent,
  LegendComponent,
  BrushComponent
} from '@xdatav/echarts/components';
import { LabelLayout, UniversalTransition } from '@xdatav/echarts/features';
import { CanvasRenderer } from '@xdatav/echarts/renderers';
import {
  getSeries,
  getToday,
  setOptions,
  getTooltip,
  getScatterData,
  exportFun,
  dispatchZoom,
  dispatchBrush,
  removeBrush,
  handleGroupChartData,
  handleFilterChartData,
  getLegendTree,
  getLegendConfig,
  serieConfig,
  findMinMax,
  getMinMaxY,
  getCommentTooltipStr,
  findPointsByXValue,
  getConditionWidth,
  getChartLegendList
} from '../../tools';
import dayjs from 'dayjs';
import ChartGroup from './../chart-group/index.vue';
import { Marker } from '@xchart/marker';
import {
  formatterY,
  setColor,
  showConfirm,
  showError,
  showWarning,
  getSingleButtonAuthority,
  EesButtonTip
} from '@futurefab/ui-sdk-ees-basic';
import { exportXlsxMoreSheets } from '@futurefab/ui-sdk-ees-basic';
import { useRouter } from 'vue-router';
import { cloneDeep, intersection, uniq, orderBy, find } from 'lodash-es';
import { getTraceParamSpec } from '@futurefab/ui-sdk-api';
import { VXETable, t } from '@futurefab/vxe-table';
import type {
  ButtonAuthority,
  CommentHistoryItem,
  CommentObj,
  DataIndex,
  LegendItem,
  SingleChartItem
} from '../../interface';
import { baseUrl } from '@/assets/const';
import type { LegendComponentOption } from '@xdatav/echarts';
import { showInfo } from '@futurefab/ui-sdk-ees-basic';
import {
  getArrayMinMax,
  checkAxisClick,
  setAxisLabelFontSize,
  EesShowSpec,
  EesShowRightY,
  EesAutoY, EesLegendFilter, EesCommentHistory,
   EesOverviewChart, EesCopyChart, EesAutoMultiY, EesCommonLegend } from '@futurefab/ui-sdk-ees-charts';
import UserComment from './../user-comment/index.vue';
import { useCmstore } from '@futurefab/ui-sdk-stores';

const cmStore = useCmstore();
//获取状态
const isHoverTooltip = computed(() => cmStore.isChartHoverTooltip);
// 注册必须的组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  GridComponent,
  DatasetComponent,
  TransformComponent,
  ToolboxComponent,
  LegendComponent,
  BrushComponent,
  LineChart,
  ScatterChart,
  LabelLayout,
  UniversalTransition,
  CanvasRenderer
]);
const props = defineProps({
  searchCondition: {
    type: String,
    default: ''
  },
  startDate: {
    type: Number,
    default: getToday().start
  },
  endDate: {
    type: Number,
    default: getToday().end
  },
  isSplit: {
    type: Boolean,
    default: false
  },
  isShowPoint: {
    type: Boolean,
    default: false
  },
  selectedPointLimit: {
    type: Number,
    default: 0
  },
  maxExportLimit: {
    type: Number,
    default: 0
  },
  isShowCopyConfirm: {
    type: Boolean,
    default: false
  },
  buttonAuthority: {
    type: Object,
    default: () => {
      return {} as ButtonAuthority;
    }
  }
});
const emits = defineEmits(['hideLoading', 'showLoading']);
//ref 获取dom
const xMutilChart = ref();
const xChart = ref();
const xScatterChart = ref();
const xGroup = ref();
const xFilter = ref();
const xTraceDetail = ref();
let chartShowRightY = ref();
let chartAutoY = ref();
const chartShowSpec = ref();
const chartShowXAxis = ref();
const router = useRouter();
const reactiveParams = reactive({
  legendTree: [] as any[],
  legendData: [] as string[] | string[][],
  filterLegendData: [] as string[] | string[][],
  multiTooltipInfo: [] as DataIndex[] | DataIndex[][],
  selectedData: [] as any[],
  isShowComment: false,
  showCommentList: {} as Record<number, CommentObj>, //key为timestamp
  currentCommentInfo: null as CommentObj | null,
  minTime: null as null | number,
  maxTime: null as null | number,
  eqpIdList: [] as string[], //用于请求commentHistory
  markLineList: [] as CommentHistoryItem[]
});
const params = shallowReactive({
  afterGroupData: [] as any[],
  yMin: undefined as string | undefined,
  yMax: undefined as string | undefined,
  yRightMin: undefined as string | undefined,
  yRightMax: undefined as string | undefined,
  yLog: false,
  yRightLog: false,
  yScale: 10,
  yRightScale: 10,
  splitRangeStore: {
    left: {},
    right: {}
  } as Record<string, any>,
  isBaseLine: false,
  isMultiTooltip: false,
  isDeleteBlank: false,
  sumModuleNameList: [] as string[],
  specParams: [] as string[],
  currentSpecParam: null as null | string,
  hasRightY: false,
  rightParamList: [] as string[], //右侧Y轴总的list
  rightYList: [] as string[], //被选中的右侧Y轴list
  isSelectArea: false,
  isShowTraceData: false,
  layout: 1,
  isSplit: false,
  chartType: 1,
  showChart: true,
  isScatter: false,
  specParamList: [] as string[],
  paramAliasList: [] as string[],
  totalCount: 0,
  chartIdList: ['fdc_summary_chart-single'],
  isShowSplitSpec: false,
  currentXAxis: null as null | string,
  xAxisMap: {} as Record<string, string>
});
let chartIdLegendList = ref<SingleChartItem[]>([]);
//chart对象
let myChart: echarts.ECharts;
let chartList = [] as echarts.ECharts[];
//tooltip marker
let marker = null as any;
let commentMarker: any = null;
//保留一份初始数据
let totalData = [] as any[];
const chartCommonLegend = ref();
const chartContentId = 'summary-chart-content';
const singleChartContentIdPrefix = 'summary-single-chart-content-';
//清楚mutiltooltip

const doClearMutilTooltip = () => {
  marker && marker.clear();
  marker = null;
  reactiveParams.multiTooltipInfo = [];
  clearCommentTooltip();
};
//处理Legend列表中的选中状态
const handleLegendListStatus = ({
  checked,
  legendName
}: {
  checked: boolean;
  legendName?: string;
}) => {
  chartIdLegendList.value.forEach((item: SingleChartItem) => {
    item.legendList.forEach((legend: LegendItem) => {
      if (legendName) {
        if (legend.name == legendName) {
          legend.type = checked ? 'legendSelect' : 'legendUnSelect';
        }
      } else {
        legend.type = checked ? 'legendSelect' : 'legendUnSelect';
      }
    });
  });
};
//filter legend 全选处理 只有在单chart中用到
const toggleAllLegend = (checked: boolean) => {
  onMultiToolTip(true);
  clearCommentTooltip();
  handleRemoveBrush();
  reactiveParams.filterLegendData = [];
  myChart.dispatchAction({
    type: 'legendAllSelect'
  });
  if (!checked) {
    reactiveParams.filterLegendData = reactiveParams.legendTree;
    myChart.dispatchAction({
      type: 'legendInverseSelect'
    });
  }
  handleLegendListStatus({ checked });
};
//filter legend 点击单选处理 只有在单chart中用到
const toggleOneLegend = (argus: {
  list: string[];
  value: string;
  checked: boolean;
  label: string;
  index: number;
  indexList: number[];
}) => {
  const { list, value, checked, index, indexList } = argus;
  onMultiToolTip(true);
  clearCommentTooltip();
  handleRemoveBrush();
  reactiveParams.filterLegendData = [];
  (reactiveParams.legendData as string[]).forEach((name: string) => {
    const curName = name.split('^')[index];
    if (checked && curName === value.toString()) {
      myChart.dispatchAction({
        type: 'legendSelect',
        name
      });
      handleLegendListStatus({ checked: true, legendName: name });
    }
    if (list.length) {
      list.forEach((it, i) => {
        const n = name.split('^')[indexList[i]];
        if (n === it.toString()) {
          (reactiveParams.filterLegendData as string[]).push(name);
          myChart.dispatchAction({
            type: 'legendUnSelect',
            name
          });
          handleLegendListStatus({ checked: false, legendName: name });
        }
      });
    }
  });
};
//点击chart的legend时， 处理与filter legend数据同步
const filterLegend = (argus: any) => {
  const { selected, clickName, chartIndex } = argus;
  if (typeof chartIndex == 'number' && params.isBaseLine) {
    handleRemoveBrush(chartIndex);
  } else {
    handleRemoveBrush();
  }
  const list: string[] = [];
  for (const key in selected) {
    if (!selected[key]) list.push(key);
  }
  if (typeof chartIndex == 'number') {
    reactiveParams.filterLegendData[chartIndex] = list;
    doClearMutilTooltip();
    onMultiToolTip(true);
    return false;
  } else {
    reactiveParams.filterLegendData = list;
    doClearMutilTooltip();
    onMultiToolTip(true);
  }
  const keyList = [] as string[];
  reactiveParams.legendTree.forEach((item: any) => {
    if (item.children.length > 0) {
      keyList.push(item.label);
    }
  });

  const legendNameList = Object.keys(selected);
  const flagList = Object.values(selected);
  const isChecked = selected[clickName];
  const clickNameList = clickName.split('^');
  clickNameList.forEach((currentName: string, index: number) => {
    let flag = true; //判断是否存在同名 但选择状态不同的
    for (let i = 0; i < legendNameList.length; i++) {
      const nameList = legendNameList[i].split('^');
      if (isChecked) {
        xFilter.value &&
          xFilter.value.setChecked({
            label: keyList[index],
            name: currentName,
            checked: isChecked
          });
      } else {
        if (nameList[index] === currentName && flagList[i] != isChecked) {
          flag = false;
          return false;
        }
      }
    }
    if (flag) {
      xFilter.value &&
        xFilter.value.setChecked({
          label: keyList[index],
          name: currentName,
          checked: isChecked
        });
    }
  });
};
//将chart的legend转为filter legend需要的数据格式，并传值给filter legend组件 只有在单chart中用到
const setFilterLegend = (data: any[], groupingMap?: any) => {
  reactiveParams.legendTree = getLegendTree(data, groupingMap);
  xFilter.value && xFilter.value.setLegendTree(reactiveParams.legendTree);
};
//处理右侧所有需要异步处理的操作按钮(需要将loading显示出来之后，再执行后面的操作)
const handleShowLoading = (callback: () => void) => {
  emits('showLoading');
  handleDispose();
  handleMutilDispose();
  setTimeout(() => {
    callback && callback();
  }, 500);
};
const handleShowSplitSpec = () => {
  const callbackFun = () => {
    params.isShowSplitSpec = !params.isShowSplitSpec;
    reactiveParams.markLineList = [];
    reDraw();
  };
  handleShowLoading(callbackFun);
};
//group&filter
const doGrouping = (data: any) => {
  const callbackFun = () => {
    console.time('handle grouping data');
    onMultiToolTip(true);
    clearCommentTooltip();
    reactiveParams.showCommentList = {};
    reactiveParams.markLineList = [];
    reactiveParams.filterLegendData = [];
    params.isSelectArea = false;
    reactiveParams.selectedData = [];
    params.afterGroupData = [];
    const { filterMap, groupingMap, chartItem } = data;
    let newData = cloneDeep(totalData);
    if (Object.keys(filterMap).length > 0) {
      for (const key in filterMap) {
        newData = handleFilterChartData({
          data: newData,
          name: key,
          value: filterMap[key]
        });
      }
    }
    if (newData.length == 0) {
      params.afterGroupData = [];
      emits('hideLoading');
      return false;
    }
    if (Object.keys(groupingMap).length > 0) {
      newData = handleGroupChartData({
        data: newData,
        groupingMap
      });
    }
    if (newData.length == 0) {
      params.afterGroupData = [];
      emits('hideLoading');
      return false;
    }
    setFilterLegend(newData, groupingMap);
    params.isSplit = chartItem.isSplitParam;
    params.isScatter = chartItem.kind == 'scatter';
    params.layout = chartItem.layout;
    params.chartType = chartItem.type;
    console.timeEnd('handle grouping data');
    if (params.isScatter) {
      console.time('handle grouping scatterData');
      params.currentSpecParam = null;
      params.showChart = false;
      newData = getScatterData({
        chartItem,
        data: newData,
        isStepGroup: groupingMap.step,
        chartType: chartItem.type
      });
      console.timeEnd('handle grouping scatterData');
    } else {
      if (chartItem.type == 1 && chartItem.isSplitParam) {
        //sumParam相同的参数放在同一个chart中
        params.showChart = false;
        let dataInfo = {} as any;
        newData.forEach((item: any) => {
          if (!dataInfo[item.paramInfo.sumModuleName]) {
            dataInfo[item.paramInfo.sumModuleName] = [item];
            return false;
          }
          dataInfo[item.paramInfo.sumModuleName].push(item);
        });
        let list = Object.values(dataInfo);
        newData = list;
      } else if (chartItem.type == 1 && !chartItem.isSplitParam) {
        //全部在一个chart中
        params.showChart = true;
      } else if (chartItem.type == 2 && chartItem.isSplitParam) {
        // 根据分组单独画chart
        params.showChart = false;
      } else if (chartItem.type == 2 && !chartItem.isSplitParam) {
        // 不同sumParam的同一个分组放在同一个chart中
        params.showChart = false;
        const myData = {} as any;
        newData.forEach((item: any) => {
          const legend = item.paramInfo.legend.split(item.paramInfo.sumParamAlias).join('');
          const keys = Object.keys(myData);
          if (keys.length == 0) {
            myData[legend] = [item];
          } else {
            if (keys.includes(legend)) {
              myData[legend].push(item);
            } else {
              myData[legend] = [item];
            }
          }
        });
        newData = Object.values(myData);
      }
    }

    params.afterGroupData = newData;
    reDraw();
  };
  handleShowLoading(callbackFun);
};
const applyAutoY = (
  min: string,
  max: string,
  minRight: string,
  maxRight: string,
  log: boolean,
  rightLog: boolean,
  scale: number,
  rightScale: number
) => {
  const callbackFun = () => {
    params.yMin = !min && Number(min) !== 0 ? undefined : min;
    params.yMax = !max && Number(max) !== 0 ? undefined : max;
    params.yRightMin = !minRight && Number(minRight) !== 0 ? undefined : minRight;
    params.yRightMax = !maxRight && Number(maxRight) !== 0 ? undefined : maxRight;
    params.yLog = log;
    params.yRightLog = rightLog;
    params.yScale = scale;
    params.yRightScale = rightScale;
    reactiveParams.markLineList = [];
    reDraw();
  };
  handleShowLoading(callbackFun);
};
//显示规格线
const showBaseLine = () => {
  params.isBaseLine = !params.isBaseLine;
  reactiveParams.markLineList = [];
  reDraw();
};
//切换为刷选状态
const doSelectArea = () => {
  onMultiToolTip(true);
  clearCommentTooltip();
  params.isSelectArea = !params.isSelectArea;
  reactiveParams.selectedData = [];
  if (params.showChart) {
    handleSelectArea(myChart);
  } else {
    chartList.forEach((target: echarts.ECharts) => {
      handleSelectArea(target);
    });
  }
};
const handleSelectArea = (target: any) => {
  if (params.isSelectArea) {
    reactiveParams.selectedData = [];
    dispatchBrush(target);
  } else {
    removeBrush(target);
    dispatchZoom(target);
  }
};
//获取刷选状态下，已选择的数据
const getSelectData = (data: any[], chartIndex?: number) => {
  // if (data.length == 0) {
  //     params.isSelectArea = false;
  // }
  if (typeof chartIndex == 'number') {
    data.forEach((item: any) => {
      item.chartIndex = chartIndex;
      const existData = reactiveParams.selectedData.filter(
        (val: any) =>
          val.serieIndex == item.serieIndex &&
          val.dataIndex == item.dataIndex &&
          val.chartIndex == chartIndex
      );
      existData.length == 0 && reactiveParams.selectedData.push(item);
    });
  } else {
    reactiveParams.selectedData = data;
  }
};
//取消刷选状态，切换为缩放状态
const handleRemoveBrush = (chartIndex?: number) => {
  params.isSelectArea = false;
  reactiveParams.selectedData = [];
  if (typeof chartIndex == 'number') {
    chartList[chartIndex] && removeBrush((chartList as any[])[chartIndex]);
    return false;
  }
  doRemoveBrush(myChart);
  chartList.length > 0 &&
    chartList.forEach((target: echarts.ECharts) => {
      doRemoveBrush(target);
    });
};
const doRemoveBrush = (target: any) => {
  target && removeBrush(target);
  target && dispatchZoom(target);
};
//弹出Trace Detail
const toTraceData = async (jumpType?: string) => {
  const moduleIdList = [] as string[];
  params.paramAliasList = [];
  const traceData = [] as any;
  const virtulTraceData = [] as any;
  let list = cloneDeep(params.afterGroupData);
  const setData = (item: any, value: any) => {
    const startDate =
      item['sumInfo'][value.dataIndex]['startDate'] === '-'
        ? item['timestamp'][value.dataIndex]
        : item['sumInfo'][value.dataIndex]['startDate'];
    const endDate =
      item['sumInfo'][value.dataIndex]['endDate'] === '-'
        ? item['timestamp'][value.dataIndex]
        : item['sumInfo'][value.dataIndex]['endDate'];
    const obj = {
      startDate: dayjs(startDate).format('YYYY-MM-DD HH:mm:ss.SSS'),
      endDate: dayjs(endDate).format('YYYY-MM-DD HH:mm:ss.SSS'),
      eqpModuleId: item['paramInfo']['eqpModuleId'],
      lotIds:
        item['paramInfo']['category'] === 'TRACE TIME SUM' ? [] : [item['lotid'][value.dataIndex]],
      substrateIds:
        item['paramInfo']['category'] === 'TRACE TIME SUM'
          ? []
          : [item['substrateid'][value.dataIndex]]
    };
    let flag = item['paramInfo']['category'] !== 'TRACE SUM';
    if (jumpType && jumpType == 'link') {
      flag = false;
    }
    if (flag) {
      virtulTraceData.push(obj);
    } else {
      let isTrue = true;
      if (jumpType && jumpType == 'link') {
        isTrue =
          item['paramInfo']['category'] === 'TRACE TIME SUM' ||
          item['paramInfo']['category'] === 'TRACE SUM';
      }
      if (isTrue) {
        moduleIdList.push(item['paramInfo']['eqpModuleId']);
        params.paramAliasList.push(item['paramInfo']['eqpParamAlias']);
      }
      traceData.push(obj);
    }
  };
  list.forEach((item: any, index: number) => {
    reactiveParams.selectedData.forEach((value: any) => {
      if (Array.isArray(item)) {
        if (value.chartIndex == index) {
          item.forEach((iVal: any, iIndex: number) => {
            if (value.serieIndex == iIndex) {
              setData(iVal, value);
            }
          });
        }
      } else {
        let myIndex = value.serieIndex;
        if (value.hasOwnProperty('chartIndex')) {
          myIndex = value.chartIndex;
        }
        if (myIndex == index) {
          setData(item, value);
        }
      }
    });
  });
  if (params.paramAliasList.length > 1) {
    params.paramAliasList = [...new Set(params.paramAliasList)];
  }
  let type = 'normal';
  if (traceData.length > props.selectedPointLimit) {
    showWarning(t('summary.tips.errorNum', { num: props.selectedPointLimit }), false);
    handleRemoveBrush();
    return false;
  }
  if (virtulTraceData.length > 0 && traceData.length > 0) {
    if (jumpType && jumpType == 'link') {
      type = await showConfirm({ msg: 'summary.tips.errorVirtual3' });
    } else {
      type = await showConfirm({ msg: 'summary.tips.errorVirtual1' });
    }
    if (type != 'confirm') {
      handleRemoveBrush();
      return false;
    }
  }
  if (virtulTraceData.length > 0 && traceData.length == 0) {
    if (jumpType && jumpType == 'link') {
      showWarning('summary.tips.errorVirtual4');
    } else {
      showWarning('summary.tips.errorVirtual2');
    }

    handleRemoveBrush();
    return false;
  }
  sessionStorage.setItem(
    'analysisChartParams',
    JSON.stringify({ lotHistories: traceData, source: 'SUMMARY' })
  );
  if (jumpType && jumpType == 'link') {
    linkToTraceData();
  } else {
    if (params.isShowTraceData) {
      xTraceDetail.value && xTraceDetail.value.onInitChart();
      return false;
    }
    const result = await getTraceParamSpec({
      headerParams: {
        log: 'N'
      },
      bodyParams: {
        moduleIdList: [...new Set(moduleIdList)],
        paramAliasList: params.paramAliasList
      }
    });
    if (result.status == 'SUCCESS') {
      params.specParamList = result.data;
      params.isShowTraceData = true;
      nextTick(() => {
        xTraceDetail.value &&
          xTraceDetail.value.onChangeChartData({
            paramList: params.paramAliasList
          });
      });
    } else {
      showError(result.msg, false);
      return false;
    }
  }
  return true;
};
//跳转Trace Detail
const linkToTraceData = () => {
  const paramList = params.paramAliasList.join(',');
  const routeData = router.resolve({
    name: 'trace-detail-chart',
    query: {
      from: baseUrl + '/summary',
      searchCondition: props.searchCondition,
      paramAlias: paramList
    }
  });
  window.open(routeData.href, '_blank');
};
//关闭Trace Detail 弹框
const closeTraceDetail = () => {
  params.isShowTraceData = false;
};
//处理缩放Trace Detail 弹框，chart resize
const handleZoomTraceDetail = () => {
  xTraceDetail.value && xTraceDetail.value.resizeChart();
};
//处理SPEC 显示
const onShowSpec = (val: { specValue: string }) => {
  const { specValue } = val;
  params.currentSpecParam = specValue ? specValue : null;
  reactiveParams.markLineList = [];
  const callbackFun = () => {
    reDraw();
  };
  handleShowLoading(callbackFun);
};
const onShowXAxis = (val: { specValue: string }) => {
  const { specValue } = val;
  params.currentXAxis = specValue ? specValue : null;
  reactiveParams.markLineList = [];
  const callbackFun = () => {
    reDraw();
  };
  handleShowLoading(callbackFun);
};
//显示右侧Y轴
const onShowRightY = (list: string[]) => {
  const callbackFun = () => {
    params.rightYList = list;
    params.hasRightY = list.length > 0;
    reactiveParams.markLineList = [];
    reDraw();
  };
  handleShowLoading(callbackFun);
};
const getTooltipDom = () => {
  return document.getElementById(
    params.showChart ? `${singleChartContentIdPrefix}fdc_summary_chart-noTitle` : chartContentId
  );
};
//切换为tooltip模式
const onMultiToolTip = (flag?: boolean) => {
  if (flag) {
    //清空tooltip
    params.isMultiTooltip = false;
  } else {
    params.isMultiTooltip = !params.isMultiTooltip;
  }
  if (params.isMultiTooltip) {
    if (!marker) {
      marker = new Marker({
        wrapper: getTooltipDom() as HTMLDivElement,
        closable: true,
        closeByRightClick: false,
        canMoveOutside: false,
        copyable: true
      });
    }
  } else {
    marker && marker.clear();
    marker = null;
    reactiveParams.multiTooltipInfo = [];
  }
};
//点击chart上的数据点，显示tooltip
const handleMutilTooltip = ({
  type,
  value,
  chartIndex
}: {
  type: string;
  value?: any;
  chartIndex?: number;
}) => {
  if (type == 'hide') {
    onMultiToolTip(true);
    return false;
  }
  if (isHoverTooltip.value === false && !params.isMultiTooltip) {
    marker && marker.clear();
    reactiveParams.multiTooltipInfo = [];
    const dom = getTooltipDom();
    marker = new Marker({
      wrapper: dom as HTMLDivElement,
      closable: true,
      closeByRightClick: false,
      canMoveOutside: false,
      copyable: true
    });
  } else {
    if (!params.isMultiTooltip) return false;
  }

  const { event, seriesIndex, dataIndex, color } = value;
  let x = event.offsetX;
  let y = event.offsetY;
  const flag = typeof chartIndex == 'number';
  const currentChartDom = flag
    ? document.getElementById(`fdc_summary_chart${chartIndex}`)
    : xChart.value;
  let offsetTop = currentChartDom?.offsetTop;
  let offsetLeft = currentChartDom?.offsetLeft;
  if (params.isScatter) {
    const contentDom = document.getElementById(chartContentId);
    if (contentDom) {
      const contentBoundingClient = contentDom.getBoundingClientRect();
      const chartBoundingClient = currentChartDom.getBoundingClientRect();
      offsetTop = chartBoundingClient.top - contentBoundingClient.top;
      offsetLeft = chartBoundingClient.left - contentBoundingClient.left;
    }
  }
  x = x + offsetLeft;
  y = y + offsetTop;
  let dataValue: any = '';
  if (flag) {
    if (Array.isArray(params.afterGroupData[chartIndex])) {
      dataValue = params.afterGroupData[chartIndex][seriesIndex];
    } else {
      dataValue = params.afterGroupData[chartIndex];
    }
    if (!reactiveParams.multiTooltipInfo[chartIndex]) {
      reactiveParams.multiTooltipInfo[chartIndex] = [];
    }
    (reactiveParams.multiTooltipInfo[chartIndex] as DataIndex[]).push({
      seriesIndex,
      dataIndex
    });
  } else {
    dataValue = params.afterGroupData[seriesIndex];
    (reactiveParams.multiTooltipInfo as DataIndex[]).push({
      seriesIndex,
      dataIndex
    });
  }
  marker.addMark({
    key: `${seriesIndex}-${dataIndex}${flag ? '-' + chartIndex : ''}`,
    x,
    y,
    data: [
      {
        color,
        label: '',
        // point value
        value: getTooltip({ value: dataValue, dataIndex, specList: params.specParams })
      }
    ]
  });
};
//删除空白区域
const onDeleteBlank = () => {
  const callbackFun = () => {
    params.isDeleteBlank = !params.isDeleteBlank;
    reactiveParams.markLineList = [];
    reactiveParams.currentCommentInfo = null;
    reactiveParams.showCommentList = {};
    reDraw();
  };
  handleShowLoading(callbackFun);
};
//导出数据
const onExport = async () => {
  const handleFun = () => {
    emits('showLoading', 'common.loading.text');
    setTimeout(() => {
      const data = exportFun(totalData);
      console.time('exprorting');
      exportXlsxMoreSheets(data, 'SummaryDataAnalysis_Chart.xlsx');
      console.timeEnd('exprorting');
      emits('hideLoading');
    }, 50);
  };
  //数据总量大于后端配置的值，显示提示框告知用户
  if (params.totalCount > props.maxExportLimit) {
    const type = await showConfirm({ msg: t('common.tip.exportTips', { count: params.totalCount }) });
    if (type == 'confirm') {
      handleFun();
    }
  } else {
    handleFun();
  }
};
//还原chart到初始状态
const onInitialize = () => {
  const callbackFun = () => {
    onMultiToolTip(true);
    clearCommentTooltip();
    reactiveParams.showCommentList = {};
    reactiveParams.markLineList = [];
    resetParams();
    setFilterLegend(totalData);
    params.showChart = !props.isSplit;
    reDraw();
  };
  handleShowLoading(callbackFun);
};
//处理chart的option
const handleOptions = ({
  data,
  chartIndex,
  colorIndex,
  chartHeight,
  chartId
}: {
  data: any;
  chartIndex?: number;
  colorIndex?: number | null;
  chartHeight: number;
  chartId?: string;
}) => {
  const chartData = data;
  let totalTimeList: number[] = [];
  let legendList: string[] = [];
  let minLeftY = undefined as undefined | string,
    maxLeftY = undefined as undefined | string,
    minRightY = undefined as undefined | string,
    maxRightY = undefined as undefined | string;
  const hasSpec =
    (!params.isSplit && params.currentSpecParam) || (params.isSplit && params.isShowSplitSpec);
  chartData.forEach((item: any) => {
    //添加Y轴索引
    item['paramInfo']['yAxisIndex'] = 0;
    //含有右侧Y轴的数据
    if (params.hasRightY) {
      params.rightYList.forEach((val: string) => {
        if (item['paramInfo']['sumParamAlias'] === val) {
          item['paramInfo']['yAxisIndex'] = 1;
        }
      });
    }
    //是deleteblank模式
    if (params.isDeleteBlank) {
      item.timestamp.forEach((it: number) => totalTimeList.push(it));
    }
    //处理legend
    const name = item['paramInfo']['legend'];
    if (!legendList.includes(name)) {
      legendList.push(name);
    }
    let { min: minVal, max: maxVal } = findMinMax(item.sumValue);
    if (hasSpec) {
      let { min: minLsl } = findMinMax(item.lsl);
      const { min: minLcl } = findMinMax(item.lcl);

      let { max: maxUsl } = findMinMax(item.usl);
      const { max: maxUcl } = findMinMax(item.ucl);
      if (minLsl === undefined && minLcl !== undefined) {
        minLsl = minLcl;
      }
      if (maxUsl === undefined && maxUcl !== undefined) {
        maxUsl = maxUcl;
      }
      if (minLsl < minVal) {
        minVal = minLsl;
      }
      if (maxUsl > maxVal) {
        maxVal = maxUsl;
      }
    }
    const isRightY = params.rightYList.includes(item.paramInfo.sumParamAlias);
    if (isRightY) {
      if (minRightY === undefined || minVal < minRightY) {
        minRightY = minVal;
      }

      if (maxRightY === undefined || maxVal > maxRightY) {
        maxRightY = maxVal;
      }
    } else {
      if (minLeftY === undefined || minVal < minLeftY) {
        minLeftY = minVal;
      }
      if (maxLeftY === undefined || maxVal > maxLeftY) {
        maxLeftY = maxVal;
      }
    }
  });
  let isShowLeftY = true,
    isShowRightY = false;
  if (typeof chartIndex == 'number') {
    reactiveParams.legendData[chartIndex] = legendList;
    if (params.hasRightY) {
      //处理多chart判断是否显示左右Y轴
      if ((params.chartType == 1 && params.isSplit) || params.chartType == 2) {
        const isExist =
          chartData.filter((item: any) => params.rightYList.includes(item.paramInfo.sumParamAlias))
            .length != chartData.length;
        isShowLeftY = (params.rightYList.length > 0 && isExist) || params.rightYList.length == 0;
      } else if (params.chartType == 2 && params.isSplit) {
        isShowLeftY = !params.rightYList.includes(chartData.paramInfo.sumParamAlias);
      }
      if (params.chartType == 2 && !params.isSplit) {
        isShowRightY = params.rightYList.length > 0;
      } else {
        isShowRightY = !isShowLeftY;
      }
    }
  } else {
    reactiveParams.legendData = legendList;
    if (params.hasRightY) {
      //单个chart判断是否显示左右Y轴
      isShowLeftY =
        (params.rightYList.length > 0 &&
          params.rightYList.length != params.rightParamList.length) ||
        params.rightYList.length == 0;
      isShowRightY = true;
    }
  }
  //时间戳去重排序
  totalTimeList = [...new Set(totalTimeList)].sort();
  //用于deleteblank，X轴时间戳List对应的indexList
  const xIndex = [] as number[];
  totalTimeList.forEach((val, index) => xIndex.push(index));
  const leftYVal = getMinMaxY({
    minY: params.yMin != null ? params.yMin : minLeftY,
    maxY: params.yMax != null ? params.yMax : maxLeftY
  });
  let storeMin = null;
  let storeMax = null;
  // 如果是多个chart的情况，需要根据splitRangeStore来设置yMin和yMax
  if (typeof chartIndex === 'number') {
    //找到当前对应的chart修改leftYVal
    const title = chartIdLegendList.value[chartIndex].title as string;
    if (params.splitRangeStore.left[title]) {
      const { yMin, yMax } = params.splitRangeStore.left[title];
      storeMin = yMin;
      storeMax = yMax;
    }
  }
  //设置左侧Y轴最大最小值
  const yAxis = [
    {
      show: isShowLeftY,
      min: () => {
        if (storeMin) return storeMin;
        return params.yMin != null ? params.yMin : leftYVal.minY;
      },
      max: () => {
        if (storeMax) return storeMax;
        return params.yMax != null ? params.yMax : leftYVal.maxY;
      },
      type: params.yLog ? 'log' : 'value',
      logBase: params.yScale,
      minorSplitLine: params.yLog
    }
  ];
  const RightYVal = getMinMaxY({
    minY: params.yRightMin != null ? params.yRightMin : minRightY,
    maxY: params.yRightMax != null ? params.yRightMax : maxRightY
  });
  let storeRightMin = null;
  let storeRightMax = null;
  // 如果是多个chart的情况，需要根据splitRangeStore来设置yMin和yMax
  if (typeof chartIndex === 'number') {
    //找到当前对应的chart修改RightYVal
    const title = chartIdLegendList.value[chartIndex].title as string;
    if (params.splitRangeStore.right[title]) {
      const { yMin, yMax } = params.splitRangeStore.right[title];
      storeRightMin = yMin;
      storeRightMax = yMax;
    }
  }
  let currMaxY = storeRightMin ?? params.yMax ?? leftYVal.maxY;
  let currMinY = storeRightMin ?? params.yMin ?? leftYVal.minY;
  //设置右侧Y轴最大最小值
  if (params.hasRightY) {
    //如果spec 参数在右侧Y轴，需要使用右侧Y轴的刻度
    if (
      (!params.isSplit &&
        params.currentSpecParam &&
        params.rightYList.includes(params.currentSpecParam)) ||
      (params.isSplit &&
        params.isShowSplitSpec &&
        params.rightYList.includes(chartData[0].paramInfo.sumParamAlias))
    ) {
      currMaxY = storeRightMax ?? params.yRightMax ?? RightYVal.maxY;
      currMinY = storeRightMin ?? params.yRightMin ?? RightYVal.minY;
    }
    yAxis.push({
      show: isShowRightY,
      min: () => {
        if (storeRightMin) return storeRightMin;
        return params.yRightMin != null ? params.yRightMin : RightYVal.minY;
      },
      max: () => {
        if (storeRightMax) return storeRightMax;
        return params.yRightMax != null ? params.yRightMax : RightYVal.maxY;
      },
      type: params.yRightLog ? 'log' : 'value',
      logBase: params.yRightScale,
      minorSplitLine: params.yRightLog
    });
  }
  // 用于计算spec status的位置
  const oneScale = (chartHeight - 70) / (Number(currMaxY) - Number(currMinY));
  //处理series(由于需要使用totalTimeList，因此不能放到同一个循环内)
  console.time('handle series');
  let currentSpecParam = params.currentSpecParam;
  //根据split进行判断是否显示spec
  if (params.isSplit) {
    currentSpecParam =
      params.specParams.includes(chartData[0].paramInfo.sumParamAlias) && params.isShowSplitSpec
        ? chartData[0].paramInfo.sumParamAlias
        : null;
  }
  const series = getSeries({
    list: chartData,
    paramName: { x: 'timestamp', y: 'sumValue' },
    isDeleteBlank: params.isDeleteBlank,
    totalTimeList: totalTimeList,
    isShowPoint: props.isShowPoint,
    colorIndex,
    currentSpecParam,
    maxY: Number(currMaxY),
    oneScale,
    markLineList: reactiveParams.markLineList,
    isSplit: params.isSplit
  });
  console.timeEnd('handle series');
  let axisLabelWidth = 70;
  if (params.currentXAxis && params.currentXAxis !== 'TIMESTAMP') {
    const key = params.xAxisMap[params.currentXAxis!];
    const name = find(chartData[0][key], (value: string) => {
      return value !== '';
    });
    if (name) {
      axisLabelWidth = getConditionWidth(name);
      if (!axisLabelWidth || axisLabelWidth < 70) {
        axisLabelWidth = 70;
      }
    }
  }
  //配置options
  const options = setOptions({
    legend: getLegendConfig(legendList, params.layout),
    xAxis: {
      type: params.isDeleteBlank ? 'category' : 'time',
      axisLabel: {
        lineHeight: 14,
        width: axisLabelWidth,
        formatter: (value: any, index: number) => {
          const item = params.isDeleteBlank ? totalTimeList[value] : value;
          let val = dayjs(item).format('MM-DD HH:mm:ss');
          if (params.currentXAxis && params.currentXAxis !== 'TIMESTAMP') {
            //可使用lotid展示在x轴;
            chartData.forEach((d: any) => {
              d.timestamp.forEach((t: number, i: number) => {
                if (t <= item) {
                  const key = params.xAxisMap[params.currentXAxis!];
                  val = d[key][i];
                }
              });
            });
          }

          return val;
        },
        hideOverlap: true
      },
      axisPointer: {
        show: params.isBaseLine,
        label: {
          formatter: (argus: any) => {
            const item = params.isDeleteBlank ? totalTimeList[argus.value] : argus.value;
            let val = dayjs(item).format('MM-DD HH:mm:ss');
            if (params.currentXAxis && params.currentXAxis !== 'TIMESTAMP') {
              //可使用lotid展示在x轴;
              chartData.forEach((d: any) => {
                d.timestamp.forEach((t: number, i: number) => {
                  if (t <= item) {
                    const key = params.xAxisMap[params.currentXAxis!];
                    val = d[key][i];
                  }
                });
              });
            }
            return val;
          }
        }
      },
      data: xIndex
    },
    yAxis,
    series: Object.freeze(series),
    axisPointer: {
      show: !props.isShowPoint
    },
    tooltip: {
      formatter: (val: any) => {
        const { dataIndex, seriesName } = val;
        const value = chartData.filter(
          (item: any) => item['paramInfo']['legend'] === seriesName
        )[0];
        return getTooltip({
          value,
          dataIndex,
          specList: params.specParams
        });
      }
    }
  });
  return options;
};
const getMin = (value: { min: number; max: number }) => {
  return value.min - (value.max - value.min) * 0.01;
};
const getMax = (value: { min: number; max: number }) => {
  return value.max + (value.max - value.min) * 0.01;
};
const handleScatterOptions = ({
  data,
  chartIndex,
  colorIndex
}: {
  data: any;
  chartIndex: number;
  colorIndex?: number | null;
}) => {
  const legendList: string[] = [];
  const series = [] as any[];
  data.forEach((v: any, i: number) => {
    series.push({
      ...serieConfig({ isShowPoint: false }),
      emphasis: {
        scale: true
      },
      name: v['paramInfo']['legend'].split(`${v['paramInfo']['sumParamAlias']}^`)[1],
      type: 'scatter',
      legendHoverLink: false,
      data: v.xyValue,
      color: typeof colorIndex == 'number' ? setColor(chartIndex) : setColor(i),
      large: true
    });
    const name = v['paramInfo']['legend'];
    legendList.indexOf(name) < 0 &&
      legendList.push(name.split(`${v['paramInfo']['sumParamAlias']}^`)[1]);
  });
  reactiveParams.legendData[chartIndex] = legendList;
  const options = setOptions({
    grid: {
      top: 60,
      right: 40
    },
    legend: getLegendConfig(legendList, params.layout),
    xAxis: {
      type: 'value',
      axisLabel: {
        hideOverlap: true,
        formatter: (value: number) => {
          const newValue = formatterY(value);
          return newValue;
        }
      },
      axisPointer: {
        show: false
      },
      min: getMin,
      max: getMax
    },
    yAxis: {
      axisLabel: {
        formatter: (value: any) => {
          const newValue = formatterY(value);
          return newValue;
        }
      },
      type: 'value',
      min: getMin,
      max: getMax
    },
    axisPointer: {
      show: false
    },
    series,
    tooltip: {
      formatter: (val: any) => {
        const { dataIndex, seriesName } = val;
        const value = data.filter(
          (item: any) =>
            item['paramInfo']['legend'].split(`${item['paramInfo']['sumParamAlias']}^`)[1] ===
            seriesName
        )[0];
        return getTooltip({
          value,
          dataIndex
        });
      }
    }
  });
  return options;
};
//销毁chart实例
const handleDispose = () => {
  if (myChart && !myChart.isDisposed()) myChart.dispose();
};
const handleMutilDispose = () => {
  chartList.forEach((target: echarts.ECharts) => {
    if (target && !target.isDisposed()) target.dispose();
  });
  chartList = [];
};
//用于多chart显示隐藏legend
const handleLegend = ({
  legendData,
  filterLegendData,
  chartIndex
}: {
  legendData: string[];
  filterLegendData: any[];
  chartIndex?: number;
}) => {
  const selectedObj = {} as any;
  legendData.forEach((name: string) => {
    if (!filterLegendData) {
      filterLegendData = [];
    }
    const num = filterLegendData.filter((item: string) => name === item).length;
    if (num == 0) {
      selectedObj[name] = true;
    } else {
      selectedObj[name] = false;
    }
  });
  return selectedObj;
};
//画图
const reDraw = (isInit?: boolean) => {
  if (totalData.length == 0) return false;
  onMultiToolTip(true);
  params.isSelectArea = false;
  reactiveParams.selectedData = [];
  chartIdLegendList.value = [];
  setTimeout(() => {
    if (params.showChart) {
      handleSingleChart();
    } else {
      handleMutilChart();
    }
    setChartIdList();
    if (!isInit) {
      emits('hideLoading');
    }
    console.timeEnd('drawing');
  }, 100);
};
//绘制单chart
const handleSingleChart = () => {
  doClearMutilTooltip();
  handleDispose();
  const charIdStr = 'fdc_summary_chart';
  chartIdLegendList.value = [
    {
      chartId: charIdStr,
      legendList: getChartLegendList({ list: params.afterGroupData }),
      title: getChartTitle()
    }
  ];
  nextTick(() => {
    myChart = echarts.init(document.getElementById(charIdStr)!);
    const chartHeight = myChart.getHeight();
    console.time('handle option');
    let options = handleOptions({
      data: params.afterGroupData,
      chartHeight,
      chartId: charIdStr
    });
    console.timeEnd('handle option');
    console.time('drawing');
    if (reactiveParams.filterLegendData && reactiveParams.filterLegendData.length > 0) {
      const selected = {} as Record<string, boolean>;
      reactiveParams.filterLegendData.forEach((item: any) => {
        selected[item] = false;
      });
      (options.legend as LegendComponentOption).selected = selected;
    }
    setAxisLabelFontSize(options);
    myChart && myChart.setOption(options);
    // !!! Bind chat event after chart rendered in idle time
    requestIdleCallback(() => {
      keepLegendStatus(myChart);
      bindChartEvent(myChart);
    });
  });
};
//绘制多chart
const handleMutilChart = () => {
  doClearMutilTooltip();
  handleMutilDispose();
  params.afterGroupData.forEach((item: any, index: number) => {
    if (params.isScatter) {
      const chartLegendList: LegendItem[] = [];
      const newArr = Array.isArray(item) ? item : [item];
      newArr.forEach((v: any, i: number) => {
        const name = v['paramInfo']['legend'];
        chartLegendList.filter((legendItem: LegendItem) => legendItem.name === name);
        chartLegendList.push({
          name: name.split(`${v['paramInfo']['sumParamAlias']}^`)[1],
          color: typeof Array.isArray(item) ? setColor(index) : setColor(i),
          type: 'legendSelect'
        });
      });
      chartIdLegendList.value.push({
        chartId: `fdc_summary_chart${index}`,
        legendList: chartLegendList,
        title: getChartTitle(index)
      });
    } else {
      chartIdLegendList.value.push({
        chartId: `fdc_summary_chart${index}`,
        legendList: getChartLegendList({
          list: Array.isArray(item) ? item : [item],
          colorIndex: Array.isArray(item) ? null : index
        }),
        title: getChartTitle(index)
      });
    }
  });
  params.afterGroupData.forEach((item: any, index: number) => {
    nextTick(() => {
      const target = echarts.init(document.getElementById(`fdc_summary_chart${index}`)!);
      const chartHeight = target.getHeight();
      let options: Record<string, unknown> = {};
      if (params.isScatter) {
        options = handleScatterOptions({
          data: Array.isArray(item) ? item : [item],
          chartIndex: index,
          colorIndex: Array.isArray(item) ? null : index //用于多个chartItem.isSplit=true&chartItem.type=2的情况;
        });
      } else {
        options = handleOptions({
          data: Array.isArray(item) ? item : [item],
          chartIndex: index,
          colorIndex: Array.isArray(item) ? null : index, //用于多个chartItem.isSplit=true&chartItem.type=2的情况;
          chartHeight
        });
        if (params.isBaseLine) {
          (options.legend as Record<string, unknown>).selected = handleLegend({
            legendData: reactiveParams.legendData[index] as string[],
            filterLegendData: reactiveParams.filterLegendData[index] as any[]
          });
        }
      }
      console.time('drawing');
      setAxisLabelFontSize(options);
      target && target.setOption(options);
      chartList[index] = target;
      if (!params.isBaseLine || params.isScatter) {
        keepLegendStatus(target, index);
      }
      bindChartEvent(target, index);
    });
  });
  nextTick(() => {
    if (params.isBaseLine && !params.isScatter) {
      echarts.connect(chartList);
    } else {
      echarts.connect('');
    }
  });
};

const handleCommentDrawChart = ({ checkedList }: { checkedList: CommentHistoryItem[] }) => {
  const callbackFun = () => {
    reactiveParams.showCommentList = {};
    reactiveParams.markLineList = checkedList;
    requestIdleCallback(() => {
      reDraw();
    });
  };

  handleShowLoading(callbackFun);
};
const getXValue = ({
  chartIndex,
  seriesIndex,
  timestampValue
}: {
  chartIndex?: number;
  seriesIndex: number;
  timestampValue: number;
}) => {
  let totalTimeList = [] as number[];
  //处理delete blank
  if (params.isDeleteBlank) {
    const chartDataList =
      chartIndex === undefined ? params.afterGroupData : params.afterGroupData[chartIndex];
    if (Array.isArray(chartDataList)) {
      chartDataList.forEach((item: { timestamp: number[] }) => {
        totalTimeList.push(...item.timestamp);
      });
    } else {
      totalTimeList = chartDataList['timestamp'];
    }
    totalTimeList = [...new Set(totalTimeList)].sort();
  }
  const xValue = params.isDeleteBlank ? totalTimeList[timestampValue] : timestampValue;
  return xValue;
};
const handleSaveComment = ({ eventName, comment }: { eventName: string; comment: string }) => {
  if (reactiveParams.currentCommentInfo) {
    emits('showLoading');
    const { event, chartTarget } = reactiveParams.currentCommentInfo;
    const initOptions = chartTarget.getOption();
    const options = cloneDeep(initOptions);
    const { series } = options;
    reactiveParams.currentCommentInfo.eventName = eventName;
    reactiveParams.currentCommentInfo.comment = comment;
    const xValue = getXValue({
      chartIndex: reactiveParams.currentCommentInfo.chartIndex,
      seriesIndex: reactiveParams.currentCommentInfo.seriesIndex,
      timestampValue: reactiveParams.currentCommentInfo.value
    });
    //添加markLine
    reactiveParams.showCommentList[xValue] = reactiveParams.currentCommentInfo;
    series[reactiveParams.currentCommentInfo.seriesIndex]['markLine']['data'].push({
      xAxis: reactiveParams.currentCommentInfo.value
    });
    requestIdleCallback(() => {
      chartTarget.setOption(
        {
          series: [...series]
        },
        { replaceMerge: ['series'], lazyUpdate: true }
      );
      emits('hideLoading');
      //显示comment tooltip
      handleCommentTooltip({
        ...event,
        xValue,
        chartIndex: reactiveParams.currentCommentInfo?.chartIndex,
        seriesIndex: reactiveParams.currentCommentInfo?.seriesIndex as number
      });
      //清空当前comment对象
      reactiveParams.currentCommentInfo = null;
    });
  }
};
const clearCommentTooltip = () => {
  commentMarker && commentMarker.clear();
  commentMarker = null;
  // reactiveParams.showCommentList = [];
  reactiveParams.currentCommentInfo = null;
};
const handleCommentTooltip = (info: {
  offsetX: number;
  offsetY: number;
  xValue: any;
  chartIndex?: number;
  seriesIndex: number;
}) => {
  if (!commentMarker) {
    const dom = getTooltipDom();
    commentMarker = new Marker({
      wrapper: dom as HTMLDivElement,
      closable: true,
      closeByRightClick: false,
      canMoveOutside: false,
      copyable: true
    });
  }
  const value =
    info.chartIndex !== undefined ? params.afterGroupData[info.chartIndex] : params.afterGroupData;
  const eqpId = Array.isArray(value)
    ? value[info.seriesIndex]['paramInfo']['eqpId']
    : value['paramInfo']['eqpId'];
  const markerKey =
    info.chartIndex !== undefined
      ? `markLine_${info.chartIndex}_${info.xValue}_${eqpId}`
      : `markLine_${info.xValue}_${eqpId}`;
  if (params.isMultiTooltip) {
    commentMarker.removeMark(markerKey);
  }
  let x = info.offsetX;
  let y = 20;
  const currentChartDom =
    info.chartIndex !== undefined
      ? document.getElementById(`fdc_summary_chart${info.chartIndex}`)
      : xChart.value;
  const offsetTop = currentChartDom?.offsetTop;
  const offsetLeft = currentChartDom?.offsetLeft;
  x = x + offsetLeft;
  y = y + offsetTop;
  const commentInfo = reactiveParams.showCommentList[info.xValue];
  if (!commentInfo) return false;
  if (!params.isMultiTooltip) {
    commentMarker.clear();
  }
  commentMarker.addMark({
    key: markerKey,
    x,
    y,
    data: [
      {
        color: 'red',
        label: '',
        // point value
        value: getCommentTooltipStr({
          value: Array.isArray(value) ? value[info.seriesIndex] : value,
          eventName: commentInfo.eventName || '',
          eventDtts: info.xValue,
          comment: commentInfo.comment || '',
          moduleAlias: commentInfo.moduleAlias
        })
      }
    ]
  });
};
const bindChartEvent = (target: any, chartIndex?: number) => {
  //切换为zoom模式
  dispatchZoom(target);
  //取消刷选模式
  removeBrush(target);
  let moveX = 0;
  let isClickBaseLine = false; //用于判断是都点击baseline上
  //鼠标按下事件，记录鼠标X轴位置
  target.getZr().on('mousedown', (event: MouseEvent) => {
    moveX = event.offsetX;
    isClickBaseLine = event.target && (event.target as any).draggable;
  });
  //鼠标抬起事件，鼠标位置小于初始位置，还原chart
  target.getZr().on('mouseup', (event: MouseEvent) => {
    if (event.offsetX < moveX && !isClickBaseLine) {
      if (params.isBaseLine && !params.showChart) {
        reDraw();
      } else {
        keepLegendStatus(target, chartIndex);
      }
      // !!!Dispatch zoom action in idle time, because it may cause performance problems
      requestIdleCallback(() => {
        handleRemoveBrush(params.isBaseLine && !params.showChart ? chartIndex : undefined);
        dispatchZoom(target);
      });
    }
  });
  //鼠标移出标记点，处理tooltip样式
  target.on('mouseout', () => {
    const dom = document.getElementsByClassName('echarts-tooltip-hide');
    Array.from(dom).forEach((element: any) => {
      element.style.display = 'none';
    });
  });
  //点击chart标记点，如果是mutilTooltip模式，显示tooltip
  target.on('click', (argus: any) => {
    const { event } = argus;
    const { type, name } = event.target;
    if (type && !params.isScatter) {
      //点击markLine的判断
      if (
        (type == 'path' && name && name == 'toSymbol') ||
        (type == 'ec-line' && name && name == 'line')
      ) {
        //获取x轴时间戳
        const xValue = getXValue({
          chartIndex,
          seriesIndex: argus.seriesIndex,
          timestampValue: argus.value
        });
        //获取附近点
        const pointValue = findPointsByXValue({
          targetXValue: argus.value,
          offsetY: event.offsetY,
          target
        });
        if (pointValue) {
          //是否按住ctrl
          if (event.event.ctrlKey) {
            reactiveParams.isShowComment = true;
            const chartData =
              chartIndex !== undefined ? params.afterGroupData[chartIndex] : params.afterGroupData;
            reactiveParams.currentCommentInfo = {
              dataIndex: pointValue.dataIndex,
              seriesIndex: pointValue.seriesIndex,
              chartIndex,
              chartTarget: target,
              value: xValue,
              event: pointValue.event,
              timestamp: xValue,
              eqpModuleId: Array.isArray(chartData)
                ? chartData[pointValue.seriesIndex]['paramInfo']['eqpModuleId']
                : chartData['paramInfo']['eqpModuleId']
            };
          } else {
            handleMutilTooltip({ type: 'show', value: pointValue, chartIndex });
          }
          return false;
        }
        //处理comment history显示的mark line，点击markLine 显示tooltip
        if (!reactiveParams.showCommentList[xValue] && reactiveParams.markLineList.length > 0) {
          const chartData =
            chartIndex !== undefined ? params.afterGroupData[chartIndex] : params.afterGroupData;
          const paramInfo = Array.isArray(chartData)
            ? chartData[argus.seriesIndex]['paramInfo']
            : chartData['paramInfo'];
          reactiveParams.markLineList.forEach((markLineObj: CommentHistoryItem) => {
            const timestamp = dayjs(markLineObj.eventDtts).valueOf();
            if (markLineObj.eqpId == paramInfo.eqpId && timestamp === xValue) {
              reactiveParams.showCommentList[xValue] = {
                dataIndex: argus.dataIndex,
                seriesIndex: argus.seriesIndex,
                chartIndex: chartIndex,
                chartTarget: target,
                value: xValue, //x轴值
                ...event,
                eventName: markLineObj.eventName,
                comment: markLineObj.comment,
                eqpModuleId: paramInfo.eqpModuleId,
                moduleAlias: markLineObj.moduleAlias
              };
            }
          });
        }
        handleCommentTooltip({
          ...event,
          xValue,
          chartIndex,
          chartTarget: target,
          dataIndex: argus.dataIndex,
          seriesIndex: argus.seriesIndex
        });
        return false;
      }
    }
    if (event.event.ctrlKey && !params.isScatter) {
      reactiveParams.isShowComment = true;
      const chartData =
        chartIndex !== undefined ? params.afterGroupData[chartIndex] : params.afterGroupData;
      reactiveParams.currentCommentInfo = {
        dataIndex: argus.dataIndex,
        seriesIndex: argus.seriesIndex,
        chartIndex,
        chartTarget: target,
        value: argus.value[0],
        timestamp: getXValue({
          chartIndex,
          seriesIndex: argus.seriesIndex,
          timestampValue: argus.value[0]
        }),
        event: argus.event,
        eqpModuleId: Array.isArray(chartData)
          ? chartData[argus.seriesIndex]['paramInfo']['eqpModuleId']
          : chartData['paramInfo']['eqpModuleId']
      };
    } else {
      handleMutilTooltip({ type: 'show', value: argus, chartIndex });
    }
  });
  //缩放chart，移除mutilTooltip
  target.on('datazoom', () => {
    if (!params.isScatter && ((!params.isSplit && params.currentSpecParam) || params.isSplit)) {
      const options = target.getOption();
      const model = target.getModel();
      let yRange = model.getComponent('yAxis').axis.scale._extent;
      if (
        ((!params.isSplit &&
          params.currentSpecParam &&
          params.rightYList.includes(params.currentSpecParam)) ||
          (params.isSplit && params.isShowSplitSpec)) &&
        options.yAxis.length > 1
      ) {
        yRange = model.getComponent('yAxis', 1).axis.scale._extent;
      }
      const oneScale = target.getHeight()! / (yRange[1] - yRange[0]);
      options.series = (options.series as any[]).map((item) => {
        item.markArea &&
          item.markArea.data &&
          item.markArea.data.forEach((it: any) => {
            if (it[0].opacity === 0.99) {
              it[0].yAxis = yRange[1] - 2 / oneScale;
              it[1].yAxis = yRange[1];
            } else if (it[0].opacity === 0.991) {
              it[0].yAxis = it[1].yAxis - 2 / oneScale;
            }
          });
        return item;
      });
      target.setOption(options);
    }
    handleMutilTooltip({ type: 'hide' });
    //zoom的时候清空
    clearCommentTooltip();
  });
  //点击chart legend，记录legend状态
  target.on('legendselectchanged', function (argus: any) {
    const { selected, name } = argus;
    filterLegend({ selected, clickName: name, chartIndex });
  });
  //绘制完成回调
  // target.on('rendered', function () {
  // 	emits('hideLoading');
  // });
  //刷选模式下，记录选择的标记点
  target.on('brushend', (params: any) => {
    //brushType 为 'rect' range 和 coordRange 的格式为：[[minX, maxX], [minY, maxY]]
    const areas = params.areas;
    const selectedSeries = [] as any;
    areas.forEach((item: any) => {
      item.coordRanges.forEach((coordRange: any) => {
        const min = [coordRange[0][0], coordRange[1][0]];
        const max = [coordRange[0][1], coordRange[1][1]];
        selectedSeries.push({
          min,
          max
        });
      });
    });
    const options = target.getOption();
    const series = options.series as any[];
    const legend = options.legend as any[];
    const legendData = legend[0].selected;
    const findData = [] as any[];
    series.forEach((item: any, index: number) => {
      if (legendData[item.name] !== undefined && !legendData[item.name]) {
        return false;
      }
      item.data.forEach((val: any, idx: number) => {
        const xVal = val[0];
        const yVal = val[1];
        selectedSeries.forEach((value: any) => {
          const min = value.min;
          const max = value.max;
          if (xVal >= min[0] && xVal <= max[0] && yVal >= min[1] && yVal <= max[1]) {
            findData.push({ serieIndex: index, dataIndex: idx });
          }
        });
      });
    });
    getSelectData && getSelectData(findData, chartIndex);
  });
  // y轴点击
  target.getZr().on('dblclick', (event: any) => {
    const clickAxis = checkAxisClick(event, target);
    if (!clickAxis.left && !clickAxis.right) return false;
    const isSingle = typeof chartIndex === 'number';
    yRangeData.splitIndex = isSingle ? chartIndex : -1;
    yRangeData.isShow = true;
    yRangeData.isSingle = isSingle;
    yRangeData.yAxis = [];
    yRangeData.title = isSingle ? getChartTitle(chartIndex) : getChartTitle();
    let yMin = params.yMin;
    let yMax = params.yMax;
    let yRightMin = params.yRightMin;
    let yRightMax = params.yRightMax;
    if (clickAxis.left) {
      if (isSingle && params.splitRangeStore.left[yRangeData.title]) {
        yMin = params.splitRangeStore.left[yRangeData.title].yMin;
        yMax = params.splitRangeStore.left[yRangeData.title].yMax;
      }
      yRangeData.yAxis.push({
        label: t('common.title.left'),
        key: 'left',
        yMin,
        yMax
      });
    }
    if (clickAxis.right) {
      if (isSingle && params.splitRangeStore.right[yRangeData.title]) {
        yRightMin = params.splitRangeStore.right[yRangeData.title].yMin;
        yRightMax = params.splitRangeStore.right[yRangeData.title].yMax;
      }
      yRangeData.yAxis.push({
        label: t('common.title.right'),
        key: 'right',
        yMin: yRightMin,
        yMax: yRightMax
      });
    }
  });
};
const yRangeData = reactive({
  isShow: false,
  yAxis: [] as any[],
  title: '',
  isSingle: false,
  splitIndex: -1
});
function confirmAxis(yAxisList: any) {
  console.log(yAxisList);
  if (!yRangeData.isSingle) {
    yAxisList.forEach(({ key, yMin, yMax }: { key: string; yMin: number; yMax: number }) => {
      const isLeft = key === 'left';
      Object.assign(params, {
        [isLeft ? 'yMin' : 'yRightMin']: yMin,
        [isLeft ? 'yMax' : 'yRightMax']: yMax
      });
    });
  } else {
    yAxisList.forEach(({ key, yMin, yMax }: { key: string; yMin: number; yMax: number }) => {
      const isLeft = key === 'left';
      params.splitRangeStore[isLeft ? 'left' : 'right'][yRangeData.title] = {
        yMin,
        yMax
      };
    });
  }
  reDraw();
}
// 保持legend状态
const keepLegendStatus = (target: echarts.ECharts, chartIndex?: number) => {
  const flag = typeof chartIndex == 'number';
  const myLegendData = flag
    ? (reactiveParams.legendData[chartIndex] as string[])
    : (reactiveParams.legendData as string[]);
  const myFilterLegendData = flag
    ? (reactiveParams.filterLegendData[chartIndex] as string[])
    : (reactiveParams.filterLegendData as string[]);
  if (myFilterLegendData) {
    chartIdLegendList.value[flag ? chartIndex : 0]?.legendList?.forEach(
      (legendItem: LegendItem) => {
        if (myFilterLegendData.includes(legendItem.name)) {
          legendItem.type = 'legendUnSelect';
        }
      }
    );
  }
  myLegendData &&
    myLegendData.forEach((name) => {
      if (myFilterLegendData && myFilterLegendData.includes(name)) {
        target.dispatchAction({
          type: 'legendUnSelect',
          name
        });
      } else {
        target.dispatchAction({
          type: 'legendSelect',
          name
        });
      }
    });
};
//重置变量params为初始值
const resetParams = () => {
  params.yMin = undefined;
  params.yMax = undefined;
  params.yRightMax = undefined;
  params.yRightMin = undefined;
  params.yLog = false;
  params.yRightLog = false;
  params.yScale = 10;
  params.yRightScale = 10;
  params.isBaseLine = false;
  params.isMultiTooltip = false;
  params.isDeleteBlank = false;
  params.hasRightY = false;
  params.rightYList = [];
  params.isSelectArea = false;
  reactiveParams.selectedData = [];
  params.isSplit = props.isSplit;
  params.isShowSplitSpec = false;
  params.splitRangeStore = {
    left: {},
    right: {}
  };
  if (totalData.length == 0) {
    params.afterGroupData = [];
  } else {
    params.afterGroupData = cloneDeep(totalData);
  }
  params.isScatter = false;
  params.layout = 1;
  reactiveParams.filterLegendData = [];
  reactiveParams.legendData = [];
  chartAutoY.value && chartAutoY.value.resetValue();
  chartAutoY.value.autoY.active = false;
  chartShowRightY.value.showRight.value = [];
  chartShowRightY.value.showRight.active = false;
  xGroup.value && xGroup.value.handleReset(props.isSplit, true);
  params.currentSpecParam = null;
  params.currentXAxis = null;
  if (chartShowSpec.value) {
    chartShowSpec.value.specData.value = undefined;
    chartShowSpec.value.specData.active = false;
  }
  if (chartShowXAxis.value) {
    chartShowXAxis.value.specData.value = undefined;
    chartShowXAxis.value.specData.active = false;
  }
};
//销毁所有chart，将总数据置空
const clearChart = () => {
  handleDispose();
  handleMutilDispose();
  totalData = [];
  params.totalCount = 0;
  chartIdLegendList.value = [];
  resetParams();
};
//处理chart的resize
const resize = (chartId?: string) => {
  onMultiToolTip(true);
  clearCommentTooltip();
  if (chartList.length > 0) {
    chartList.forEach((target: echarts.ECharts, chartIndex: number) => {
      if (chartId) {
        if (target['_dom']?.id == chartId) {
          target && target.resize();
        }
      } else {
        target && target.resize();
      }
    });
  } else if (myChart) {
    myChart.resize();
  }
};
//用于处理overview&copy
const setChartIdList = () => {
  if (params.showChart) {
    params.chartIdList = [chartContentId];
  } else {
    params.chartIdList = [];
    // params.afterGroupData.forEach((item: any, index: number) => {
    //     params.chartIdList.push(`${singleChartContentIdPrefix}${index}`);
    // });
    chartIdLegendList.value.forEach((item: SingleChartItem, index: number) => {
      params.chartIdList.push(`${singleChartContentIdPrefix}${item.chartId}`);
    });
  }
};
let minTime: null | number = null,
  maxTime: null | number = null;
const getMinMaxTime = () => {
  minTime = null;
  maxTime = null;
  params.afterGroupData.forEach((item: any) => {
    if (item['timestamp'] && item['timestamp'].length > 0) {
      const [minTimestamp, maxTimestamp] = getArrayMinMax(item['timestamp']);
      if (!minTime || minTime > minTimestamp) {
        minTime = minTimestamp;
      }
      if (!maxTime || maxTime < maxTimestamp) {
        maxTime = maxTimestamp;
      }
    }
  });
  reactiveParams.minTime = minTime;
  reactiveParams.maxTime = maxTime;
};
const getXAxisData = () => {
  if (params.afterGroupData.length > 0) {
    const keys = Object.keys(params.afterGroupData[0]);
    const excludes = [
      'lcl',
      'lsl',
      'ucl',
      'usl',
      'target',
      'paramInfo',
      'specRawId',
      'sumInfo',
      'sumValue',
      'SPLIT_SOURCE_YN'
    ];
    const map = {} as Record<string, string>;
    keys.forEach((key: string) => {
      if (!excludes.includes(key)) {
        if (key.includes('^') && key.includes('RSD')) {
          const k = key.split('^')[1];
          map[k] = key;
        } else {
          map[key.toLocaleUpperCase()] = key;
        }
      }
    });
    params.xAxisMap = map;
  }
};
//获取总数据，设置params属性值
const getData = ({
  data,
  rightParamList,
  sumModuleNameList,
  specParams,
  count,
  isEnd,
  currentSpec,
  eqpIdList
}: {
  data: any[];
  rightParamList: string[];
  sumModuleNameList: string[];
  specParams: string[];
  count: number;
  isEnd: boolean;
  currentSpec?: null | string;
  eqpIdList: string[];
}) => {
  console.time('handle data');
  clearChart();
  if (data.length == 0) return false;
  params.totalCount = count;
  totalData = data;
  params.afterGroupData = cloneDeep(data);
  getXAxisData();
  getMinMaxTime();
  params.showChart = props.isSplit;
  params.isSplit = props.isSplit;
  if (params.isSplit) {
    params.showChart = false;
  } else {
    params.showChart = true;
  }
  setChartIdList();
  params.chartType = params.isSplit ? 2 : 1;
  params.rightParamList = cloneDeep(rightParamList);
  params.sumModuleNameList = sumModuleNameList;
  params.specParams = intersection(specParams, rightParamList);
  reactiveParams.eqpIdList = eqpIdList;
  params.currentXAxis = null;
  if (currentSpec) {
    params.currentSpecParam = currentSpec;
    if (chartShowSpec.value) {
      chartShowSpec.value.specData.value = currentSpec;
      chartShowSpec.value.specData.active = true;
    }
  }

  xGroup.value &&
    xGroup.value.getData({
      data: totalData,
      paramsAlias: params.sumModuleNameList,
      isSplit: params.isSplit,
      chartType: params.chartType
    });
  if (params.isSplit) {
    params.showChart = false;
  } else {
    params.showChart = true;
    setFilterLegend(totalData);
  }
  console.timeEnd('handle data');
  reactiveParams.markLineList = [];
  reactiveParams.showCommentList = {};
  reDraw();
  if (isEnd) {
    emits('hideLoading');
    if (params.totalCount == 0) {
      showInfo('common.tip.noData');
      handleDispose();
      handleMutilDispose();
      params.showChart = false;
    }
  }
};
const getAuthority = (buttonId: string) => {
  if (Object.keys(props.buttonAuthority).length == 0) {
    return false;
  }
  return getSingleButtonAuthority({ ...(props.buttonAuthority as ButtonAuthority) })(buttonId);
};
defineExpose({
  resize,
  getData,
  clearChart
});
onMounted(() => {
  window.onresize = () => {
    resize();
  };
});
onBeforeUnmount(() => {
  myChart && myChart.clear();
});
const getContentHeight = () => {
  const dom = document.getElementById('fdc_summary_chart-content-wrapper');
  if (dom) {
    return (dom.clientHeight - 14) / 2;
  } else {
    return 380;
  }
};
// 获取chart的title
function getChartTitle(index?: number) {
  let tit = '';
  const list: any = index === undefined ? params.afterGroupData : params.afterGroupData[index];
  if (Array.isArray(list)) {
    tit = uniq(
      list.map((item) => {
        return item.paramInfo.sumParamAlias;
      })
    ).join('^');
  } else {
    tit = list.paramInfo.sumParamAlias;
  }

  return tit;
}
const copyStatus = ref(false);
const clickBtnCode = ref('');
const handleCopy = (type: string) => {
  copyStatus.value = type == 'start';
  if (type == 'end') {
    clickBtnCode.value = '';
  }
};
const copyChartTitle = ref<string[]>([]);
const handleCopyConfirm = async () => {
  if (chartCommonLegend.value) {
    const flag = chartCommonLegend.value.validateChartTitleHasTooltip('fdc_summary_chart');
    if (flag) {
      copyChartTitle.value = [getChartTitle()];
      const type = await VXETable.modal.confirm('summary.tips.chartTitleTip', undefined, {
        'confirm-button-text': t('common.btn.yes'),
        'cancel-button-text': t('common.btn.no')
      });
      if (type == 'close') {
        return 'close';
      } else if (type == 'cancel') {
        clickBtnCode.value = 'copy';
        return 'copy';
      }
    } else {
      copyChartTitle.value = [];
      clickBtnCode.value = 'copy';
      return 'copy';
    }
  }
};
const xAxisList = computed(() => {
  const arr = Object.keys(params.xAxisMap);
  return orderBy(arr, ['0'], 'asc');
});
const handleFilterLegendStatus = ({
  legendName,
  chartIndex
}: {
  legendName: string;
  chartIndex?: number;
}) => {
  const selected = {} as Record<string, boolean>;
  chartIdLegendList.value[chartIndex !== undefined ? chartIndex : 0].legendList.forEach(
    (item: LegendItem) => {
      selected[item.name] = item.type === 'legendSelect';
    }
  );
  filterLegend({ selected, clickName: legendName, chartIndex });
};
//处理点击legend
const handleClickLegend = (legendItem: LegendItem, chartId: string) => {
  onMultiToolTip(true);
  clearCommentTooltip();
  handleRemoveBrush();
  if (chartList.length > 0) {
    chartList.forEach((target: echarts.ECharts, index: number) => {
      if (target['_dom']?.id == chartId) {
        target &&
          target.dispatchAction({
            type: legendItem.type,
            name: legendItem.name
          });
        handleFilterLegendStatus({ legendName: legendItem.name, chartIndex: index });
      }
    });
  } else if (myChart) {
    myChart.dispatchAction({
      type: legendItem.type,
      name: legendItem.name
    });
    handleFilterLegendStatus({ legendName: legendItem.name });
  }
};
const getChartIdList = computed(() => {
  const list = [
    clickBtnCode.value == 'copy'
      ? chartContentId
      : `${singleChartContentIdPrefix}fdc_summary_chart-noTitle`
  ];
  return list;
});
</script>
<template>
  <div class="fdc_summary_chart_wrapper">
    <div v-show="params.totalCount > 0" class="fdc_summary_toolbox">
      <EesShowSpec
        v-if="!params.isScatter"
        ref="chartShowXAxis"
        :param-list="xAxisList"
        btn-icon="#icon-btn-x-range-setting"
        :btn-name="$t('summary.btn.changeXAxis')"
        @on-show-spec="(val: any) => onShowXAxis({ ...val, isLoading: true })"
      />
      <EesShowSpec
        v-if="!params.isScatter && !params.isSplit"
        ref="chartShowSpec"
        :param-list="params.specParams"
        @on-show-spec="(val: any) => onShowSpec({ ...val, isLoading: true })"
      />
      <ees-button-tip
        v-show="!params.isScatter && params.isSplit"
        icon="#icon-btn-spec-show"
        :text="$t('eesCharts.commonBtn.spec')"
        :is-active="params.isShowSplitSpec"
        @on-click="handleShowSplitSpec"
      />
      <EesShowRightY
        v-show="!params.isScatter"
        ref="chartShowRightY"
        :param-list="params.rightParamList"
        :text="$t('eesCharts.commonBtn.rightY')"
        @on-show-right-y="onShowRightY"
      />
      <EesAutoY
        v-show="!params.isScatter"
        ref="chartAutoY"
        version="v2"
        @apply-auto-y="(applyAutoY as any)"
      />
      <ees-button-tip
        v-show="!params.isScatter"
        icon="#icon-btn-base-line-show"
        :text="$t('eesCharts.commonBtn.baseLine')"
        :is-active="params.isBaseLine"
        @on-click="showBaseLine"
      />
      <ees-button-tip
        v-show="!params.isScatter"
        icon="#icon-btn-blank-delete"
        :text="$t('eesCharts.commonBtn.deleteBlank')"
        :is-active="params.isDeleteBlank"
        @on-click="onDeleteBlank"
      />
      <ees-button-tip
        icon="#icon-btn-multiple-tooltip"
        :text="$t('eesCharts.commonBtn.multipleTooltip')"
        :is-active="params.isMultiTooltip"
        @on-click="() => onMultiToolTip()"
      />
      <chart-group
        ref="xGroup"
        :is-split="params.isSplit"
        :params-alias="params.sumModuleNameList"
        @do-group="doGrouping"
      ></chart-group>
      <EesLegendFilter
        v-show="params.showChart"
        ref="xFilter"
        @toggle-all-legend="toggleAllLegend"
        @toggle-one-legend="toggleOneLegend"
      />
      <ees-button-tip
        v-if="getAuthority(buttonIds.selectPoint)"
        icon="#icon-btn-select-drilldown-analysis"
        :text="$t('eesCharts.commonBtn.select')"
        :is-active="params.isSelectArea"
        @on-click="doSelectArea"
      />
      <ees-button-tip
        v-if="getAuthority(buttonIds.selectPoint)"
        v-show="reactiveParams.selectedData.length > 0"
        icon="#icon-btn-trace-detail-analysis"
        :text="$t('eesCharts.commonBtn.traceData')"
        @on-click="() => toTraceData()"
      />
      <ees-button-tip
        v-if="getAuthority(buttonIds.selectPoint)"
        v-show="reactiveParams.selectedData.length > 0"
        icon="#icon-btn-link-to-trace-detail-analysis"
        :text="$t('eesCharts.commonBtn.linkTraceData')"
        @on-click="toTraceData('link')"
      />
      <EesCommentHistory
        v-show="!params.isScatter"
        :eqp-id-list="reactiveParams.eqpIdList"
        :min-time="(reactiveParams.minTime as any)"
        :max-time="(reactiveParams.maxTime as any)"
        @do-draw-chart="handleCommentDrawChart"
      ></EesCommentHistory>
      <EesOverviewChart
        :chart-id-list="params.chartIdList"
        :export-name="'SummaryDataAnalysis_Chart'"
      ></EesOverviewChart>
      <template v-if="isShowCopyConfirm">
        <EesOverviewChart
          v-if="params.showChart"
          :chart-id-list="getChartIdList"
          :chart-name-list="copyChartTitle"
          :export-name="'SummaryDataAnalysis_Chart'"
          type="copy"
          :confirm-func="handleCopyConfirm"
          @copy-start="handleCopy('start')"
          @success-copy="handleCopy('end')"
        ></EesOverviewChart>
        <EesOverviewChart
          v-else
          :chart-id-list="[chartContentId]"
          :chart-name-list="[]"
          :export-name="'SummaryDataAnalysis_Chart'"
          type="copy"
        ></EesOverviewChart
      ></template>
      <template v-else>
        <EesCopyChart :container="chartContentId" :chart-name="'SummaryDataAnalysis_Chart'"
      /></template>
      <ees-button-tip
        icon="#icon-btn-export"
        :text="$t('eesCharts.commonBtn.exportData')"
        @on-click="onExport"
      />
      <ees-button-tip
        icon="#icon-btn-initialize"
        :text="$t('eesCharts.commonBtn.initialize')"
        :marginRight="0"
        @on-click="onInitialize"
      />
    </div>
    <div id="fdc_summary_chart-content-wrapper" class="fdc_summary_chart-content">
      <EesCommonLegend
        ref="chartCommonLegend"
        :chart-content-id="chartContentId"
        :single-chart-content-id-prefix="singleChartContentIdPrefix"
        :chart-list="chartIdLegendList"
        :layout="params.layout"
        :chart-height="getContentHeight()"
        @single-resized="
          (event: any, chartId: string) => {
            resize(chartId);
          }
        "
        @resized="
          () => {
            resize();
          }
        "
        @click-legend="handleClickLegend"
      >
        <template v-for="(item, index) in chartIdLegendList" :key="item.chartId" #[item.chartId]>
          <!--单个chart-->
          <div
            v-if="params.showChart && params.afterGroupData.length > 0"
            id="fdc_summary_chart"
            ref="xChart"
            style="width: 100%; height: 100%"
          ></div>
          <!--多Trend Chart-->
          <div
            v-if="!params.showChart && params.afterGroupData.length > 0 && !params.isScatter"
            :id="`fdc_summary_chart${index}`"
            style="width: 100%; height: 100%"
          ></div>
          <div
            v-if="!params.showChart && params.afterGroupData.length > 0 && params.isScatter"
            class="fdc_summary_scatter-signal"
          >
            <div :id="`fdc_summary_chart${index}`" style="width: 100%; height: 100%"></div>
            <div class="fdc_summary_scatter-signal-x">
              <EesButtonTip
                :text="
                  Array.isArray(params.afterGroupData[index])
                    ? params.afterGroupData[index][0].paramInfo.xName
                    : params.afterGroupData[index].paramInfo.xName
                "
                type="text"
              >
              </EesButtonTip>
            </div>
            <div class="fdc_summary_scatter-signal-y">
              <EesButtonTip
                :text="
                  Array.isArray(params.afterGroupData[index])
                    ? params.afterGroupData[index][0].paramInfo.yName
                    : params.afterGroupData[index].paramInfo.yName
                "
                type="text"
              >
              </EesButtonTip>
            </div>
          </div>
        </template>
      </EesCommonLegend>
    </div>
    <UserComment
      v-if="reactiveParams.isShowComment"
      v-model:visible="reactiveParams.isShowComment"
      :current-info="reactiveParams.currentCommentInfo as any"
      @get-comment="handleSaveComment"
    ></UserComment>
    <vxe-modal
      v-model="params.isShowTraceData"
      class-name="fdc-summary-tract-detail-pop"
      :mask="false"
      :title="$t('summary.traceDetailAnalysis')"
      show-zoom
      show-close
      resize
      :z-index="999"
      width="940"
      height="500"
      transfer
      destroy-on-close
      type="confirm"
      :lock-view="false"
      @close="closeTraceDetail"
      @zoom="handleZoomTraceDetail"
    >
      <template #default>
        <analysis-chart
          ref="xTraceDetail"
          style="min-width: 850px; min-height: 200px"
          :param-alias="params.paramAliasList"
          :spec-param-list="params.specParamList"
          :enter-chart-data="{
            x: [],
            y: [],
            layout: 1,
            kind: 'trend',
            isSplitParam: false
          }"
        />
      </template>
    </vxe-modal>
    <EesAutoMultiY
      v-model:is-show="yRangeData.isShow"
      :y-axis="yRangeData.yAxis"
      :title="yRangeData.title"
      @confirm-y-axis="confirmAxis"
    ></EesAutoMultiY>
  </div>
</template>
<style lang="less" scoped>
@import url('@/assets/style/variable.less');
:deep(#summary-single-chart-content-fdc_summary_chart-noTitle) {
  position: relative;
}
.fdc_summary_chart_wrapper {
  width: 100%;
  height: 100%;
  overflow: hidden;
  box-sizing: border-box;

  .fdc_summary_toolbox {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    width: 100%;
    background: @bg-block-color;
    padding: 14px;
  }
  .fdc_summary_chart-content {
    width: 100%;
    height: calc(100% - 60px);
    overflow: hidden;
    padding-bottom: @padding-basis + 6;
  }
  .fdc_summary_chart-title {
    width: 50%;
    position: absolute;
    top: -4px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1;
    &.multi {
      top: -4px;
    }
  }

  #fdc_summary_chart-single {
    width: 100%;
    height: 100%;
    position: relative;
    overflow-x: hidden;
  }
  #fdc_summary_chart {
    width: 100%;
    height: 100%;
  }

  #fdc_summary_chart-single {
    overflow-y: hidden;
  }

  .fdc_summary_scatter-signal {
    height: 100%;
    width: 100%;
    position: relative;
    .fdc_summary_scatter-signal-y {
      position: absolute;
      top: 10px;
      left: 0;
      width: 100%;
      :deep(.text-tooltip) {
        text-align: left;
      }
    }
    &-x {
      width: 100%;
      position: absolute;
      bottom: 20px;
      left: 50%;
      transform: translateX(-50%);
    }
  }
}
:deep(.xchart-marker__data) {
  align-items: flex-start !important;
  justify-content: space-between !important;
  white-space: nowrap;
  .xchart-marker__data__left {
    height: 20px;
  }
}
</style>
