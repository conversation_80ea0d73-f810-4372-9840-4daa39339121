<script lang="ts" setup>
import { ref, reactive, watch, onBeforeMount, onUnmounted, nextTick } from 'vue';
import { xConfig, dConfig } from './config';
import {
  t,
  type VxeGridDefines,
  VXETable,
  type VxeTablePropTypes,
  VxeTableDefines
} from '@futurefab/vxe-table';
import { successInfo, showConfirm, showWarning, EesSidebar } from '@futurefab/ui-sdk-ees-basic';
import {
  saveStateViewerReset,
  saveUserStateViewerReset,
  getStateViewerReset,
  getUserStateViewerReset
} from '@futurefab/ui-sdk-api';
import { stateViewer as buttonIds } from '@/log-config/index';
import { filterMethod } from '@/utils/tools';

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  selected: {
    type: Object,
    default: {}
  },
  selectedState: {
    type: Object,
    default: {}
  },
  selectedCols: {
    type: Object,
    default: {}
  },
  type: String
});
const xFormRef = ref();
const xGrid = ref();
const xTable = reactive({
  options: xConfig,
  data: [] as any[],
  rules: {} as VxeTablePropTypes.EditRules,
  optionMap: new Map(),
  apiRemove: [] as any
});
const dGrid = ref();
const dTable = reactive({
  options: dConfig,
  data: [] as any[],
  rules: {} as VxeTablePropTypes.EditRules,
  optionMap: new Map(),
  apiRemove: [] as any,
  loading: false
});
const emits = defineEmits(['hideSidebar', 'refresh']);
const onSave = async () => {
  console.log(inputData, dTable.data);
  // xFormRef.value &&
  //     xFormRef.value
  //         .validate()
  //         .then(async () => {
  if (!dTable.data.length) {
    showWarning(t('common.tip.isRequired', { name: t('stateViewer.field.stateItem') }));
    return;
  }
  VXETable.tableFun.useValid({
    data: dTable.data,
    xGird: dGrid,
    callback: async () => {
      let res;
      if (props.type === 'user') {
        const bodyParams = dTable.data;
        console.log('onSave', bodyParams);
        res = await saveUserStateViewerReset({
          headerParams: {
            log: 'Y',
            page: buttonIds.pageId,
            action: buttonIds.user.reset
          },
          bodyParams
        });
      } else {
        const { updateRecords } = dGrid.value?.getRecordset();
        const bodyParams = {
          modelName: inputData.modelName,
          modelRawId: inputData.modelRawId,
          stateName: inputData.stateName,
          stateTypeCd: inputData.stateTypeCd,
          stateKey: inputData.stateKey,
          resetType: inputData.resetType,
          stateTrxRawId: inputData.stateTrxRawId,
          actionMode: inputData.actionMode,
          stateDetailOuts: updateRecords.map((v: any) => {
            return {
              itemName: v.itemName,
              itemValue: v.itemValue
            };
          })
        };
        console.log('onSave', bodyParams);
        res = await saveStateViewerReset({
          headerParams: {
            log: 'Y',
            page: buttonIds.pageId,
            action: buttonIds.system.reset
          },
          bodyParams
        });
      }
      if (res.status !== 'SUCCESS') return;
      successInfo('common.tip.saveSuccess');
      emits('hideSidebar', true);
    }
  });
  // })
  // .catch((err: any) => {
  //     console.log(err);
  // });
};
const handleClick = async (code: string) => {
  code === 'save' ? onSave() : emits('hideSidebar');
};
/* const onAddRow = async () => {
    const $table: any = xGrid.value;
    const record = {};
    const { row } = await $table.insertAt(record, -1);
    xTable.data.push(row);
    // await $table.setActiveRow(row);
    $table.loadData(xTable.data);
};
const gridEvents = {
    toolbarToolClick: ({ code }: VxeGridDefines.ToolbarToolClickEventParams) => {
        console.log(code);
        switch (code) {
            case 'add':
                onAddRow();
                break;
            // case 'deleteData':
            //     onDelete();
            //     break;
        }
    },
}; */
const inputData = reactive<any>({
  rawId: null,
  stateName: '',
  stateType: '',
  resetType: 'STATE KEY',
  stateKey: []
});
const getDetail = async () => {
  dTable.loading = true;
  const res = await (
    props.type === 'user'
      ? getUserStateViewerReset({
          headerParams: {
            log: 'Y',
            page: buttonIds.pageId,
            action: 'user-reset-detail'
          },
          bodyParams: {
            stateName: props.selectedState?.title,
            //   stateTrxRawId: props.selected?.stateTrxRawId,
            stateTrxRawId: props.selected?.STATE_TRX_RAWID
          }
        })
      : getStateViewerReset({
          headerParams: {
            log: 'Y',
            page: buttonIds.pageId,
            action: 'system-reset-detail'
          },
          bodyParams: {
            // activityClassName: props.selectedState?.stateType,
            // activityFunctionName: props.selectedState?.function,
            modelName: props.selectedState?.modelName,
            stateName: props.selectedState?.stateName,
            stateTypeCd: props.selectedState?.stateTypeCd,
            stateType: props.selectedState?.stateType
          }
        })
  ).finally(() => {
    dTable.loading = false;
  });
  if (res.status !== 'SUCCESS') return;
  const resData = res?.data;
  res.data.forEach((ff: any) => {
    ff.actualItem = ff.displayName || ff.itemName;
  });
  dTable.data = resData;
};
const refill = () => {
  console.log('selectedState', props.selectedState);
  if (props.type === 'user') {
    inputData.stateName = props.selectedState?.title;
    inputData.stateType = 'SHARE';
    getDetail();
  } else {
    inputData.modelName = props.selectedState?.modelName;
    inputData.modelRawId = props.selectedState?.modelRawId;
    inputData.stateName = props.selectedState?.stateName;
    inputData.stateType = props.selectedState?.stateType;
    inputData.stateTypeCd = props.selectedState?.stateTypeCd;
    inputData.stateTrxRawId = props.selected?.STATE_TRX_RAWID;
    inputData.actionMode = props.selected?.ACTION_MODE_CD;
    // const stateValueMap = Object.entries(props.selected?.stateValueMap || {});
    // if (stateValueMap.length) {
    // dTable.data = stateValueMap.map((item: any) => {
    //     return {
    //         itemName: item[0],
    //         itemValue: item[1],
    //     };
    // });
    if (props.selectedState?.isModel) {
      // if (props.selectedState?.isModel && !Object.keys(props.selected).length) {
      getDetail();
    } else {
      dTable.data = props.selectedCols['STATE VALUE'].map((e: any) => {
        console.log(e, props.selected[e]);
        return {
          actualItem: e,
          itemValue: props.selected[e]
        };
      });
    }
  }
  inputData.isModel = props.selectedState?.isModel;
  if (props.selectedState?.stateType !== 'SHARE' && props.selectedState?.isModel)
    inputData.resetType = 'MODEL';
  console.log('refill-inputData', inputData);
  console.log('selected', props.selected);
  inputData.stateKey = props.selected?.STATE_KEY_VALUE;
  if (!props.selectedState.isModel)
    // xTable.data = Object.entries(props.selected?.stateKeyMap || {}).map((item: any) => {
    //     return {
    //         key: item[0],
    //         value: item[1],
    //     };
    // });
    xTable.data = props.selectedCols['STATE KEY'].map((e: any, index: number) => {
      console.log(e, props.selected[e]);
      if (props.type === 'user') {
        return {
          key: props.selectedCols['STATE KEY ACTUAL'][index],
          value: props.selected[e]
        };
      } else {
        return {
          key: e,
          value: props.selected[e]
        };
      }
    });
};
onBeforeMount(() => {
  refill();
});
const sidebar = reactive<any>({
  buttonList: [
    { name: 'common.btn.save', code: 'save', status: 'primary' },
    { name: 'common.btn.cancel', code: 'cancel' }
  ]
});
</script>

<template>
  <ees-sidebar v-if="show" :button-list="sidebar.buttonList" @clickbutton="handleClick">
    <template #sidebar_content>
      <div class="detail-reset">
        <a-form
          ref="xFormRef"
          class="detail-reset-form"
          auto-complete="off"
          :colon="false"
          label-align="left"
        >
          <a-form-item :label="t('stateViewer.label.stateName')">
            <span class="span-text">{{ inputData.stateName }}</span>
          </a-form-item>
          <a-form-item :label="t('stateViewer.label.stateType')">
            <span class="span-text">{{ inputData.stateType }}</span>
          </a-form-item>
          <a-form-item class="none-item" :label="t('stateViewer.label.resetType')">
            <a-radio-group
              v-model:value="inputData.resetType"
              :disabled="inputData.stateType === 'SHARE' || selectedState.isModel"
            >
              <a-radio value="MODEL">Model</a-radio>
              <a-radio value="STATE KEY">State Key</a-radio>
            </a-radio-group>
          </a-form-item>
          <!-- <a-form-item :label="t('stateViewer.label.stateKey')"> </a-form-item> -->
          <!-- <a-form-item :label="t('stateViewer.label.stateItems')"> </a-form-item> -->
        </a-form>
        <div class="tables-wrapper">
          <div v-if="!selectedState.isModel" class="detail-reset-keys">
            <!-- v-on="gridEvents" -->
            <vxe-grid
              ref="xGrid"
              v-bind="xTable.options"
              :data="xTable.data"
              class="vxe-toolbar-top-left-radius-xs vxe-toolbar-top-right-radius-xs vxe-table-bottom-left-radius-xs vxe-table-bottom-right-radius-xs"
            >
            </vxe-grid>
          </div>
          <div class="detail-reset-items" :class="{ single: selectedState.isModel }">
            <vxe-grid
              ref="dGrid"
              v-bind="dTable.options"
              :data="dTable.data"
              :edit-rules="dTable.rules"
              :loading="dTable.loading"
              class="vxe-toolbar-top-left-radius-xs vxe-toolbar-top-right-radius-xs vxe-table-bottom-left-radius-xs vxe-table-bottom-right-radius-xs"
            >
            </vxe-grid>
          </div>
        </div>
      </div>
    </template>
  </ees-sidebar>
</template>

<style lang="less" scoped>
@import url('@/assets/style/variable.less');
@ns: detail-reset;

.@{ns} {
  height: 100%;
  display: flex;
  flex-direction: column;

  &-form {
    border: 1px solid @border-color;
    padding: @padding-sm + 2px;
    background: @bg-group-color;
    border-radius: @border-radius-xs;
    position: relative;
    margin-bottom: @margin-xs;
  }

  :deep(.ant-form-item) {
    margin-bottom: @margin-sm - 2px;
    &.none-item {
      margin-bottom: 0;
    }
    .ant-form-item-label {
      label {
        width: 82px;
      }
    }
    .ant-row {
      align-items: baseline;
    }
  }

  .span-text {
    color: @text-subtitle-color;
  }

  :deep(.ant-radio-disabled + span) {
    color: @text-subtitle-color;
  }
  .tables-wrapper {
    height: calc(100% - 154px);
  }
  &-keys {
    // flex-grow: 1;
    margin-bottom: @margin-xs;
    height: 25%;
  }

  &-items {
    // flex-grow: 3;
    height: calc(75% - @margin-xs);
    &.single {
      height: 100%;
    }
  }
}
</style>
