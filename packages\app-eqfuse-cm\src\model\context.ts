export class Context {
    constructor(
        public isSelected: boolean,
        public lot: string,
        public wafer: string,
        public datasetId: number,
        public tool?: string,
        public chamber?: string,
        public recipe?: string,
        public startTime?: string,
        public endTime?: string | null,
        public descriptor_1?: string | null,
        public descriptor_2?: string | null,
        public isVisible?: boolean | null,
    ) {}

    public getProperyValue(propName: string): string {
        switch (propName) {
            case 'datasetId':
                return this.datasetId.toString();
            case 'lot':
                return this.lot;
            case 'wafer':
                return this.wafer;
            case 'tool':
                return this.tool as string;
            case 'chamber':
                return this.chamber as string;
            case 'recipe':
                return this.recipe as string;
            case 'startTime':
                return this.startTime as string;
            case 'endTime':
                return this.endTime as string;
            case 'descriptor_1':
                return this.descriptor_1 as string;
            case 'descriptor_2':
                return this.descriptor_2 as string;
            default:
                return toString();
        }
    }

    public toString(): string {
        return (
            (this.datasetId ? this.datasetId.toString() : '') +
            ' - ' +
            this.lot.toString() +
            ' ; ' +
            this.wafer.toString()
        );
    }

    public getContext(): string {
        return this.lot.toString() + ',' + this.wafer.toString();
    }
}
