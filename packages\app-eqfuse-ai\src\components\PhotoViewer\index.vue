<template>
  <div class="my-viewer">
    <div class="thumbnail-list">
      <img
        v-for="(src, index) in images"
        :key="index"
        :src="src"
        @click="openViewer(index)"
        class="thumbnail"
      />
    </div>

    <div v-if="isOpen" class="viewer-overlay" @click.self="closeViewer">
      <div class="close-button" @click="closeViewer"> <CloseOutlined /> </div>

      <div class="viewer-content">
        <img
          :src="currentImage"
          class="zoomed-image"
          :style="{ transform: `scale(${scale})` }"
          @wheel="handleWheel"
        />
      </div>

      <div class="custom-toolbar">
        <div @click="prevImage" class="pre-toolbar-button">上一张</div>
        <div @click="nextImage" class="next-toolbar-button">下一张</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, onUnmounted, computed } from 'vue';
  import { CloseOutlined } from '@ant-design/icons-vue';

  const props = withDefaults(
    defineProps<{
      images: any;
    }>(),
    {
      images: ''
    }
  );

  const images = ref<string[]>(JSON.parse(props.images));

  const isOpen = ref(false);
  const currentIndex = ref(0);
  const scale = ref(1);

  // 当前显示的图片
  const currentImage = computed(() => images.value[currentIndex.value]);

  // 打开 Viewer
  const openViewer = (index: number) => {
    currentIndex.value = index;
    isOpen.value = true;
    scale.value = 1;
    document.body.style.overflow = 'hidden'; // 禁止背景滚动
  };

  // 关闭 Viewer
  const closeViewer = () => {
    isOpen.value = false;
    document.body.style.overflow = '';
  };

  // 上一张
  const prevImage = () => {
    if (currentIndex.value > 0) {
      currentIndex.value--;
    } else {
      currentIndex.value = images.value.length - 1; // 循环到末尾
    }
    scale.value = 1; // 重置缩放
  };

  // 下一张
  const nextImage = () => {
    if (currentIndex.value < images.value.length - 1) {
      currentIndex.value++;
    } else {
      currentIndex.value = 0; // 循环到开头
    }
    scale.value = 1; // 重置缩放
  };

  // 鼠标滚轮缩放
  const handleWheel = (e: WheelEvent) => {
    e.preventDefault();
    if (e.deltaY < 0) {
      scale.value = Math.min(scale.value + 0.1, 3); // 放大
    } else {
      scale.value = Math.max(scale.value - 0.1, 0.5); // 缩小
    }
  };

  // 键盘事件监听
  const handleKeyDown = (e: KeyboardEvent) => {
    if (!isOpen.value) return;
    switch (e.key) {
      case 'ArrowLeft':
        prevImage();
        break;
      case 'ArrowRight':
        nextImage();
        break;
      case 'Escape':
        closeViewer();
        break;
    }
  };

  onMounted(() => {
    document.addEventListener('keydown', handleKeyDown);
  });

  onUnmounted(() => {
    document.removeEventListener('keydown', handleKeyDown);
  });
</script>

<style scoped>
  .my-viewer {
    font-family: Arial, sans-serif;
  }

  .thumbnail-list {
    display: flex;
  }

  .thumbnail {
    object-fit: cover;
    cursor: pointer;
    transition: transform 0.2s;
    margin-right: 10px;
    max-width: 32px;
    max-height: 32px;
  }

  .thumbnail:hover {
    transform: scale(1.05);
  }

  .viewer-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(135, 142, 152, 0.46);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }

  .custom-toolbar {
    margin-top: 20px;
    display: flex;
    cursor: pointer;
    font-family: PingFang SC;
    font-size: 14px;
    .pre-toolbar-button {
      width: 120px;
      height: 40px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding: 10px;
      background: #f2f7fa;
      border-radius: 100px 0px 0px 100px;
      &:hover {
        background: #fff;
      }
    }

    .next-toolbar-button {
      width: 120px;
      height: 40px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding: 10px;
      background: #f2f7fa;
      border-radius: 0 100px 100px 0;
      &:hover {
        background: #fff;
      }
    }
  }

  .close-button {
    width: 40px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 10000px;
    background: #f2f7fa;
    position: absolute;
    right: 20px;
    top: 20px;
    cursor: pointer;
  }

  .viewer-content {
    display: flex;
    justify-content: center;
    align-items: center;
    max-width: 90%;
    max-height: 80vh;
  }

  .zoomed-image {
    max-width: 100%;
    max-height: 80vh;
    width: 544px;
    height: 408px;
    object-fit: contain;
    transition: transform 0.3s;
  }
</style>
