<script setup lang="ts">
import { nextTick, reactive, ref, watchEffect } from 'vue';
import type { TrimForm, TrimRuleOption, TrimType, TypeTrimOption } from '../../interface';
import { getLabels, TrimOptions } from './config';

const props = withDefaults(
    defineProps<{
        data: TrimRuleOption;
    }>(),
    {},
);
const emits = defineEmits(['apply']);
const formRef = ref();
const trimType = ref<TrimType>('COUNT');
const trimOption: TypeTrimOption = TrimOptions;
const formState = reactive<TrimForm>({
    startCount: null,
    startTime: null,
    startPercent: null,
    endCount: null,
    endTime: null,
    endPercent: null,
});
const formLabelArr = ref(getLabels(trimType.value));

const showModel = ref(false);
const handleHide = () => {
    showModel.value = false;
};
const changeType = () => {
    formLabelArr.value = getLabels(trimType.value);
    formRef.value?.clearValidate();
};

const apply = () => {
    formRef.value.validate().then(() => {
        emits('apply', {
            trimBy: trimType.value,
            start: {
                count: formState.startCount,
                time: formState.startTime,
                percent: formState.startPercent,
            },
            end: {
                count: formState.endCount,
                time: formState.endTime,
                percent: formState.endPercent,
            },
        });
        showModel.value = false;
    });
};

defineExpose({
    show: () => {
        showModel.value = true;
        nextTick(() => {
            formState.startCount = props.data.start.count;
            formState.startTime = props.data.start.time;
            formState.startPercent = props.data.start.percent;
            formState.endCount = props.data.end.count;
            formState.endTime = props.data.end.time;
            formState.endPercent = props.data.end.percent;
            trimType.value = props.data.trimBy || 'COUNT';
            formLabelArr.value = getLabels(trimType.value);
        });
    },
});
</script>

<template>
    <Teleport to="body">
        <vxe-modal
            v-model="showModel"
            :title="$t('cm.title.trimOption')"
            :mask-closable="false"
            :width="'975px'"
            :height="'272px'"
            show-footer
            @hide="handleHide"
            @close="handleHide"
        >
            <div class="cm-trim-modal-content">
                <div class="cm-trim-modal-content-type">
                    <span>{{ $t('cm.label.trimType') }}:</span>
                    <a-radio-group
                        v-model:value="trimType"
                        :options="trimOption"
                        @change="changeType"
                    ></a-radio-group>
                </div>
                <div class="ant-advanced-search-form">
                    <a-form
                        ref="formRef"
                        name="advanced_search"
                        :label-col="{ style: { width: '133px' } }"
                        :model="formState"
                        :layout="'horizontal'"
                    >
                        <a-row :gutter="24">
                            <template v-for="item in formLabelArr" :key="item.key">
                                <a-col :span="8">
                                    <a-form-item
                                        :name="item.key"
                                        :label="item.label"
                                        :rules="[{ required: true }]"
                                    >
                                        <a-input-number
                                            v-model:value="formState[item.key]"
                                            :title="formState[item.key]"
                                            :max="item.max"
                                            :min="0"
                                            style="width: 160px"
                                            :addon-after="item.unit"
                                            :precision="item.precision"
                                        ></a-input-number>
                                    </a-form-item>
                                </a-col>
                            </template>
                        </a-row>
                    </a-form>
                </div>
            </div>
            <template #footer>
                <a-button type="primary" style="margin-right: 10px" @click="apply">{{
                    $t('common.btn.apply')
                }}</a-button>
                <a-button @click="handleHide">{{ $t('common.btn.cancel') }}</a-button>
            </template>
        </vxe-modal>
    </Teleport>
</template>

<style scoped lang="less">
@import url('@/assets/style/variable.less');
.cm-trim-modal-content-type {
    height: 20px;
    span {
        color: @text-sub-color;
        font-size: 14px;
        font-weight: bold;
        line-height: 20px;
        margin-right: 10px;
    }
}
.ant-advanced-search-form {
    margin-top: 10px;
    padding: 14px;
    background: @bg-group-color;
    border: 1px solid @bg-hover-1-color;
    border-radius: 4px;
    width: 947px;
    height: 118px;
    overflow-y: auto;
    :deep(.ant-form-item .ant-form-item-label) {
        text-align: left;
        & > label {
            font-size: 14px;
            font-weight: normal;
            color: @text-subtitle-color;
            &::after {
                content: '';
            }
        }
    }
    :deep(.ant-form-item .ant-form-item-explain-error) {
        white-space: nowrap;
    }
    .ant-row .ant-col:nth-last-child(-n + 3) .ant-form-item {
        margin-bottom: 6px;
    }
}
</style>
