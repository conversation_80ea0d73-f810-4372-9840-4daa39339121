<script lang="ts" setup>
import { ref, reactive, provide, onBeforeMount } from 'vue';
import { stateManager as buttonIds } from '@/log-config/index';
import { getPermissionButton, judgeTab<PERSON>er<PERSON>son, EesLeftMenu } from '@futurefab/ui-sdk-ees-basic';
import UserViewer from './user-viewer/index.vue';
import SystemViewer from './system-viewer/index.vue';

const initTabList = [
  { name: 'stateManager.title.shareState', id: buttonIds.userDefineStateTab },
  { name: 'stateManager.title.state', id: buttonIds.systemStateTab }
];
const params = reactive({
  tabList: [] as any,
  currentTabId: '',
  currentTabIdx: 0,
  buttonAuthority: {} as any
});
const getButtonAuthority = () => {
  return {
    buttonAuthority: () => params.buttonAuthority
  };
};
provide('getButtonAuthority', getButtonAuthority);
onBeforeMount(async () => {
  params.buttonAuthority = await getPermissionButton('/r2r/state-viewer');
  params.tabList = judgeTabPermisson(initTabList, params.buttonAuthority);
  if (params.tabList.length) params.currentTabId = params.tabList[0].id;
});
</script>

<template>
  <div v-if="params.tabList.length" class="r2r-state-viewer">
    <ees-left-menu v-model:active-key="params.currentTabId" :menu-list="params.tabList">
      <template #right-content>
        <KeepAlive>
          <user-viewer v-if="params.currentTabId == buttonIds.userDefineStateTab" />
          <system-viewer v-else-if="params.currentTabId == buttonIds.systemStateTab" />
        </KeepAlive>
      </template>
    </ees-left-menu>
  </div>
</template>

<style lang="less" scoped>
@import url('@/assets/style/variable.less');
@ns: r2r-state-viewer;
.@{ns} {
  height: 100%;
  .r2r-state-viewer-tab {
    margin-bottom: @margin-xs;
  }
}
</style>
