import { type UnknownData } from '@/types/common';
import { XChart } from '@xchart/vue';
import { type Ref, ref } from 'vue';
import { customColor } from '@/utils/custom-color';
import { useCmstore } from '@futurefab/ui-sdk-stores';

const cmStore = useCmstore();
//数组去重
export const arrUnique = function (arr: any[], key: string) {
    const result = {} as any;
    const finalResult = [];
    for (let i = 0; i < arr.length; i++) {
        result[arr[i][key]] ? '' : (result[arr[i][key]] = true && finalResult.push(arr[i]));
    }
    return finalResult;
};
// 处理table中存在的数据组成的filters列表
// datas：当前table中的数据, types：当前全量的filter列表
// dataId: table中对应的字段名, typeId: filter列表中对应的字段名
export const dealFilterListWithChildren = (datas: any, types: any, dataId: any, typeId: any) => {
    const newArr: Array<any> = [];
    types.forEach((type: any) => {
        const fd = findItem(datas, dataId, type[typeId]);
        if (fd) {
            newArr.push(type);
        }
    });
    return newArr;
};
const findItem = (lists: any, dataId: any, value: any) => {
    for (const list of lists) {
        if (list[dataId] === value) {
            return list;
        }
        const child: any =
            list.children && list.children.length > 0 && findItem(list.children, dataId, value);
        if (child) {
            return child;
        }
    }
};

export const clipBoard = (text: string) => {
    const theClipboard = navigator.clipboard;
    console.log('复制成功' + text);
    theClipboard.writeText(text);
};
//获取相对应数据字典列表
export const getOptions = (type: any) => {
    return cmStore.codeList[type];
};
export const setLetterUpper = (letter: string) => {
    return letter.charAt(0).toUpperCase() + letter.slice(1);
};
//根据属性名进行数组去重
export const deduplication = (arr: UnknownData[], param: string) => {
    const obj: UnknownData = {};
    return arr.reduce((cur, next) => {
        if (!obj[next[param]]) {
            obj[next[param]] = true;
            cur.push(next);
        }
        return cur;
    }, []);
};
export function urlQueryParse(name: string) {
    const search = location.search;
    const res = new URLSearchParams(search);
    return res.get(name);
}

/**
 * 拼接主设备与moduleAlias
 * @param eqpId
 * @param modulAlias
 */
export function joinEqpModuleAlias(eqpId: string, modulAlias: string) {
    if (eqpId === modulAlias) {
        return eqpId;
    } else {
        const aliasArr = modulAlias.split('/');
        return `${eqpId}:${aliasArr[aliasArr.length - 1]}`;
    }
}
export const getTooltipText = (cnText: string, enText: string) => {
    const lang = localStorage.getItem('language');
    return lang == 'en-US' ? enText : cnText;
};
/**
 * 启用或关闭xchart的标记数据功能
 * @param xchart xchart实例
 * @param enableValue 设定
 */
export function enableXChartMarkData(xchart: XChart, enableValue = true) {
    if (xchart && xchart.toolbox) {
        const { isMarkingData } = xchart;
        if (isMarkingData !== enableValue) {
            const button = xchart.toolbox.getButton('markData');
            button?.click();
        }
    }
}

export function getModalWidth() {
    return document.body.clientWidth * 0.85;
}

export const useSelfFullScreen = (el: Ref<HTMLElement | null>) => {
    const isFullscreen = ref(false);
    const enter = () => {
        if (el.value) {
            el.value.style.position = 'fixed';
            el.value.style.width = '100vw';
            el.value.style.height = '100vh';
            el.value.style.top = '0';
            el.value.style.left = '0';
            el.value.style.zIndex = '100';
        }
        isFullscreen.value = true;
    };
    const exit = () => {
        if (el.value) {
            el.value.style.position = '';
            el.value.style.width = '';
            el.value.style.height = '';
            el.value.style.top = '';
            el.value.style.left = '';
            el.value.style.zIndex = '';
        }
        isFullscreen.value = false;
    };
    const toggle = () => {
        if (isFullscreen.value) {
            exit();
        } else {
            enter();
        }
    };
    return {
        isFullscreen,
        enter,
        exit,
        toggle,
    };
};

export const omittedTooltip = (omittedValues: any[]) => {
    let tooltip = '';
    omittedValues.map((item, i) => {
        tooltip += item.label;
        tooltip += i < omittedValues.length - 1 ? ', ' : '';
    });
    return tooltip;
};

// 为trace-detail的parameter list表格设置行背景色
// F=Fail W=Warning R=Run
export const setParamColor = (row: any, RDefaultColor?: string) => {
    let color = '';
    if (row.typeCd === 'F') {
        color = customColor.typeCd_F; // red
    } else if (row.typeCd === 'W') {
        color = customColor.typeCd_W; // orange
    } else if (row.typeCd === 'R') {
        color = RDefaultColor ?? customColor.typeCd_W;
    } else if (row.specExist === 'Y') {
        color = customColor.specExist_Y; // blue
    } else if (row.cttSpecExist === 'Y') {
        color = customColor.cttSpecExist_Y; // dark-red
    }
    return color;
};

export const strToOptionMap = (item: string) => ({
    label: item,
    value: item,
});
