<script setup lang="ts">
import { watch, ref } from 'vue';
import type { Measuredata } from '@/utils/spline-interpolate';
import Chart from '../chart.vue';
import type { Specification } from '@/views/metrology-management/interface';
import { calcGroup, getToolbox } from './index';
import { colorList } from '@futurefab/ui-sdk-ees-charts';
const props = defineProps<{
    data: Measuredata[];
    valueKeys: string[];
    groupKey: string;
    groupNumber: number;
    specification: Specification;
}>();
const options = ref<any>(null);
const getMultiScatter = (
    data: Measuredata[],
    valueKeys: string[],
    groupKey: string,
    groupNumber: number,
    specification: Specification,
) => {
    let maxV = -Infinity;
    let minV = Infinity;
    type GroupList = number[];
    const groupLists: GroupList[] = [];
    const xIndexs: number[][] = [];
    const yIndexs: number[][] = [];
    let center = [0, 0];
    if (groupKey === 'Radius') {
        const radius = specification.jsonObj.diameter / 2;
        switch(specification.jsonObj.originPosition) {
            case 'Bottom Left': 
                center = [radius, radius];
                break;
            case 'Bottom Right':
                center = [-radius, radius];
                break;
            case 'Top Left':
                center = [radius, -radius];
                break;
            case 'Top Right':
                center = [-radius, -radius];
                break;
        }
    }

    data.forEach((measure: Measuredata) => {
        let groupList: GroupList;
        const xIndex = measure.header.findIndex(item => item === specification.jsonObj.xField);
        xIndexs.push(measure.data.map(item => Number(item[xIndex])));
        const yIndex = measure.header.findIndex(item => item === specification.jsonObj.yField);
        yIndexs.push(measure.data.map(item => Number(item[yIndex])));
        if (groupKey !== 'Radius') {
            const groupIndex = measure.header.findIndex(item => item === groupKey);
            groupList = measure.data.map(item => Number(item[groupIndex]));
        } else {
            groupList = measure.data.map(item =>
                Math.sqrt((Number(item[xIndex]) - center[0]) ** 2 + (Number(item[yIndex]) - center[1]) ** 2),
            );
        }
        groupLists.push(groupList);
        const tempMax = Math.max(...groupList);
        if (tempMax > maxV) {
            maxV = tempMax;
        }
        const tempMin = Math.min(...groupList);
        if (tempMin < minV) {
            minV = tempMin;
        }
    });

    const gap = (maxV - minV) / groupNumber;

    // 按区间分组的对象
    const groupRes = valueKeys.map(valueKey =>
        new Array(groupNumber).fill(null).map((_: null, index: number) => ({
            start: minV + gap * index,
            end: index === groupNumber - 1 ? maxV : minV + gap * (index + 1),
            data: [] as {value: number; groupValue: number; x: number, y: number}[],
            count: 0,
            valueKey,
        })),
    );
    valueKeys.forEach((valueKey, keyIndex) => {
        groupLists.forEach((groupList, gIndex) => {
            const vIndex = data[gIndex].header.findIndex(item => item === valueKey);
            groupList.forEach((groupValue: number, index: number) => {
                const groupI = calcGroup(groupValue, maxV, minV, gap, groupNumber);
                const value = Number(data[gIndex].data[index][vIndex]);
                const x = xIndexs[gIndex][index];
                const y = yIndexs[gIndex][index];
                groupRes[keyIndex][groupI].data.push({value, x, y, groupValue});
            });
        });
    });

    const series: any = [];
    groupRes.forEach((keyGroup, index: number) => {
        const temp: any = {
            name: valueKeys[index],
            type: 'scatter',
            data: [],
            symbolSize: 5,
            emphasis: {
                scale: 1.05,
            },
            itemStyle: {
                borderWidth: 1,
                borderColor: 'gray'
            },
        };
        keyGroup.forEach((item, groupIndex: number) => {
            item.data.forEach((obj: any) => temp.data.push({ value: [groupIndex, obj.value], info: {...item, ...obj} }));
        });
        series.push(temp);
    });
    return {
        grid: {
            left: 80,
            top: 30,
            right: 20,
            bottom: 30,
        },
        toolbox: getToolbox(),
        xAxis: {
            show: true,
            type: 'category',
            data: new Array(groupNumber).fill(null).map((item, index) => index + 1),
            splitArea: {
                show: true,
            },
        },
        yAxis: {
            show: true,
            scale: true,
            splitArea: {
                show: false,
            },
        },
        legend: {
            show: true,
        },
        tooltip: {
            formatter: (event: any) => {
                if (event?.data?.info) {
                    const info = event.data.info;
                    return [
                        `${groupKey}: ${info.groupValue}`,
                        `${specification.jsonObj.xField}: ${info.x}`,
                        `${specification.jsonObj.yField}: ${info.y}`,
                    ].join('<br/>');
                }
            },
            appendToBody: true, 
        },
        series,
    };
};

watch(
    [
        () => props.data,
        () => props.valueKeys,
        () => props.groupKey,
        () => props.groupNumber,
        () => props.specification,
    ],
    v => {
        if (v) {
            options.value = getMultiScatter(
                props.data,
                props.valueKeys,
                props.groupKey,
                props.groupNumber,
                props.specification,
            );
        }
    },
    { immediate: true },
);
</script>
<template>
    <div class="common-chart-container">
        <Chart :options="options" />
    </div>
</template>
<style scoped lang="less">
.common-chart-container {
    width: 100%;
    height: 100%;
}
</style>
