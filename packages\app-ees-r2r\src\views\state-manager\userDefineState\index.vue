<script lang="ts" setup>
import { ref, reactive, inject, computed, onBeforeMount, nextTick } from 'vue';
import { xConfig, kConfig, dConfig } from './config';
import { t, type VxeGridDefines, VXETable, type VxeTablePropTypes } from '@futurefab/vxe-table';
import {
  setHeaderSelectFilter,
  successInfo,
  showConfirm,
  showWarning,
  getPermissionButton,
  EesSearchConditionLayer,
  useSearchConditionLayer
} from '@futurefab/ui-sdk-ees-basic';
import type { ButtonAuthority } from '@/interface/index';
import { Splitpanes, Pane } from 'splitpanes';
import {
  getUserDefineStateList,
  getUserDefineStateKeyList,
  getUserDefineStateItemList,
  deleteStateManager,
  getUserDefineStateCondition,
  getModelList,
  getChamberList
} from '@futurefab/ui-sdk-api';
import { stateManager as buttonIds } from '@/log-config/index';
import SearchCondition from '@/components/search/index.vue';
import AddStateManager from './add-state-manager/index.vue';
import StateManagerHistory from './state-manager-history/index.vue';
import { APP_NAME } from '@/utils/constant';
import { filterMethod, getAreaValue } from '@/utils/tools';
import { sharedStateCnText, sharedStateUsText } from '@/docs/state-manager/index';
import type { ConditionItem } from '@/components/search/interface';
import type { GetButtonAuthority } from '@/views/setup-data-manager/interface';

const getButtonAuthority = inject('getButtonAuthority');
const buttonAuthority = computed(() =>
  (getButtonAuthority as GetButtonAuthority)().buttonAuthority()
);
// const auth = reactive({
//     buttonAuthority: {} as ButtonAuthority
// });
const inputData = reactive({
  stateType: [] as string[]
});
inputData.stateType = ['Share'];
const xCondition = ref();
const { layerFn, isDisabled, searchFormFn } = useSearchConditionLayer(xCondition);

const xGrid = ref();
const kGrid = ref();
const dGrid = ref();
const xTable = reactive({
  options: xConfig,
  data: [] as any[],
  loading: false
});
const kTable = reactive({
  options: kConfig,
  data: [] as any[],
  columns: [{ field: 'name', title: 'Keys', treeNode: 'true', slots: { default: 'item' } }],
  config: {
    children: 'children',
    // line: true,
    accordion: false, // 一层只允许展开一个节点
    expandAll: true // 默认是否全部展开
  },
  loading: false
});
const dTable = reactive({
  options: dConfig,
  data: [] as any[],
  columns: [{ field: 'itemName', title: 'Items', treeNode: 'true', slots: { default: 'item' } }],
  config: {
    children: 'children',
    accordion: false, // 一层只允许展开一个节点
    expandAll: true // 默认是否全部展开
  },
  loading: false
});
const data = reactive({
  showAdd: false,
  showHistory: false
});
const selected = ref({ code: '', selectData: { activityFunctionName: '' } });
const hide = async (refresh = false, newData?: { [x: string]: string }) => {
  data.showAdd = false;
  const item = document.querySelector('.vxe-table--tooltip-wrapper.is--visible');
  if (item) item?.classList.remove('is--visible');
  if (refresh) {
    const row = xGrid.value?.getCurrentRecord();
    await getList();
    xGrid.value?.setCurrentRow(xTable.data.find((v) => v?.rawId === row?.rawId));
    if (row?.rawId) getDetail(row.rawId);
  }
};
const hideHistory = () => {
  data.showHistory = false;
};
const onHandleKeyItem = (code: string, menuClick: boolean, row: any) => {
  const selectData = menuClick ? row : xGrid.value?.getCurrentRecord();
  if (!selectData) return showWarning('common.tip.selectData');
  selected.value = { code, selectData };
  console.log('onHandleKeyItem', selected.value);
  switch (code) {
    case 'history':
      data.showHistory = true;
      break;
    default:
      data.showAdd = true;
      break;
  }
};
const onDelete = async (menuClick: boolean, row: any) => {
  const $table: any = xGrid.value;
  const removeList = menuClick ? [row] : $table.getCheckboxRecords();
  console.log('removeList', removeList);
  if (!removeList.length) return showWarning('common.tip.selectData');
  const type = await showConfirm({
    msg: t('common.tip.deleteConfirm', { total: removeList.length })
  });
  if (type !== 'confirm') return;
  const bodyParams = removeList.map((v: any) => v.rawId);
  const res = await deleteStateManager({
    headerParams: {
      log: 'Y',
      page: buttonIds.pageId,
      action: buttonIds.user.delete
    },
    bodyParams
  });
  if (res.status !== 'SUCCESS') return;
  successInfo('common.tip.delSuccess');
  getList();
};
const gridEvents = {
  toolbarToolClick: ({ code, tool }: any) => {
    console.log(code);
    const { menuClick, row } = tool;
    switch (code) {
      case 'refresh':
        getList(true);
        break;
      case 'add':
        selected.value = { code, selectData: { activityFunctionName: '' } };
        data.showAdd = true;
        break;
      case 'deleteData':
        onDelete(menuClick, row);
        break;
      default:
        onHandleKeyItem(code, menuClick, row);
        break;
    }
  },
  cellMenu: ({ row }: { row: any }) => {
    getDetail(row.rawId);
  },
  currentChange: ({ row }: { row: any }) => {
    console.log(row);
    getDetail(row.rawId);
    // dTable.options = getDetailConfig({
    //     tableName: t('stateManager.title.stateManagerDetail', {
    //         name: ` - ${row.activityFunctionName}`,
    //     }),
    // });
  }
};
const params = reactive({
  areaList: [] as string[],
  modelList: [],
  chamberList: [],
  conditionParams: {} as ConditionItem,
  routePath: `/${APP_NAME}/state-manager`
});
const getList = async (isRefresh?: boolean, isInit?: boolean) => {
  const res = await getUserDefineStateList({
    headerParams: {
      log: 'Y',
      page: buttonIds.pageId,
      action: 'user-list'
    },
    bodyParams: {
      areaList: params.areaList,
      eqpModelList: params.modelList,
      chamberList: params.chamberList,
      routePath: params.routePath
    }
  });
  if (res.status !== 'SUCCESS') return false;
  xTable.data = res.data;
  setTimeout(() => {
    xGrid.value &&
      setHeaderSelectFilter({
        xGrid: xGrid,
        tableData: xTable.data as any,
        columnFieldList: [
          'activityClassName',
          'useYn',
          'areaName',
          'eqpModelName',
          'chamberName',
          'activityFunctionName',
          'lastUpdateBy',
          'createBy'
        ],
        splitTypeMap: {
          areaName: ',',
          eqpModelName: ',',
          chamberName: ','
        }
      });
  }, 300);
  nextTick(() => {
    if (xTable.data.length > 0) {
      xGrid.value.setCurrentRow(xTable.data[0]);
      getDetail(xTable.data[0].rawId);
    }
  });
  kTable.data = [];
  dTable.data = [];
  if (isRefresh) successInfo('common.tip.refreshSuccess');
  if (isInit) setVisibleToolMenu();
};
const getDetail = (rawId: number) => {
  getUserDefineStateKeyList({
    headerParams: {
      log: 'Y',
      page: buttonIds.pageId,
      action: 'user-key-list'
    },
    bodyParams: { rawId }
  }).then((res: any) => {
    if (res.status == 'SUCCESS') {
      kTable.data = res.data;
      setHeaderSelectFilter({
        xGrid: kGrid,
        tableData: kTable.data as any,
        columnFieldList: ['itemName', 'displayName']
      });
    }
  });
  getUserDefineStateItemList({
    headerParams: {
      log: 'Y',
      page: buttonIds.pageId,
      action: 'user-item-list'
    },
    bodyParams: { rawId }
  }).then((res: any) => {
    if (res.status == 'SUCCESS') {
      dTable.data = res.data;
      setHeaderSelectFilter({
        xGrid: dGrid,
        tableData: dTable.data as any,
        columnFieldList: ['itemName', 'displayName']
      });
    }
  });
  /* kTable.data = res.data.filter((v: any) => v.type === 'STATE KEY');
            dTable.data = res.data.filter((v: any) => v.type === 'STATE ITEM'); */
  // dTable.data = res.data;
};
let cols = ref<any>([]);
const requestCondition = () => {
  getUserDefineStateCondition({
    headerParams: {
      log: 'Y',
      page: buttonIds.pageId,
      action: 'user-search-condition'
    },
    bodyParams: {
      routePath: params.routePath
    }
  }).then((result: any) => {
    if (result.status == 'SUCCESS') {
      if (result.data.dataRestrictionLevel) {
        const list = result.data.dataRestrictionLevel.split(',');
        // const list = ['AREA', 'EQP_MODEL', 'CHAMBER_TYPE'];
        const conditionMap: any = {};
        list.forEach((item: string) => {
          if (item == 'AREA') {
            conditionMap['area'] = {
              code: 'area',
              index: 0,
              title: 'common.title.area',
              list: result.data.areaList ? result.data.areaList : [],
              multi: true,
              isInternational: true
              // required: true,
            };
          } else if (item == 'EQP_MODEL') {
            conditionMap['eqpModel'] = {
              code: 'eqpModel',
              index: 1,
              title: 'common.title.eqpModel',
              list: [],
              multi: true,
              isInternational: true
            };
          } else if (item == 'CHAMBER_TYPE') {
            conditionMap['chamberType'] = {
              code: 'chamberType',
              index: 2,
              title: 'common.title.chamberType',
              list: [],
              multi: true,
              isInternational: true
            };
          }
        });
        params.conditionParams = { ...conditionMap };
        console.log('requestCondition', params.conditionParams);
        const keys = Object.keys(params.conditionParams);
        const values = Object.values(params.conditionParams) as any;
        const oldCols = xTable.options.columns;
        const newCols = keys.map((item: string, index: number) => {
          if (item == 'area') {
            params.areaList = [...getAreaValue(values[index].list)];
          }
          return {
            field: item == 'chamberType' ? 'chamberName' : `${item}Name`,
            title: `common.field.${item}`,
            sortable: true,
            minWidth: 150,
            filters: [],
            filterMethod,
            params: {
              symbolSplit: ','
            }
          };
        });
        cols.value = [...oldCols!.slice(0, 3), ...newCols, ...oldCols!.slice(3)];
        console.log('cols', cols.value);
        xGrid.value && xGrid.value.reloadColumn(cols.value);
      }
      getList(false, true);
    }
  });
};
let setupKeyController: AbortController | undefined = undefined;
const requestItem = (argus: {
  value: string | string[];
  item: string;
  index: number;
  keys: string[];
  isSidebar?: boolean;
}) => {
  const { value, item, index, keys, isSidebar } = argus;
  console.log('requestItem', argus);
  if (index >= keys.length - 1) return false;
  setupKeyController = new AbortController(); //bodyParams里面多传一个参数signal: setupKeyController.signal,
  if (item == 'area' && keys.includes('eqpModel')) {
    if (!value || value.length == 0) {
      if (params.conditionParams['eqpModel']) {
        params.conditionParams['eqpModel'].list = [];
      }
      xCondition.value &&
        xCondition.value.setSelectValue({
          item: 'eqpModel',
          arr: []
        });
    } else {
      getModelList({
        headerParams: {
          log: 'Y',
          page: buttonIds.pageId,
          action: 'user-search-model'
        },
        bodyParams: {
          signal: setupKeyController.signal,
          areaList: !Array.isArray(value) && value ? [value] : value,
          routePath: params.routePath
        }
      }).then((result: any) => {
        if (result.status == 'SUCCESS') {
          if (!isSidebar) {
            params.conditionParams['eqpModel'].list = (value.length && result.data) || [];
            const eqpModelValue = xCondition.value.getSelectValue('eqpModel');
            if (eqpModelValue && eqpModelValue.length) {
              const list = eqpModelValue.filter(
                (val: string) => result.data && result.data.length && result.data.includes(val)
              );
              xCondition.value.setSelectValue({
                item: 'eqpModel',
                arr: list
              });
            }
          } else {
            params.conditionParams['eqpModel'].list2 = (value.length && result.data) || [];
          }
        }
      });
    }
  }
  if (item == 'area' && keys.includes('chamberType')) {
    if (!value || value.length == 0) {
      params.conditionParams['chamberType'].list = [];
      xCondition.value.setSelectValue({
        item: 'chamberType',
        arr: []
      });
    } else {
      getChamberList({
        headerParams: {
          log: 'Y',
          page: buttonIds.pageId,
          action: 'user-search-chamber'
        },
        bodyParams: {
          signal: setupKeyController.signal,
          areaList: !Array.isArray(value) && value ? [value] : value,
          routePath: params.routePath
        }
      }).then((result: any) => {
        if (result.status == 'SUCCESS') {
          if (!isSidebar) {
            params.conditionParams['chamberType'].list = result.data;
            const chamberTypeValue = xCondition.value.getSelectValue('chamberType');
            if (chamberTypeValue && chamberTypeValue.length) {
              const list = chamberTypeValue.filter(
                (val: string) => result.data && result.data.length && result.data.includes(val)
              );
              xCondition.value.setSelectValue({
                item: 'chamberType',
                arr: list
              });
            }
          } else {
            params.conditionParams['chamberType'].list2 = result.data;
          }
        }
      });
    }
  }
};
const handleSearch = (argus: any) => {
  if (!Array.isArray(argus.area) && argus.area) {
    argus.area = [argus.area];
  } else if (!Array.isArray(argus.eqpModel) && argus.eqpModel) {
    argus.eqpModel = [argus.eqpModel];
  } else if (!Array.isArray(argus.chamberType) && argus.chamberType) {
    argus.chamberType = [argus.chamberType];
  }
  params.areaList = argus.area;
  params.modelList = argus.eqpModel;
  params.chamberList = argus.chamberType;
  getList();
};
const clearConditionParamLists = (keys: string[]) => {
  keys.forEach((key) => {
    if (params.conditionParams[key]) {
      params.conditionParams[key].list = [];
    }
  });
};
const handleReset = () => {
  clearConditionParamLists(['eqpModel', 'chamberType']);
};
const setVisibleToolMenu = () => {
  setTimeout(async () => {
    // auth.buttonAuthority = (await getPermissionButton(
    //     '/r2r/state-manager'
    // )) as ButtonAuthority;
    xGrid.value &&
      xGrid.value.setVisibleTools({
        authorityList: buttonAuthority.value.buttonList,
        flag: buttonAuthority.value.allButtonFlag
      });
  }, 200);
};
onBeforeMount(async () => {
  requestCondition();
  // getList(false, true);
});
const getContent = () => {
  const lang = localStorage.getItem('language');
  return lang == 'en-US' ? sharedStateUsText : sharedStateCnText;
};
</script>

<template>
  <div class="state-manager">
    <div class="state-manager-content">
      <splitpanes class="default-theme" style="width: 100%" horizontal>
        <pane class="cardItem">
          <vxe-grid
            ref="xGrid"
            v-bind="xTable.options"
            :data="xTable.data"
            :loading="xTable.loading"
            class="common-table-grid-maximize-style vxe-toolbar-top-left-radius-lg vxe-toolbar-top-right-radius-lg"
            v-on="gridEvents"
          >
            <template #search-condition>
              <ees-search-condition-layer
                :page-id="buttonIds.pageId"
                :is-disabled-search="isDisabled"
                :input-data="inputData"
                :show-condition-list="false"
                :is-show-btn-save-as="false"
                v-on="layerFn"
              >
                <template #search-form>
                  <search-condition
                    ref="xCondition"
                    :condition-params="params.conditionParams"
                    :show-mode="false"
                    v-on="searchFormFn"
                    @do-request="requestItem"
                    @do-search="handleSearch"
                    @do-reset="handleReset"
                  ></search-condition>
                </template>
              </ees-search-condition-layer>
            </template>
            <template #beforeTools>
              <vxe-tooltip
                :use-h-t-m-l="true"
                :enterable="true"
                theme="light"
                :content="getContent()"
              >
                <button class="vxe-button type--button icon-type--svgButton">
                  <svg class="icon" aria-hidden="true">
                    <use xlink:href="#icon-btn-help"></use>
                  </svg>
                </button>
              </vxe-tooltip>
            </template>
          </vxe-grid>
        </pane>
        <pane class="cardItem">
          <splitpanes class="default-theme" style="height: 100%; width: 100%">
            <pane class="cardItem" size="40">
              <vxe-grid
                ref="kGrid"
                v-bind="kTable.options"
                :data="kTable.data"
                :loading="kTable.loading"
                class="common-table-grid-maximize-style vxe-table-bottom-left-radius-lg"
              >
              </vxe-grid>
            </pane>
            <pane class="cardItem" size="60">
              <vxe-grid
                ref="dGrid"
                v-bind="dTable.options"
                :data="dTable.data"
                :loading="dTable.loading"
                class="common-table-grid-maximize-style vxe-table-bottom-right-radius-lg"
              >
              </vxe-grid>
            </pane>
          </splitpanes>
        </pane>
      </splitpanes>
    </div>
    <add-state-manager
      v-if="data.showAdd"
      :title="
        t('stateManager.title.shareState', {
          name: ` - ${t(`common.btn.${selected.code}`)}`
        })
      "
      :show="data.showAdd"
      :selected="selected"
      :condition-params="params.conditionParams"
      @do-request="requestItem"
      @hideSidebar="hide"
    />
    <state-manager-history
      v-if="data.showHistory"
      :title="
        t('executionSetting.title.versionHistory', {
          name: selected.selectData.activityFunctionName
        })
      "
      :show="data.showHistory"
      :selected="selected"
      :cols="cols"
      @hideSidebar="hideHistory"
    />
  </div>
</template>

<style lang="less" scoped>
@import url('@/assets/style/variable.less');
@ns: state-manager;

.@{ns} {
  height: 100%;

  :deep(.splitpanes__pane) {
    display: block !important;
    background-color: transparent !important;
  }

  &-content {
    height: 100%;
    overflow: auto;
  }

  :deep(.vxe-table-name-text) {
    max-width: 500px;
  }
}
</style>
