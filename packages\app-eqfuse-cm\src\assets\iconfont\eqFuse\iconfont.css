@font-face {
    font-family: 'iconfont';
    src: url('iconfont.eot?t=27403aac7de0441f5e7b4d5286594725'); /* IE9 */
    src: url('iconfont.eot?t=27403aac7de0441f5e7b4d5286594725#iefix') format('embedded-opentype'),
        /* IE6-IE8 */ url('iconfont.woff?t=27403aac7de0441f5e7b4d5286594725') format('woff2'),
        url('iconfont.woff?t=27403aac7de0441f5e7b4d5286594725') format('woff'),
        /* chrome、firefox */ url('iconfont.ttf?t=27403aac7de0441f5e7b4d5286594725')
            format('truetype'),
        /* chrome、firefox、opera、Safari, Android, iOS 4.2+*/
            url('iconfont.svg?t=27403aac7de0441f5e7b4d5286594725#iconfont') format('svg'); /* iOS 4.1- */
}

.iconfont {
    font-family: 'iconfont' !important;
    font-size: 16px;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.icon-btn-add-schedule-job:before {
    content: '\ef14';
}
.icon-btn-overlay-trace-chart:before {
    content: '\ef15';
}
.icon-btn-schedule-job-management:before {
    content: '\ef16';
}
.icon-btn-schedule:before {
    content: '\ef17';
}
.icon-btn-view-data:before {
    content: '\ef18';
}
.icon-btn-substract:before {
    content: '\ef19';
}
.icon-btn-context:before {
    content: '\ef20';
}
.icon-btn-overwrite-copy:before {
    content: '\ef22';
}
.icon-btn-partial-upload-uquipment-constants:before {
    content: '\ef23';
}
.icon-btn-compare:before {
    content: '\ef21';
}
.icon-btn-run-analysis:before {
    content: '\ef24';
}
.icon-Logo-01:before {
    content: '\ef27';
}
.icon-Logo-02:before {
    content: '\ef28';
}
.icon-Logo-04:before {
    content: '\ef29';
}
.icon-Logo-03:before {
    content: '\ef30';
}
.icon-btn-automatic-grouping:before {
    content: '\ef26';
}
.icon-btn-eq-add:before {
    content: '\ef25';
}
