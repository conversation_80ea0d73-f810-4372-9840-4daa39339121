// 定义圆角半径变量
@border-radius-value: 16px;

// 基本圆角样式（保持原样）
.b-l-radius {
    border-radius: 0 0 0 @border-radius-value;
}
.b-r-radius {
    border-radius: 0 0 @border-radius-value 0;
}
.b-radius {
    border-radius: 0 0 @border-radius-value @border-radius-value;
}
.t-l-radius {
    border-radius: @border-radius-value 0 0 0;
}
.t-r-radius {
    border-radius: 0 @border-radius-value 0 0;
}
.t-radius {
    border-radius: @border-radius-value @border-radius-value 0 0;
}
.r-radius {
    border-radius: 0 @border-radius-value @border-radius-value 0;
}

// 表格样式（手动编写每个变体）
.table-b-l-radius {
    border-radius: 0 0 0 @border-radius-value;
    .vxe-table--border-line {
        border-radius: 0 0 0 @border-radius-value;
    }
    .vxe-grid--toolbar-wrapper.border,
    .vxe-toolbar {
        border-radius: 0;
    }
}
.table-b-r-radius {
    border-radius: 0 0 @border-radius-value 0;
    .vxe-table--border-line {
        border-radius: 0 0 @border-radius-value 0;
    }
    .vxe-grid--toolbar-wrapper.border,
    .vxe-toolbar {
        border-radius: 0;
    }
}
.table-b-radius {
    border-radius: 0 0 @border-radius-value @border-radius-value;
    .vxe-table--border-line {
        border-radius: 0 0 @border-radius-value @border-radius-value;
    }
    .vxe-grid--toolbar-wrapper.border,
    .vxe-toolbar {
        border-radius: 0;
    }
}
.table-t-l-radius {
    border-radius: @border-radius-value 0 0 0;
    .vxe-table--border-line {
        border-radius: 0;
    }
    .vxe-grid--toolbar-wrapper.border,
    .vxe-toolbar {
        border-radius: @border-radius-value 0 0 0;
    }
}
.table-t-r-radius {
    border-radius: 0 @border-radius-value 0 0;
    .vxe-table--border-line {
        border-radius: 0;
    }
    .vxe-grid--toolbar-wrapper.border,
    .vxe-toolbar {
        border-radius: 0 @border-radius-value 0 0;
    }
}
.table-t-radius {
    border-radius: @border-radius-value @border-radius-value 0 0;
    .vxe-table--border-line {
        border-radius: 0;
    }
    .vxe-grid--toolbar-wrapper.border,
    .vxe-toolbar {
        border-radius: @border-radius-value @border-radius-value 0 0;
    }
}
.table-r-radius {
    border-radius: 0 @border-radius-value @border-radius-value 0;
    .vxe-table--border-line {
        border-radius: 0 0 @border-radius-value 0;
    }
    .vxe-grid--toolbar-wrapper.border,
    .vxe-toolbar {
        border-radius: 0 @border-radius-value 0 0;
    }
}
.no-title-table-small-radius {
    .vxe-table--border-line {
        border-radius: 4px;
    }
}
.modal-table-small-radius {
    .vxe-grid--toolbar-wrapper.border,
    .vxe-toolbar {
        border-radius: 4px 4px 0 0;
    }
    .vxe-table--border-line {
        border-radius: 0 0 4px 4px;
    }
}
