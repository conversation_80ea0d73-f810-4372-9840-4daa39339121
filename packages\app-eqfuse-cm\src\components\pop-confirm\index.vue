<script lang="ts" setup>
import { ref } from 'vue';
import { t } from '@futurefab/vxe-table';

const props = withDefaults(
  defineProps<{
    title: string;
    content: string;
    trigger?: string;
    iconType?: 'warning' | 'error';
    placement?: string;
    okText?: string;
    cancelText?: string;
  }>(),
  {
    title: '',
    content: '',
    trigger: 'click',
    iconType: 'warning',
    placement: 'topRight',
    okText: t('common.btn.confirm'),
    cancelText: t('common.btn.cancel')
  }
);
const emits = defineEmits(['ok', 'cancel']);
const open = ref<boolean>(false);
const handleOk = () => {
  emits('ok');
  open.value = false;
};
const handleCancel = () => {
  emits('cancel');
  open.value = false;
};
</script>

<template>
  <a-popover
    v-model:open="open"
    :trigger="trigger"
    :autoAdjustOverflow="true"
    :placement="placement"
  >
    <template #content>
      <p class="title">
        <i
          :class="
            iconType === 'warning'
              ? 'iconfont icon-warning title-icon-warning'
              : 'iconfont icon-error title-icon-error'
          "
        />
        <span class="title-text">{{ title }}</span>
      </p>
      <p class="content">{{ content }}</p>

      <div class="btn-group">
        <a-button
          type="primary"
          size="small"
          :class="iconType === 'warning' ? 'warning-ok-btn' : 'error-ok-btn'"
          @click="handleOk"
        >
          {{ okText }}
        </a-button>
        <a-button size="small" class="cancel-btn" @click="handleCancel">{{ cancelText }}</a-button>
      </div>
    </template>
    <slot name="popover-btn"></slot>
  </a-popover>
</template>

<style lang="less" scoped>
@import url('@/assets/style/variable.less');
.title {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  .title-icon-warning {
    color: @warning-color;
    margin-right: 10px;
  }
  .title-icon-error {
    color: @error-color;
    margin-right: 10px;
  }
  .title-text {
    font-weight: bold;
  }
}
.content {
  margin: 5px 0 0 26px;
}
.btn-group {
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
  .warning-ok-btn {
    background-color: @warning-color;
  }
  .error-ok-btn {
    background-color: @error-color;
  }
  .cancel-btn {
    margin-left: 10px;
  }
}
</style>
