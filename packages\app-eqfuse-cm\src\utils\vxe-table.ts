export const dealFilters = (filterType: string) => {
    if (filterType == 'N') {
        return [
            {
                data: {
                    list: [
                        {
                            type: 'eq',
                            name: '',
                            val: '',
                        },
                    ],
                    condition: 'and',
                },
            },
        ];
    } else if (filterType == 'I') {
        return [{ data: '' }];
    }
    return [];
};
export const dealFilterRender = (filterType: string) => {
    if (filterType == 'I') {
        return {
            name: 'input',
            events: {
                keyup: (argus: any, event: any) => {
                    const { $panel } = argus;
                    if (event.code == 'Enter') {
                        $panel.confirmFilter();
                    }
                },
            },
        };
    } else if (filterType == 'N') {
        return {
            name: '$number-filter',
        };
    }
};

export const dealFilterMethod = (filterType: string, columnName: string) => {
    if (filterType == 'I') {
        return ({ option, row }: any) => {
            // 自定义插槽实现模糊搜索
            const str = `\S*${option.data}\S*`;
            const reg = new RegExp(str, 'i');
            return reg.test(row[columnName]);
        };
    }
};
