import { VXETable, t } from '@futurefab/vxe-table';
const { tableDefaultConfig } = VXETable.tableFun;
export const options = tableDefaultConfig({
    border: true,
    columns: [
        {
            field: 'rawId',
            type: 'checkbox',
        },
        {
            field: 'eqpAlias',
            title: 'common.field.eqpAlias',
            minWidth: '120px',
        },
        {
            field: 'module<PERSON>lia<PERSON>',
            title: 'common.field.moduleAlias',
            minWidth: '140px',
        },
        {
            field: 'eventName',
            title: 'common.field.eventName',
            minWidth: '140px',
            editRender: {
                name: '$input',
            },
        },
        {
            field: 'eventDtts',
            title: 'common.field.eventDtts',
            minWidth: '180px',
        },
        {
            field: 'comment',
            title: 'common.field.comment',
            minWidth: '180px',
            editRender: {
                name: '$input',
            },
        },
        {
            field: 'createBy',
            title: 'common.field.createdBy',
            minWidth: 120,
        },
        {
            field: 'createDtts',
            title: 'common.field.createdTime',
            minWidth: 200,
        },
        {
            field: 'lastUpdateBy',
            title: 'common.field.updatedBy',
            minWidth: 120,
        },
        {
            field: 'lastUpdateDtts',
            title: 'common.field.updatedTime',
            minWidth: 200,
        },
    ],
    toolbarConfig: {
        tableName: t('common.title.commonList'),
        import: false,
        export: false,
        tools: [
            ...VXETable.tableFun.getToolsButton([
                {
                    name: 'common.btn.save',
                    icon: 'icon-btn-save',
                    isAuthority: false,
                    visible: true,
                },
                {
                    name: 'common.btn.delete',
                    icon: 'icon-btn-delete',
                    isAuthority: false,
                    visible: true,
                },
            ]),
        ],
    },
    checkboxConfig: {
        showHeader: true,
    },
    maxHeight: '400px',
});
