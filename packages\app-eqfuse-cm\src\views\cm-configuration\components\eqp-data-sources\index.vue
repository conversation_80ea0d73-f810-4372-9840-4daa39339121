<script setup lang="ts">
import {
  deleteEqpDataSources,
  getEqpDataSourcesList,
  saveEqpDataSources,
  viewCodeGroupList
} from '@futurefab/ui-sdk-api';
import { onMounted, ref, watch, reactive } from 'vue';
import { eqpDataValidate, getEqpDataSourceConfig, eqpDataRules } from './config';
import type { EqpDataTypeSourcesItem, EqpDataTypeSourcesSelectOptions } from './interface';
import { showWarning, showConfirm, successInfo } from '@futurefab/ui-sdk-ees-basic';
import { t } from '@futurefab/vxe-table';
import Schedual from './schedual.vue';
import { useAuthority } from '@/utils/use-authority';
import { eqpDataSources as buttonIds } from '@/log-config';
import { getPermissionButton } from '@futurefab/ui-sdk-ees-basic';

const xGrid = ref();
const loading = ref(false);
const data = ref<EqpDataTypeSourcesItem[]>([]);
const validRules = eqpDataRules(xGrid);
const baseClass = 'cm-eqp-data-sources';
const filterEqp = ref('');

const getData = async (eqp: string) => {
  try {
    loading.value = true;
    const res = await getEqpDataSourcesList({
      bodyParams: { eqpId: eqp || '' },
      headerParams: {
        log: 'Y',
        page: buttonIds.pageId,
        action: buttonIds.search
      }
    });
    if (res.status === 'SUCCESS') {
      data.value = res.data || [];
    }
  } catch {
  } finally {
    loading.value = false;
  }
};
let beforeSearchEqp: string;
const search = () => {
  beforeSearchEqp = filterEqp.value || '';
  getData(filterEqp.value);
};
const refresh = () => {
  getData(beforeSearchEqp);
};

const selectOption = ref<EqpDataTypeSourcesSelectOptions>({
  dataType: [],
  protocal: []
});

const getSelectOptions = () => {
  const codeConfigList = ['EQP_DATA_TYPE_CD', 'PROTOCOL_CD'] as const;
  viewCodeGroupList({
    bodyParams: { categoryList: codeConfigList }
  }).then(
    (res: { data: Record<(typeof codeConfigList)[number], { code: string; name: string }[]> }) => {
      const mapFunc = (item: { name: string; code: string }) => ({
        label: item.name,
        value: item.code
      });
      selectOption.value = {
        dataType: res.data.EQP_DATA_TYPE_CD.map(mapFunc),
        protocal: res.data.PROTOCOL_CD.map(mapFunc)
      };
    }
  );
};

const options = ref(getEqpDataSourceConfig(selectOption.value));

const schedualRef = ref();
const scheduleInfo = reactive({
  isEdit: false,
  schedualForm: {} as any,
  dataTypeStr: '',
  eqpId: ''
});
watch(
  () => selectOption.value,
  () => {
    options.value = getEqpDataSourceConfig(selectOption.value);
  }
);

const dealAdd = async () => {
  const { row } = await xGrid.value.insert();
  row.eqpId = '';
  row.eqpDataTypeCd = '';
  row.protocolCd = '';
  row.ipAddr = '';
  row.portNo = '';
  row.userName = '';
  row.userPass = '';
  row.remotePath = '';
  row.localPath = '';
  row.schdJobRawId = '';
  await xGrid.value.setActiveRow(row);
};

const dealSave = async () => {
  const { updateRecords, insertRecords } = xGrid.value.getRecordset();
  const { fullData } = xGrid.value.getTableData();
  const temp = [...updateRecords, ...insertRecords];
  if (!temp.length) {
    return showWarning('common.tip.saveTip');
  }
  const errorItem = eqpDataValidate(temp, fullData);
  if (errorItem) {
    await xGrid.value.validate(errorItem.row).then((error: any) => {
      if (!error) {
        showWarning(errorItem.msg);
        xGrid.value.scrollToRow(errorItem.row);
      }
    });
    return;
  }
  try {
    const type = await showConfirm({ msg: 'common.tip.saveConfirm' });
    if (type !== 'confirm') return;
    loading.value = true;
    const res = await saveEqpDataSources({
      bodyParams: temp,
      headerParams: {
        log: 'Y',
        page: buttonIds.pageId,
        action: buttonIds.save
      }
    });
    if (res.status === 'SUCCESS') {
      await refresh();
      successInfo('common.tip.actionSuccess');
    }
  } finally {
    loading.value = false;
  }
};

const dealDelete = async (checkboxRecords: EqpDataTypeSourcesItem[]) => {
  if (checkboxRecords.length) {
    // 二次确认弹窗
    const type = await showConfirm({
      msg: t('common.tip.deleteConfirm', {
        total: checkboxRecords.length as any
      })
    });
    if (type !== 'confirm') return;
    const addRows: EqpDataTypeSourcesItem[] = []; // 新增的直接删除
    const existRows: EqpDataTypeSourcesItem[] = []; // 请求接口删除

    checkboxRecords.forEach((row) => {
      if (!row.eqpDataSourceId) {
        addRows.push(row);
        xGrid.value.remove(row);
      } else {
        existRows.push(row);
      }
    });
    let deleteSuccess = false;
    if (existRows.length) {
      try {
        loading.value = true;
        const res = await deleteEqpDataSources({
          bodyParams: existRows.map((item) => item.eqpDataSourceId),
          headerParams: {
            log: 'Y',
            page: buttonIds.pageId,
            action: buttonIds.delete
          }
        });
        if (res.status === 'SUCCESS') {
          deleteSuccess = true;
        }
      } finally {
        loading.value = false;
      }
    } else {
      deleteSuccess = true;
    }
    if (deleteSuccess == true) {
      successInfo('common.tip.actionSuccess');
      refresh();
    }
  } else {
    return showWarning('common.tip.selectData');
  }
};

const dealSchedule = async (checkboxRecords: EqpDataTypeSourcesItem[]) => {
  if (!checkboxRecords.length) {
    return showWarning('common.tip.selectData');
  }
  const eqpSet = new Set();
  for (let i = 0; i < checkboxRecords.length; i++) {
    const row = checkboxRecords[i];
    eqpSet.add(row.eqpId);
    if (eqpSet.size > 1) {
      return showWarning('cm.tips.oneEqp');
    }
  }
  const hasSaveRow = checkboxRecords.some((item) => item.eqpDataSourceId);
  if (!hasSaveRow) {
    return showWarning('cm.tips.onsaveConnotSchedule');
  }
  scheduleInfo.isEdit = !!checkboxRecords[0].schdJobRawId;
  scheduleInfo.schedualForm = checkboxRecords[0].schdJobInfo;
  scheduleInfo.eqpId = checkboxRecords[0].eqpId;
  const { fullData } = xGrid.value.getTableData();
  scheduleInfo.dataTypeStr = fullData
    .filter(
      (item: EqpDataTypeSourcesItem) =>
        item.eqpDataSourceId && item.eqpId == checkboxRecords[0].eqpId
    )
    .map((item: EqpDataTypeSourcesItem) => item.eqpDataTypeCd)
    .join('/');
  schedualRef.value.show();
};

const events = {
  toolbarToolClick: ({ code }: { code: string }) => {
    const checkboxRecords = xGrid.value && xGrid.value.getCheckboxRecords();
    switch (code) {
      case 'refresh':
        refresh();
        break;
      case 'add':
        dealAdd();
        break;
      case 'save':
        dealSave();
        break;
      case 'delete':
        dealDelete(checkboxRecords);
        break;
      case 'schedule':
        dealSchedule(checkboxRecords);
        break;
    }
  }
};
const activeMethod = ({ row, column }: any) =>
  !row.eqpDataSourceId || (column.field !== 'eqpId' && column.field !== 'eqpDataTypeCd');
const cmTrimAuthorityInfo = ref({} as any);
onMounted(() => {
  getSelectOptions();
  getPermissionButton('/cm/cm-config').then((res: any) => {
    cmTrimAuthorityInfo.value = res;
  });
});
useAuthority(xGrid, options, cmTrimAuthorityInfo, buttonIds, true);
</script>
<template>
  <div :class="[baseClass, 'b-radius']">
    <vxe-grid
      :id="baseClass + '-grid'"
      ref="xGrid"
      :class="[baseClass + '-grid', 'table-b-radius']"
      v-bind="options"
      :data="data"
      :edit-rules="validRules"
      :loading="loading"
      :edit-config="{
        trigger: 'dblclick',
        mode: 'cell',
        showStatus: false,
        showStatusBorder: true,
        activeMethod
      }"
      v-on="events"
    >
      <template #beforeTools>
        <div :class="baseClass + '-filter-input'">
          <label>{{ $t('cm.label.eqp') }}</label>
          <a-input
            v-model:value="filterEqp"
            allow-clear
            :style="{ width: '220px', marginLeft: '5px' }"
            :defadivt-active-first-option="false"
          ></a-input>
          <a-button type="primary" style="margin-left: 10px" @click="search">{{
            $t('eesBasic.searchCondition.button.search')
          }}</a-button>
        </div>
      </template>
    </vxe-grid>
  </div>
  <Schedual
    ref="schedualRef"
    :is-edit="scheduleInfo.isEdit"
    :data-type-str="scheduleInfo.dataTypeStr"
    :eqp-id="scheduleInfo.eqpId"
    :schd-job-info="scheduleInfo.schedualForm"
    @refresh="refresh"
  ></Schedual>
</template>
<style scoped lang="less">
@import url('@/assets/style/variable.less');
.cm-eqp-data-sources {
  height: 100%;
  width: 100%;
}
.cm-eqp-data-sources-filter-input {
  position: absolute;
  left: 190px;
  top: 50%;
  margin-top: -15px;
  display: flex;
  align-items: center;
}
</style>
