<script lang="ts" setup>
import { ref, computed } from 'vue';
import { datasetGroupStore } from '@/views/cm-home/cm-main/add-dataset/dataset-group-store';
import CommonTitle from '@/views/cm-home/cm-main/add-dataset/common-title/index.vue';
import GroupTable from '@/views/cm-home/cm-main/add-dataset/common-grid/group-table.vue';
import NoData from '@/views/cm-home/cm-main/add-dataset/common-grid/no-data.vue';
import { handleCopyData } from '@/views/cm-home/cm-main/add-dataset/config';
import { showWarning, EesButtonTip } from '@futurefab/ui-sdk-ees-basic';
import { Splitpanes, Pane } from 'splitpanes';

const props = withDefaults(
  defineProps<{
    runByModel: string;
    columns: any[];
    maxGroupCount?: number;
    allMetrologyCols: string[];
  }>(),
  {
    runByModel: 'wafer',
    columns: () => [],
    maxGroupCount: 0,
    allMetrologyCols: () => []
  }
);

const group = defineModel<string>('group');
const emits = defineEmits(['handleOriginTableAdd', 'reloadLeftBottomTableOptions']);

const groupStore = datasetGroupStore;
const groupWarpRef = ref();

const handleDelete = () => {
  const allCheckData = [];
  for (const [key, value] of groupStore.tempCheckData) {
    groupStore.handleDelete(key, value);
    handleCopyData('delete', key, value, groupStore, group.value);
    allCheckData.push(...value);
  }
  // 清除选中的tempCheckData
  groupStore.handleTempDataClear();
  // 清除数据为空的分组
  groupStore.handleClearEmpty();
  // 删除数据的时候,需要将原数据回显回去
  emits('handleOriginTableAdd', allCheckData);
};
const handleClear = () => {
  const allCheckData = [];
  for (const [key, value] of groupStore.checkData) {
    // 防止复制的内容回到选择表内
    if (!groupStore.copySet.has(key)) {
      allCheckData.push(...value);
    }
  }
  emits('handleOriginTableAdd', allCheckData);
  // 清空选择的数据
  groupStore.handleClear();
};
// 设置分组区域高度，默认高度260px
const totalPanesHeight = computed(() => {
  return groupStore.checkData?.size * 265;
});
// 设置dataset分组所占百分比
const paneSize = computed(() => {
  return groupStore.checkData?.size > 0 ? 100 / groupStore.checkData?.size : 0;
});
// 右键移动分组
const clickGrouping = (newGroupName: string, data: any, oldGroupName: string) => {
  if (['Add Dataset', 'Move Dataset'].includes(newGroupName)) return;
  if (groupStore.copySet.has(newGroupName)) {
    showWarning('cm.tips.noAddCopyGroup');
    return;
  }
  // 处理新增分组数据
  groupStore.handleAdd(newGroupName, data);
  // 处理原分组数据
  groupStore.handleDelete(oldGroupName, data);
  // 清除数据为空的分组
  groupStore.handleClearEmpty();
};
const shouldReload = ref<boolean>(false);
const reloadTableOptions = () => {
  shouldReload.value = !shouldReload.value;
};
const reloadLeftBottomTableOptions = () => {
  emits('reloadLeftBottomTableOptions');
};

defineExpose({ handleClear, reloadTableOptions });
</script>

<template>
  <div class="dataset-modal-content-right" :key="groupStore.key">
    <div ref="groupWarpRef" class="group-container">
      <CommonTitle
        v-model:box-ref="groupWarpRef"
        :title="$t('cm.title.datasetConfig')"
        :count="groupStore.checkData?.size"
        :border="true"
        :borderBottom="true"
      >
        <template #right-icon>
          <EesButtonTip
            is-border
            :marginRight="10"
            icon="#icon-btn-delete"
            :text="$t('cm.btn.deleteDataset')"
            :disabled="groupStore.checkData?.size <= 0"
            class="add-btn"
            @click="handleDelete"
          />
          <EesButtonTip
            is-border
            icon="#icon-btn-clear"
            :text="$t('cm.btn.clearDataset')"
            :disabled="groupStore.checkData?.size <= 0"
            class="delete-btn"
            @click="handleClear"
          />
        </template>
      </CommonTitle>
      <div v-if="groupStore.checkData?.size > 0" class="group-box">
        <splitpanes class="default-theme" horizontal :style="{ height: totalPanesHeight + 'px' }">
          <pane
            :size="paneSize"
            v-for="[key, value] of groupStore.checkData"
            :key="key"
            class="group-item"
          >
            <GroupTable
              v-if="value.length > 0"
              :run-by-model="runByModel"
              :columns="columns"
              :group="key"
              :data="value"
              :group-store="datasetGroupStore"
              :max-group-count="maxGroupCount"
              :allMetrologyCols="allMetrologyCols"
              v-model:shouldReload="shouldReload"
              @clickGrouping="clickGrouping"
              @reloadLeftBottomTableOptions="reloadLeftBottomTableOptions"
            />
          </pane>
        </splitpanes>
      </div>
      <NoData v-else :border="false" />
    </div>
  </div>
</template>

<style lang="less" scoped>
@import url('@/assets/style/variable.less');
.dataset-modal-content-right {
  height: 100%;
  .group-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: scroll;
    .group-box {
      overflow: scroll;
      padding: 14px;
      height: calc(100% - 52px);
      // 显示滚动条
      &:hover {
        &::-webkit-scrollbar-thumb {
          background-color: @bg-scroll-color;
        }
      }
      &::-webkit-scrollbar-corner {
        background-color: @bg-scroll-color;
      }
    }
  }
}
</style>
