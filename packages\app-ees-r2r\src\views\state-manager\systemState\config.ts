import { VXETable, type VxeGridProps, t } from '@futurefab/vxe-table';
import { tableDefaultConfig } from '@futurefab/ui-sdk-ees-basic';
const toolAttr = { zoom: false, import: false, export: false, refresh: false, custom: false };
const colAttr = { treeNode: true, slots: { default: 'item' } };
export const xConfig = {
  id: 'systemState',
  borderOutLine: [0, 0, 0, 0],
  stripe: false,
  toolbarConfig: { border: false, tableName: 'Function', ...toolAttr },
  columns: [{ field: 'name', ...colAttr }],
  treeConfig: {
    accordion: false, // 一层只允许展开一个节点
    expandAll: true // 默认是否全部展开
  }

  // treeConfig: {
  //     transform: true,
  //     rowField: 'id',
  //     parentField: 'pid',
  //     trigger: 'row',
  // },
  // rowConfig: {
  //     keyField: 'rawId',
  // },
};
export const dConfig = tableDefaultConfig(
  {
    id: 'systemStateItems',
    borderOutLine: [1, 0, 0, 0],
    stripe: false,
    toolbarConfig: {
      border: false,
      tableName: 'Items',
      slots: {
        beforeTools: 'beforeTools'
      },
      ...toolAttr
    },
    columns: [{ field: 'itemName', ...colAttr }],
    treeConfig: {
      // children: 'children',
      accordion: false, // 一层只允许展开一个节点
      expandAll: true // 默认是否全部展开
    }
  },
  false
);
