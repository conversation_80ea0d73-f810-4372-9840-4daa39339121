<script lang="ts">
import { t } from '@futurefab/vxe-table';
import { showWarning, stringToDBLength } from '@futurefab/ui-sdk-ees-basic';
import { defineComponent, reactive, type ComponentOptions } from 'vue';
import { addUserComment } from '@futurefab/ui-sdk-api';
import { summaryInfo as buttonIds } from '@/log-config/index';
import type { CommentObj } from '../../interface';
export default defineComponent({
  name: 'UserComment',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    currentInfo: {
      type: Object,
      default: () => null as CommentObj | null
    }
  },
  emits: ['update:visible', 'getComment'],
  setup(props, { emit }) {
    const params = reactive({
      eventName: '',
      comment: ''
    });
    const events = {
      handleOk: () => {
        emit('update:visible', false);
      },
      handleClick: async ({ type }: { type: any }) => {
        if (type == 'confirm') {
          params.eventName = params.eventName.trim();
          if (!params.eventName) {
            showWarning(t('common.tip.required', { name: t('common.title.eventName') }));
            return new Error('');
          } else if (stringToDBLength(params.eventName) > 128) {
            showWarning(
              t('common.tip.inputLength', {
                name: t('common.title.eventName'),
                max: '128'
              })
            );
            return new Error('');
          }
          try {
            const result: any = await addUserComment({
              headerParams: {
                log: 'Y',
                page: buttonIds.pageId,
                action: 'summary-comment-history-delete'
              },
              bodyParams: {
                eqpModuleId: props.currentInfo.eqpModuleId,
                eventDtts: props.currentInfo.timestamp,
                eventName: params.eventName,
                comment: params.comment
              }
            });
            if (result.status == 'SUCCESS') {
              emit('getComment', {
                eventName: params.eventName,
                comment: params.comment
              });
              emit('update:visible', false);
            }
          } catch (e) {
            emit('update:visible', false);
          } finally {
            emit('update:visible', false);
          }
        } else {
          emit('update:visible', false);
        }
      }
    };
    return {
      params,
      events,
      props
    };
  }
}) as ComponentOptions;
</script>
<template>
  <vxe-modal
    v-model="props.visible"
    :title="$t('summary.useComment')"
    :confirm-button-text="$t('common.btn.save')"
    show-footer
    type="confirm"
    :before-hide-method="events.handleClick as any"
  >
    <a-input
      v-model:value="params.eventName"
      :placeholder="$t('common.tip.inputName', { name: $t('common.title.eventName') })"
      maxlength="128"
    ></a-input>
    <a-textarea
      v-model:value="params.comment"
      style="margin-top: 10px"
      :auto-size="{ minRows: 3, maxRows: 5 }"
      :placeholder="$t('common.tip.inputName', { name: $t('common.title.comment') })"
      maxlength="128"
    />
    <template #footer>
      <div class="modal-footer">
        <p>
          <a-button
            type="primary"
            style="margin-right: 10px"
            @click="events.handleClick({ type: 'confirm' })"
          >
            {{ $t('common.btn.confirm') }}
          </a-button>
          <a-button class="btn" @click="events.handleClick({ type: 'cancel' })">{{
            $t('common.btn.cancel')
          }}</a-button>
        </p>
      </div>
    </template>
  </vxe-modal>
</template>
