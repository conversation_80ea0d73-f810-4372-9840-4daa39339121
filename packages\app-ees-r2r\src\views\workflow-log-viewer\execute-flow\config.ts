import { VXETable } from '@futurefab/vxe-table';
import { tableDefaultConfig } from '@futurefab/ui-sdk-ees-basic';
export default tableDefaultConfig({
  id: 'log-viewer-table',
  borderOutLine: [0, 0, 0, 0],
  columns: [
    {
      field: 'workflowType',
      title: 'common.field.type',
      sortable: true,
      minWidth: '180px',
      filters: [{ data: '' }]
    },
    {
      field: 'workflowName',
      title: 'common.field.name',
      sortable: true,
      minWidth: '180px',
      filters: [{ data: '' }]
    },
    {
      field: 'version',
      title: 'common.field.version',
      sortable: true,
      minWidth: '130px',
      align: 'right',
      filters: [{ data: '' }]
    },
    {
      field: 'description',
      title: 'common.field.description',
      sortable: true,
      minWidth: '150px',
      filters: [{ data: '' }],
      filterRender: {
        name: '$input'
      }
    },
    {
      field: 'createBy',
      title: 'common.field.createBy',
      sortable: true,
      minWidth: '150px',
      filters: [{ data: '' }],
      filterRender: {
        name: '$input'
      }
    },
    {
      field: 'createDtts',
      title: 'common.field.createDtts',
      sortable: true,
      minWidth: '200px',
      filters: [{ data: '' }],
      filterRender: {
        name: '$input'
      }
    },
    {
      field: 'lastUpdateBy',
      title: 'common.field.lastUpdateBy',
      sortable: true,
      minWidth: '150px',
      filters: [{ data: '' }],
      filterRender: {
        name: '$input'
      }
    },
    {
      field: 'lastUpdateDtts',
      title: 'common.field.lastUpdateDtts',
      sortable: true,
      minWidth: '200px',
      filters: [{ data: '' }],
      filterRender: {
        name: '$input'
      }
    }
  ],
  exportConfig: {
    filename: 'WorkflowLogViewer_FlowInfo.xlsx'
  },
  toolbarConfig: {
    tableName: 'workflowLogViewer.btn.flowInfo',
    refresh: true,
    import: false,
    export: true,
    tools: [],
    border: false
  }
});
