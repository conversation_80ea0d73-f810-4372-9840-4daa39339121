<script lang="ts" setup>
import type { TreeProps } from '@futurefab/ant-design-vue';
import type { MenuInfo } from '@futurefab/ant-design-vue/es/menu/src/interface';
import type { MenuClickType } from './interface';
import { useAttrs } from 'vue';
import { CheckCircleTwoTone } from '@futurefab/icons-vue';
import { customColor } from '@/utils/custom-color';

type MenuType = { key: string; value: string; isPermission: boolean };
const props = defineProps<{
  treeData: TreeProps['treeData'];
  menuList?: MenuType[];
  checkable?: boolean;
  selectedKeys?: (string | number)[];
  isShow?: boolean;
}>();
const emit = defineEmits<{
  (event: 'onMenuClick', val: MenuClickType): void;
  (event: 'onSelect', conditions: any): void;
  (
    event: 'onCheck',
    {
      checkedKeys,
      checkedNodes
    }: { checkedKeys: string[]; checkedNodes: { title: string; key: string }[] }
  ): void;
}>();
const attrs = useAttrs();
function getMenuList(exclude: string[]) {
  return props.menuList
    ? props.menuList!.filter(
        ({ key, isPermission }) => (!exclude || !exclude.includes(key)) && isPermission
      )
    : [];
}

function onSelect(value: number[], { node }: any) {
  if (node && node.extraParams && value.length) {
    emit('onSelect', node.extraParams);
  }
}
const onCheck = (
  checkedKeys: string[],
  e: { checked: boolean; checkedNodes: { title: string; key: string }[] }
) => {
  emit('onCheck', { checkedKeys, checkedNodes: e.checkedNodes });
};
</script>

<template>
  <div v-isLoading="{ isShow }" class="left-tree scrollbar-wrap-hover">
    <a-tree
      v-if="treeData?.length"
      class="overwrite-ant-tree-style"
      :checkable="checkable"
      :tree-data="treeData"
      block-node
      :selected-keys="selectedKeys"
      v-bind="attrs"
      @select="onSelect"
      @check="onCheck"
    >
      <template #title="{ title, excludeMenu, dataSourceType, extraParams, validateYn }">
        <a-dropdown
          :trigger="['contextmenu']"
          overlay-class-name="overwrite-dropdown-right-contextmenu"
        >
          <div class="left-tree-title">
            {{ title }}
            <check-circle-two-tone
              v-if="validateYn === true"
              :two-tone-color="customColor.twoToneColor"
            />
          </div>
          <template #overlay>
            <a-menu
              v-if="getMenuList(excludeMenu).length"
              @click="
                (menu: MenuInfo) => emit('onMenuClick', { menu, dataSourceType, extraParams })
              "
            >
              <a-menu-item v-for="item in getMenuList(excludeMenu)" :key="item.key">{{
                item.value
              }}</a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </template>
    </a-tree>
  </div>
</template>

<style scoped lang="less">
@import url('@/assets/style/variable.less');
.left-tree {
  position: relative;
  height: 100%;
  width: 100%;
  background-color: @bg-block-color;
  border-radius: @border-radius-xs;
  border: 1px solid @border-color;
  overflow-y: auto;
  overflow-x: hidden;
  :deep(.ant-tree-treenode) {
    height: 32px !important;
  }
  :deep(.ant-tree-treenode-checkbox-checked) {
    .ant-tree-title {
      font-weight: @font-weight-lg;
    }
  }
}
</style>
<style lang="less">
@import url('@/assets/style/variable.less');
.overwrite-dropdown-right-contextmenu {
  .ant-dropdown-menu,
  .ant-dropdown-menu-submenu .ant-dropdown-menu {
    border-radius: @border-radius-xss;
    box-shadow: @right-menu-box-shadow;
    background: @right-menu-bg-block-color;
    color: @color-default;
    .ant-dropdown-menu-item {
      height: 32px;
      padding: @padding-basis - 2; // 6px
      border-radius: @border-radius-xs;
      cursor: pointer;
      border-radius: @border-radius-xs;
      box-sizing: border-box;
      color: @color-default;
      &:hover {
        background-color: @right-menu-item-hover-bg-color;
      }
      &:active {
        background-color: @right-menu-item-active-bg-color;
      }
    }
  }
}
</style>
<!-- <style lang="less">
@import url('@/assets/style/variable.less');
.left-tree {
    // .ant-tree-node-content-wrapper:hover {
    //     background: none;
    // }
    // .ant-tree-checkbox-checked {
    //     .ant-tree-checkbox-inner {
    //         border-color: @primary-color;
    //         background: @primary-color;
    //     }
    //     &::after {
    //         border: none;
    //     }
    // }
    // .ant-tree-checkbox .ant-tree-checkbox-inner:hover {
    //     border-color: @primary-color;
    // }
    // .ant-tree .ant-tree-node-content-wrapper.ant-tree-node-selected {
    //     background: @primary-color;
    //     color: #fff;
    // }
    &-title {
        // display: flex;
        // flex-wrap: nowrap;
        // align-items: center;
        // span.anticon-check-circle {
        //     margin-left: 4px;
        // }
    }
}
</style> -->
