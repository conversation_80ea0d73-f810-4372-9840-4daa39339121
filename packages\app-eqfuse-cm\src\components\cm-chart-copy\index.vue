<script setup lang="ts">
import { CopyOutlined, DownloadOutlined, LoadingOutlined } from '@futurefab/icons-vue';
import { copyChart, downloadChart } from './index'
import { ref } from 'vue';
import { successInfo } from '@futurefab/ui-sdk-ees-basic';
const props = withDefaults(defineProps<{
    name?: string;
}>(), {
    name: 'cm-chart'
});
const chartBoxRef = ref();
const copyLoading = ref(false);
const downloadLoading = ref(false);
const hasCopy = window?.navigator?.clipboard;
const copy = async () => {
    if (copyLoading.value) {
        return;
    }
    copyLoading.value = true;
    try {
        const res = await copyChart(chartBoxRef.value, 'snapdom');
        if (res) {
            successInfo('common.tip.copySuccess');
        }
    } catch(e) {
    } finally {
        copyLoading.value = false;
    }
};
const download = async () => {
    if (downloadLoading.value) {
        return;
    }
    downloadLoading.value = true;
    try {
        const res = await downloadChart(chartBoxRef.value, 'snapdom', props.name);
        if (res) {
            successInfo('common.tip.actionSuccess');
        }
    } catch(e) {
    } finally {
        downloadLoading.value = false;
    }
};
const baseClass = 'cm-chart-copy';
</script>

<template>
  <div :class="baseClass">
    <div :class="baseClass + '-copy-box'">
        <div v-if="hasCopy" :class="baseClass + '-copy-box-item'" @click="copy" >
            <CopyOutlined v-if="!copyLoading" />
            <LoadingOutlined v-else />
            {{ 'Copy Chart' }}
        </div>
        <div :class="baseClass + '-copy-box-item'" @click="download">
            <DownloadOutlined v-if="!downloadLoading" />
            <LoadingOutlined v-else />
            {{ 'Down Chart' }}
        </div>
    </div>
    <div ref="chartBoxRef" :class="baseClass + '-chart-box'">
        <slot></slot>
    </div>
  </div>
</template>

<style scoped lang="less">
@import url('@/assets/style/variable.less');
.cm-chart-copy {
    position: relative;
    height: 100%;
    width: 100%;
    &-copy-box {
        z-index: 10;
        position: absolute;
        display: flex;
        flex-wrap: nowrap;
        top: 0;
        right: 0;
        gap: 20px;
        width: 0;
        overflow: hidden;
        padding: 0;
        &-item {
            cursor: pointer;
            font-size: 12px;
        }
    }
    &:hover &-copy-box {
        padding: 8px 15px;
        width: auto;
    }
    &-chart-box {
        background-color: @bg-color;
        height: 100%;
        width: 100%;
    }
}
</style>