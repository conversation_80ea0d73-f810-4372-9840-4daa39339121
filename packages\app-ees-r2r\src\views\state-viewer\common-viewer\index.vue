<script lang="ts" setup>
import { ref, reactive, onBeforeMount, inject, computed, watch } from 'vue';
import { xGetConfig } from './config';
import { t, type VxeTablePropTypes, type VxeGridProps } from '@futurefab/vxe-table';
import {
  setHeaderSelectFilter,
  successInfo,
  showWarning,
  type EESPermissionBtn,
  getPermissionButton,
  EesButtonTip,
  EesSearchConditionLayer,
  useSearchConditionLayer
} from '@futurefab/ui-sdk-ees-basic';
import { Splitpanes, Pane } from 'splitpanes';
import {
  getUserDefineStateCondition,
  getModelList,
  getChamberList,
  getStateViewer,
  getStateViewerDetail,
  getStateViewerOption,
  exportStateViewer,
  getUserStateViewer,
  getUserStateViewerDetail,
  getUserStateViewerOption,
  exportUserStateViewer
} from '@futurefab/ui-sdk-api';
import { APP_NAME } from '@/utils/constant';
import SearchCondition from '@/components/search/index.vue';
import { exportToExcel, filterMethod, getAreaValue } from '@/utils/tools';
import { stateViewer as buttonIds } from '@/log-config/index';
// import mock from 'C:/Projects/mock/stateViewer.json';
import cardList from '@/components/card-list/index.vue';
import DetailReset from './detail-reset/index.vue';
import DetailHistory from './detail-history/index.vue';
import ResetHistory from './reset-history/index.vue';
import type { ConditionItem, SearchItem } from '@/components/search/interface';
import {
  sharedStateCnText,
  sharedStateUsText,
  stateCnText,
  stateUsText
} from '@/docs/state-viewer/index';
import type { GetButtonAuthority } from '@/views/setup-data-manager/interface';

const props = defineProps({
  type: String
});
const getButtonAuthority = inject('getButtonAuthority');
const buttonAuthority = computed(() =>
  (getButtonAuthority as GetButtonAuthority)().buttonAuthority()
);
// const auth = reactive({
//     buttonAuthority: {} as EESPermissionBtn
// });
const xCondition = ref();
const { layerFn, isDisabled, inputData, searchFormFn } = useSearchConditionLayer(xCondition);
const xFilterCondition = ref();
const {
  layerFn: layerFnTable,
  isDisabled: isDisabledTable,
  inputData: inputDataTable,
  searchFormFn: searchFormFnTable
} = useSearchConditionLayer(xFilterCondition);
const xGrid = ref();
const dGrid = ref();
const xTable = reactive({
  options: {} as VxeGridProps,
  data: [] as any[],
  loading: false,
  optionMap: new Map(),
  keyList: [] as any[],
  areaList: [] as any[],
  rules: {} as VxeTablePropTypes.EditRules,
  query: {} as any,
  currentRow: {} as any,
  addCols: [] as any,
  selectedState: {} as any,
  totalPage: 0,
  currentPage: 1,
  pageSize: 20,
  column: {} as any,
  listReqCount: 0
});
const selected = ref<any>({});
const params = reactive({
  areaList: [] as string[],
  modelList: [],
  chamberList: [],
  conditionParamsFilter: {} as ConditionItem,
  conditionParams: {} as ConditionItem,
  searchParams: {} as SearchItem,
  routePath: `/${APP_NAME}/state-viewer`,
  cardList: [],
  cardListAll: [],
  activeItem: '',
  modelOptions: [] as any,
  modelFilter: null
});
const formatAliasFunc = (
  stateAlias: string | null | any[],
  stateKeyValue: string | null | any[]
) => {
  let result: any[] = [];
  let aliasArr: any[] = [];
  let stateKeyValueArr: any[] = [];
  if (stateAlias) {
    if (Array.isArray(stateAlias)) {
      aliasArr = stateAlias;
    } else {
      aliasArr = stateAlias.split(',');
    }
  } else {
    if (Array.isArray(stateKeyValue)) {
      result = stateKeyValue;
    } else {
      result = stateKeyValue?.split(',') as any;
    }
  }
  if (stateKeyValue) {
    if (Array.isArray(stateKeyValue)) {
      stateKeyValueArr = stateKeyValue;
    } else {
      stateKeyValueArr = stateKeyValue.split(',');
    }
  } else {
    if (Array.isArray(stateKeyValue)) {
      result = stateKeyValue;
    } else {
      result = stateKeyValue?.split(',') as any;
    }
  }
  aliasArr.length &&
    aliasArr.forEach((alias: string | null, index: number) => {
      if (alias) {
        result.push(alias);
      } else {
        result.push(stateKeyValueArr[index]);
      }
    });
  return result;
};
const setColumn = async (resData: any) => {
  if (!resData.column) return;
  console.log('setColumn----', resData.column, xTable.query);
  // 处理 displayName 相关的逻辑
  let stateKeyAliasData: any[] = []; // 实际展示的 State Key Alias
  let stateValueAliasData: any[] = []; // 实际展示的 State Value Alias
  if (props.type === 'user') {
    stateKeyAliasData = formatAliasFunc(
      resData.column['STATE KEY ALIAS'],
      resData.column['STATE KEY']
    );
    stateValueAliasData = formatAliasFunc(
      resData.column['STATE VALUE ALIAS'],
      resData.column['STATE VALUE']
    );
  }
  xTable.column = {
    ...resData.column,
    'STATE KEY ACTUAL': stateKeyAliasData,
    'STATE VALUE ACTUAL': stateValueAliasData
  };
  let cols: any = [];
  Object.entries(resData.column).forEach((item: any) => {
    // console.log('resCols', item);
    if (!['STATE KEY', 'STATE VALUE', 'STATE KEY ALIAS', 'STATE VALUE ALIAS'].includes(item[0])) {
      let minWidth = item[1].length * 16 > 100 ? item[1].length * 16 : 100;
      if (item[0].toLowerCase().includes('time') || item[0].toLowerCase().includes('dtt')) {
        minWidth = 200;
      }
      cols.push({
        field: item[1],
        title: item[0],
        sortable: true,
        minWidth,
        filters: [],
        filterMethod
        // align: item[1] === 'RESET_YN' ? 'center' : 'left',
        // type: item[1] === 'RESET_YN' ? 'columnCheckbox-default' : '',
      });
    } else {
      if (!['STATE KEY ALIAS', 'STATE VALUE ALIAS'].includes(item[0])) {
        cols.push({
          title: item[0],
          align: 'center',
          minWidth: 180,
          children: item[1].map((v: any, index: number) => {
            const minWidth = v.length * 16 > 100 ? v.length * 16 : 100;
            // 增加 displayName，后端字段的field需要处理
            let stateKeyValueAlias = '';
            if (item[0] == 'STATE KEY') {
              stateKeyValueAlias = 'STATE KEY ALIAS';
            } else if (item[0] == 'STATE VALUE') {
              stateKeyValueAlias = 'STATE VALUE ALIAS';
            }
            const stateKeyValueAliasColumn = resData.column[stateKeyValueAlias];
            console.log('setColumn----', stateKeyValueAliasColumn);
            return {
              field: v,
              title: (stateKeyValueAliasColumn && stateKeyValueAliasColumn[index]) || v,
              // title: v,
              sortable: true,
              minWidth,
              filters: [],
              filterMethod
            };
          })
        });
      }
    }
  });
  xTable.options.columns = [...xTable.options.columns!.slice(0, 2), ...cols];
  console.log('setColumn', cols);
  // xGrid.value?.reloadColumn(cols);
  setTimeout(() => {
    const columnFieldList = [
      ...Object.values(resData.column),
      ...resData.column['STATE KEY'],
      ...resData.column['STATE VALUE']
    ];
    // const columnFieldList = ['actionModeCd', ...resCols.key, ...resCols.value];
    xGrid.value &&
      setHeaderSelectFilter({
        xGrid: xGrid,
        columnFieldList,
        tableData: xTable.data
      });
  }, 100);
};
const getList = async (isRefresh?: boolean) => {
  if (!xTable.query?.title)
    return showWarning(
      t('common.tip.selectName', {
        name: t(`stateViewer.title.${props.type === 'user' ? 'states' : 'model'}`)
      })
    );
  xTable.listReqCount++;
  let a = xTable.listReqCount;
  xTable.loading = true;
  // const conditionKeys = Object.keys(params.conditionParamsFilter);
  // const stateKeyValue = Object.entries(params.searchParams)
  //     .filter((item: any) => conditionKeys.includes(item[0]) && item[1].length)
  //     .map((item: any) => `${item[0]}=${item[1]}`)
  //     .join(',');
  const stateKey = props.type === 'user' ? 'stateKey' : 'subStateKeyItem';
  console.log('getList', params.searchParams, xTable.query[stateKey]);
  params.searchParams.sortKey =
    (params.searchParams?.sortMap &&
      params.searchParams?.sortMap[xTable.query[stateKey].split(',')[0]]?.toUpperCase()) ||
    'ASC';
  Object.keys(params.searchParams).forEach((key: any) => {
    if (!Object.keys(params.conditionParamsFilter)?.includes(key) && key !== 'sortKey') {
      delete params.searchParams[key];
      delete params.searchParams?.sortMap;
    }
  });
  console.log('searchParams', params.searchParams);
  xTable.data = [];
  const res = await (props.type === 'user'
    ? getUserStateViewerDetail({
        headerParams: {
          log: 'Y',
          page: buttonIds.pageId,
          action: 'user-detail'
        },
        bodyParams: {
          limit: xTable.pageSize,
          page: xTable.currentPage,
          stateName: xTable.query?.activityFunctionName,
          stateKey: xTable.query?.stateKey,
          stateKeySearch: params.searchParams
        }
      }).finally(() => {
        xTable.loading = false;
      })
    : getStateViewerDetail({
        headerParams: {
          log: 'Y',
          page: buttonIds.pageId,
          action: 'system-detail'
        },
        bodyParams: {
          limit: xTable.pageSize,
          page: xTable.currentPage,
          modelName: xTable.query?.modelName,
          stateName: xTable.query?.stateName,
          stateTypeCd: xTable.query?.stateTypeCd,
          stateType: xTable.query?.stateType,
          stateKeySearch: params.searchParams
        }
      }).finally(() => {
        xTable.loading = false;
      }));
  if (res.status !== 'SUCCESS') return;
  if (a !== xTable.listReqCount) return;
  const resData = res?.data;
  // const resData = mock?.list;
  xTable.totalPage = resData?.count;
  xTable.data = resData?.table;
  // resData?.map((v: any) => {
  //     return {
  //         ...v,
  //         ...v.stateKeyMap,
  //         ...v.stateValueMap,
  //     };
  // }) || [];
  // console.log('cols', resCols);
  if (isRefresh) successInfo('common.tip.refreshSuccess');
  setColumn(resData);
};
const getFilterKey = async () => {
  let stateKeys: any[] = [];
  if (props.type === 'user') {
    stateKeys = xTable.query['items'][0].value.split(',');
    // stateKeys = xTable.query['stateKey']?.split(',');
  } else {
    stateKeys = xTable.query['subStateKeyItem']?.split(';');
  }
  console.log('getFilterKey', stateKeys, xTable.query);
  params.conditionParamsFilter = {};
  stateKeys.forEach((key: any, index: number) => {
    params.conditionParamsFilter[key] = {
      title: key,
      code: key,
      index: index,
      sort: index ? '' : 'asc',
      list: [],
      multi: false,
      isInternational: false,
      required: false
    };
  });
  console.log('conditionParamsFilter', params.conditionParamsFilter);
};
const getFilterOption = async () => {
  const res = await (props.type === 'user'
    ? getUserStateViewerOption({
        bodyParams: {
          stateName: xTable.query?.activityFunctionName
        }
      })
    : getStateViewerOption({
        bodyParams: {
          modelName: xTable.query?.modelName,
          stateName: xTable.query?.stateName,
          stateTypeCd: xTable.query?.stateTypeCd
        }
      }));
  if (res.status !== 'SUCCESS') return;
  console.log('getFilterOption', res?.data);
  params.conditionParamsFilter = {};
  res?.data.forEach((item: any, index: number) => {
    const key = Object.keys(item)[0];
    params.conditionParamsFilter[key] = {
      title: key,
      code: key,
      index: index,
      sort: index ? '' : 'asc',
      list: [...item[key]],
      multi: true,
      isInternational: false,
      required: false
    };
  });
  console.log('conditionParamsFilter', params.conditionParamsFilter);
};
const requestItemFilter = (argus: {
  value: string | string[];
  item: string;
  index: number;
  keys: string[];
}) => {
  const { value, item, index, keys } = argus;
  if (index >= keys.length - 1) return false;
  //setupKeyController = new AbortController(); //bodyParams里面多传一个参数signal: setupKeyController.signal,
};
const handleSearchFilter = (argus: any) => {
  params.searchParams = { ...argus };
  xTable.currentPage = 1;
  getList();
};
const data = reactive({
  showReset: false,
  showHistory: false,
  showResetHistory: false
});
const hideResetHistory = () => {
  data.showResetHistory = false;
};
const hideHistory = () => {
  data.showHistory = false;
};
const hideReset = async (refresh = false) => {
  data.showReset = false;
  const item = document.querySelector('.vxe-table--tooltip-wrapper.is--visible');
  if (item) item?.classList.remove('is--visible');
  if (refresh) {
    const row = xGrid.value?.getCurrentRecord();
    await getList();
    xGrid.value?.setCurrentRow(xTable.data.find((v) => v?.rawId === row?.rawId));
  }
};
const onResetHistory = () => {
  if (!xTable.query?.title)
    return showWarning(
      t('common.tip.selectName', {
        name: t(`stateViewer.title.${props.type === 'user' ? 'states' : 'model'}`)
      })
    );
  data.showResetHistory = true;
};
const onHistory = (menuClick?: boolean, row?: any) => {
  if (!xTable.query?.title)
    return showWarning(
      t('common.tip.selectName', {
        name: t(`stateViewer.title.${props.type === 'user' ? 'states' : 'model'}`)
      })
    );
  const selectData = menuClick ? [row] : xGrid.value.getCheckboxRecords();
  if (!selectData.length) return showWarning('common.tip.selectData');
  selected.value = selectData;
  data.showHistory = true;
};
const onReset = (isModel?: boolean, menuClick?: boolean, row?: any) => {
  if (!xTable.query?.title)
    return showWarning(
      t('common.tip.selectName', {
        name: t(`stateViewer.title.${props.type === 'user' ? 'states' : 'model'}`)
      })
    );
  const selectData = menuClick ? [row] : xGrid.value.getCheckboxRecords();
  if (!isModel && selectData.length !== 1) return showWarning('common.tip.selectRow');
  selected.value = selectData[0];
  xTable.query.isModel = !!isModel;
  data.showReset = true;
};

const exportMethod = async () => {
  if (!xTable.query?.title)
    return showWarning(
      t('common.tip.selectName', {
        name: t(`stateViewer.title.${props.type === 'user' ? 'states' : 'model'}`)
      })
    );
  if (!xTable.data.length) return showWarning(t(`common.tip.exportCommonTip`));
  console.log(xTable.query);
  Object.keys(params.searchParams).forEach((key: any) => {
    if (
      !xTable.query[props.type === 'user' ? 'stateKey' : 'subStateKeyItem'].includes(key) &&
      key !== 'sortMap'
    ) {
      delete params.searchParams[key];
      params.searchParams?.sortMap && delete params.searchParams?.sortMap[key];
    }
  });
  console.log('searchParams', params.searchParams);
  const res = await (props.type === 'user'
    ? exportUserStateViewer({
        headerParams: {
          log: 'Y',
          page: buttonIds.pageId,
          action: buttonIds.user.export
        },
        bodyParams: {
          exportFileName: `ShareStateViewer - ${xTable.query?.title}`,
          stateName: xTable.query?.activityFunctionName,
          stateKey: xTable.query?.stateKey,
          stateKeySearch: params.searchParams
        }
      })
    : exportStateViewer({
        headerParams: {
          log: 'Y',
          page: buttonIds.pageId,
          action: buttonIds.system.export
        },
        bodyParams: {
          exportFileName: `StateViewer - ${xTable.query?.stateName}`,
          modelName: xTable.query?.modelName,
          stateName: xTable.query?.stateName,
          stateTypeCd: xTable.query?.stateTypeCd,
          stateType: xTable.query?.stateType,
          stateKeySearch: params.searchParams
        }
      }));
  if (res.status !== 'SUCCESS') return;
  exportToExcel(res.data);
};
const gridEvents = {
  toolbarToolClick: ({ code, tool }: any) => {
    console.log(code, tool);
    const { menuClick, row } = tool;
    switch (code) {
      case 'resetHistory':
        onResetHistory();
        break;
      case 'history':
        onHistory(menuClick, row);
        break;
      case 'reset':
        onReset(false, menuClick, row);
        break;
      case 'refresh':
        getList(true);
        break;
      case 'exportData':
        exportMethod();
        break;
    }
  },
  pageChange: ({ currentPage, pageSize }: any) => {
    console.log('pageChange', currentPage, pageSize);
    xTable.currentPage = currentPage;
    xTable.pageSize = pageSize;
    getList();
  }
  // currentChange: ({ row }: { row: any }) => {
  //     console.log('currentChange', row);
  //     xTable.currentRow = row;
  // },
};

const formatCard = (data: any) => {
  return data.map((v: any) => {
    // 处理 displayName 相关的逻辑
    let stateKeyAliasData: any[] = []; // 实际展示的 State Key Alias
    let stateValueAliasData: any[] = []; // 实际展示的 State Value Alias
    if (props.type === 'user') {
      stateKeyAliasData = formatAliasFunc(v.stateKeyAlias, v.stateKey);
      stateValueAliasData = formatAliasFunc(v.stateValueAlias, v.stateValue);
    }
    return props.type === 'user'
      ? {
          ...v,
          key: v.activityFunctionName,
          title: v.activityFunctionName,
          items: [
            {
              name: 'State Key',
              value: stateKeyAliasData.join(','),
              icon: 'icon-tree-state-key'
            },
            {
              name: 'State Item',
              value: stateValueAliasData.join(','),
              icon: 'icon-link'
            }
          ]
        }
      : {
          ...v,
          key: v.modelName + '^' + v.stateName + ' ^ ' + v.modelGrpName,
          title: v.modelName,
          items: [
            {
              name: 'State Name',
              value: v.stateName,
              icon: 'icon-tree-state-name'
            },
            {
              name: 'Category',
              value: v.categoryValue,
              icon: 'icon-tree-category'
            },
            { name: 'Function', value: v.function, icon: 'icon-tree-function' },
            {
              name: 'State Type',
              value: v.stateType,
              icon: 'icon-tree-type'
            },
            {
              name: 'State Key',
              value: v.subStateKeyItem,
              icon: 'icon-tree-state-key'
            }
          ]
        };
  });
};
const getCardList = async () => {
  const res = await (props.type === 'user'
    ? getUserStateViewer({
        headerParams: {
          log: 'Y',
          page: buttonIds.pageId,
          action: 'user-card-list'
        },
        bodyParams: {
          areaList: params.areaList,
          eqpModelList: params.modelList,
          chamberList: params.chamberList,
          routePath: params.routePath
        }
      })
    : getStateViewer({
        headerParams: {
          log: 'Y',
          page: buttonIds.pageId,
          action: 'system-card-list'
        },
        bodyParams: {
          areaList: params.areaList,
          eqpModelList: params.modelList,
          chamberList: params.chamberList,
          routePath: params.routePath
        }
      }));
  if (res.status !== 'SUCCESS') return;
  // if (props.type === 'user') return;
  // const data = new Array(20).fill(res.data).flat();
  // const data = mock.key;
  const data = res.data;
  params.cardList = params.cardListAll = formatCard(data);
  params.modelOptions = [...new Set(params.cardList.map((v: any) => v.modelName))].map((v: any) => {
    return {
      label: v,
      value: v
    };
  });
  if (xTable.query.key && !params.cardList.find((v: any) => v.key === xTable.query.key)) {
    params.conditionParamsFilter = {};
    params.activeItem = '';
    xTable.query = {};
    xTable.data = [];
    xTable.totalPage = 0;
    xTable.currentPage = 1;
    xTable.pageSize = 20;
  }
};
const changeModelFilter = (val: any) => {
  console.log('changeModelFilter', val);
  params.cardList = val
    ? params.cardListAll.filter((v: any) => v.modelName === val)
    : params.cardListAll;
};
const changeActive = (data: any) => {
  xTable.query = data;
  params.activeItem = data.key;
  params.searchParams = {};
  xTable.currentPage = 1;
  xTable.pageSize = 20;
  getList();
  getFilterKey();
  // getFilterOption();
};
const requestCondition = async () => {
  const result = await getUserDefineStateCondition({
    headerParams: {
      log: 'Y',
      page: buttonIds.pageId,
      action: 'search-condition'
    },
    bodyParams: {
      routePath: params.routePath
    }
  });
  setVisibleToolMenu();
  if (result.status !== 'SUCCESS') return;
  // const result = mock?.condition;
  if (result.data.dataRestrictionLevel) {
    const list = result.data.dataRestrictionLevel.split(',');
    // const list = ['AREA'];
    const conditionMap: any = {};
    list.forEach((item: string) => {
      if (item == 'AREA') {
        conditionMap['area'] = {
          code: 'area',
          index: 0,
          title: 'common.title.area',
          list: result.data.areaList ? result.data.areaList : [],
          multi: true,
          isInternational: true
          // required: true,
        };
      } else if (item == 'EQP_MODEL') {
        conditionMap['eqpModel'] = {
          code: 'eqpModel',
          index: 1,
          title: 'common.title.eqpModel',
          list: [],
          multi: true,
          isInternational: true
        };
      } else if (item == 'CHAMBER_TYPE') {
        conditionMap['chamberType'] = {
          code: 'chamberType',
          index: 2,
          title: 'common.title.chamberType',
          list: [],
          multi: true,
          isInternational: true
        };
      }
    });
    params.conditionParams = { ...conditionMap };
    console.log('conditionParams', params.conditionParams);
    const keys = Object.keys(params.conditionParams);
    const values = Object.values(params.conditionParams) as any;
    keys.forEach((item: string, index: number) => {
      if (item == 'area') {
        params.areaList = [...getAreaValue(values[index].list)];
      }
    });
  }
  getCardList();
};
let setupKeyController: AbortController | undefined = undefined;
const requestItem = (argus: {
  value: string | string[];
  item: string;
  index: number;
  keys: string[];
}) => {
  const { value, item, index, keys } = argus;
  console.log('requestItem', argus);
  if (index >= keys.length - 1) return false;
  setupKeyController = new AbortController(); //bodyParams里面多传一个参数signal: setupKeyController.signal,
  if (item == 'area' && keys.includes('eqpModel')) {
    if (!value || value.length == 0) {
      if (params.conditionParams['eqpModel']) {
        params.conditionParams['eqpModel'].list = [];
      }
      xCondition.value &&
        xCondition.value.setSelectValue({
          item: 'eqpModel',
          arr: []
        });
    } else {
      getModelList({
        headerParams: {
          log: 'Y',
          page: buttonIds.pageId,
          action: 'search-model'
        },
        bodyParams: {
          signal: setupKeyController.signal,
          areaList: !Array.isArray(value) && value ? [value] : value,
          routePath: params.routePath
        }
      }).then((result: any) => {
        if (result.status == 'SUCCESS') {
          params.conditionParams['eqpModel'].list = (value.length && result.data) || [];
          const eqpModelValue = xCondition.value.getSelectValue('eqpModel');
          if (eqpModelValue && eqpModelValue.length) {
            const list = eqpModelValue.filter(
              (val: string) => result.data && result.data.length && result.data.includes(val)
            );
            xCondition.value.setSelectValue({
              item: 'eqpModel',
              arr: list
            });
          }
        }
      });
    }
  }
  if (item == 'area' && keys.includes('chamberType')) {
    if (!value || value.length == 0) {
      params.conditionParams['chamberType'].list = [];
      xCondition.value.setSelectValue({
        item: 'chamberType',
        arr: []
      });
    } else {
      getChamberList({
        headerParams: {
          log: 'Y',
          page: buttonIds.pageId,
          action: 'search-chamber'
        },
        bodyParams: {
          signal: setupKeyController.signal,
          areaList: !Array.isArray(value) && value ? [value] : value,
          routePath: params.routePath
        }
      }).then((result: any) => {
        if (result.status == 'SUCCESS') {
          params.conditionParams['chamberType'].list = result.data;
          const chamberTypeValue = xCondition.value.getSelectValue('chamberType');
          if (chamberTypeValue && chamberTypeValue.length) {
            const list = chamberTypeValue.filter(
              (val: string) => result.data && result.data.length && result.data.includes(val)
            );
            xCondition.value.setSelectValue({
              item: 'chamberType',
              arr: list
            });
          }
        }
      });
    }
  }
};
const handleSearch = (argus: any) => {
  console.log('handleSearch', argus);
  if (!Array.isArray(argus.area) && argus.area) {
    argus.area = [argus.area];
  } else if (!Array.isArray(argus.eqpModel) && argus.eqpModel) {
    argus.eqpModel = [argus.eqpModel];
  } else if (!Array.isArray(argus.chamberType) && argus.chamberType) {
    argus.chamberType = [argus.chamberType];
  }
  params.areaList = argus.area;
  params.modelList = argus.eqpModel;
  params.chamberList = argus.chamberType;
  params.modelFilter = null;
  getCardList();
};
const clearConditionParamLists = (keys: string[]) => {
  keys.forEach((key) => {
    if (params.conditionParams[key]) {
      params.conditionParams[key].list = [];
    }
  });
};
const handleReset = () => {
  clearConditionParamLists(['eqpModel', 'chamberType']);
};
watch(
  () => props.type,
  (val) => {
    console.log('w-t', val);
    if (!val) return;
    const typeMap: any = {
      user: 'user',
      system: 'system'
    };
    xTable.options = xGetConfig(typeMap[val]);
    // getCardList();
  },
  { deep: true, immediate: true }
);
const setVisibleToolMenu = () => {
  setTimeout(() => {
    // auth.buttonAuthority = (await getPermissionButton('/r2r/state-viewer')) as ButtonAuthority;
    xGrid.value &&
      xGrid.value.setVisibleTools({
        authorityList: buttonAuthority.value.buttonList,
        flag: buttonAuthority.value.allButtonFlag
      });
    dGrid.value &&
      dGrid.value.setVisibleTools({
        authorityList: buttonAuthority.value.buttonList,
        flag: buttonAuthority.value.allButtonFlag
      });
    console.log('buttonAuthority', buttonAuthority.value);
  }, 200);
};
onBeforeMount(async () => {
  requestCondition();
});
const getContent = () => {
  const lang = localStorage.getItem('language');
  return lang == 'en-US'
    ? props.type === 'user'
      ? sharedStateUsText
      : stateUsText
    : props.type === 'user'
      ? sharedStateCnText
      : stateCnText;
};
const showLayer = ref(false);
</script>

<template>
  <div class="common-viewer">
    <div class="common-viewer-content">
      <splitpanes class="default-theme" style="height: 100%; width: 100%">
        <pane class="cardItem-left pane-border-radius-top-left pane-border-radius-bottom-left">
          <card-list
            :title="t(`stateViewer.title.${type === 'user' ? 'states' : 'model'}`)"
            :list="params.cardList"
            v-model:active-item="params.activeItem"
            :has-top-slot="type === 'system'"
            @change-active="changeActive"
          >
            <template #top-search>
              <ees-search-condition-layer
                :is-disabled-search="isDisabled"
                :input-data="inputData"
                :is-show-btn-save-as="false"
                :show-condition-list="false"
                v-on="layerFn"
              >
                <template #search-form>
                  <search-condition
                    ref="xCondition"
                    :condition-params="params.conditionParams"
                    :show-mode="false"
                    v-on="searchFormFn"
                    @do-request="requestItem"
                    @do-search="handleSearch"
                    @do-reset="handleReset"
                  ></search-condition>
                </template>
              </ees-search-condition-layer>
            </template>
            <template #top-button>
              <div v-if="type === 'system'" class="top-button-slot">
                <a-select
                  v-model:value="params.modelFilter"
                  allow-clear
                  :options="params.modelOptions"
                  class="model-filter"
                  :placeholder="t('stateViewer.title.searchModelName')"
                  :not-found-content="t('eesBasic.tips.noData')"
                  @change="changeModelFilter"
                />
                <ees-button-tip
                  v-if="
                    type === 'system' &&
                    (buttonAuthority.allButtonFlag ||
                      buttonAuthority.buttonList.includes(buttonIds.system.resetModel))
                  "
                  icon="#icon-btn-reset"
                  :text="t('common.btn.reset')"
                  :is-border="true"
                  @onClick="onReset(true)"
                >
                </ees-button-tip>
              </div>
            </template>
          </card-list>
        </pane>
        <pane
          class="cardItem-right pane-border-radius-top-right pane-border-radius-bottom-right"
          size="80"
        >
          <div class="common-viewer-table">
            <div class="common-viewer-grid cardItem">
              <vxe-grid
                ref="xGrid"
                class="common-table-grid-maximize-style"
                v-bind="xTable.options"
                :data="xTable.data"
                :edit-rules="xTable.rules"
                :loading="xTable.loading"
                :pager-config="{
                  layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes', 'FullJump'],
                  align: 'right',
                  pageSizes: [10, 15, 20, 25, 100],
                  pageSize: xTable.pageSize,
                  total: xTable.totalPage,
                  currentPage: xTable.currentPage
                }"
                :export-config="{
                  filename: `${
                    type === 'user' ? 'Share' : ''
                  }StateViewer - ${xTable.query?.stateName || xTable.query?.title}.xlsx`,
                  id: buttonIds[type as string].export
                }"
                v-on="gridEvents"
              >
                <template #search-condition>
                  <ees-search-condition-layer
                    v-show="showLayer"
                    :is-disabled-search="isDisabledTable"
                    :input-data="inputDataTable"
                    :is-show-btn-save-as="false"
                    :show-condition-list="false"
                    v-on="layerFnTable"
                  >
                    <template #search-form>
                      <search-condition
                        ref="xFilterCondition"
                        :condition-params="params.conditionParamsFilter"
                        :show-mode="false"
                        :only-input="true"
                        :show-count="2"
                        v-on="searchFormFnTable"
                        @do-request="requestItemFilter"
                        @do-search="handleSearchFilter"
                        @display-search="(val) => (showLayer = val)"
                      ></search-condition>
                    </template>
                  </ees-search-condition-layer>
                </template>
                <template #beforeTools>
                  <vxe-tooltip
                    :use-h-t-m-l="true"
                    :enterable="true"
                    theme="light"
                    :content="getContent()"
                  >
                    <button class="vxe-button type--button icon-type--svgButton">
                      <svg class="icon" aria-hidden="true">
                        <use xlink:href="#icon-btn-help"></use>
                      </svg>
                    </button>
                  </vxe-tooltip>
                </template>
              </vxe-grid>
            </div>
          </div>
        </pane>
      </splitpanes>
    </div>
    <reset-history
      v-if="data.showResetHistory"
      :title="
        t('stateViewer.title.stateResetHistory', {
          name: xTable.query?.stateName
            ? ` - ${xTable.query?.stateName}`
            : xTable.query?.title
              ? ` - ${xTable.query?.title}`
              : ''
        })
      "
      :show="data.showResetHistory"
      :type="type"
      :selected-state="xTable.query"
      @hideSidebar="hideResetHistory"
    />
    <detail-history
      v-if="data.showHistory"
      :title="t('stateViewer.title.stateDetailHistory')"
      :show="data.showHistory"
      :type="type"
      :selected-cols="xTable.column"
      :selected="selected"
      :selected-state="xTable.query"
      @hideSidebar="hideHistory"
    />
    <detail-reset
      v-if="data.showReset"
      :title="t('stateViewer.title.stateReset')"
      :show="data.showReset"
      :type="type"
      :selected-cols="xTable.column"
      :selected="selected"
      :selected-state="xTable.query"
      @hideSidebar="hideReset"
    />
  </div>
</template>

<style lang="less" scoped>
@import url('@/assets/style/variable.less');
@ns: common-viewer;

.@{ns} {
  height: 100%;

  &-content {
    width: 100%;
    height: 100%;
    display: flex;
  }

  .cardItem-left {
    width: 300px;
    min-width: 300px;
  }
  .cardItem-right {
    width: calc(100% - 300px);
    background-color: transparent !important;
  }
  &-table {
    overflow: auto;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  .top-button-slot {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: @margin-basis + 2;
  }
  .model-filter {
    flex: 1;
    margin-right: @margin-btn;
  }

  &-grid {
    height: 100%;
    overflow: hidden;
  }
  // :deep(.col--group) {
  //     &::after {
  //         width: 2px;
  //         height: 100%;
  //         background-color: @bg-block-color;
  //         content: '';
  //         display: inline-block;
  //         vertical-align: middle;
  //         position: absolute;
  //         right: -1px;
  //         bottom: 0;
  //     }
  //}
  :deep(.vxe-grid--pager-wrapper) {
    background-color: @bg-block-color;
  }
}
</style>
