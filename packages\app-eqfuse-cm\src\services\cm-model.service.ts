import { UUIDUtil } from '../utils/uuid.util';
import {
  cancelAnalyze,
  deleteTriggerByTriggerId,
  runAnalyze,
  exportReport,
  getAttributesConfigs,
  getAttributeValuesByContext,
  getCodeParameterGroups,
  getConfigByCodeService,
  getDatasetByTriggerId,
  getDatasetConfigurationByUserId,
  getDataSetConfigurationByWidgetId,
  getDatasetSplit,
  getParamCategorySum,
  getParameters,
  getResultGroup,
  uploadCsvXmlFile
} from '@futurefab/ui-sdk-api';
import { ToolChamberCondition } from '@/model/tool-chamber-condition';
import { CategoryItem } from '@/model/category-item';
import { ParameterGroupItem } from '@/model/parameter-group-item';
import { Parameter } from '@/model/parameter';
import { ChamberMatchingResultResponse } from '@/model/chamber-matching-result-response';
import { ChamberMatchingResultGroup } from '@/model/chamber-matching-result-group';
import { ChamberMatchingConfig } from '@/model/chamber-matching-config';
import { CMAttributeConfig } from '@/model/cm-attribute-config';
import { ReferenceType } from '@/model/reference-type';
import { GroupConfigVO } from '@/model/GroupConfigVO';
import { type TrimOriginData } from '@/views/cm-configuration/interface';
import { showError } from '@futurefab/ui-sdk-ees-basic';
import { cmMainInfo as buttonIds } from '@/log-config';

const _cancelPromise: any = undefined;
export default {
  _cancelPromise,
  _uuid: '',
  _analyzingSate: false,

  getSplitDatasetData(params: any): any {
    return getDatasetSplit({
      bodyParams: params,
      headerParams: {
        log: 'Y',
        page: buttonIds.pageId,
        action: 'get-split-dataset'
      }
    });
  },

  getParameters(
    categoryNames: string[],
    toolChamberConditions: ToolChamberCondition[],
    selectedParameterGroups: string[],
    parameterNames: string[]
  ): any {
    const params = {
      categoryNames: categoryNames,
      toolChamberConditions: toolChamberConditions,
      selectedParameterGroups: selectedParameterGroups,
      parameterNames: parameterNames
    };
    return getParameters({ bodyParams: params });
  },

  uploadFile(fileBody: any): Promise<any> {
    // let headers = new HttpHeaders({enctype:'multipart/form-data'})
    return uploadCsvXmlFile(fileBody);
  },

  startAnalyze() {
    this._uuid = UUIDUtil.new();
    this._analyzingSate = true;
  },
  endAnalyze() {
    this._uuid = null as any;
    this._analyzingSate = false;
  },

  analyze(
    runParams: any,
    groupConfigs: GroupConfigVO[],
    preAnalysisId: string,
    busySpinner: any,
    isDynamicReference: boolean
  ): Promise<ChamberMatchingResultResponse> {
    this.startAnalyze();
    if (!preAnalysisId && busySpinner) {
      busySpinner.showAnalysisSpinner('Uploading...', true);
    }
    let promise = this.uploadFiles(preAnalysisId, groupConfigs);
    return promise.then(() => {
      if (!preAnalysisId && busySpinner) {
        busySpinner.showAnalysisSpinner('Preparing data...', true);
        if (!isDynamicReference) {
          setTimeout(() => {
            if (this._analyzingSate) {
              // check this status since analysis is finished in 10 sec.
              busySpinner.showAnalysisSpinner('Analyzing...', true);
            }
          }, 10000);
        }
      } else if (busySpinner) {
        busySpinner.showAnalysisSpinner('Analyzing...', true);
      }
      let cancel = () => {
        console.log('cancel');
      };
      promise = new Promise((resolve, reject) => {
        cancel = () => {
          reject(new Error('The request has been canceled'));
          return;
        };
        runAnalyze({
          bodyParams: runParams,
          headerParams: {
            log: 'Y',
            page: buttonIds.pageId,
            action: 'run-analyze'
          },
          timeout: 600000
        })
          .then((res: { data: ChamberMatchingResultResponse }) => {
            let responseData = res.data as ChamberMatchingResultResponse;
            resolve(responseData);
            // timeout也会走成功的回调
            busySpinner.showAnalysisSpinner('Analyzing...', false, false);
            this.endAnalyze();
          })
          .catch((err: any) => {
            busySpinner.showAnalysisSpinner('Analyzing...', false, false);
            this.endAnalyze();
            showError('Analysis failure');
          });
      });
      this._cancelPromise = { promise, cancel };

      return promise;
    });
  },

  uploadFiles(preAnalysisId: string, groupConfigs: GroupConfigVO[]) {
    let promise: Promise<any> = Promise.resolve();
    if (!preAnalysisId || preAnalysisId.length < 0) {
      const uploadingFiles = groupConfigs
        .filter((gc) => gc.file && gc.file.name.length > 0)
        .map((gc) => gc.file);
      if (uploadingFiles && uploadingFiles.length > 0) {
        promise = Promise.all(uploadingFiles.map((f) => this.uploadFile({ fileInfo: f })));
      }
    }
    return promise;
  },

  cancelRequest(datasetConfigurationId: number): Promise<any> {
    const params = {
      clientRequestId: this._uuid,
      datasetConfigurationId: datasetConfigurationId
    };
    return cancelAnalyze({
      bodyParams: params,
      headerParams: {
        log: 'Y',
        page: buttonIds.pageId,
        action: 'cancelAnalyze'
      }
    }).then((res: { data: any }) => {
      this._uuid = '';
      return res.data;
    });
  },

  runCancel(datasetConfigurationId: number) {
    this.cancelRequest(datasetConfigurationId).then((result) => {
      if (result.canceled && this._cancelPromise) {
        this._cancelPromise.cancel();
      }
    });
  },

  getResultGroup(
    analysisId: string,
    resultRequestId: string,
    groupConfigId: number,
    similarityThreshold: number,
    criticalThreshold: number,
    warningThreshold: number,
    useDataNormalization: boolean,
    selectedRecipeSteps: string[]
  ): Promise<ChamberMatchingResultGroup> {
    const params = {
      analysisId: analysisId,
      resultRequestId: resultRequestId,
      groupConfigId: groupConfigId,
      similarityThreshold: similarityThreshold,
      criticalThreshold: criticalThreshold,
      useDataNormalization: useDataNormalization,
      warningThreshold: warningThreshold,
      selectedRecipeSteps: selectedRecipeSteps
    };
    return getResultGroup({
      bodyParams: params,
      headerParams: {
        log: 'Y',
        page: buttonIds.pageId,
        action: 'get-result-group'
      }
    }).then((res: { data: any }) => res.data);
  },

  getConfig(code: string): Promise<ChamberMatchingConfig> {
    const api = 'getconfig/' + code;
    const params = null;
    return getConfigByCodeService({
      bodyParams: { code },
      headerParams: {
        log: 'Y',
        page: buttonIds.pageId,
        action: 'get-config'
      }
    });
  },

  getCMConfigValue(code: string): Promise<number> {
    return this.getConfig(code)
      .then((config) => {
        return config.configValue as number;
      })
      .catch((err) => {
        // this.notify.warn('Couldn\'t get maximum context count configuration. \n' + err);
        console.log(err);
        return 1000;
      });
  },

  getAttributeValuesByContext(
    analysisId: string,
    groupConfigId: number,
    lotId: string,
    substrateId: string,
    descriptorValues: string[],
    filteringAttrValues: string[][],
    startDtts: number
  ): Promise<any> {
    const params = {
      analysisId: analysisId,
      lot: lotId,
      wafer: substrateId,
      descriptorValues: descriptorValues.join('_,_'),
      filteringAttributeValueMap: filteringAttrValues.map((arr) => arr.join('_:_')).join('_,_'),
      startDtts: startDtts
    };
    return getAttributeValuesByContext({
      bodyParams: params,
      headerParams: {
        log: 'Y',
        page: buttonIds.pageId,
        action: 'get-attribute-values'
      }
    });
  },

  getDatasetConfiguration(triggerId: string): Promise<any> {
    const api = `cmautomation/getdataset/${triggerId}`;
    const params = null;
    return getDatasetByTriggerId({
      bodyParams: triggerId,
      headerParams: {
        log: 'Y',
        page: buttonIds.pageId,
        action: 'get-dataset-configuration'
      }
    });
  },

  exportReport(exportReportRequest: any): Promise<any> {
    return exportReport({
      bodyParams: exportReportRequest,
      headerParams: {
        log: 'Y',
        page: buttonIds.pageId,
        action: 'export-report'
      }
    });
  },

  getDatasetConfigurationByUserId(): Promise<any> {
    let api = 'getdataset/user/' + this.getUserId();
    return getDatasetConfigurationByUserId({
      bodyParams: { userId: this.getUserId() },
      headerParams: {
        log: 'Y',
        page: buttonIds.pageId,
        action: 'get-dataset-configuration'
      }
    }).then((res: { data: any }) => res.data);
  },

  getDatasetConfigurationByWidgetId(pageId: any): Promise<any> {
    const api = 'getdataset/' + pageId;

    return getDataSetConfigurationByWidgetId({
      bodyParams: { WidgetId: pageId },
      headerParams: {
        log: 'Y',
        page: buttonIds.pageId,
        action: 'get-dataset-configuration'
      }
    });
  },
  deleteTrigger(triggerId: number) {
    const api = `cmautomation/deletetrigger/${triggerId}`;
    return deleteTriggerByTriggerId({
      bodyParams: triggerId,
      headerParams: {
        log: 'Y',
        page: buttonIds.pageId,
        action: 'delete-trigger'
      }
    });
  },

  async getAttributesConfig(): Promise<CMAttributeConfig[]> {
    const api = 'attributes/configs';
    return getAttributesConfigs({
      bodyParams: {},
      headerParams: {
        log: 'Y',
        page: buttonIds.pageId,
        action: 'get-attributes-configs'
      }
    }).then((res: { data: any }) => res.data);
  },

  async getReferenceTypes(): Promise<ReferenceType[]> {
    const referenceTypes = [];
    referenceTypes.push(new ReferenceType('static', 'Static', 'CM_STATIC', false, 'Static'));
    referenceTypes.push(
      new ReferenceType(
        'dynamic_trace',
        'Dynamic Reference with Trace',
        'CM_TRACE',
        false,
        'Dynamic'
      )
    );
    referenceTypes.push(
      new ReferenceType(
        'reference_metrology',
        'Reference by Metrology',
        'CM_METROLOGY',
        false,
        'Metrology'
      )
    );
    return referenceTypes;
  },

  getParameterGroups(): Promise<any> {
    return getCodeParameterGroups({
      bodyParams: {},
      headerParams: {
        log: 'Y',
        page: buttonIds.pageId,
        action: 'get-parameter-groups'
      }
    });
  },

  getParamCategorySum(): Promise<any> {
    return getParamCategorySum({
      bodyParams: {},
      headerParams: {
        log: 'Y',
        page: buttonIds.pageId,
        action: 'get-param-category-sum'
      }
    });
  },

  convertToCSV(paramData: any[], headerList: Array<string>, headerNames: Array<string>): string {
    const separatorChar = ',';
    const array = typeof paramData !== 'object' ? JSON.parse(paramData) : paramData;
    let str = '';
    let row = '';

    for (const index in headerList) {
      let headerName = headerNames[index];
      if (headerName.indexOf(',') > -1) headerName = `"${headerName}"`;
      row += headerName + separatorChar;
    }
    row = row.slice(0, -1);
    str += row + '\r\n';
    for (let i = 0; i < array.length; i++) {
      let line = '';
      for (const index of headerList) {
        line += array[i][index] + separatorChar;
      }
      line = line.slice(0, -1);
      str += line + '\r\n';
    }
    return str;
  },

  getUserId(): string {
    return 'standalonecm';
  }
};
