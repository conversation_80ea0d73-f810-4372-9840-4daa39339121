<script lang="ts" setup>
import { EesNoData } from '@futurefab/ui-sdk-ees-basic';

const props = withDefaults(
  defineProps<{
    border?: boolean;
    specialBorder?: boolean;
  }>(),
  {
    border: true,
    specialBorder: false
  }
);
const className = () => {
  const baseName = props.border ? 'no-data-warp' : 'no-data-no-border-warp';
  const specialName = props.specialBorder ? 'no-data-no-top-border-warp' : '';
  if (specialName) {
    return baseName + ' ' + specialName;
  }
  return baseName;
};
</script>

<template>
  <div :class="className()">
    <ees-no-data></ees-no-data>
  </div>
</template>

<style lang="less" scoped>
@import url('@/assets/style/variable.less');
.no-data-warp {
  color: @text-disabled-color-1;
  height: 100%;
  width: 100%;
  display: flex;
  border: 1px solid @border-color;
  border-radius: 4px;
  background-color: @bg-block-color;
}
.no-data-no-border-warp {
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: @text-disabled-color-1;
  // background-color: @bg-block-color;
}
.no-data-no-top-border-warp {
  border-left: 1px solid @border-color;
  border-right: 1px solid @border-color;
  border-bottom: 1px solid @border-color;
  border-radius: 0 0 4px 4px;
}
</style>
