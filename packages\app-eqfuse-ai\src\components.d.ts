/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    PDFViewer: typeof import('./components/PDFViewer/index.vue')['default']
    PhotoViewer: typeof import('./components/PhotoViewer/index.vue')['default']
    ReferenceTip: typeof import('./components/ReferenceTip/index.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    SvgIcon: typeof import('./components/SvgIcon/index.vue')['default']
  }
}
