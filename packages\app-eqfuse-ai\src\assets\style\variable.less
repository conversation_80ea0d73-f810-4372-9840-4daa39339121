@import './themes/light.less';
@import './themes/dark.less';
@import '@futurefab/ui-sdk-ees-static-files/dist/styles/variable.less';
/* 主题色号 */
@primary-color-1: var(--primary-color-1);
@primary-font-color-3: var(--primary-font-color-3);
@on-primary-color-1: var(--on-primary-color-1);
@on-primary-color-2: var(--on-primary-color-2);

/* 背景色 */
@body-bg-color: var(--body-bg-color);
@bg-color: var(--bg-color);

/* search框颜色 */
@input-error-font-color: var(--input-error-font-color);
@input-br-color: var(--input-br-color);
@input-bg-color: var(--input-bg-color);
@input-disabled-bg-color: var(--input-disabled-bg-color);
@input-font-color: var(--input-font-color);
@select-multiple-bg-color: var(--select-multiple-bg-color);

/**  表格颜色  **/
@th-bg-color: var(--th-bg-color);
@th-font-color: var(--th-font-color);
@td-font-color: var(--td-font-color);
@td-br-color: var(--td-br-color);
@tr-even-bg-color: var(--tr-even-bg-color);

/**  弹框 */
@model-bg-color: var(--model-bg-color);

/**  check勾选框  **/
@check-default-br-color: var(--check-default-br-color);

/**  边框颜色 */
@br-default-color: var(--br-default-color);
@br-default-color-1: var(--br-default-color-1);

/**  card  **/
@card-default-br-color: var(--card-default-br-color);

/* 高亮背景色  **/
@active-bg-color: var(--active-bg-color);

/* 圆角边框 */
@border-circle: 8px;

/* 控制表头可编辑标识 */
.table_header_color(@color) {
    .vxe-cell--title {
        &::after {
            content: '●';
            color: @color;
            padding: 0 6px;
        }
    }
}

/* 模块之间的边距，模块自身的边距 */
@main-module-padding: 6px;
@module-padding: 8px;
