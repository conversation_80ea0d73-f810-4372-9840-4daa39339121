<script lang="ts" setup>
import { ref, watch } from 'vue';
import { setHeaderSelectFilter } from '@futurefab/ui-sdk-ees-basic';

const props = withDefaults(
  defineProps<{
    id: string;
    data: any[];
    options: object;
    total?: boolean;
    columnFieldList?: string[];
    maxHeight?: number;
  }>(),
  {
    id: () => 'common-table-id',
    data: () => [],
    options: () => ({}),
    total: false,
    columnFieldList: () => [],
    maxHeight: 330
  }
);
const checkData = defineModel<any[]>('checkData');
const xGrid = ref();
const option = ref(props.options);
const setHeader = () => {
  setHeaderSelectFilter({
    xGrid: xGrid,
    tableData: xGrid.value.getTableData()?.fullData,
    columnFieldList: props.columnFieldList
  });
};
// const checkCount = ref(0);
// const beforeCheckRows = shallowRef<any[]>([]);
const handleCheckData = () => {
  const checkRecords = xGrid.value.getCheckboxRecords();
  if (checkData.value) {
    checkData.value = checkRecords;
  }
};
const tableEvent = {
  checkboxAll: (val: any) => {
    handleCheckData();
  },
  checkboxChange: (val: any) => {
    handleCheckData();
  }
};
const reloadFilterHeader = () => {
  setTimeout(() => {
    setHeader();
  });
};
// 获取表格当前显示数据
const getData = () => {
  return xGrid.value.getTableData();
};
const addTableData = (data: any[]) => {
  xGrid.value.loadData(data);
  reloadFilterHeader();
};
const deleteTableData = () => {
  // 删除复选框选中的所有数据
  xGrid.value.removeCheckboxRow();
  reloadFilterHeader();
};
const cellClick = (rowInfo: any) => {
  const { row, column } = rowInfo;
  if (column?.type !== 'checkbox') {
    xGrid.value.toggleCheckboxRow(row);
    handleCheckData();
  }
};
const filterChange = () => {
  const renderData = xGrid.value.getTableData().tableData;
  xGrid.value.toggleAllCheckboxRow(renderData);
  handleCheckData();
};
watch(
  () => props.data,
  () => {
    // 默认全选
    setTimeout(() => {
      if (xGrid.value) {
        reloadFilterHeader();
        if (checkData.value && checkData.value.length > 0) {
          checkData.value.forEach((value: any) => {
            const row = props.data?.find((item: any) => {
              if (value?.category) return item?.category === value?.category;
              if (value?.parameterName) return item?.parameterName === value?.parameterName;
              if (value?.recipeStepId) return item?.recipeStepId === value?.recipeStepId;
              return false;
            });
            if (row) xGrid.value.setCheckboxRow(row, true);
          });
        } else {
          xGrid.value.setAllCheckboxRow(true);
          handleCheckData();
        }
      }
    });
  },
  { immediate: true }
);

defineExpose({ getData, addTableData, deleteTableData });
</script>
<template>
  <div class="common-table-wrapper">
    <div v-if="total" class="label">
      <p><span>Selected：</span> {{ checkData?.length }}</p>
      <p><span>Total：</span> {{ data?.length }}</p>
    </div>
    <div :class="total ? 'table-box-calc' : 'table-box'">
      <vxe-grid
        :id="props.id"
        ref="xGrid"
        :height="'auto'"
        :max-height="maxHeight"
        v-bind="option"
        :data="data"
        :class="'no-title-table-small-radius'"
        v-on="tableEvent"
        @cell-click="cellClick"
        @filter-change="filterChange"
      >
        <template #categorySlot="{ row }">
          <span v-if="row?.color" style="display: flex; align-items: center; gap: 4px">
            <span
              style="width: 12px; height: 12px; border-radius: 2px"
              :style="{ backgroundColor: row?.color }"
            ></span>
            {{ row?.category }}
          </span>
          <span v-else>{{ row?.category }}</span>
        </template>
      </vxe-grid>
    </div>
  </div>
</template>
<style lang="less" scoped>
@import url('@/assets/style/variable.less');
.common-table-wrapper {
  height: 100%;
  width: 100%;

  .label {
    display: flex;
    flex-direction: row;
    background-color: @bg-group-color;
    margin: 0 0 10px 0;
    padding: 14px;
    p {
      color: @text-subtitle-color;
      margin: 0 10px 0 0;
      span {
        color: @text-hint-color;
      }
    }
  }

  .table-box-calc {
    height: calc(100% - 59px);
  }

  .table-box {
    height: calc(100%);
  }
}
</style>
