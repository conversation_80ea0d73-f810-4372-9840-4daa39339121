// hooks/useScreenSize.ts
import { ref, onMounted, onUnmounted, watchEffect } from 'vue';

export const useScreenSize = (breakpoint: number = 1600) => {
  const isSmallScreen = ref(false);
  const screenWidth = ref(window.innerWidth);

  const updateScreenSize = () => {
    screenWidth.value = window.innerWidth;
    isSmallScreen.value = screenWidth.value < breakpoint;
  };

  onMounted(() => {
    updateScreenSize(); // 初始化
    window.addEventListener('resize', updateScreenSize);
  });

  onUnmounted(() => {
    window.removeEventListener('resize', updateScreenSize);
  });

  return {
    isSmallScreen,
    screenWidth
  };
};
