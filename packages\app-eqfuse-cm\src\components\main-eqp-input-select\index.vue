<template>
  <a-auto-complete
    v-model:value="eqpLabelValue"
    :options="options"
    style="width: 180px"
    placeholder=""
    allow-clear
    :label-in-value="true"
    :field-names="{ value: 'alias' }"
    :filter-option="filterOption('alias')"
    :not-found-content="$t('eesBasic.tips.noData')"
    :default-active-first-option="false"
    :disabled="disabled"
    :bordered="bordered"
    @change="change"
  >
  </a-auto-complete>
</template>
<script lang="ts" setup>
import { ref, watch } from 'vue';
import type { SelectProps } from '@futurefab/ant-design-vue';

const props = defineProps<{
  value: any;
  disabled: boolean;
  options: SelectProps['options'];
  bordered: boolean;
}>();
const eqpLabelValue = ref<string>();
const emits = defineEmits(['update:value', 'change']);
const filterOption = (labelKey?: string) => (val: string, option: { [k: string]: string }) =>
  option[labelKey ?? 'label'].toLowerCase().includes(val.toLowerCase());
const change = (val: any, option: any) => {
  if (option?.rawId) {
    emits('update:value', option?.rawId);
  } else {
    emits('update:value', val?.value);
  }
  emits('change', option);
};
watch(
  () => props.value,
  (v) => {
    eqpLabelValue.value = props.options?.find((item) => item.rawId === v)?.alias;
  },
  { immediate: true }
);
</script>
