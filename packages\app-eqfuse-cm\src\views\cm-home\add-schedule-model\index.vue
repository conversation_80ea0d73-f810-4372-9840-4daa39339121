<script setup lang="ts">
import { ref, watchEffect } from 'vue';
import { type AddScheduleForm } from '@/utils/schedule';
import { getInitFormValue, getValidationMsg } from './config';
import dayjs from 'dayjs';
import { cloneDeep } from 'lodash-es';
import {
  type AddScheduleItem,
  addScheduleJob,
  getCurrentUser,
  getUserData,
  viewCodeGroupList,
  getFilterRunParameter
} from '@futurefab/ui-sdk-api';
import { successInfo } from '@futurefab/ui-sdk-ees-basic';
import { cmMainInfo } from '@/log-config';
import { getEndTime, getTriggerTime } from '@/utils/schedule';
import Alarm from './alarm.vue';

const props = withDefaults(
  defineProps<{
    isEdit: boolean;
    alarmParams: any;
  }>(),
  {
    isEdit: false,
    alarmParams: {}
  }
);
const emits = defineEmits(['add', 'modify', 'reloadManageList']);
const showModel = ref(false);
const handleHide = () => {
  showModel.value = false;
};
const currentUser = ref('');
const loading = ref(false);
const formState = ref<AddScheduleForm>(
  getInitFormValue({
    isEdit: props.isEdit,
    form: null,
    currentUser: currentUser.value
  })
);
const errorMsgList = ref<string[]>([]);
const apply = () => {
  const result = getValidationMsg(formState.value, props.isEdit);
  errorMsgList.value = result;
  if (!result.length) {
    if (props.isEdit) {
      emits('modify', {
        params: getJobParams(formState.value),
        form: formState.value
      });
    } else {
      emits('add', cloneDeep(formState.value));
    }
  }
};
const notificationRecipientsOptions = ref<any[]>([]);
const startDateChange = (events: any) => {
  console.log(events, 'startDateChange');
  if (events?.$d) {
    formState.value.endDate = dayjs(events?.$d).add(1, 'day');
  }
};
const disabledEndDate = (time: any) => {
  if (formState.value.startDate?.$d) {
    return dayjs(time.$d).endOf('day').toDate().getTime() < formState.value.startDate?.$d.getTime();
  }
  return false;
};
const getJobParams = (form: AddScheduleForm) => {
  const timeFormat = 'YYYY-MM-DD HH:mm:ss';
  const startTime = form.startDate.$d;
  const startStr = dayjs(startTime).format(timeFormat);
  const isDay = form.recurrenceBy === 'day';
  const interval = isDay ? form.dayInterval : form.hourInterval;
  const startSecond = dayjs(startTime).second();
  const startMinute = dayjs(startTime).minute();
  const startHour = dayjs(startTime).hour();
  const startDay = dayjs(startTime).date();
  const startMonth = dayjs(startTime).month() + 1;
  const hour = 1000 * 60 * 60;
  // 只执行一次
  const executeOnce =
    formState.value.recurrenceBy === 'never' ||
    (formState.value.endType === 'after' && formState.value.afterOccurrences === 1);

  const getDescTail = () => {
    if (form.endType === 'noEnd') {
      return '';
    } else if (form.endType === 'after') {
      return ` after occurrences ${form.afterOccurrences}`;
    } else {
      return ` end at ${dayjs(form.endDate).format(timeFormat)}`;
    }
  };

  const params: AddScheduleItem = {
    applicationName: 'cm',
    serverUrl: 'ScheduleRunAnalysisService',
    description: executeOnce
      ? `At ${startStr} execute once`
      : `At ${startStr} every ${interval} ${form.recurrenceBy}` + getDescTail(),
    applicationData: '',
    jobName: form.jobName as string,
    isEnable: 1,
    triggerTime: getTriggerTime(
      executeOnce,
      startMinute,
      startSecond,
      startHour,
      startDay,
      startMonth,
      isDay,
      interval
    ),
    jobEnableDtts: dayjs(startTime).subtract(0.5, 'hour').format(timeFormat),
    jobDisableDtts: getEndTime(
      timeFormat,
      executeOnce,
      form,
      startTime,
      startDay,
      interval,
      isDay,
      startHour,
      hour,
      startMinute
    ) as string
  };
  return params;
};
defineExpose({
  show: (form?: any) => {
    showModel.value = true;
    formState.value = getInitFormValue({
      isEdit: props.isEdit,
      form,
      currentUser: currentUser.value
    });
  },
  addJob: async (jobData: any, form: AddScheduleForm) => {
    try {
      loading.value = true;
      const params = {
        ...getJobParams(form),
        applicationData: JSON.stringify({ ...jobData, form })
      };
      const res = await addScheduleJob({
        bodyParams: [params],
        headerParams: {
          log: 'Y',
          page: 'cm-cm-main',
          action: 'add-schedule-job'
        }
      });
      if (res.status === 'SUCCESS') {
        successInfo('common.tip.actionSuccess');
        handleHide();
        // 查询manage list
        emits('reloadManageList');
      }
    } catch (e) {
      console.error(e);
    } finally {
      loading.value = false;
    }
  },
  hide: () => {
    showModel.value = false;
  }
});
const baseClass = 'add-schedule-Job-content';

// Email Options
const handleAddEmailOption = () => {
  const uniqueId =
    'id-' + new Date().getTime().toString(36) + '-' + Math.random().toString(36).substring(2, 9);

  formState.value.alarmConditions.push({
    id: uniqueId,
    conditionType: 'TOTAL',
    conditionRule: {
      ruleParam: '',
      ruleValue: 50
    }
  });
};
const handleDeleteEmailOption = (id: string) => {
  formState.value.alarmConditions = formState.value.alarmConditions.filter(
    (item: { id: string }) => item.id !== id
  );
};
const isDisabled = ref<boolean>(false);
const selectOption = ref<any>([]);
const parameterOption = ref<any>([]);
const parameterGroupOption = ref<any>([]);
const formatResult = (data: any) => {
  const groupTemp: any = [];
  const parameterTemp: any = [];
  data?.paramGroupList?.forEach((item: any) => {
    groupTemp.push({ parameterName: item.groupName });
    const paramsTemp = item.params?.map((param: any) => ({
      parameterName: param.paramAlias,
      originalParams: param.originalParams
    }));
    parameterTemp.push(...paramsTemp);
  });
  return { groupTemp, parameterTemp };
};
watchEffect(async () => {
  if (!showModel.value) return;
  // 获取用户列表
  getUserData({ bodyParams: {} }).then((res: { status: string; data: any[] }) => {
    if (res.status === 'SUCCESS') {
      notificationRecipientsOptions.value = res.data || [];
    }
  });
  // 获取当前用户
  getCurrentUser({ bodyParams: {} }).then((res: any) => {
    if (res.status === 'SUCCESS') {
      currentUser.value = res.data.userId;
      if (!props.isEdit) {
        formState.value.notificationRecipients = [res.data.userId];
      }
    }
  });
  // 类型下拉
  const codeConfigList = ['CM_ALARM_COND_TYPE'] as const;
  viewCodeGroupList({
    bodyParams: { categoryList: codeConfigList }
  }).then(
    (res: { data: Record<(typeof codeConfigList)[number], { code: string; name: string }[]> }) => {
      const mapFunc = (item: { name: string; code: string }) => ({
        label: item.name,
        value: item.code
      });
      selectOption.value = res.data?.CM_ALARM_COND_TYPE?.map(mapFunc);
    }
  );
  // 类型下细分下拉内容
  if (props.alarmParams?.runType) {
    const params = cloneDeep(props.alarmParams);
    const result = await getFilterRunParameter({
      bodyParams: params,
      headerParams: {
        log: 'Y',
        page: cmMainInfo.pageId,
        action: 'get-filter-run-parameter'
      }
    });
    if (result.status === 'SUCCESS') {
      const { groupTemp, parameterTemp } = formatResult(result.data);
      parameterGroupOption.value = groupTemp?.map((item: { parameterName: string }) => ({
        label: item.parameterName,
        value: item.parameterName
      }));
      parameterOption.value = parameterTemp?.map(
        (item: { parameterName: string; originalParams: string[] }) => ({
          label: item.parameterName,
          value: params.stationCompare ? item.originalParams : item.parameterName
        })
      );
    }
  }
  isDisabled.value =
    props.isEdit && parameterOption.value?.length <= 0 && parameterGroupOption.value?.length <= 0;
});
</script>
<template>
  <div class="schedule-job-container">
    <vxe-modal
      v-model="showModel"
      :title="$t('cm.title.scheduleJob')"
      :mask-closable="false"
      :width="'800px'"
      show-footer
      @hide="handleHide"
      @close="handleHide"
    >
      <div :class="baseClass">
        <ul v-if="errorMsgList.length" :class="baseClass + '-error-list'">
          <li v-for="msg in errorMsgList" :key="msg">{{ msg }}</li>
        </ul>
        <div :class="baseClass + '-header'">
          <a-form
            ref="headFormRef"
            :class="baseClass + '-form'"
            name="advanced_search"
            :label-col="{ style: { width: '147px', 'text-align': 'left' } }"
            :model="formState"
            :layout="'horizontal'"
          >
            <a-form-item
              :name="'jobName'"
              :label="$t('cm.label.jobName')"
              :rules="[{ required: true, message: 'Please input Job Name!' }]"
            >
              <a-input
                v-model:value="formState.jobName"
                :placeholder="$t('cm.label.jobName')"
                :disabled="isEdit"
                style="width: 240px"
              />
            </a-form-item>
            <a-form-item
              :name="'startDate'"
              :label="$t('cm.label.startDate')"
              :rules="[{ required: true, message: 'Please input Start Date!' }]"
            >
              <a-date-picker
                v-model:value="formState.startDate"
                show-time
                style="width: 240px"
                format="YYYY-MM-DD HH:mm:ss"
                :disabled="isEdit"
                @change="startDateChange"
              ></a-date-picker>
            </a-form-item>
            <a-form-item :name="'recurrence'" :label="$t('cm.label.recurrence')">
              <a-radio-group v-model:value="formState.recurrenceBy">
                <a-radio :value="'never'">{{ $t('cm.label.never') }}</a-radio>
                <a-radio :value="'hour'">{{ $t('cm.label.hourly') }}</a-radio>
                <a-radio :value="'day'">{{ $t('cm.label.daily') }}</a-radio>
              </a-radio-group>
            </a-form-item>
          </a-form>
        </div>
        <div
          v-if="formState.recurrenceBy !== 'never'"
          :class="[baseClass + '-recurrence', baseClass + '-panel']"
        >
          <div :class="baseClass + '-recurrence-by-option'">
            <div :class="baseClass + '-recurrence-by-option-daily-interval'">
              <span>{{ $t('cm.label.every') }}</span>
              <template v-if="formState.recurrenceBy === 'day'">
                <a-input-number
                  v-model:value="formState.dayInterval"
                  style="width: 160px; margin: 0 4px"
                  :min="1"
                  :max="15"
                />
                <span>{{ $t('cm.label.day') }}</span>
              </template>
              <template v-else>
                <a-input-number
                  v-model:value="formState.hourInterval"
                  style="width: 160px; margin: 0 4px"
                  :min="1"
                  :max="12"
                />
                <span>{{ $t('cm.label.hour') }}</span>
              </template>
            </div>

            <div :class="baseClass + '-recurrence-by-option-daily-end'">
              <a-radio-group v-model:value="formState.endType">
                <a-radio :style="{ display: 'flex' }" :value="'noEnd'">
                  <span>{{ $t('cm.label.noEndDate') }}</span>
                </a-radio>
                <a-radio :style="{ display: 'flex', marginTop: '10px' }" :value="'after'">
                  <div :class="baseClass + '-recurrence-by-option-daily-end-inline-block'">
                    <div class="flex-temp-box">
                      <span>{{ $t('cm.label.after') }}</span>
                      <a-input-number
                        v-model:value="formState.afterOccurrences"
                        style="width: 160px; margin: 0 4px"
                        :disabled="formState.endType !== 'after'"
                        :min="1"
                        :max="999"
                      />
                      <span>{{ $t('cm.label.occurrences') }}</span>
                    </div>
                  </div>
                </a-radio>
                <a-radio :style="{ display: 'flex', marginTop: '10px' }" :value="'endBy'">
                  <div :class="baseClass + '-recurrence-by-option-daily-end-inline-block'">
                    <div class="flex-temp-box">
                      <span>{{ $t('cm.label.endBy') }}</span>
                      <a-date-picker
                        v-model:value="formState.endDate"
                        show-time
                        style="width: 240px; margin: 0 4px"
                        :disabled="formState.endType !== 'endBy'"
                        format="YYYY-MM-DD HH:mm:ss"
                        :disabled-date="disabledEndDate"
                      ></a-date-picker>
                    </div>
                  </div>
                </a-radio>
              </a-radio-group>
            </div>
          </div>
        </div>
        <vxe-checkbox
          v-model="formState.emailNotification"
          :class="baseClass + '-email-checkbox'"
          style="margin-top: 15px"
          :content="$t('cm.label.enableEmailNotification')"
          size="medium"
        ></vxe-checkbox>
        <template v-if="formState.emailNotification">
          <div :class="[baseClass + '-email-options', baseClass + '-panel']">
            <div :class="baseClass + '-select-options-content'">
              <div :class="baseClass + '-select-options-content-label'">
                <span :class="baseClass + '-select-options-content-label-label'">
                  Alarm Conditions
                </span>
                <a-button
                  :class="baseClass + '-select-options-content-label-icon-btn'"
                  size="small"
                  :disabled="isDisabled"
                  @click="handleAddEmailOption"
                >
                  <template #icon>
                    <i class="iconfont icon-eq-add" style="height: 100%; font-size: 15px" />
                  </template>
                </a-button>
              </div>
              <div :class="baseClass + '-select-options-content-options'">
                <div v-for="(item, index) in formState.alarmConditions" :key="item.id">
                  <Alarm
                    :id="item.id"
                    v-model:alarm="formState.alarmConditions[index]"
                    :is-edit="isEdit"
                    :is-disabled="isDisabled"
                    :select-option="selectOption"
                    :parameter-group-option="parameterGroupOption"
                    :parameter-option="parameterOption"
                    @handle-delete-email-option="handleDeleteEmailOption(item.id)"
                  />
                </div>
              </div>
            </div>
          </div>
          <div :class="baseClass + '-panel-recipient-content'">
            <div :class="baseClass + '-panel-recipient-content-lable'">
              {{ $t('cm.label.notificationRecipients') }}
            </div>
            <a-select
              ref="selectRef"
              v-model:value="formState.notificationRecipients"
              :options="notificationRecipientsOptions"
              mode="multiple"
              style="width: 240px"
              :show-arrow="true"
              :field-names="{ value: 'userId' }"
              allow-clear
            >
              <template #clearIcon>
                <i
                  class="iconfont icon-close"
                  style="font-size: 15px; color: var(--bg-icon-active-color)"
                />
              </template>
              <template #option="item">
                <span :title="item.userId + (item.emailaddress ? `(${item.emailaddress})` : '')">
                  {{ item.userId }}
                  <template v-if="item.emailaddress">{{ `(${item.emailaddress})` }}</template>
                </span>
              </template>
            </a-select>
          </div>
        </template>
      </div>
      <template #footer>
        <a-button type="primary" style="margin-right: 10px" @click="apply">{{
          $t('common.btn.apply')
        }}</a-button>
        <a-button @click="handleHide">{{ $t('common.btn.cancel') }}</a-button>
      </template>
    </vxe-modal>
  </div>
</template>
<style scoped lang="less">
@import url('@/assets/style/variable.less');
.schedule-job-container {
  :deep(.vxe-modal--footer) {
    border: none;
  }
}
.add-schedule-Job-content {
  min-height: 550px;
  max-height: calc(100vh - 114px);
  overflow-x: hidden;
  &-error-list {
    border-radius: @border-circle;
    padding: @module-padding;
    border: 1px solid @card-default-br-color;
    li {
      font-size: 14px;
      &:before {
        content: '';
        display: inline-block;
        height: 5px;
        width: 5px;
        border-radius: 50%;
        background-color: red;
        margin: 0px 5px 2px 5px;
      }
    }
    margin-bottom: 10px;
  }
  // :deep(.ant-picker.ant-picker-disabled) {
  //     background-color: @input-disabled-bg-color;
  // }
  :deep(.ant-form-inline .ant-form-item-with-help) {
    margin-bottom: 0;
  }
  :deep(.ant-form-item-label label) {
    color: @text-subtitle-color;
    font-size: 14px;
    font-weight: bold;
    &::after {
      content: none;
    }
  }
  :deep(.ant-form > .ant-form-item:last-child) {
    margin-bottom: 0px;
  }
  height: 100%;
  overflow-y: auto;
  &-panel {
    background-color: @bg-group-color;
    padding: 14px;
  }
  &-recurrence {
    margin-top: 5px;
    border-radius: 4px;
    border: 1px solid @border-form-color;
    &-by-option {
      &-daily-interval {
        display: flex;
        flex-wrap: nowrap;
        height: 32px;
        align-items: center;
        margin-bottom: 10px;
        span {
          color: @text-sub-color;
        }
      }
      &-daily-end {
        &-inline-block {
          display: inline-block;
          .flex-temp-box {
            display: flex;
            align-items: center;
            flex-wrap: nowrap;
            .ant-radio-wrapper {
              span {
                display: inline-block;
                margin: 3px;
                color: @text-sub-color;
              }
            }
          }
        }
      }
    }
  }
  &-email-checkbox {
    margin-top: 20px;
    margin-bottom: 0px;
    :deep(span) {
      font-size: 14px;
      font-weight: bold;
      color: @text-subtitle-color;
    }
  }
  &-email-options {
    margin-top: 10px;
    border: 1px solid @border-form-color;
    border-radius: 4px;
  }
  &-select-options-content {
    display: flex;
    flex-direction: column;

    &-label {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      margin: 0;
      height: 24px;
      &-label {
        font-size: 14px;
        font-weight: bold;
        color: @text-subtitle-color;
      }
      &-icon-btn {
        color: @primary-color;
        display: flex;
        justify-content: center;
        align-items: center;
      }
      &-icon-btn:disabled {
        color: @text-disabled-color-1;
      }
    }
  }
  &-panel-recipient-content {
    margin-top: 20px;
    display: flex;
    align-items: center;
    &-lable {
      font-size: 14px;
      font-weight: bold;
      margin-right: 10px;
      color: @text-subtitle-color;
    }
    // 调整图标位置
    :deep(.ant-select-arrow) {
      top: 15px;
    }
    :deep(.ant-select-clear) {
      top: 15px;
    }
  }

  &-panel-condition-notification-content {
    &-threshold-detail {
      display: flex;
      margin-top: 10px;
    }
  }
}
</style>
