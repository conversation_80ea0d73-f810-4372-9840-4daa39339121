import { VXETable, type VxeGridProps, t } from '@futurefab/vxe-table';
import { stateViewer as buttonIds } from '@/log-config/index';
import { tableDefaultConfig } from '@futurefab/ui-sdk-ees-basic';
export const xGetConfig = (type: any) => {
  console.log('xGetConfig', type);
  return tableDefaultConfig(
    {
      id: 'StateViewer',
      borderOutLine: [1, 0, 0, 0],
      toolbarConfig: {
        tableName: 'stateViewer.title.stateViewer',
        import: false,
        export: false,
        refresh: true,
        border: false,
        slots: {
          beforeTools: 'beforeTools',
          beforeButtonsList: 'search-condition'
        },
        tools: [
          ...VXETable.tableFun.getToolsButton([
            {
              name: 'common.btn.resetHistory',
              icon: 'icon-btn-reset-history',
              id: buttonIds[type].resetHistory,
              visible: false
            },
            {
              name: 'common.btn.history',
              icon: 'icon-btn-history',
              id: buttonIds[type].history,
              visible: false
            },
            {
              name: 'common.btn.reset',
              icon: 'icon-btn-reset',
              id: buttonIds[type].reset,
              visible: false
            },
            {
              name: 'common.btn.exportData',
              icon: 'icon-btn-export',
              id: buttonIds[type].export,
              visible: false
            }
          ])
        ]
      },
      columns: [
        {
          field: 'rawId',
          type: 'checkbox',
          width: 40,
          align: 'center'
        },
        {
          field: 'actionModeCd',
          minWidth: 150,
          title: 'stateViewer.field.actionMode',
          align: 'left',
          sortable: true,
          filters: []
        },
        {
          field: 'lastUpdateDtts',
          minWidth: 200,
          title: 'common.field.lastUpdateDtts',
          align: 'left',
          sortable: true,
          filters: [{ data: '' }],
          filterRender: {
            name: '$input'
          }
        },
        {
          field: 'lastUpdateBy',
          minWidth: 150,
          title: 'common.field.lastUpdateBy',
          align: 'left',
          sortable: true,
          filters: [{ data: '' }],
          filterRender: {
            name: '$input'
          }
        },
        {
          field: 'createDtts',
          minWidth: 200,
          title: 'common.field.createDtts',
          align: 'left',
          sortable: true,
          filters: [{ data: '' }],
          filterRender: {
            name: '$input'
          }
        },
        {
          field: 'createBy',
          minWidth: 150,
          title: 'common.field.createBy',
          align: 'left',
          sortable: true,
          filters: [{ data: '' }],
          filterRender: {
            name: '$input'
          }
        }
      ],
      checkboxConfig: {
        showHeader: true
      },
      columnCheckboxConfig: {
        checkMethod: () => false
      }
    } as VxeGridProps,
    true,
    [buttonIds[type].resetHistory, buttonIds[type].history, buttonIds[type].reset]
  );
};
