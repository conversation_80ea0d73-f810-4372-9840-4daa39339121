<script lang="ts" setup>
import { ref, watch, watchEffect, reactive } from 'vue';
import { DownOutlined } from '@ant-design/icons-vue';
import { getHisFileList, getHisWaferLoopList, getHisFileRootPath } from '@futurefab/ui-sdk-api';
import { cmMainInfo as buttonIds } from '@/log-config';
import NoData from '@/views/cm-home/cm-main/add-dataset/common-grid/no-data.vue';
import { showWarning } from '@futurefab/ui-sdk-ees-basic';

const props = withDefaults(
  defineProps<{
    selectType: 'checkboxGroup' | 'tree';
  }>(),
  {
    selectType: 'checkboxGroup'
  }
);

const runByModel = defineModel('runByModel');
const groupLoading = defineModel('groupLoading');
const emits = defineEmits(['setWaferData']);

const allFileOptions = ref<any[]>([]);
const fileBaseDir = ref<string>(``);
const fileLoading = ref(false);
const getFileNameList = async (address?: string) => {
  fileLoading.value = true;
  const fileNameList = await getHisFileList({
    bodyParams: address
      ? {
          directory: address
        }
      : {},
    headerParams: {
      log: 'Y',
      page: buttonIds.pageId,
      action: 'get-his-file-list'
    }
  });
  if (fileNameList.status === 'SUCCESS') {
    if (!address) fileBaseDir.value = fileNameList.data?.localHisParentDirectory;
    allFileOptions.value = fileNameList.data?.hisFileTree;
    fileLoading.value = false;
  } else {
    fileLoading.value = false;
  }
};

// 选中
const selectedKeys = ref<string[]>([]);
// 勾选
const checkedKeys = ref<string[]>([]);

const onSelected = (
  selectedKeys: string[],
  e: { selected: boolean; selectedNodes: any; node: any }
) => {
  const key = e?.node?.key;
  if (checkedKeys.value.includes(key)) {
    let index = checkedKeys.value.indexOf(key);
    checkedKeys.value.splice(index, 1);
  } else {
    checkedKeys.value.push(key);
  }
};
// 解析his文件
const getHisFileWaferData = async () => {
  groupLoading.value = true;
  if (props.selectType === 'tree' && !checkedKeys.value.length) {
    groupLoading.value = false;
    return showWarning('cm.tips.noFile');
  } else if (props.selectType === 'checkboxGroup' && !state.checkedList.length) {
    groupLoading.value = false;
    return showWarning('cm.tips.noFile');
  }
  const waferFileList = await getHisWaferLoopList({
    bodyParams: {
      loop: runByModel.value === 'loop',
      fileNames: props.selectType === 'tree' ? checkedKeys.value : state.checkedList
    },
    headerParams: {
      log: 'Y',
      page: buttonIds.pageId,
      action: 'get-his-wafer-loop-list'
    }
  });
  if (waferFileList.status === 'SUCCESS') {
    waferFileList.data.data = waferFileList.data?.data?.map((item: any) => ({
      ...item,
      wafer: item?.waferId?.split(';')?.[0]
    }));
    const tempData = waferFileList.data;
    emits('setWaferData', tempData);
  }
  groupLoading.value = false;
};

// tree结构-文件选择
const showFileAddress = ref(false);
const fileAddressTreeData = ref<any[]>([]);
const fileBaseDirAddress = ref<string[]>([]);
const handleTreeSelect = (selectedKeys: string[]) => {
  handleGetHisFileRootPath(selectedKeys[0]);
};
/**
 * 根据 parentDirectory 获取历史文件路径，并挂载到对应节点
 * @param parentDirectory 父目录路径，为空表示根目录
 */
const handleGetHisFileRootPath = async (parentDirectory?: string) => {
  const res = await getHisFileRootPath({
    bodyParams: {
      parentDirectory: parentDirectory
    },
    headerParams: {
      log: 'Y',
      page: buttonIds.pageId,
      action: 'get-his-file-root-path'
    }
  });
  if (res.status === 'SUCCESS') {
    if (props.selectType === 'tree' && parentDirectory && Array.isArray(res.data)) {
      // 给返回的数据添加层级前缀（/、//、///...）
      const depth = parentDirectory ? parentDirectory.split('/').filter(Boolean).length + 1 : 1;
      const processedData = addPrefixToNodes(res.data, depth);
      // 找到父节点，挂载 children
      const parentNode = findNodeInTree(fileAddressTreeData.value, parentDirectory);
      if (parentNode) {
        parentNode.children = processedData;
        // 可选：标记已加载，避免重复请求
        parentNode.loaded = true;
      }
    } else {
      // checkbox group结构-根目录直接赋值
      fileAddressTreeData.value = res.data;
    }
  }
};
/**
 * 递归处理节点，添加前缀（/ 的数量 = 层级 depth）
 */
function addPrefixToNodes(nodes: any[], depth: number) {
  return nodes.map((node) => {
    return {
      ...node,
      title: `${node.title || node.key}`, // 假设 title 是显示名
      // 可选：记录层级
      depth: depth
    };
  });
}
/**
 * 在树中查找 key 等于 targetKey 的节点（用于挂载 children）
 */
function findNodeInTree(nodes: any[], targetKey: string): any {
  for (const node of nodes) {
    if (node.key === targetKey) {
      return node;
    }
    if (node.children) {
      const found = findNodeInTree(node.children, targetKey);
      if (found) return found;
    }
  }
  return null;
}

// 初始化的时候请求his文件地址
watchEffect(() => {
  getFileNameList();
});

// 选择文件夹地址
watch(fileBaseDirAddress, (newV) => {
  fileBaseDir.value = newV[0];
});

const clearFileCheckList = () => {
  checkedKeys.value = [];
};

// checkbox group结构-文件选择
const state = reactive({
  indeterminate: true,
  checkAll: false,
  checkedList: []
});
const onCheckAllChange = (e: any) => {
  const allKeys = allFileOptions.value.map((item: any) => item.key);
  Object.assign(state, {
    checkedList: e.target.checked ? allKeys : [],
    indeterminate: false
  });
};
watch(
  () => state.checkedList,
  (val) => {
    const allKeys = allFileOptions.value.map((item: any) => item.key);
    state.indeterminate = !!val.length && val.length < allKeys.length;
    state.checkAll = val.length === allKeys.length;
  }
);

defineExpose({ getHisFileWaferData, clearFileCheckList });
</script>

<template>
  <div class="eqp-db-content">
    <div class="eqp-db-content-directory-root">
      <span class="label">{{ $t('cm.label.dataSourceDirectoryRoot') }}</span>
      <a-tooltip :trigger="['hover']" placement="topLeft" overlay-class-name="numeric-input">
        <template v-if="fileBaseDir" #title>
          <span class="numeric-input-title">
            {{ fileBaseDir }}
          </span>
        </template>

        <a-input class="input" v-model:value="fileBaseDir" placeholder="Absolute path">
          <template #suffix>
            <a-popover
              v-model:open="showFileAddress"
              trigger="click"
              placement="bottomLeft"
              :autoAdjustOverflow="true"
              :overlayInnerStyle="{ width: 'auto', maxHeight: '300px', overflow: 'scroll' }"
            >
              <template #content>
                <a-directory-tree
                  v-model:selectedKeys="fileBaseDirAddress"
                  :tree-data="fileAddressTreeData"
                  @select="handleTreeSelect"
                ></a-directory-tree>
              </template>
              <a class="select-folder-btn" @click="handleGetHisFileRootPath()">
                <i class="iconfont icon-link" />
                {{ selectType === 'checkboxGroup' ? 'Select EQP' : 'Select Folder' }}
              </a>
            </a-popover>
          </template>
        </a-input>
      </a-tooltip>
      <a-button @click="getFileNameList(fileBaseDir)">{{ $t('cm.btn.parsedDirectory') }}</a-button>
    </div>
    <div
      class="file-checkbox"
      v-isLoading="{
        isShow: fileLoading,
        hasButton: false,
        title: 'Loading...'
      }"
    >
      <!-- checkbox group结构-文件选择 -->
      <div v-if="selectType === 'checkboxGroup' && allFileOptions.length > 0">
        <div>
          <div>
            <a-checkbox
              v-model:checked="state.checkAll"
              :indeterminate="state.indeterminate"
              @change="onCheckAllChange"
            >
              Check all{{ '(' + state.checkedList.length + '/' + allFileOptions.length + ')' }}
            </a-checkbox>
          </div>
          <a-checkbox-group v-model:value="state.checkedList">
            <a-row>
              <a-col v-for="item in allFileOptions" :key="item">
                <a-checkbox :value="item.key">{{ item.title }}</a-checkbox>
              </a-col>
            </a-row>
          </a-checkbox-group>
        </div>
      </div>
      <!-- 树结构-文件选择 -->
      <a-tree
        v-else-if="selectType === 'tree' && allFileOptions.length > 0"
        v-model:selected-keys="selectedKeys"
        v-model:checked-keys="checkedKeys"
        default-expand-all
        checkable
        :tree-data="allFileOptions"
        @select="onSelected"
      >
        <template #switcherIcon="{ switcherCls }">
          <down-outlined :class="switcherCls" />
        </template>
        <template #title="{ title }">
          <span>{{ title }}</span>
        </template>
      </a-tree>
      <NoData v-else :border="false" :special-border="false" />
    </div>
  </div>
</template>

<style lang="less" scoped>
@import url('@/assets/style/variable.less');
.eqp-db-content {
  &-directory-root {
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-bottom: 10px;
    .label {
      min-width: 106px;
      width: max-content;
      color: @text-title-color;
      font-weight: bold;
    }
    .input {
      display: flex;
      flex: 1;
      margin-right: 10px;
      .select-folder-btn {
        color: @text-subtitle-color;
        padding: 0 8px;
        &:hover {
          border-radius: 4px;
          background-color: @bg-active-1-color;
        }
      }
    }
  }
  .file-checkbox {
    display: flex;
    margin: 0 0 10px 106px;
    height: 160px;
    border: 1px solid @border-group-color;
    padding: 14px;
    overflow: auto;
    background-color: @bg-group-color;
    :deep(.ant-tree) {
      background: @bg-group-color;
    }
  }
}
</style>
