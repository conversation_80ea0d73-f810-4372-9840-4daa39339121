<script lang="ts" setup>
import { reactive, onMounted, onUnmounted, nextTick } from 'vue';
import type { EESButtonParams } from './interface';
import { t } from '@futurefab/vxe-table';
import { throttle } from 'lodash-es';

// defineOptions({ name: 'EqFuseSidebar' });
const props = withDefaults(
  defineProps<{
    title?: string | null;
    buttonList?: EESButtonParams[];
    width?: number;
    hasZoom?: boolean;
    isFullscreen?: boolean;
    hasMask?: boolean;
    zIndex?: number;
    beforeClose?: Function | null;
    drawerContainer?: string;
    key?: string;
  }>(),
  {
    title: null, //默认值如果是空字符串，会覆盖slot
    buttonList: () => [],
    width: 0,
    hasZoom: true,
    isFullscreen: false,
    hasMask: true,
    zIndex: 100,
    beforeClose: null,
    drawerContainer: '.eq-fuse-sidebar-wrapper',
    key: 'eqFuse-sidebar'
  }
);
const open = defineModel<boolean>('open', { default: false });
const emit = defineEmits<{
  clickbutton: [value: string];
  resize: [];
}>();

const sidebarParams = reactive({
  // open: true,
  width: props.width || document.body.clientWidth * 0.85,
  isMaximized: props.isFullscreen
});
onMounted(() => {
  if (props.isFullscreen) {
    sidebarParams.width = document.body.clientWidth;
    sidebarParams.isMaximized = true;
  }
  window.addEventListener('resize', handleResize);
  nextTick(() => {
    handleWidth();
  });
});
onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  document.removeEventListener('mousemove', handleMouseMove);
  document.removeEventListener('mouseup', stopResize);
});
//点击底部按钮
const handleClick = (code: string) => {
  emit('clickbutton', code);
};
//点击右上角关闭
const handleClose = () => {
  if (!props.beforeClose) {
    emit('clickbutton', 'closeCancel');
  } else {
    const res = props.beforeClose({ type: 'close' });
    if (res instanceof Promise) {
      res.then(() => {
        emit('clickbutton', 'closeCancel');
      });
    }
    if (res instanceof Error) {
      console.error(res);
    }
  }
};
const getContainer = () => document.querySelector(props.drawerContainer);
const handleScrollDomWidth = () => {
  const sidebarScrollDom = document.querySelector('.sidebar_content') as HTMLElement;
  sidebarScrollDom.style.width = sidebarParams.width + 'px';
};
let preWidth = sidebarParams.width; //点击全屏前的宽度
//放大或缩小
const handleZoom = () => {
  const clientWidth = document.body.clientWidth;
  sidebarParams.isMaximized = !sidebarParams.isMaximized;
  if (sidebarParams.isMaximized) {
    preWidth = sidebarParams.width;
    sidebarParams.width = clientWidth;
  } else {
    sidebarParams.width = preWidth;
  }
  emit('resize');
  handleScrollDomWidth();
};
const handleWidth = throttle(() => {
  const clientWidth = document.body.clientWidth;
  if (sidebarParams.width > clientWidth && props.width >= 1280) {
    sidebarParams.width = clientWidth;
  }
}, 200);
const handleResize = () => {
  emit('resize');
  handleWidth();
};

const MIN_SIDEBAR_WIDTH = 300;
let startX = 0; //鼠标拖拽开始的位置
let startWidth = 0; //鼠标拖拽开始的宽度
let isResizing = false; //是否正在拖拽
const startResize = (e: MouseEvent) => {
  startX = e.clientX;
  startWidth = sidebarParams.width;
  isResizing = true;
  document.addEventListener('mousemove', handleMouseMove);
  document.addEventListener('mouseup', stopResize);
};
const handleMouseMove = (e: MouseEvent) => {
  if (!isResizing) return;
  const delta = startX - e.clientX;
  sidebarParams.width = Math.max(MIN_SIDEBAR_WIDTH, startWidth + delta);
  if (sidebarParams.width > document.body.clientWidth) {
    sidebarParams.width = document.body.clientWidth;
  }
  handleScrollDomWidth();
  emit('resize');
};
const stopResize = () => {
  isResizing = false;
  document.removeEventListener('mousemove', handleMouseMove);
  document.removeEventListener('mouseup', stopResize);
};
</script>

<template>
  <div class="eq-fuse-sidebar-wrapper">
    <a-drawer
      :key="key"
      v-model:open="open"
      placement="right"
      :footer-style="{ textAlign: 'right', border: 'none' }"
      :mask-style="{ zIndex: zIndex ? zIndex + 1 : 101 }"
      :root-class-name="hasMask ? 'sidebar-with-mask' : 'sidebar-no-mask'"
      :mask-closable="false"
      :closable="false"
      :get-container="getContainer"
      :width="sidebarParams.width"
      :mask="hasMask"
      :z-index="zIndex ? zIndex + 1 : 101"
      :keyboard="false"
      v-bind="{ ...(title ? { title: t(title as string) } : {}) }"
    >
      <template v-if="!title" #title>
        <div class="basic_sidebar_header">
          <slot name="sidebar_header"></slot>
        </div>
      </template>
      <template #extra>
        <template v-if="hasZoom">
          <i
            v-if="!sidebarParams.isMaximized"
            class="iconfont icon-screen-full"
            @click="handleZoom"
            :title="t('common.btn.maximization')"
          ></i>
          <i
            v-else
            class="iconfont icon-screen-reduction"
            @click="handleZoom"
            :title="t('common.title.zoomOut')"
          ></i>
        </template>
        <i class="iconfont icon-close2" @click="handleClose" :title="t('common.btn.close')"></i>
      </template>
      <div v-if="hasZoom" class="resize-handle" @mousedown="startResize"></div>
      <div class="sidebar_content scrollbar-wrap-hover">
        <slot name="sidebar_content"></slot>
      </div>
      <template #footer v-if="buttonList.length || $slots.sidebar_footer">
        <div class="basic_sidebar_footer">
          <template v-if="buttonList.length > 0">
            <a-button
              v-for="(btn, index) in buttonList"
              :id="btn.id"
              :key="index"
              :content="btn.name"
              :code="btn.code"
              :type="btn.status"
              class="custom-btn"
              :style="{
                marginRight: index !== buttonList.length - 1 ? '10px' : ''
              }"
              @click="handleClick(btn.code)"
              >{{ t(btn.name) }}</a-button
            >
          </template>
          <slot v-else name="sidebar_footer"></slot>
        </div>
      </template>
    </a-drawer>
  </div>
</template>

<style lang="less" scoped>
@import '@futurefab/ui-sdk-ees-static-files/dist/styles/variable.less';

@padding-drawer: @padding-basis+2 @padding-basis+6;
.eq-fuse-sidebar-wrapper {
  .sidebar_content {
    height: 100%;
    width: 100%;
    overflow-x: auto;
  }
  :deep(.ant-drawer) {
    .ant-drawer-header {
      padding: @padding-drawer;
      border-bottom: 1px solid @border-form-color;
    }
    .ant-drawer-mask {
      transition: 0s;
    }
  }
  :deep(.ant-drawer-content-wrapper) {
    transition: 0s;
    border-radius: @border-radius-basis 0 0 @border-radius-basis;
    .ant-drawer-content {
      border-radius: @border-radius-basis 0 0 @border-radius-basis;
      .ant-drawer-title {
        color: @text-title-color;
      }
      .ant-drawer-body {
        padding: @padding-basis+6;
      }
      .ant-drawer-footer {
        padding: @padding-drawer;
      }
    }
  }
  :deep(.sidebar-with-mask) {
    .ant-drawer-content-wrapper {
      box-shadow: 0px 6px 24px 0px @shadow-color-3;
    }
  }
  :deep(.sidebar-no-mask) {
    .ant-drawer-content-wrapper {
      box-shadow: 0px 2px 24px 0px @shadow-color-1;
    }
  }

  .iconfont {
    cursor: pointer;
    color: @bg-icon-active-color;
    &:hover {
      color: @primary-color;
    }
    &.icon-close2 {
      margin-left: @margin-basis+2;
    }
  }
  .resize-handle {
    width: 8px;
    cursor: ew-resize;
    height: 100%;
    position: absolute;
    left: -4px;
    top: 0;
    z-index: 100;
  }
}
</style>
