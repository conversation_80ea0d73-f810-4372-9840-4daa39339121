import { t, VXETable } from '@futurefab/vxe-table';
import { getReplenishGroup, groupMenuConfig } from '../add-dataset/config';
export const getAddMetrologyOptions = (
  columns: any[],
  beforeMetrologyCol: string[],
  curAdd: string[],
  curDel: string[],
  noAddDataset: boolean,
) => {
  const temp = noAddDataset 
  ? columns.map((item: any) => {
      // const field = specialKey ? (item?.key === 'waferId' ? specialKey : item?.key) : item?.key;
      const field = item?.key;
      const isStartTimeOrEndTime = ['fileName', 'StartTime', 'EndTime'].includes(item?.key);
      return {
        field,
        title: item?.name,
        minWidth: isStartTimeOrEndTime ? 200 : 150,
        sortable: true,
        filters: [],
        filterRender: {},
        formatter: item.key === 'waferId' ? ({row}: any) => {
          return row.waferId?.includes(';') ? row.waferId.split(';')[0] : '';
        } : undefined,
      };
    }) 
  : columns
      .filter((item) => item.type !== 'seq' && item.type !== 'checkbox')
      // .filter((item) => {
      //   // 不是 之前的 metrologyCol
      //   return (
      //     !beforeMetrologyCol.includes(item.field) ||
      //     (beforeMetrologyCol.includes(item.field) && !curDel.includes(item.field))
      //   ); // 是 之前的 metrologyCol，且不是当前删除的
      // })
      .map((item) => {
        // if (beforeMetrologyCol.includes(item.field)) {
        //   return {
        //     ...item,
        //     slots: { header: item.field },
        //     headerClassName: 'metrology-col-header',
        //     editRender: {
        //       name: '$input',
        //       props: { type: 'number', clearable: 'true' }
        //     },
        //     sortable: false
        //   };
        // } else {
        return { ...item, slots: {}, editRender: undefined };
        // }
      });
  temp.splice(
    3,
    0,
    ...[
      {
        title: 'Dataset',
        field: 'datasetGroup',
        editRender: {
          name: '$select',
          options: [...getReplenishGroup(), { label: 'Ungrouped', value: 'Ungrouped' }],
          required: true
        },
        sortable: true,
        filters: [...getReplenishGroup(), { label: 'Ungrouped', value: 'Ungrouped' }]
      },
      ...[...curAdd, ...beforeMetrologyCol.filter(key => !curAdd.includes(key) && !curDel.includes(key))].map((item) => ({
        title: item.slice(0, -'-metrology'.length),
        field: item,
        slots: {
          header: item
        },
        headerClassName: 'metrology-col-header',
        width: '150px',
        editRender: {
          name: '$input',
          props: {
            type: 'number',
            clearable: 'true'
          }
        }
        // sortable: true,
      }))
    ]
  );
  // console.log([...getReplenishGroup(), {label: 'Ungrouped', value: 'Ungrouped'}], '-----------')
  return VXETable.tableFun.tableDefaultConfig({
    toolbarConfig: {
      import: false,
      export: true,
      tools: [
        ...VXETable.tableFun.getToolsButton([
          {
            name: 'common.btn.drawChart',
            icon: 'icon-btn-trend-chart-draw',
            visible: true
          }
        ])
      ]
    },
    border: true,
    columns: temp
    // menuConfig: groupMenuConfig([])
  });
};

