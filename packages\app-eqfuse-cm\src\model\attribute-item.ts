export class AttributeItem {
    constructor(
        public sequence: number,
        public name: string, // matches to eDR attribute name
        public alias: string, // matches to eDR attribute alias
        public isSingle: boolean, // configuration by user on admin page
        public isMandatory: boolean, // matches to eDR attribute isOptional
        public visibleOnHeader: boolean, // whether to show on grid header or not
        public supportShortValue: boolean, // whether to support shortened display
        public shortValueDelimiter: string | null, // delimiter if support shortened display
        public hide: boolean, // obsoleted
        public visibleOnDashboard: boolean, // obsoleted
        public regex: boolean, // configuration by user on admin page
        public isDescriptor: boolean, // matches to eDR attribute ROLE descriptor
        public isReferenceAttribute: boolean, // whether it is created based on quality data parameter
    ) {}

    getShortValue(fullValue: string): string {
        if (!fullValue || !this.shortValueDelimiter) {
            return fullValue;
        }
        const tokens = fullValue.split(this.shortValueDelimiter);
        return tokens[tokens.length - 1];
    }
}
