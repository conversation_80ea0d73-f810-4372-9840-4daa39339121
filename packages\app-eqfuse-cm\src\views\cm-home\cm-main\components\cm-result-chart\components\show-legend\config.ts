import { type Ref } from 'vue';
import { GroupConfigVO } from '@/model/GroupConfigVO';
import type { LegendTree } from '../../interface';
import type { ChartConfig } from '../../../group-custom-configuration/interface';
import { GroupIdGap } from '../../util';
/**
 * get legend data
 */
export const getLegendData = (
    groupConfigs: GroupConfigVO[],
    groupConfigIds: Ref,
    metaData: any,
    ids?: number[],
): LegendTree[] => {
    const chamberTemp: any = [];
    const toolTemp: any = [];
    const rcpTemp: any = [];
    const groupTemp: any[] = [];
    const idsTemp: number[] = [];
    const chamberKey = metaData?.columnMap.chamber.name || 'Chamber';
    const eqpKey = metaData?.columnMap.eqpId.name || 'EQP';
    const rcpKey = metaData?.columnMap.recipeId.name || 'Recipe';
    groupConfigs?.forEach((info: any) => {
        const color = info.color;
        const lengedName = info.name;
        idsTemp.push(info.id);
        groupTemp.push({ id: info.id, checkLabel: lengedName, color });
        const chamberList = info?.[chamberKey]?.split(',') || [];
        const eqpList = info?.[eqpKey]?.split(',') || [];
        const rcpList = info?.[rcpKey]?.split(',') || [];

        chamberTemp.push(...chamberList.map((chamber: string) =>({ id: info.id + GroupIdGap + chamber, checkLabel: chamber, color })));
        toolTemp.push(...eqpList.map((eqp: string) =>({ id: info.id + GroupIdGap + eqp, checkLabel: eqp, color })));
        rcpTemp.push(...rcpList.map((rcp: string) =>({ id: info.id + GroupIdGap + rcp, checkLabel: rcp, color })));
    });
    groupConfigIds.value = ids && ids.length > 0 ? ids : idsTemp;

    return [
        { label: 'Group', value: 'Group', valueList: groupTemp },
        { label: 'EQP', value: 'EQP', valueList: toolTemp },
        { label: 'Chamber', value: 'Chamber', valueList: chamberTemp },
        { label: 'Recipe', value: 'Recipe', valueList: rcpTemp },
    ];
};
