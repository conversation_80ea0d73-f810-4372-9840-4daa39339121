<script lang="ts" setup>
import { ref, watch, shallowRef, computed } from 'vue';
import { showWarning, EesButtonTip } from '@futurefab/ui-sdk-ees-basic';
import { t } from '@futurefab/vxe-table';
import { handleCopyData } from '@/views/cm-home/cm-main/add-dataset/config';
import { datasetGroupStore } from '@/views/cm-home/cm-main/add-dataset/dataset-group-store';
import { DATA_GROUP } from '@/constant/charts';
import { getWaferOptions, getTSVWaferOptions } from '@/views/cm-home/cm-main/add-dataset/config';
import CommonTitle from '@/views/cm-home/cm-main/add-dataset/common-title/index.vue';
import CommonGrid from '@/views/cm-home/cm-main/add-dataset/common-grid/index.vue';
import NoData from '@/views/cm-home/cm-main/add-dataset/common-grid/no-data.vue';
import { deduplicateByContext } from '@/views/cm-home/cm-main/add-dataset/config';

import LoopTable from './loop-table.vue';
import AddMetrolog from '../../add-metrology/index.vue';
import type { MetrologyGroup } from '../../add-metrology/interface';
const groupStore = datasetGroupStore;

const props = withDefaults(
  defineProps<{
    waferData: any;
    specialKey: string;
    runType: string;
    maxGroupCount?: number;
  }>(),
  {
    waferData: () => [],
    specialKey: '',
    runType: '',
    maxGroupCount: 0
  }
);
const runByModel = defineModel<string>('runByModel');
const group = defineModel<string>('group', { default: 'A' });
const metrologyCols = defineModel<string[]>('metrologyCols', { default: [] });
const emits = defineEmits(['beforeMetrologyGrouping', 'reloadRightContentTableOptions']);
// 表格选中数据
const waferGroupData: any = shallowRef([]);
const loopGroupData: any = shallowRef([]);
// 表格数据
const waferTableData: any = shallowRef([]);
const loopTableData: any = shallowRef([]);
const clearCheckData = () => {
  if (runByModel.value === 'loop') {
    loopGroupData.value = [];
  } else {
    waferGroupData.value = [];
  }
};
const addMetrologyRef = ref();
const commonGridRef = ref();
const waferTableWarp = ref();
const cruxTableDataLength = computed(
  () => commonGridRef.value?.getData()?.visibleData?.length || 0
);
const metrologyData = computed(() => {
  const fullData = commonGridRef.value?.getData()?.fullData || [];
  if (props.runType === 'HIS') {
    return fullData.filter((row: any) => row.existsTraceRow);
  } else {
    return fullData;
  }
});
const verify = (value: string[]) => {
  return value && value.length > 0;
};
// 针对分组名称的补充
const replenishGroup = ref(DATA_GROUP);
const localGroupName = ref<string[]>([]);
const getReplenishGroup = () => {
  localGroupName.value = localStorage.getItem('REPLENISH_GROUP')?.split(',') || [];
  const replenishGroupCache =
    localGroupName.value?.map((item: string) => {
      return { label: item, value: item };
    }) || [];
  replenishGroup.value = [...replenishGroupCache, ...DATA_GROUP];
  return replenishGroup.value;
};
// 还原原先被选中的数据
const handleOriginTableAdd = (data: any) => {
  const oldTableData = commonGridRef.value?.getData()?.fullData;
  // 分组后的数据作为老数据，分组前因为可能存在编辑所有作为新数据
  const newTableData = deduplicateByContext([...data, ...oldTableData]);
  if (runByModel.value === 'loop') {
    newTableData.sort((a: any, b: any) => Number(a['loopNo']) - Number(b['loopNo']));
  } else {
    newTableData.sort((a: any, b: any) => {
      const lotIdComparison = a['lotId'].localeCompare(b['lotId']);
      if (lotIdComparison !== 0) {
        return lotIdComparison;
      }
      return Number(a['waferId']) - Number(b['waferId']);
    });
  }
  commonGridRef.value?.addTableData(newTableData);
};
// 新增dataset分组
const handleAdd = () => {
  if (groupStore.copySet.has(group.value)) {
    showWarning('cm.tips.noAddCopyGroup');
    return;
  }
  const groupCheckData = runByModel.value === 'loop' ? loopGroupData.value : waferGroupData.value;
  if (groupCheckData?.length === 0) {
    showWarning('cm.tips.noFilterTrace');
    return;
  }
  if (!verifyMaxGroupCount()) return;
  // 存储用户自定义的分组名
  if (!replenishGroup.value.find((item: any) => item.value === group.value)) {
    const groupCache = localStorage.getItem('REPLENISH_GROUP') || '';
    if (groupCache) {
      const groupArr = groupCache.split(',');
      groupArr.push(group.value);
      localStorage.setItem('REPLENISH_GROUP', groupArr.join(','));
    } else {
      localStorage.setItem('REPLENISH_GROUP', group.value);
    }
    replenishGroup.value.unshift({ label: group.value, value: group.value });
    // 更新右键菜单内容
    reloadTableOptions();
    emits('reloadRightContentTableOptions');
  }
  // 处理新增分组数据
  groupStore.handleAdd(group.value, groupCheckData);
  handleCopyData('add', group.value, groupCheckData, groupStore, group.value);
  handleDelete();
  clearCheckData();
};
const handleDelete = (deleteRows?: any[]) => {
  commonGridRef.value.deleteTableData(deleteRows);
};
// 右键分组
const verifyMaxGroupCount = () => {
  if (props.maxGroupCount && groupStore.checkData?.size >= props.maxGroupCount) {
    showWarning(t('cm.tips.maxGroupCount', { count: props.maxGroupCount }));
    return false;
  }
  return true;
};
const clickGrouping = (groupName: string, data: any) => {
  // 右键标题不生效
  if (['Add Dataset', 'Move Dataset'].includes(groupName)) return;
  if (groupStore.copySet.has(groupName)) {
    showWarning('cm.tips.noAddCopyGroup');
    return;
  }
  // 没有选中的数据
  if (data?.length === 0) {
    showWarning('cm.tips.noFilterTrace');
    return;
  }
  if (!verifyMaxGroupCount()) return;
  // 处理新增分组数据
  groupStore.handleAdd(groupName, data);
  handleCopyData('add', groupName, data, groupStore, groupName);
  handleDelete(data);
};

const uploadTable = ref<boolean>(true);
const fieldList = ref<string[]>([]);
watch([() => props.waferData], () => {
  uploadTable.value = false;
  if (runByModel.value === 'loop') {
    loopTableData.value = props.waferData?.data;
  } else {
    waferTableData.value = props.waferData?.data;
  }
  fieldList.value = props.waferData?.columns?.map((item: any) =>
    props.specialKey ? (item?.key === 'waferId' ? props.specialKey : item?.key) : item?.key
  );
  setTimeout(() => {
    uploadTable.value = true;
  });
});

// 缓存数据处理
// 获取表格展示数据
const getCruxTableData = () => {
  return commonGridRef.value?.getData()?.visibleData;
};
// 更新表格数据
const setWaferTableData = (newTableData: any) => {
  commonGridRef.value?.addTableData(newTableData);
};
const setWaferLoopTableData = (row: any, type: string) => {
  commonGridRef.value?.reloadCacheData(row, type);
};
const getOptions = () => {
  return props.runType === 'TSV'
    ? getTSVWaferOptions(
        props.waferData?.columns,
        metrologyCols.value!,
        runByModel.value,
        props.specialKey
      )
    : getWaferOptions(
        props.waferData?.columns,
        metrologyCols.value!,
        runByModel.value,
        props.specialKey
      );
};
const options = ref(getOptions());
watch([() => props.waferData?.columns, () => metrologyCols.value], () => {
  options.value = getOptions();
});

const showInputMetrology = () => {
  addMetrologyRef.value.show();
};

const metrologyGrouping = ({
  allMetrologyCols,
  groupMap,
  curDelCol,
  curAddCol
}: MetrologyGroup) => {
  // 处理需要context需要过滤的列名
  groupStore.excludeFields = ['_X_ROW_KEY', 'StartTime', 'EndTime', ...allMetrologyCols];
  metrologyCols.value = allMetrologyCols;
  options.value = getOptions();

  // 等父组件 clear右侧的数据
  emits('beforeMetrologyGrouping');
  // 处理分组
  commonGridRef.value?.groupByMap(groupMap);
};
// 删除自定义分组
const handleDeleteGroup = (value: string) => {
  localGroupName.value = localGroupName.value.filter((item) => item !== value);
  if (localGroupName.value.length === 0) {
    localStorage.removeItem('REPLENISH_GROUP');
  } else {
    localStorage.setItem('REPLENISH_GROUP', localGroupName.value.join(','));
  }
  // 更新右键菜单内容
  reloadTableOptions();
  emits('reloadRightContentTableOptions');
};
// 更新右键菜单内容
const reloadTableOptions = () => {
  if (runByModel.value === 'loop') {
    commonGridRef.value?.reloadTableOptions();
  } else {
    options.value = getOptions();
  }
};

defineExpose({
  handleOriginTableAdd,
  getCruxTableData,
  setWaferTableData,
  setWaferLoopTableData,
  reloadTableOptions
});
</script>

<template>
  <div class="dataset-modal-content-left-bottom">
    <!-- Data List/loop -->
    <div v-if="runByModel === 'loop'" class="data-warp">
      <LoopTable
        :id="'loop-table-grid'"
        ref="commonGridRef"
        :columns="props.waferData?.columns"
        :loop-data="loopTableData"
        :group-store="datasetGroupStore"
        :run-type="runType"
        :special-key="specialKey"
        v-model:check-data="loopGroupData"
        v-model:uploadTable="uploadTable"
        @clickGrouping="clickGrouping"
      />
    </div>
    <!-- Data List/wafer & station -->
    <div v-else class="data-warp">
      <div key="wafer-table" ref="waferTableWarp" class="wafer-table-warp">
        <CommonTitle
          v-model:boxRef="waferTableWarp"
          :title="$t('cm.title.dataList')"
          :count="cruxTableDataLength"
          :border="false"
        >
          <template #left-icon>
            <span class="text-icon">
              <a-tooltip placement="bottom">
                <i class="iconfont chart_icon_icon zoom-icon icon-btn-help" />
                <template #title>
                  {{ $t('cm.label.datasetListTip') }}
                </template>
              </a-tooltip>
            </span>
          </template>
          <template #right-icon>
            <EesButtonTip
              is-border
              :icon="'#icon-btn-input-metrology-data'"
              :text="'MET'"
              :disabled="!cruxTableDataLength"
              @click="showInputMetrology"
            />
          </template>
        </CommonTitle>
        <div class="wafer-table-warp-table-box">
          <CommonGrid
            v-if="verify(props.waferData?.columns) && uploadTable"
            :id="'wafer-table-grid'"
            ref="commonGridRef"
            v-model:check-data="waferGroupData"
            :options="options"
            :data="waferTableData"
            :column-field-list="fieldList"
            :run-type="runType"
            :group-store="datasetGroupStore"
            :clickRowToSelect="runType !== 'TSV'"
            @clickGrouping="clickGrouping"
          />
          <NoData v-else :border="false" :special-border="false" />
        </div>
      </div>
    </div>
    <!-- dataset group -->
    <div class="add-dataset-group-box">
      <span class="label">
        {{ runByModel === 'station' ? 'Station' : 'Dataset' }}
      </span>
      <a-auto-complete
        class="select"
        allowClear
        v-model:value="group"
        :options="getReplenishGroup()"
      >
        <template #option="{ value, label }">
          <div class="option-item" :title="label">
            <span class="label">{{ label }}</span>
            <span class="delete-icon">
              <i
                v-if="localGroupName.includes(value)"
                class="iconfont icon-close2"
                @click.stop="handleDeleteGroup(value)"
              />
            </span>
          </div>
        </template>
      </a-auto-complete>
      <a-button class="add-dataset-btn" @click="handleAdd">
        {{ $t('common.btn.add') }}
      </a-button>
    </div>
    <AddMetrolog
      ref="addMetrologyRef"
      :data="metrologyData"
      :columns="options.columns"
      :before-metrology-col="metrologyCols"
      :checkData="groupStore.checkData"
      @metrologyGroup="metrologyGrouping"
    ></AddMetrolog>
  </div>
</template>

<style lang="less" scoped>
@import url('@/assets/style/variable.less');
.dataset-modal-content-left-bottom {
  height: 100%;
  // padding: 14px;
  border-top: 1px solid @border-color;
  overflow-x: hidden;
  overflow-y: scroll;
  .data-warp {
    overflow-x: hidden;
    overflow-y: scroll;
    height: calc(100% - 52px);
    // wafer & station
    .wafer-table-warp {
      display: flex;
      flex-direction: column;
      height: 100%;
      // border: solid 1px @border-color;
      border-radius: 0 0 4px 4px;
      background-color: @bg-block-color;

      .text-icon {
        color: @text-weak-text;
        margin-right: 4px;
        :hover {
          color: @primary-color;
        }
      }
      &-table-box {
        height: calc(100% - 52px);
      }
    }
  }
  // dataset group
  .add-dataset-group-box {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: center;
    margin: 10px 14px;
    .label {
      font-weight: bold;
      color: @text-sub-color;
      margin-right: 10px;
    }
    .select {
      width: 128px;
      margin-right: 10px;
    }
    .add-dataset-btn {
      color: @color-default;
      background: @primary-color;
    }
  }
}
// select下拉删除样式
.option-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .label {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .delete-icon {
    height: 20px;
    width: 20px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    color: @text-weak-text;
    cursor: pointer;
    border-radius: 50%;
    &:hover {
      background-color: @bg-hover-2-color;
    }
    &:active {
      background-color: @bg-active-2-color;
    }
  }
}
</style>
