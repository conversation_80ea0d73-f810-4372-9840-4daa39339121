<script setup lang="ts">
import { ref, watch, shallowRef, nextTick } from 'vue';
import { getFilterTraceTableOptions, getFilterFieldList } from './config';
import { t } from '@futurefab/vxe-table';
import moment from 'moment';
import { CHART_CONSTANT } from '@/constant/charts';
import { setHeaderSelectFilter, showWarning, EesButtonTip } from '@futurefab/ui-sdk-ees-basic';
import { DATA_GROUP } from '@/constant/charts';

const props = withDefaults(
  defineProps<{
    id?: string;
    data: any[];
    groupColumns: any[];
  }>(),
  {
    id: () => 'filter-trace-grid',
    data: () => []
  }
);
const emits = defineEmits(['filterLotWafer']);
const visible = ref(false);
const xGrid = ref();
const tableData = shallowRef<any[]>([]);
const option = getFilterTraceTableOptions();
const checkDataTemp = ref<any[]>([]);
const checkData = shallowRef<any>([]);
const handleCheckData = () => {
  const checkRecords = xGrid.value.getCheckboxRecords(true);
  if (checkData.value) {
    checkData.value = checkRecords;
  }
};
const cellClick = (rowInfo: any) => {
  const { row, column } = rowInfo;
  if (column?.type !== 'checkbox') {
    xGrid.value.toggleCheckboxRow(row);
    handleCheckData();
  }
};
const tableEvent = {
  checkboxAll: (val: any) => {
    handleCheckData();
  },
  checkboxChange: (val: any) => {
    handleCheckData();
  }
};
const filterChange = () => {
  // const renderData = xGrid.value.getTableData().tableData;
  // xGrid.value.toggleAllCheckboxRow(renderData);
  // handleCheckData();
};
const handleOpen = () => {
  visible.value = true;
  setTimeout(() => {
    if (xGrid.value) {
      const temp = checkDataTemp.value.length ? checkDataTemp.value : xGrid.value.getTableData()?.fullData;
      xGrid.value?.setCheckboxRow(temp, true);
      checkData.value = temp;
      reloadFilterHeader();
    }
  });
};
const handleCancel = () => {
  visible.value = false;
  checkData.value = checkDataTemp.value;
};
const handleOk = () => {
  if (checkData.value.length === 0) {
    showWarning('cm.tips.noFilterTrace');
    return;
  }
  checkDataTemp.value = checkData.value;
  emits('filterLotWafer', checkDataTemp.value);
  handleCancel();
};
const setHeader = () => {
  setHeaderSelectFilter({
    xGrid: xGrid,
    tableData: xGrid.value?.getTableData()?.fullData,
    columnFieldList: getFilterFieldList()
  });
};
const reloadFilterHeader = () => {
  setTimeout(() => {
    setHeader();
  });
};
const buildTableData = () => {
    tableData.value = props.data?.map((item: any) => {
      const startTime = moment(item.startDtts);
      const endTime = moment(item.endDtts);
      const diff = moment.duration(endTime.diff(startTime));
      const col = props.groupColumns.find(col => col.id == item.groupConfigId);
      return {
        key: col.groupNameAlias || col.key,
        wafer: item.substrateId?.split(';')[0],
        dataSet: item.groupConfigId,
        lotId: item.lotId,
        substrateId: item.substrateId,
        startTime: startTime.format(CHART_CONSTANT.TraceTimeFormat),
        endTime: endTime.format(CHART_CONSTANT.TraceTimeFormat),
        processDuration: moment.utc(diff.asMilliseconds()).format(CHART_CONSTANT.ProcessDuration),
        reference: item.referenceYn
      };
    });
}
watch(
  [() => props.data],
  () => {
    buildTableData();
    checkDataTemp.value = [];
  },
  { immediate: true }
);
watch(
  [() => props.groupColumns],
  () => {
    buildTableData();
    let temp: any[] = [];
    if (checkDataTemp.value.length && checkDataTemp.value.length === tableData.value.length) {
      // 勾选全部
      temp = xGrid.value.getTableData()?.fullData || [];
    } else {
      // 勾选部分，匹配这些数据
      const uniqueKeys = ['wafer', 'dataSet', 'lotId', 'substrateId', 'startTime', 'endTime', 'reference', 'processDuration'];
      checkDataTemp.value.forEach(checkRow => {
        const matchRow = tableData.value.find(item => {
          return uniqueKeys.every(key => checkRow[key] === item[key]);
        });
        matchRow && temp.push(matchRow);
      });
    }
    checkDataTemp.value = temp;
  },
  { immediate: true }
);
</script>

<template>
  <div class="runs-warp">
    <ees-button-tip
      is-border
      :marginRight="10"
      icon="#icon-btn-context"
      :text="$t('cm.btn.showContexts')"
      @click="handleOpen"
    />
    <vxe-modal
      v-model="visible"
      :width="'90%'"
      :height="'90%'"
      title="Process Runs"
      :destroy-on-close="true"
      resize
      :show-footer="true"
      @ok="handleOk"
      @cancel="handleCancel()"
    >
      <div class="filter-content-warp">
        <div class="label">
          <p><span>Selected：</span> {{ checkData?.length }}</p>
          <p><span>Total：</span> {{ tableData?.length }}</p>
        </div>
        <div class="content-table-warp">
          <vxe-grid
            :id="id"
            ref="xGrid"
            :data="tableData"
            v-bind="option"
            v-on="tableEvent"
            @cell-click="cellClick"
            @filter-change="filterChange"
          >
          </vxe-grid>
        </div>
      </div>
      <template #footer>
        <div class="apply-box">
          <a-button key="submit" type="primary" @click="handleOk">Apply</a-button>
          <a-button key="back" class="close-btn" @click="handleCancel()">Close</a-button>
        </div>
      </template>
    </vxe-modal>
  </div>
</template>

<style lang="less" scoped>
@import url('@/assets/style/variable.less');
.runs-warp {
  .filter-content-warp {
    display: flex;
    flex-direction: column;
    height: 100%;
    .label {
      display: flex;
      flex-direction: row;
      background-color: @bg-group-color;
      margin: 0 0 10px 0;
      padding: 14px;
      p {
        color: @text-subtitle-color;
        margin: 0 10px 0 0;
        span {
          color: @text-hint-color;
        }
      }
    }
    .content-table-warp {
      height: 94%;
    }
  }
  :deep(.vxe-modal--footer) {
    border: none;

    .close-btn {
      margin-left: 10px;
    }
  }
}
</style>
