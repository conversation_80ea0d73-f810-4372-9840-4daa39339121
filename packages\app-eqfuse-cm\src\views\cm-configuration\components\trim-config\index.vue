<script setup lang="ts">
import { onMounted, reactive, ref, watchEffect, watch } from 'vue';
import { getTrimDataRuleOptions, getValidRules } from './config';
import type { TrimOriginData, TrimRuleData, TrimRuleOption } from '../../interface';
import TrimOptionModal from '../trim-option-modal/index.vue';
import { getTrimList, saveTrimList, deleteTrimList } from '@futurefab/ui-sdk-api';
import { cloneDeep } from 'lodash-es';
import {
  showConfirm,
  successInfo,
  showWarning,
  getPermissionButton,
  setHeaderSelectFilter
} from '@futurefab/ui-sdk-ees-basic';
import { t } from '@futurefab/vxe-table';
import { useAuthority } from '@/utils/use-authority';
import { cmTrimingInfo as buttonIds } from '@/log-config/index';
import { timeArr, countArr, percentArr } from '../../config';
import { useCmConfigStore } from '@futurefab/ui-sdk-stores';

const cmCongfigStore = useCmConfigStore();
const props = withDefaults(
  defineProps<{
    isModel?: boolean;
    isHistory?: boolean;
    historyList?: TrimOriginData[];
  }>(),
  {
    isModel: false,
    historyList: () => [],
    isHistory: false
  }
);

const cmTrimAuthorityInfo = ref({} as any);
const trimContextOption = ref<{ alias: string; column: string }[]>([]);
const options = ref(
  getTrimDataRuleOptions(trimContextOption.value, props.isModel, props.isHistory)
);

const data = ref<TrimRuleData[]>([]);
const loading = ref(false);
const trimModalRef = ref();
const xGrid = ref();
watch(
  () => props.isHistory,
  () => {
    getData();
  }
);
function setHeader() {
  const columns = trimContextOption.value.map((item) => item.column);
  setHeaderSelectFilter({
    xGrid: xGrid,
    tableData: xGrid.value.getTableData().fullData,
    columnFieldList: ['rawId', ...columns]
  });
}
const events = {
  toolbarToolClick: ({ code }: { code: string }) => {
    const checkboxRecords = xGrid.value && xGrid.value.getCheckboxRecords();
    xGrid.value?.clearValidate();
    switch (code) {
      case 'refresh':
        getData();
        break;
      case 'add':
        dealAdd();
        break;
      case 'save':
        dealSave();
        break;
      case 'delete':
        dealDelete(checkboxRecords);
        break;
    }
  }
};
const modelValue = reactive({
  row: {} as TrimRuleData,
  data: {} as TrimRuleOption
});
const showEdit = (row: TrimRuleData) => {
  if (props.isModel) {
    return;
  }
  modelValue.row = row;
  modelValue.data = cloneDeep(row.trimRuleOption);
  trimModalRef.value.show();
};
const baseClass = 'cm-trim-data-config';
function originDataToTrim(item: TrimOriginData): TrimRuleData {
  return {
    ...item,
    ...item.trimRuleContext,
    trimming: JSON.stringify(item.trimRuleOption)
  };
}
async function getData() {
  loading.value = true;
  try {
    const res: {
      data: { dataList: TrimOriginData[]; fields: { alias: string; column: string }[] };
    } = await getTrimList({
      bodyParams: { applyRule: props.isModel },
      headerParams: {
        log: 'Y',
        page: 'cm-cm-config',
        action: 'get-trim-list'
      }
    });
    trimContextOption.value = res.data?.fields || [];
    if (props.isHistory) {
      data.value = props.historyList.map(originDataToTrim);
    } else {
      data.value = res.data?.dataList.map(originDataToTrim) || [];
    }
    options.value = getTrimDataRuleOptions(trimContextOption.value, props.isModel, props.isHistory);
    setTimeout(setHeader, 50);
  } finally {
    loading.value = false;
  }
}
function defaultNumber(val: any, defaultValue: number) {
  return isNaN(Number(val)) ? defaultValue : Number(val);
}
async function dealAdd() {
  const { row } = await xGrid.value.insert();
  row.rawId = null;
  row.trimRuleOption = {
    trimBy: 'COUNT',
    start: {
      count: defaultNumber(cmCongfigStore.state.trimmingOptionMaxCount, 1),
      time: defaultNumber(cmCongfigStore.state.trimmingOptionMaxTime, 1000),
      percent: defaultNumber(cmCongfigStore.state.trimmingOptionMaxPercent, 10)
    },
    end: {
      count: defaultNumber(cmCongfigStore.state.trimmingOptionMaxCount, 1),
      time: defaultNumber(cmCongfigStore.state.trimmingOptionMaxTime, 1000),
      percent: defaultNumber(cmCongfigStore.state.trimmingOptionMaxPercent, 10)
    }
  };
  row.trimming = JSON.stringify(row.trimRuleOption);
  await xGrid.value.setActiveRow(row);
}

const transToOrigin = (item: TrimRuleData): TrimOriginData => {
  const contextObj: Record<string, string> = {};
  trimContextOption.value.forEach(({ column }: { column: string }) => {
    contextObj[column] = item[column];
  });
  return {
    rawId: item.rawId,
    trimRuleContext: contextObj,
    trimRuleOption: item.trimRuleOption
  };
};
async function dealSave() {
  const { updateRecords, insertRecords } = xGrid.value.getRecordset();
  const temp = [...updateRecords, ...insertRecords];
  if (!temp.length) {
    return showWarning('common.tip.saveTip');
  }

  try {
    const errInfo = await xGrid.value.validate(temp);
    if (errInfo && Object.keys(errInfo)?.length) {
      return;
    }

    const type = await showConfirm({ msg: 'common.tip.saveConfirm' });
    if (type !== 'confirm') return;

    loading.value = true;
    const res = await saveTrimList({
      bodyParams: temp.map(transToOrigin),
      headerParams: {
        log: 'Y',
        page: 'cm-cm-config',
        action: 'save-trim-list'
      }
    });
    if (res.status === 'SUCCESS') {
      await getData();
      successInfo('common.tip.actionSuccess');
    }
  } finally {
    loading.value = false;
  }
}

async function dealDelete(checkboxRecords: any[]) {
  if (checkboxRecords.length) {
    // 二次确认弹窗
    const type = await showConfirm({
      msg: t('common.tip.deleteConfirm', {
        total: checkboxRecords.length as any
      })
    });
    if (type !== 'confirm') return;
    const addRows: TrimRuleData[] = []; // 新增的直接删除
    const existRows: TrimRuleData[] = []; // 请求接口删除
    checkboxRecords.forEach((row) => {
      if (!row.rawId) {
        addRows.push(row);
        xGrid.value.remove(row);
      } else {
        existRows.push(row);
      }
    });
    if (existRows.length) {
      try {
        loading.value = true;
        const res = await deleteTrimList({
          bodyParams: existRows.map((item) => item.rawId as number),
          headerParams: {
            log: 'Y',
            page: 'cm-cm-config',
            action: 'delete-trim-list'
          }
        });
        if (res.status === 'SUCCESS') {
          xGrid.value.remove(existRows);
          successInfo('common.tip.actionSuccess');
        }
      } finally {
        loading.value = false;
      }
    } else {
      successInfo('common.tip.actionSuccess');
    }
    xGrid.value.remove(addRows);
  } else {
    showWarning('common.tip.selectData');
  }
}

const apply = (events: TrimRuleOption) => {
  modelValue.row.trimRuleOption = events;
  modelValue.row.trimming = JSON.stringify(events);
};

onMounted(() => {
  getData();

  getPermissionButton('/cm/cm-config').then((res: any) => {
    cmTrimAuthorityInfo.value = res;
  });
});
useAuthority(xGrid, options, cmTrimAuthorityInfo, buttonIds);
const _tempFn = (row: TrimRuleData) => {
  switch (row.trimRuleOption.trimBy) {
    case 'COUNT':
      return countArr;
    case 'TIME':
      return timeArr;
    case 'PERCENT':
      return percentArr;
    default:
      return [];
  }
};
const getTipArr = (row: TrimRuleData) => {
  const temp = _tempFn(row);
  if (temp.length) {
    return [
      [temp[0], temp[3]],
      [temp[1], temp[4]],
      [temp[2], temp[5]]
    ];
  } else {
    return [];
  }
};

const validRules = ref<Record<string, any[]>>({});
watchEffect(() => {
  validRules.value = getValidRules(trimContextOption.value);
});
defineExpose({
  setCheckList: (checkList: any) => {
    // 传入的内容全部选中，未传入的全部取消选中
    xGrid.value?.setAllCheckboxRow(false);
    xGrid.value?.setCheckboxRow(
      data.value.filter((item) => checkList.some((row: any) => row.rawId === item.rawId)),
      true
    );
  },
  getCheckList: () => {
    return xGrid.value?.getCheckboxRecords().map(transToOrigin) || [];
  },
  clear: () => {
    xGrid.value?.clearCheckboxRow();
  },
  refresh: () => {
    getData();
  }
});
</script>
<template>
  <div :class="[baseClass, isModel ? '' : 'b-radius', 'modal-table-small-radius']">
    <vxe-grid
      :id="baseClass + '-grid'"
      ref="xGrid"
      :class="[baseClass + '-grid', isModel ? '' : 'table-b-radius']"
      v-bind="options"
      :data="data"
      :loading="loading"
      :edit-rules="validRules"
      v-on="events"
    >
      <template #editRule="{ row }">
        <span :class="[baseClass + '-rule-cell-box']" @click.prevent="showEdit(row)">
          <a-tooltip
            placement="bottomRight"
            trigger="hover"
            :auto-adjust-overflow="true"
            :overlay-class-name="baseClass + '-popover'"
          >
            <template v-if="row?.trimRuleOption?.trimBy" #title>
              <div :class="baseClass + '-tip-title'">
                <div :class="baseClass + '-tip-title-label'">{{ $t('cm.label.trimType') }}:</div>
                <div :class="baseClass + '-tip-title-value'">
                  {{ row?.trimRuleOption?.trimBy }}
                </div>
              </div>
              <div :class="baseClass + '-tip-content'">
                <div
                  v-for="(col, index) in getTipArr(row)"
                  :key="index"
                  :class="baseClass + '-tip-content-item'"
                >
                  <div :class="baseClass + '-tip-content-item-label'">
                    <div
                      v-for="item in col"
                      :key="item.key"
                      :class="baseClass + '-tip-content-item-label-item'"
                    >
                      {{ item.unit ? item.label + `(${item.unit})` : item.label }}:
                    </div>
                  </div>
                  <div :class="baseClass + '-tip-content-item-value'">
                    <div
                      v-for="item in col"
                      :key="item.key"
                      :class="baseClass + '-tip-content-item-value-item'"
                      :title="row.trimRuleOption[item.fristK][item.secondK]"
                    >
                      {{ row.trimRuleOption[item.fristK][item.secondK] }}
                    </div>
                  </div>
                </div>
              </div>
            </template>
            <span :class="[baseClass + '-rule-setting']">{{ 'setting' }}</span>
          </a-tooltip>
        </span>
      </template>
    </vxe-grid>
  </div>
  <TrimOptionModal ref="trimModalRef" :data="modelValue.data" @apply="apply"></TrimOptionModal>
</template>

<style scoped lang="less">
@import url('@/assets/style/variable.less');
.cm-trim-data-config {
  height: 100%;
  width: 100%;
  &-rule-cell-box {
    cursor: pointer;
    display: inline-block;
    width: 100%;
    cursor: pointer;
    height: 30px;
    line-height: 30px;
    &:hover {
      color: @primary-color;
    }
    .cm-trim-data-config-rule-setting {
      padding: 0 !important;
      color: @primary-color;
      &:hover {
        color: @primary-color-hover;
      }
    }
  }

  :deep(.trimming-rule-cell .vxe-cell--valid) {
    top: 0;
    width: auto;
    right: calc(100% + 6px);
    left: auto;
    transform: none;
    height: 30px;
    &::after {
      display: none;
    }
    &::before {
      content: '';
      position: absolute;
      border: 6px solid;
      border-color: transparent;
      border-left-color: #f56c6c;
      top: calc(50% - 6px);
      right: -12px;
    }
  }
  :deep(.trimming-rule-cell .vxe-cell) {
    padding: 0;
    span {
      padding: 0 14px;
    }
  }
}

.cm-trim-data-config-tip-title {
  display: flex;
  flex-wrap: nowrap;
  font-size: 14px !important;
  color: @text-reserve;
  &-label {
    color: @text-reserve;
    font-weight: bold !important;
    margin-right: 10px;
  }
  &-value {
    font-weight: 400 !important;
  }
}
.cm-trim-data-config-tip-content {
  display: flex;
  flex-wrap: nowrap;
  color: @text-reserve;
  padding-top: 6px;
  &-item {
    display: flex;
    flex-wrap: nowrap;
    flex-shrink: 0;
    margin-top: 4px;
    margin-right: 10px;
    &:last-child {
      margin-right: 0px;
    }
    &-label {
      margin-right: 5px;
      flex-shrink: 0;
      &-item {
        font-size: 12px;
        text-align: left;
        white-space: nowrap;
        font-weight: bold;
      }
    }
    &-value {
      max-width: 90px;
      overflow: hidden;
      text-overflow: ellipsis;
      min-width: 60px;
      &-item {
        font-size: 12px;
      }
    }
  }
}
</style>
<style lang="less">
@import url('@/assets/style/variable.less');
.cm-trim-data-config-popover {
  .ant-tooltip-inner {
    padding: 14px;
  }
  max-width: 600px !important;
  z-index: 99999 !important;
  border-radius: 4px;
  .ant-popover-inner-content {
    height: 98px;
  }
}
</style>
