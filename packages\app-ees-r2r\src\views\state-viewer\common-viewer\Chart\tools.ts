import dayjs from 'dayjs';
import * as echarts from '@futurefab/echarts';
import type { GroupValue, GroupTableValue } from './interface';
import { cloneDeep } from 'lodash-es';
import { formatterY, binarySearch } from '@futurefab/ui-sdk-ees-basic';
import { setColor } from '@/utils/tools';
export const serieConfig = ({ isShowPoint }: { isShowPoint: boolean }) => {
  return {
    showSymbol: isShowPoint,
    showAllSymbol: isShowPoint,
    // symbolSize: isShowPoint ? 2 : 4,
    symbolSize: isShowPoint ? 1 : 2,
    smooth: false,
    animationThreshold: 1000,
    emphasis: {
      // disabled: true,
      disabled: false,
      scale: isShowPoint
    }
  };
};
//获取chart的series数据
export const getSeries = ({
  list,
  paramName,
  isDeleteBlank,
  totalTimeList,
  isShowPoint,
  colorIndex
}: {
  list: any[];
  paramName: { x: string; y: string };
  isDeleteBlank?: boolean;
  totalTimeList?: number[];
  isShowPoint?: boolean;
  colorIndex?: number | null;
}) => {
  const series = [] as echarts.SeriesOption[];
  list.forEach((data: any, i: number) => {
    const seriesData = [] as any[];
    if (data[paramName.x] && data[paramName.x].length > 0) {
      data[paramName.x].forEach((val: any, index: number) => {
        if (isDeleteBlank) {
          if (totalTimeList && totalTimeList.length > 0) {
            const idx = binarySearch(totalTimeList, val);
            seriesData.push([idx, data[paramName.y][index]]);
          } else {
            seriesData.push([index, data[paramName.y][index]]);
          }
        } else {
          seriesData.push([val, data[paramName.y][index]]);
        }
      });
    }
    let color = data['paramInfo']['color'] || setColor(i);
    if (typeof colorIndex == 'number') {
      color = setColor(colorIndex);
    }
    series.push({
      type: 'line',
      name: data['paramInfo']['legend'],
      color,
      yAxisIndex: data['paramInfo']['yAxisIndex'],
      lineStyle: {
        // width: 1,
        width: 0.2
      },
      connectNulls: true,
      data: seriesData,
      ...serieConfig({ isShowPoint: !!isShowPoint || data['sumValue'].length == 1 })
    });
  });
  return series;
};

//获取chart单条数据的data
export const getSignalSeries = ({
  data,
  paramName,
  isDeleteBlank
}: {
  data: any;
  paramName: { x: string; y: string };
  isDeleteBlank?: boolean;
}) => {
  const seriesData = [] as any[];
  data[paramName.x].forEach((val: any, index: number) => {
    if (isDeleteBlank) {
      seriesData.push([index, data[paramName.y][index]]);
    } else {
      seriesData.push([val, data[paramName.y][index]]);
    }
  });
  return seriesData;
};

//获取legend
export const getLegend = (list: any, isSumAliasSplit?: boolean) => {
  const legend = [] as string[];
  if (Array.isArray(list)) {
    list &&
      list.forEach((item: any) => {
        const name = item['paramInfo']['legend'];
        if (legend.indexOf(name) > -1) return false;
        if (isSumAliasSplit) {
          legend.push(name.split(`${item['paramInfo']['sumParamAlias']}^`)[1]);
        } else {
          legend.push(name);
        }
      });
  } else {
    if (isSumAliasSplit) {
      legend.push(list['paramInfo']['legend'].split(`${list['paramInfo']['sumParamAlias']}^`)[1]);
    } else {
      legend.push(list['paramInfo']['legend']);
    }
  }

  return legend;
};
//获取小数点后保留两位的百分比
export const getPercentage = (val: number) => {
  const num = val * 100;
  return num.toFixed(2);
};
//获取当天的日期 时间戳形式
export const getToday = () => {
  return {
    start: dayjs().startOf('day'),
    end: dayjs().endOf('day')
  };
};

export const formatterDate = (timestamp: number | string) => {
  return dayjs(timestamp).format('YYYY-MM-DD HH:mm:ss.SSS');
  // return dayjs(Number(timestamp)).format('YYYY-MM-DD HH:mm:ss.SSS');
};
//获取tooltip
export const getTooltip = ({ value, dataIndex }: { value: any; dataIndex: number }) => {
  // console.log('getTooltip', value, dataIndex);
  if (!value) return '';
  const paramInfo = value['paramInfo'];
  let tip = '';
  tip += `${value.itemName || ''}${(value.itemName && ': ') || ''}${
    value['seriesValue'][dataIndex] || ''
  }</br>`;
  tip += `${value.keyEntries.map((v: string[]) => `${v[0]}: ${v[1]}`).join('</br>')}</br>`;
  /* tip += `EQP ID: ${paramInfo['eqpId'] || ''}</br>`;
    tip += `Module Alias: ${paramInfo['moduleAlias'] || ''}</br>`;
    tip += `Lot ID: ${value['lotid'] ? value['lotid'][dataIndex] : ''}</br>`;
    tip += `Substrate ID: ${value['substrateid'] ? value['substrateid'][dataIndex] : ''}</br>`;
    tip += `Slot ID: ${value['slot'] ? value['slot'][dataIndex] : ''}</br>`;
    tip += `Recipe ID: ${value['recipe'] ? value['recipe'][dataIndex] : ''}</br>`;
    tip += `Product ID: ${value['product'] ? value['product'][dataIndex] : ''}</br>`;
    tip += `Step ID: ${value['step'] ? value['step'][dataIndex] : ''}</br>`;
    tip += `Step Name: ${value['step_name'] ? value['step_name'][dataIndex] : ''}</br>`; */
  tip += `Time: ${value['timestamp'] ? formatterDate(value['timestamp'][dataIndex]) : ''}</br>`;
  /* tip += `${paramInfo['sumParamAlias'] || ''}: ${
        value['sumValue'] ? value['sumValue'][dataIndex] : ''
    }</br>`;
    if (paramInfo['yName'] && paramInfo['yName'] != '') {
        const name = paramInfo['yName'].split('^')[0];
        tip += `${name || ''}: ${value['ySumVal'] ? value['ySumVal'][dataIndex] : ''}</br>`;
    } */
  /* const keys = Object.keys(value);
    console.log('getTooltip', keys);
    keys.forEach((item: string) => {
        if (
            item.indexOf('^') > -1 &&
            item.indexOf('RSD_') > -1 &&
            value[item][dataIndex] != undefined
        ) {
            const arr = item.split('^');
            console.log(arr);
            const name = arr[1];
            tip += `${name}: ${value[item][dataIndex]}</br>`;
        }
    }); */
  return tip;
};
//echarts 基础配置
export const setOptions = (config: any) => {
  let yAxis = {} as any;
  if (config.yAxis && Array.isArray(config.yAxis)) {
    const list = [] as any[];
    config.yAxis.forEach((item: any) => {
      const val = {
        type: 'value',
        axisLine: {
          show: true,
          onZero: false
        },
        axisLabel: {
          formatter: (value: any) => {
            const newValue = formatterY(value);
            return newValue;
          }
        },
        // alignTicks: true,
        ...item
      };
      list.push(val);
    });
    yAxis = list;
  } else {
    yAxis = {
      type: 'value',
      axisLine: {
        show: true,
        onZero: false
      },
      alignTicks: true,
      ...config.yAxis
    };
  }
  const options: echarts.EChartsOption = {
    ...config,
    grid: {
      top: 20,
      left: 80,
      right: 80,
      ...config.grid
    },
    xAxis: {
      type: 'time',
      ...config.xAxis,
      // boundaryGap: ['1%',0],
      axisLine: { onZero: false },
      axisLabel: {
        formatter: '{MM}-{dd} {HH}:{mm}:{ss}',
        color: '#7E88AE',
        width: 70,
        overflow: 'break',
        ...(config.xAxis && config.xAxis.axisLabel)
      },
      axisPointer: {
        snap: true,
        type: 'line',
        triggerTooltip: false,
        lineStyle: {
          color: '#7581BD',
          width: 2,
          type: 'solid'
        },
        handle: {
          show: true,
          color: '#7581BD'
        },
        triggerOn: 'mousemove',
        ...(config.xAxis && config.xAxis.axisPointer)
      }
    },
    axisPointer: {
      show: true,
      type: 'line',
      snap: true,
      label: {
        show: true
      },
      ...config.axisPointer
    },
    yAxis: yAxis,
    legend: {
      type: 'scroll',
      top: 'bottom',
      textStyle: {
        color: '#7E88AE'
      },
      ...config.legend
    },
    tooltip: {
      borderColor: '#fff',
      textStyle: {
        fontSize: 12,
        color: '#666'
      },
      appendToBody: true,
      className: 'echarts-tooltip-hide',
      ...config.tooltip
    },
    toolbox: {
      show: true,
      itemSize: 0,
      feature: {
        dataZoom: {
          enableBrushBack: true
        },
        brush: {
          type: ['rect']
        }
      },
      iconStyle: {
        opacity: 0
      },
      showTitle: false
    },
    brush: {
      xAxisIndex: 'all',
      yAxisIndex: 'all',
      brushLink: 'none',
      seriesIndex: 'all',
      brushType: 'rect',
      brushMode: 'multiple',
      throttleType: 'debounce',
      transformable: false
    },
    animation: false,
    animationThreshold: 0,
    animationDurationUpdate: 0,
    animationDuration: 0
  };
  return options;
};
export const getLegendConfig = (legendList: string[], layout: number) => {
  return {
    type: legendList.length > 1 ? 'scroll' : 'plain',
    data: legendList,
    formatter: (name: string) => {
      if (layout == 2) {
        return echarts.format.truncateText(name, 400, '12px', '…', {});
      } else if (layout > 2) {
        return echarts.format.truncateText(name, 200, '12px', '…', {});
      } else {
        return name;
      }
    },
    tooltip: {
      show: layout > 0 ? true : false
    }
  };
};
export const dispatchBrush = (myChart: echarts.ECharts) => {
  myChart.dispatchAction({
    type: 'takeGlobalCursor',
    key: 'brush',
    brushOption: {
      brushType: 'rect',
      brushMode: 'multiple'
    }
  });
};
export const removeBrush = (myChart: echarts.ECharts) => {
  myChart.dispatchAction({
    type: 'brush', //选择action行为
    areas: [] //areas表示选框的集合，此时为空即可。
  });
};
export const dispatchZoom = (myChart: echarts.ECharts) => {
  myChart.dispatchAction({
    type: 'takeGlobalCursor',
    key: 'dataZoomSelect',
    dataZoomSelectActive: true
  });
};
export const initChartEvents = ({
  myChart,
  filterLegendFun,
  mutilTooltipFun,
  getSelectData,
  finishedFun,
  drawChartFun
}: {
  myChart: echarts.ECharts;
  filterLegendFun?: (arg0: Object) => void | null;
  mutilTooltipFun?: (arg0: Object) => void;
  getSelectData?: (arg0: any[]) => void;
  finishedFun?: () => void;
  drawChartFun?: () => undefined | boolean;
}) => {
  dispatchZoom(myChart);
  removeBrush(myChart);
  let moveX = 0;
  let isClickBaseLine = false;
  myChart.getZr().on('mousedown', (event: any) => {
    moveX = event.offsetX;
    isClickBaseLine = event.target && event.target.draggable;
  });
  myChart.getZr().on('mouseup', (event: any) => {
    if (event.offsetX < moveX && !isClickBaseLine) {
      if (drawChartFun) {
        drawChartFun();
      }
      dispatchZoom(myChart);
    }
  });
  myChart.on('click', (params: any) => {
    mutilTooltipFun && mutilTooltipFun({ type: 'show', value: params });
  });
  myChart.on('datazoom', () => {
    mutilTooltipFun && mutilTooltipFun({ type: 'hide' });
  });
  myChart.on('mouseout', () => {
    const dom = document.getElementsByClassName('echarts-tooltip-hide');
    Array.from(dom).forEach((element: any) => {
      element.style.display = 'none';
    });
  });
  myChart.on('legendselectchanged', (argus: any) => {
    const { selected, name } = argus;
    filterLegendFun && filterLegendFun({ selected, clickName: name });
  });
  myChart.on('rendered', function () {
    finishedFun && finishedFun();
  });
  myChart.on('brushend', (params: any) => {
    //brushType 为 'rect' range 和 coordRange 的格式为：[[minX, maxX], [minY, maxY]]
    const areas = params.areas;
    const selectedSeries = [] as any;
    areas.forEach((item: any) => {
      item.coordRanges.forEach((coordRange: any) => {
        const min = [coordRange[0][0], coordRange[1][0]];
        const max = [coordRange[0][1], coordRange[1][1]];
        selectedSeries.push({
          min,
          max
        });
      });
    });
    const options = myChart.getOption();
    const series = options.series as any[];
    const legend = options.legend as any[];
    const legendData = legend[0].selected;
    const findData = [] as any[];
    series.forEach((item: any, index: number) => {
      if (item.type == 'scatter') {
        if (legendData[item.name] !== undefined && !legendData[item.name]) {
          return false;
        }
      } else {
        if (!legendData[item.name]) return false;
      }
      item.data.forEach((val: any, idx: number) => {
        const xVal = val[0];
        const yVal = val[1];
        selectedSeries.forEach((value: any) => {
          const min = value.min;
          const max = value.max;
          if (xVal >= min[0] && xVal <= max[0] && yVal >= min[1] && yVal <= max[1]) {
            findData.push({ serieIndex: index, dataIndex: idx });
          }
        });
      });
    });
    getSelectData && getSelectData(findData);
  });
};

//设置分组
export const setSeriesGroup = (i: number) => {
  let group = '';
  if (i < 26) {
    group = String.fromCharCode('A'.charCodeAt(0) + i);
  } else {
    let num = Math.floor(i / 26) + 1;
    let rest = i % 26;
    let str = '';
    for (let ind = 0; ind < num; ind++) {
      str += String.fromCharCode('A'.charCodeAt(0) + rest);
    }
    group = str;
  }
  return group;
};
//移除tooltip
export const clearMutilTooltip = (dom: Element) => {
  if (dom.children.length == 0) return false;
  const marksList = document.getElementsByClassName('xchart-marker__item xchart-marker');
  const lineList = document.getElementsByClassName('xchart-marker__line');
  marksList.length > 0 &&
    Array.from(marksList).forEach((item: Element) => {
      dom && dom.removeChild(item);
    });
  lineList.length > 0 &&
    Array.from(lineList).forEach((item: Element) => {
      dom && dom.removeChild(item);
    });
};
// 过滤数据
const filterFun = ({
  data,
  type,
  value
}: {
  data: any;
  type: string; //key
  value: any[] | string | number; // 要找的值
}) => {
  const keys: string[] = Object.keys(data);
  const values: any = Object.values(data);
  const obj = {} as any;
  const setKey = (i: number) => {
    keys.forEach((key: string, index: number) => {
      if (key == 'paramInfo') {
        obj[key] = cloneDeep(values[index]);
      } else {
        if (obj[key] && Array.isArray(obj[key])) {
          obj[key].push(values[index][i]);
        } else {
          obj[key] = [values[index][i]];
        }
      }
    });
  };
  data[type].forEach((val: string, index: number) => {
    if (Array.isArray(value)) {
      if (value.includes(val)) {
        setKey(index);
      }
    } else {
      if (value === val) {
        setKey(index);
      }
    }
  });
  return obj;
};

export const handleFilterChartData = ({
  data,
  name,
  value
}: {
  data: any[];
  name: string;
  value: string[];
}) => {
  const newData = [] as any;
  data.forEach((item: any) => {
    let obj = {} as any;
    switch (name) {
      case 'eqp':
        if (value.includes(item['paramInfo']['eqpId'])) {
          obj = item;
        }
        break;
      case 'moduleAlias':
        if (value.includes(item['paramInfo']['moduleName'])) {
          obj = item;
        }
        break;
      case 'recipe':
        obj = filterFun({ data: item, type: 'recipe', value });
        break;
      case 'product':
        obj = filterFun({ data: item, type: 'product', value });
        break;
      case 'step':
        obj = filterFun({ data: item, type: 'step', value });
        break;
      default:
        break;
    }
    if (Object.keys(obj).length > 0 && obj.sumValue.length > 0) {
      newData.push(obj);
    }
  });
  newData.forEach((item: any) => {
    item['paramInfo']['step'] = [...new Set(item['step'])];
    item['paramInfo']['recipe'] = [...new Set(item['recipe'])];
    item['paramInfo']['product'] = [...new Set(item['product'])];
  });
  return newData;
};

const groupFun = ({ data, type }: { data: any; type: string }) => {
  const newData = [] as any[];
  data['paramInfo'][type] &&
    data['paramInfo'][type].forEach((item: string) => {
      const obj = filterFun({ data, type, value: item });
      if (Object.keys(obj).length > 0 && obj.sumValue.length > 0) {
        obj['paramInfo'][type] = [item];
        newData.push(obj);
      }
    });
  return newData;
};

export const handleGroupChartData = ({ data, groupingMap }: { data: any[]; groupingMap: any }) => {
  const keys = Object.keys(groupingMap);
  let myData = data as any[];
  if (keys.includes('recipe') || keys.includes('step') || keys.includes('product')) {
    const loopFun = (chartData: any, type: string) => {
      const newData = [] as any[];
      chartData.forEach((item: any) => {
        const list = groupFun({
          data: item,
          type
        });
        newData.push(...list);
      });
      return newData;
    };

    for (const key in groupingMap) {
      switch (key) {
        case 'eqp':
        case 'moduleAlias':
          break;
        case 'recipe':
        case 'product':
        case 'step':
          myData = loopFun(myData, key);
          break;
        default:
          break;
      }
    }
  }
  const groupList = [] as any[];
  const values = Object.values(groupingMap) as any[];
  const notNullValue = values.filter((item: any[]) => item.length > 0);
  const loopGroup = (key: string, index: number, name: string, group: string) => {
    groupingMap[key].forEach((item: GroupValue) => {
      const myName = `${name}^${item.name}`;
      const myGroup = `${group}${item.group}`;
      if (index < keys.length - 1) {
        loopGroup(keys[index + 1], index + 1, myName, myGroup);
      } else {
        if (name.split('^').length != notNullValue.length) return false;
        groupList.push({
          name: myName,
          group: myGroup
        });
      }
    });
  };

  if (notNullValue.length > 1) {
    keys.forEach((key: string, index: number) => {
      if (groupingMap[key].length > 0) {
        loopGroup(key, index, '', '');
      }
    });
  } else {
    notNullValue.length > 0 &&
      notNullValue[0].forEach((item: any) => {
        groupList.push({
          name: `^${item.name}`,
          group: item.group
        });
      });
  }
  let groupNameList = [] as string[];
  groupList.forEach((item: any) => {
    groupNameList.push(item.group);
  });
  groupNameList = [...new Set(groupNameList)] as string[];
  const colorMap = {} as any;
  groupNameList.forEach((name: string, index: number) => {
    colorMap[name] = setColor(index);
  });
  myData.forEach((item: any) => {
    let legendStr = `${item['paramInfo']['sumParamAlias']}`;
    if (keys.includes('eqp') || keys.includes('moduleAlias')) {
      legendStr += `^${item['paramInfo']['moduleName']}`;
    }
    keys.forEach((val: string) => {
      if (val == 'recipe') {
        legendStr += `^${item['paramInfo']['recipe'][0]}`;
      } else if (val == 'product') {
        legendStr += `^${item['paramInfo']['product'][0]}`;
      } else if (val == 'step') {
        legendStr += `^${item['paramInfo']['step'][0]}`;
      }
    });
    item['paramInfo']['legend'] = legendStr;
    groupList.forEach((val: any) => {
      let currentLegend = item['paramInfo']['sumParamAlias'];
      if (
        (keys.includes('eqp') || keys.includes('moduleAlias')) &&
        !(val.name.indexOf(item['paramInfo']['moduleName']) > -1)
      ) {
        currentLegend += `^${item['paramInfo']['moduleName']}`;
      }
      if (`${currentLegend}${val.name}` == legendStr) {
        item['paramInfo']['color'] = colorMap[val.group];
      }
    });
  });
  return myData;
};

const loopScatterData = ({
  xItem,
  yItem,
  type,
  xName,
  yName,
  isStep
}: {
  xItem: any;
  yItem: any;
  type: string;
  xName: string;
  yName: string;
  isStep?: boolean;
}) => {
  const obj = {} as any;
  const keys = Object.keys(xItem);
  xItem[type].forEach((nxValue: string, nxIndex: number) => {
    yItem[type].forEach((nyValue: string, nyIndex: number) => {
      let flag = false;
      if (type == 'lotid') {
        flag = nxValue == nyValue;
      } else if (type == 'substrateid') {
        flag = nxValue == nyValue && xItem.lotid[nxIndex] == yItem.lotid[nyIndex];
      } else if (type == 'step') {
        flag =
          nxValue == nyValue &&
          xItem.lotid[nxIndex] == yItem.lotid[nyIndex] &&
          xItem.substrateid[nxIndex] == yItem.substrateid[nyIndex];
      }
      if (flag) {
        keys.forEach((name: string) => {
          if (name == 'paramInfo') {
            obj['paramInfo'] = xItem.paramInfo;
            if (isStep) {
              obj['paramInfo'].xName =
                `${xItem.paramInfo.sumParamAlias}_${xItem.paramInfo.step[0]}^${xItem.paramInfo.moduleName}`;
              obj['paramInfo'].yName =
                `${yItem.paramInfo.sumParamAlias}_${yItem.paramInfo.step[0]}^${yItem.paramInfo.moduleName}`;
            } else {
              obj['paramInfo'].xName = xName;
              obj['paramInfo'].yName = yName;
            }

            return false;
          }
          if (obj[name]) {
            obj[name].push(xItem[name][nxIndex]);
          } else {
            obj[name] = [xItem[name][nxIndex]];
          }
        });
        if (obj['yTimestamp']) {
          obj['yTimestamp'].push(yItem.timestamp[nyIndex]);
        } else {
          obj['yTimestamp'] = [yItem.timestamp[nyIndex]];
        }
        if (obj['ySumVal']) {
          obj['ySumVal'].push(yItem.sumValue[nyIndex]);
        } else {
          obj['ySumVal'] = [yItem.sumValue[nyIndex]];
        }
        if (obj['xyValue']) {
          obj['xyValue'].push([xItem.sumValue[nxIndex], yItem.sumValue[nyIndex]]);
        } else {
          obj['xyValue'] = [[xItem.sumValue[nxIndex], yItem.sumValue[nyIndex]]];
        }
      }
    });
  });
  return obj;
};

export const getScatterData = ({
  chartItem,
  data,
  isStepGroup,
  chartType
}: {
  chartItem: { x: string[]; y: string[] };
  data: any[];
  isStepGroup?: false;
  chartType: number;
}) => {
  let scatterChartData = [] as any[];
  chartItem.x.forEach((x: string) => {
    chartItem.y.forEach((y: string) => {
      //x与y相同的sumModuleName，不显示scatter
      if (x == y) return false;
      const xData = data.filter((item: any) => item.paramInfo.sumModuleName == x);
      const yData = data.filter((item: any) => item.paramInfo.sumModuleName == y);
      xData.forEach((xItem: any) => {
        yData.forEach((yItem: any) => {
          if (xItem.paramInfo.sumCategory == yItem.paramInfo.sumCategory) {
            let type = '';
            if (!isStepGroup) {
              if (xItem.paramInfo.sumCategory === 'LOT' || xItem.paramInfo.sumCategory === 'TIME') {
                //仅需lot相等
                type = 'lotid';
              } else if (xItem.paramInfo.sumCategory === 'SUBSTRATE') {
                //lot&substrate相等
                type = 'substrateid';
              } else if (xItem.paramInfo.sumCategory === 'STEP') {
                //lot&substrate&step相等
                type = 'step';
              }
              if (
                type == '' &&
                xItem.substrateid &&
                xItem.substrateid.length > 0 &&
                yItem.substrateid &&
                yItem.substrateid.length > 0
              ) {
                //event Sum数据， sumCategory为空，需要判断数据中是否有substrateid是否为空或
                type = 'substrateid';
              }
            } else {
              //包含step的grouping,lot&substrate两个都要相等
              type = 'substrateid';
            }
            if (type == '') return false;
            const obj = loopScatterData({
              xItem,
              yItem,
              type,
              xName: x,
              yName: y,
              isStep: true
            });
            if (Object.keys(obj).length > 0) {
              scatterChartData.push(obj);
            }
          }
        });
      });
    });
  });
  if (chartType == 1) {
    const newSData = [] as any[];
    scatterChartData.forEach((item: any) => {
      const exist = newSData.filter(
        (value: any) =>
          value[0].paramInfo.xName == item.paramInfo.xName &&
          value[0].paramInfo.yName == item.paramInfo.yName
      );
      if (exist.length == 0) {
        newSData.push([item]);
      } else {
        newSData.forEach((value: any) => {
          if (
            value[0].paramInfo.xName == item.paramInfo.xName &&
            value[0].paramInfo.yName == item.paramInfo.yName
          ) {
            value.push(item);
          }
        });
      }
    });
    scatterChartData = newSData;
  }
  return scatterChartData;
};

export const exportFun = (data: any[]) => {
  console.log('exportF', data);
  console.time('export handle data');
  const tableData = [] as any[];
  //LOTID, SUBSTRATEID, RECIPE, OPERATION, PRODUCT, SLOT, STEP, STEP_NAME, CONTEXT KEY, EQP_ID, MODULE_ID, MODULE ALIAS, PARAM ALIAS, SUM_VALUE
  data.forEach((item: any, idx: number) => {
    const dataList = [] as any[];
    item.seriesValue &&
      item.seriesValue.forEach((value: string, index: number) => {
        const obj: any = {
          // LOTID: item.lotid[index],
          // SUBSTRATEID: item.substrateid[index],
          // RECIPE: item.recipe[index],
          // OPERATION: item.operation[index],
          // PRODUCT: item.product[index],
          // SLOT: item.slot[index],
          // STEP: item.step[index],
          // STEP_NAME: item.step_name[index],
          // CONTEXT: item.CONTEXT_KEY[index],
          // EQP_ID: item.paramInfo.eqpId,
          // MODULE_ID: item.paramInfo.eqpModuleId,
          // 'MODULE ALIAS': item.paramInfo.moduleAlias,
          // 'PARAM ALIAS': item.paramInfo.sumParamAlias,
          // TIME: formatterDate(item.timestamp[index]),
          // SUM_VALUE: item.seriesValue[index],
          ITEM_NAME: item.itemName,
          ...Object.fromEntries(item.keyEntries),
          TIME: formatterDate(item.timestamp[index]),
          VALUE: item.seriesValue[index]
        };
        // const keys = Object.keys(item);
        // console.log(keys);
        // keys.forEach((key: string) => {
        //     if (key.indexOf('^') > -1 && key.indexOf('RSD_') > -1) {
        //         const arr = key.split('^');
        //         const name = arr[1];
        //         obj[name] = item[key][index];
        //     }
        // });
        dataList.push(obj);
      });
    // let paramName = item.paramInfo.eqpParamAlias;
    let paramName = item.paramInfo.legend;
    const count = 30;
    const numLength = String(idx).length;
    if (paramName.length > count - numLength) {
      paramName = paramName.slice(0, count - numLength);
    }
    tableData[idx] = {
      name: paramName,
      // name: `${paramName}_${idx + 1}`,
      data: dataList
    };
  });
  console.timeEnd('export handle data');
  console.log('exportF', tableData);
  return tableData;
};

export const getLegendTree = (data: any[], groupingMap?: GroupTableValue) => {
  console.log('getLegendTree', data);
  // const paramList = ['param', 'eqpId', 'moduleAlias', 'recipe', 'product', 'step'];
  let paramList = ['stateItem', data[0]?.keyEntries?.map((item: any) => item[0])].flat();
  console.log('paramList', paramList);
  const legendList = [] as any;
  paramList.forEach((item: string) => {
    legendList.push({ label: item, children: [] });
  });
  data.forEach((item: any) => {
    // console.log(item);
    legendList.forEach((legend: any) => {
      if (legend.label === 'stateItem') {
        legend.children.push(item.itemName);
      } else {
        legend.children.push(item.keyEntries.find((e: any) => e[0] === legend.label)[1]);
      }
    });
    // legendList[0]['children'].push(item.itemName);
    // legendList[1]['children'].push(item.stateKeyValue);
  });
  legendList.forEach((item: any, index: number) => {
    item.children = [...new Set(item.children)];
    item.label !== 'stateItem' && (item.label = item.label.toLowerCase());
  });
  /* data.forEach((item: any) => {
        legendList[0]['children'].push(item.paramInfo.sumParamAlias);
        if (groupingMap) {
            const keys = Object.keys(groupingMap);
            keys.includes('moduleAlias') &&
                legendList[2]['children'].push(item.paramInfo.moduleName);
            keys.includes('eqpId') && legendList[1]['children'].push(item.paramInfo.eqpId);
            keys.includes('recipe') && legendList[3]['children'].push(...item.paramInfo.recipe);
            keys.includes('product') && legendList[4]['children'].push(...item.paramInfo.product);
            keys.includes('step') && legendList[5]['children'].push(...item.paramInfo.step);
        } else {
            legendList[2]['children'].push(item.paramInfo.moduleName);
        }
    });
    legendList.forEach((item: any, index: number) => {
        if (item.children.length > 0) {
            item.children = [...new Set(item.children)];
            if (index == 5) {
                const strList = item.children.filter(
                    (item: string) => item == '' || isNaN(Number(item)),
                );
                const numList = item.children.filter(
                    (item: string) => item != '' && !isNaN(Number(item)),
                );
                const newList = [
                    ...numList.sort((a: string, b: string) => Number(a) - Number(b)),
                    ...strList.sort(),
                ];
                item.children = newList;
            }
        }
    }); */
  console.log('legendList', legendList);
  return legendList.filter((item: any) => item.children.length > 0);
};

export const handleData = ({
  data,
  totalData,
  // chartData,
  totalCount,
  rightParamList,
  sumModuleNameList
}: {
  data: any;
  totalData: any;
  // chartData:any,
  totalCount: number;
  rightParamList: string[];
  sumModuleNameList: string[];
}) => {
  const key = Object.keys(data)[0];
  const value: any = Object.values(data)[0];
  value['paramInfo']['sumModuleName'] = key;
  value['paramInfo']['legend'] = key;
  value['paramInfo']['moduleName'] =
    `${value['paramInfo']['eqpId']}:${value['paramInfo']['moduleAlias']}`;
  value['paramInfo']['yAxisIndex'] = 0;
  if (totalData[key]) {
    const keys = Object.keys(value);
    totalCount += value.sumValue.length;
    keys.forEach((str) => {
      if (str !== 'paramInfo') {
        const values = [...totalData[key][str], ...value[str]];
        totalData[key][str] = values;
      }
    });
    totalData[key]['paramInfo']['step'] = [...new Set(totalData[key]['step'])];
    totalData[key]['paramInfo']['recipe'] = [...new Set(totalData[key]['recipe'])];
    totalData[key]['paramInfo']['product'] = [...new Set(totalData[key]['product'])];
  } else {
    totalData[key] = value;
    totalCount += value.sumValue.length;
    rightParamList.push(value['paramInfo']['sumParamAlias']);
    sumModuleNameList.push(value['paramInfo']['sumModuleName']);
  }
  return {
    totalData,
    totalCount,
    rightParamList,
    sumModuleNameList
  };
};
