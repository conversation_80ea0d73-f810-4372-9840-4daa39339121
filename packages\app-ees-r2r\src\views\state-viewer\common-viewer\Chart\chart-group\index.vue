<script lang="ts" setup>
import { reactive, nextTick, ref, watch } from 'vue';
import { t, VXETable } from '@futurefab/vxe-table';
import type { ChartDataType, GroupValue, GroupTableValue } from '../interface';
import { setSeriesGroup } from '../tools';
import { options, showOptions } from './config';
import { showWarning, EesButtonTip } from '@futurefab/ui-sdk-ees-basic';
import { EesGroupBottom } from '@futurefab/ui-sdk-ees-charts';

const props = defineProps({
  isSplit: {
    type: Boolean,
    default: false
  },
  paramsAlias: {
    type: Array<string>,
    default: () => {
      return [] as string[];
    }
  }
});
const emits = defineEmits(['doGroup']);
const xChartGroupBottom = ref();
const params = reactive({
  initIsSplit: false,
  totaldata: [] as any[],
  visibleGroup: false,
  isReset: false,
  tableList: [
    {
      code: 'eqp',
      itemName: t('common.field.eqp'),
      checked: false,
      isGrouping: false,
      isFilter: false,
      filterList: [] as GroupValue[]
    },
    {
      code: 'moduleAlias',
      itemName: t('common.title.module'),
      checked: true,
      isGrouping: false,
      isFilter: false,
      filterList: [] as GroupValue[]
    },
    {
      code: 'recipe',
      itemName: t('common.title.recipe'),
      checked: false,
      isGrouping: false,
      isFilter: false,
      filterList: [] as GroupValue[]
    },
    {
      code: 'product',
      itemName: t('common.title.product'),
      checked: false,
      isGrouping: false,
      isFilter: false,
      filterList: [] as GroupValue[]
    },
    {
      code: 'step',
      itemName: t('common.title.step'),
      checked: false,
      isGrouping: false,
      isFilter: false,
      filterList: [] as GroupValue[]
    }
  ] as GroupTableValue[],
  checkedList: [] as GroupTableValue[],
  eqpList: [] as GroupValue[],
  checkedEqp: [] as GroupValue[],
  updateEqp: [] as GroupValue[],

  moduleList: [] as GroupValue[],
  checkedModule: [] as GroupValue[],
  updateModule: [] as GroupValue[],

  recipeList: [] as GroupValue[],
  checkedRecipe: [] as GroupValue[],
  updateRecipe: [] as GroupValue[],

  productList: [] as GroupValue[],
  checkedProduct: [] as GroupValue[],
  updateProduct: [] as GroupValue[],

  stepList: [] as GroupValue[],
  checkedStep: [] as GroupValue[],
  updateStep: [] as GroupValue[],

  showList: [] as GroupValue[],
  groupList: [] as any[],
  currentIndex: 0,
  visible: false,
  isWarnPoP: false
});
const xGird = ref();
const xShowGird = ref();
const chartData = reactive({
  type: 1,
  layout: 1,
  kind: 'trend',
  isSplitParam: false,
  paramList: [] as string[],
  x: [] as string[],
  y: [] as string[]
});
const getChartData = (data: ChartDataType) => {
  const { type, layout, kind, isSplitParam, x, y } = data;
  chartData.type = type;
  chartData.layout = layout;
  chartData.kind = kind;
  chartData.isSplitParam = isSplitParam;
  chartData.x = x;
  chartData.y = y;
};
const events = {
  checkboxChange: ({ $event, selectedData, checked, _rowIndex, row }: any) => {
    // VXETable.tableFun.handleCheckboxAreaChange({
    //     selectedData,
    //     xGird: params.visible ? xShowGird : xGird,
    //     checked,
    //     rowIndex: _rowIndex,
    //     $event,
    // });
    if (params.visibleGroup) {
      row.checked = checked;
      params.checkedList = xGird.value && xGird.value.getCheckboxRecords();
      params.tableList.forEach((tableValue: GroupTableValue) => {
        tableValue.checked = false;
      });
      params.checkedList.forEach((item: GroupTableValue) => {
        params.tableList.forEach((tableValue: GroupTableValue) => {
          if (tableValue.itemName == item.itemName) {
            tableValue.checked = true;
          }
        });
      });
    }
  },
  cellAreaPaste: (argus: any) => {
    const { $table } = argus;
    const { tableData } = $table.getTableData();
    tableData.forEach((item: any) => {
      if (item.group == '') {
        item.group = params.groupList[0].value;
      }
    });
  }
};
watch(
  () => params.visibleGroup,
  (val: boolean) => {
    if (val) {
      nextTick(() => {
        xChartGroupBottom.value && xChartGroupBottom.value.onChangeChartData(chartData);
        params.tableList.forEach((item: GroupTableValue) => {
          if (item.checked) {
            if (params.checkedList) {
              params.checkedList.push(item);
            } else {
              params.checkedList = [item];
            }
          }
          xGird.value && xGird.value.setCheckboxRow(item, item.checked);
        });
      });
    }
  }
);

const handleApply = () => {
  const filterMap = {} as any;
  params.tableList.forEach((item: GroupTableValue) => {
    if (item.isFilter) {
      item.filterList.forEach((value: GroupValue) => {
        if (value.filter) {
          if (filterMap[item.code]) {
            filterMap[item.code].push(value.name);
          } else {
            filterMap[item.code] = [value.name];
          }
        }
      });
    }
  });
  const groupingMap = {} as any;
  // params.checkedList = xGird.value && xGird.value.getCheckboxRecords(true);
  params.tableList.forEach((tableValue: GroupTableValue) => {
    tableValue.checked = false;
  });
  params.checkedList.forEach((item: GroupTableValue) => {
    params.tableList.forEach((tableValue: GroupTableValue) => {
      if (tableValue.itemName == item.itemName) {
        tableValue.checked = true;
      }
    });
    if (item.isGrouping) {
      item.filterList.forEach((value: GroupValue) => {
        if (groupingMap[item.code]) {
          groupingMap[item.code].push({ name: value.name, group: value.group });
        } else {
          groupingMap[item.code] = [{ name: value.name, group: value.group }];
        }
      });
    } else {
      groupingMap[item.code] = [];
    }
  });
  if (
    chartData.kind == 'scatter' &&
    chartData.x.length == 1 &&
    chartData.y.length == 1 &&
    chartData.x[0] == chartData.y[0]
  ) {
    params.isWarnPoP = true;
    showWarning('summary.tips.sumParamSame');
    return false;
  }
  emits('doGroup', { groupingMap, filterMap, chartItem: chartData });
  handleClose();
};
const handleChange = (visible: boolean) => {
  if (params.isWarnPoP) {
    params.visibleGroup = true;
    params.isWarnPoP = false;
  }
};
const handleReset = (isSplit: boolean, isInit?: boolean) => {
  params.tableList.forEach((item: GroupTableValue) => {
    item.isGrouping = false;
    item.isFilter = false;
    if (item.code == 'moduleAlias') {
      item.checked = true;
    } else {
      item.checked = false;
    }
    item.filterList.forEach((val: GroupValue, index: number) => {
      val.group = setSeriesGroup(index);
      val.filter = true;
    });
    xGird.value && xGird.value.setCheckboxRow(item, item.checked);
  });
  params.checkedList = xGird.value && xGird.value.getCheckboxRecords();
  params.isReset = true;
  chartData.type = 1;
  chartData.layout = 1;
  chartData.kind = 'trend';
  chartData.isSplitParam = isSplit;
  chartData.paramList = props.paramsAlias;
  chartData.x = [] as string[];
  chartData.y = [] as string[];
  if (!isInit) {
    xChartGroupBottom.value && xChartGroupBottom.value.onReset(isSplit);
    emits('doGroup', { groupingMap: [], filterMap: [], chartItem: chartData });
    handleClose();
  }
};
//关闭group
const handleClose = () => {
  params.visibleGroup = false;
};
//处理group和filter
const handleModal = ({ row, rowIndex }: any) => {
  if (row.filterList.length == 0) return false;
  params.visible = true;
  params.visibleGroup = false;
  params.currentIndex = rowIndex;
  params.showList = row.filterList;
  params.groupList = [];
  params.showList.forEach((item: GroupValue, index: number) => {
    const letter = setSeriesGroup(index);
    params.groupList.push({ label: letter, value: letter });
  });
  showOptions.columns.forEach((item: any) => {
    if (item.field == 'name') {
      item.title = row.itemName.toUpperCase();
    }
    if (item.field == 'group') {
      item.editRender.options = params.groupList;
    }
  });

  xShowGird.value && xShowGird.value.reloadColumn(showOptions.columns);
  nextTick(() => {
    params.showList.forEach((item: GroupValue) => {
      xShowGird.value && xShowGird.value.setCheckboxRow(item, item.filter);
    });
  });
};
// 处理group和filter
const handleOk = () => {
  const checkedList = xShowGird.value && xShowGird.value.getCheckboxRecords();
  const { fullData } = xShowGird.value && xShowGird.value.getTableData();
  const afterCheckedList = checkedList.length == fullData.length ? [] : checkedList;
  params.tableList[params.currentIndex].isFilter = afterCheckedList.length > 0;
  params.tableList[params.currentIndex].filterList.forEach((value: GroupValue) => {
    value.filter = false;
  });
  checkedList.forEach((item: GroupValue) => {
    item.filter = true;
  });
  const groupList = [] as string[];
  fullData.forEach((item: GroupValue) => {
    const existList = groupList.filter((val: string) => val == item.group);
    existList.length == 0 && groupList.push(item.group);
  });
  params.tableList[params.currentIndex].isGrouping = groupList.length != fullData.length;
  xGird.value && xGird.value.reloadData(params.tableList);
  switch (params.currentIndex) {
    case 0:
      params.checkedEqp = checkedList;
      params.updateEqp = fullData;
      break;
    case 1:
      params.checkedModule = checkedList;
      params.updateModule = fullData;
      break;
    case 2:
      params.checkedRecipe = checkedList;
      params.updateRecipe = fullData;
      break;
    case 3:
      params.checkedProduct = checkedList;
      params.updateProduct = fullData;
      break;
    case 4:
      params.checkedStep = checkedList;
      params.updateStep = fullData;
      break;
    default:
      break;
  }
  params.isReset = false;
  params.visible = false;
  params.visibleGroup = true;
};
//取消group和filter
const handleCancel = () => {
  params.isReset = false;
  params.visibleGroup = true;
};
// 设置filter与group数组的值
const setParams = (list: GroupValue[], val: string | string[]) => {
  const fun = (l: GroupValue[], v: string) => {
    const existParams = l.filter((item: any) => item.name === v);
    if (existParams.length == 0) {
      const letter = setSeriesGroup(l.length);
      if (v != '') {
        l.push({ code: v, name: v, group: letter, filter: true });
      }
    }
  };
  if (Array.isArray(val)) {
    if (val && val.length > 0) {
      val.forEach((v: string) => {
        fun(list, v);
      });
    }
  } else {
    fun(list, val);
  }
};
//遍历totalData 获取需要的group和filter的数组
const setTableList = () => {
  params.eqpList = [];
  params.moduleList = [];
  params.recipeList = [];
  params.productList = [];
  params.stepList = [];
  params.totaldata &&
    params.totaldata.forEach((item: any) => {
      const paramInfo = item['paramInfo'];
      setParams(params.eqpList, paramInfo['eqpId']);
      setParams(params.moduleList, paramInfo['moduleName']);
      paramInfo['recipe'] && setParams(params.recipeList, paramInfo['recipe']);
      paramInfo['product'] && setParams(params.productList, paramInfo['product']);
      paramInfo['step'] && setParams(params.stepList, paramInfo['step']);
    });
  params.tableList.forEach((item: any, index) => {
    if (index == 0) {
      item.filterList = params.eqpList;
    }
    if (index == 1) {
      item.filterList = params.moduleList;
    }
    if (index == 2) {
      item.filterList = params.recipeList;
    }
    if (index == 3) {
      item.filterList = params.productList;
    }
    if (index == 4) {
      const strList = params.stepList.filter(
        (item: GroupValue) => item.name == '' || isNaN(Number(item.name))
      );
      const numList = params.stepList.filter(
        (item: GroupValue) => item.name != '' && !isNaN(Number(item.name))
      );
      const newList = [
        ...numList.sort((a: GroupValue, b: GroupValue) => Number(a.name) - Number(b.name)),
        ...strList.sort((a: GroupValue, b: GroupValue) => Number(a.name) - Number(b.name))
      ];
      params.stepList = newList.map((item: GroupValue, index: number) => {
        item.group = setSeriesGroup(index);
        return item;
      });
      item.filterList = params.stepList;
    }
  });
  return params.tableList;
};
const getData = ({
  data,
  paramsAlias,
  isSplit
}: {
  data: any[];
  paramsAlias: string[];
  isSplit: boolean;
}) => {
  chartData.isSplitParam = isSplit;
  params.initIsSplit = isSplit;
  if (data && data.length > 0) {
    params.totaldata = data;
    setTableList();
    chartData.paramList = paramsAlias;
  } else {
    handleReset(params.initIsSplit);
  }
};
defineExpose({
  handleReset,
  getData
});
</script>
<template>
  <div class="group_dropdown">
    <a-dropdown
      v-model:visible="params.visibleGroup"
      :trigger="['click']"
      overlay-class-name="my_drop"
      :overlay-style="{ zIndex: 998 }"
      @visibleChange="handleChange"
    >
      <ees-button-tip
        icon="#icon-btn-series-grouping-filtering"
        :text="t('eesCharts.commonBtn.groupFilter')"
      />
      <template #overlay>
        <a-menu>
          <a-menu-item key="0">
            <div class="fdc_summary_toolbox-grouping-content">
              <strong style="margin-bottom: 4px">{{ t('eesCharts.commonBtn.groupFilter') }}</strong>
              <vxe-grid ref="xGird" :data="params.tableList" v-bind="options" v-on="events">
                <template #groupBtn="{ row, rowIndex }">
                  <em
                    style="font-size: 12px; margin-right: 6px"
                    class="iconfont icon-btn-list"
                    :class="{
                      active: row.isGrouping,
                      disable: row.filterList.length == 0
                    }"
                    @click="handleModal({ row, rowIndex })"
                  ></em>
                  <em
                    style="font-size: 12px"
                    class="iconfont icon-filter"
                    :class="{
                      active: row.isFilter,
                      disable: row.filterList.length == 0
                    }"
                    @click="handleModal({ row, rowIndex })"
                  ></em>
                </template>
              </vxe-grid>
            </div>
          </a-menu-item>
          <a-menu-item>
            <ees-group-bottom ref="xChartGroupBottom" @changeChartData="getChartData" />
          </a-menu-item>
          <a-menu-item>
            <div class="fdc_summary_toolbox-grouping-btn" style="text-align: end">
              <a-button
                key="apply"
                :disabled="
                  (chartData.kind == 'scatter' &&
                    (chartData.x.length == 0 || chartData.y.length == 0)) ||
                  (params.checkedList && params.checkedList.length == 0)
                "
                type="primary"
                @click="handleApply"
                >{{ t('common.btn.apply') }}</a-button
              >
              <a-button
                key="reset"
                style="margin-left: 10px"
                type="primary"
                @click="handleReset(params.initIsSplit)"
                >{{ t('common.btn.reset') }}</a-button
              >
              <a-button key="close" style="margin-left: 10px" @click="handleClose">{{
                t('common.btn.close')
              }}</a-button>
            </div>
          </a-menu-item>
        </a-menu>
      </template>
    </a-dropdown>
    <a-modal
      v-model:visible="params.visible"
      :title="t('eesCharts.commonBtn.detail')"
      :cancel-text="t('eesCharts.Cancel')"
      :ok-text="t('eesCharts.OK')"
      @ok="handleOk"
      @cancel="handleCancel"
    >
      <vxe-grid
        ref="xShowGird"
        :data="params.showList"
        v-bind="showOptions"
        v-on="events"
      ></vxe-grid>
    </a-modal>
  </div>
</template>

<style lang="less" scoped>
@import url('@/assets/style/variable.less');
:deep(.fdc_chart_group_show) {
  display: none;
}
.fdc_summary_toolbox-grouping-content {
  // text-align: right;
  button {
    margin-left: @margin-btn;
  }
  .active {
    color: @primary-color;
  }
  .disable {
    cursor: not-allowed;
  }
  :deep(.vxe-grid--toolbar-wrapper) {
    display: none;
  }
}
.my_drop {
  width: 500px;
  .ant-dropdown-menu-item:hover {
    background-color: #fff;
  }
}
</style>
