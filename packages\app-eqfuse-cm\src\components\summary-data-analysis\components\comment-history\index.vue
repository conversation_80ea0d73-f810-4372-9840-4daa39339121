<script lang="ts">
import { defineComponent, reactive, ref, watch } from 'vue';
import { options } from './config';
import {
  showConfirm,
  showWarning,
  stringToDBLength,
  successInfo,
  EesButtonTip
} from '@futurefab/ui-sdk-ees-basic';
import { uniqBy } from 'lodash-es';
import { VxeTableDefines, t } from '@futurefab/vxe-table';
import dayjs, { Dayjs } from 'dayjs';
import { summaryInfo as buttonIds } from '@/log-config/index';
import {
  getUserCommentList,
  updateUserCommentList,
  deleteUserCommentList
} from '@futurefab/ui-sdk-api';
import { EesCustomTime } from '@futurefab/ui-sdk-ees-basic';

export default defineComponent({
  name: 'CommentHistory',
  props: {
    minTime: {
      type: Number || null,
      default: null
    },
    maxTime: {
      type: Number || null,
      default: null
    },
    eqpIdList: {
      type: Array<string>,
      default: () => []
    }
  },
  emits: ['doDrawChart'],
  setup(props, { emit }) {
    const xGrid = ref();
    const params = reactive({
      visible: false,
      tableList: [],
      isWarnPoP: false,
      startDate: '',
      endDate: '',
      tableLoading: false,
      timeArea: [dayjs().startOf('day'), dayjs().endOf('day')] as [Dayjs, Dayjs]
    });
    const tableRules = {
      eventName: [
        {
          trigger: 'blur',
          required: true,
          validator: ({ cellValue, column, row }: any) => {
            if (column.field == 'eventName' && cellValue == '') {
              return new Error(
                `${t('common.tip.required', {
                  name: t('common.title.eventName')
                })}`
              );
            } else if (column.field == 'eventName' && stringToDBLength(cellValue) > 128) {
              return new Error(
                `${t('common.tip.inputLength', {
                  name: t('common.title.eventName'),
                  max: '128'
                })}`
              );
            }
          }
        }
      ]
    };
    const events = {
      timePop: (val: [number, number]) => {
        params.startDate = dayjs(val[0]).format('YYYY-MM-DD HH:mm:ss');
        params.endDate = dayjs(val[1]).format('YYYY-MM-DD HH:mm:ss');
      },
      handleSearch: () => {
        events.requestTableList();
      },
      handleChange: (visible: boolean) => {
        if (params.isWarnPoP) {
          params.visible = true;
          params.isWarnPoP = false;
        }
      },
      handleDrawChart: () => {
        const updateRecords = xGrid.value.getUpdateRecords();
        if (updateRecords.length > 0) {
          params.isWarnPoP = true;
          showWarning('common.tip.saveData');
          return false;
        }
        const checkedList = xGrid.value.getCheckboxRecords();
        // if (checkedList.length === 0) {
        //     params.isWarnPoP = true;
        //     showWarning('common.tip.selectData');
        //     return false;
        // }
        if (checkedList.length > 1) {
          const uniqueArray = uniqBy(checkedList, 'eventDtts');
          if (uniqueArray.length !== checkedList.length) {
            params.isWarnPoP = true;
            showWarning('common.tip.selectEventDtts');
            return false;
          }
        }
        emit('doDrawChart', {
          checkedList,
          startTime: dayjs(params.startDate).valueOf(),
          endTime: dayjs(params.endDate).valueOf()
        });
        params.visible = false;
      },
      requestTableList: (isRefresh?: boolean) => {
        params.tableLoading = true;
        getUserCommentList({
          headerParams: {
            log: 'Y',
            page: buttonIds.pageId,
            action: 'summary-comment-history'
          },
          bodyParams: {
            eqpIds: props.eqpIdList,
            eventStartDtts: params.startDate,
            eventEndDtts: params.endDate
          }
        }).then((result: any) => {
          if (result.status == 'SUCCESS') {
            params.tableList = result.data;
            if (isRefresh) {
              successInfo('common.tip.refreshSuccess');
            }
          }
          params.tableLoading = false;
        });
      },
      toolbarToolClick: async ({ code }: { code: string }) => {
        if (code === 'refresh') {
          events.requestTableList(true);
        } else if (code == 'delete') {
          const checkedList = xGrid.value.getCheckboxRecords();
          if (checkedList.length === 0) {
            params.isWarnPoP = true;
            showWarning('common.tip.selectData');
            return false;
          }
          params.isWarnPoP = true;
          const type = await showConfirm({
            msg: t('common.tip.deleteConfirm', { total: checkedList.length })
          });
          if (type == 'confirm') {
            const eventExtRawIds = checkedList.map((item: { eventExtRawId: number }) => {
              return item.eventExtRawId;
            });
            deleteUserCommentList({
              headerParams: {
                log: 'Y',
                page: buttonIds.pageId,
                action: 'summary-comment-history-delete'
              },
              bodyParams: {
                eventExtRawIds
              }
            }).then((result: any) => {
              if (result.status == 'SUCCESS') {
                successInfo('common.tip.delSuccess');
                events.requestTableList();
              }
            });
          }
        } else if (code == 'save') {
          const updateRecords = xGrid.value.getUpdateRecords();
          if (updateRecords.length == 0) {
            params.isWarnPoP = true;
            showWarning('common.tip.saveTip');
            return false;
          }
          const result = await xGrid.value.validate(updateRecords);
          if (result && Object.keys(result).length > 0) {
            params.isWarnPoP = true;
            return false;
          }
          updateUserCommentList({
            headerParams: {
              log: 'Y',
              page: buttonIds.pageId,
              action: 'summary-comment-history-update'
            },
            bodyParams: updateRecords
          }).then((result: any) => {
            if (result.status == 'SUCCESS') {
              successInfo('common.tip.saveSuccess');
              events.requestTableList();
            }
          });
        }
      }
    };
    watch(
      () => params.visible,
      (val: boolean) => {
        if (val) {
          params.startDate = dayjs(props.minTime).format('YYYY-MM-DD HH:mm:ss');
          params.endDate = dayjs(props.maxTime).format('YYYY-MM-DD HH:mm:ss');
          events.requestTableList();
          const startDate = dayjs(props.minTime);
          const endDate = dayjs(props.maxTime);
          params.timeArea = [startDate, endDate];
        }
      }
    );
    return {
      params,
      events,
      options,
      xGrid,
      props,
      tableRules
    };
  }
});
</script>

<template>
  <div class="comment-history_dropdown">
    <a-dropdown
      v-model:open="params.visible"
      :trigger="['click']"
      overlay-class-name="comment-history_drop"
      :overlay-style="{ zIndex: 998 }"
      :get-popup-container="(triggerNode: any) => triggerNode.parentNode"
      @open-change="events.handleChange"
    >
      <ees-button-tip icon="#icon-btn-comment-history" :text="$t('common.btn.commentHistory')" />
      <template #overlay>
        <a-menu>
          <a-menu-item key="0">
            <strong class="title">{{ $t('common.btn.commentHistory') }}</strong>
          </a-menu-item>
          <a-menu-item key="0">
            <div style="display: flex">
              <EesCustomTime
                class="comment-history-time-wrapper"
                :time-area="params.timeArea"
                :bordered="true"
                @time-pop="events.timePop"
              ></EesCustomTime>
              <a-button style="margin-left: 10px" type="primary" @click="events.handleSearch">{{
                $t('common.btn.search')
              }}</a-button>
            </div>
          </a-menu-item>
          <a-menu-item key="1">
            <div class="common-history-content">
              <vxe-grid
                ref="xGrid"
                class="vxe-toolbar-top-left-radius-xs vxe-toolbar-top-right-radius-xs vxe-table-bottom-left-radius-xs vxe-table-bottom-right-radius-xs"
                :loading="params.tableLoading"
                :data="params.tableList"
                v-bind="options"
                :edit-rules="tableRules"
                v-on="events"
              >
              </vxe-grid>
            </div>
          </a-menu-item>

          <a-menu-item key="2">
            <div class="fdc_summary_toolbox-grouping-btn" style="text-align: end">
              <a-button key="drawChart" type="primary" @click="events.handleDrawChart">{{
                $t('common.btn.draw')
              }}</a-button>
              <a-button
                key="close"
                style="margin-left: 10px"
                @click="() => (params.visible = false)"
                >{{ $t('common.btn.close') }}</a-button
              >
            </div>
          </a-menu-item>
        </a-menu>
      </template>
    </a-dropdown>
  </div>
</template>
<style lang="less" scoped>
@import url('@/assets/style/variable.less');
.comment-history_dropdown {
  position: relative;
  .title {
    font-size: @font-size-sm;
    font-weight: @font-weight-md;
    color: @text-title-color;
    margin-bottom: @margin-xs + 2;
    display: block;
  }
  .common-history-content {
    margin: @margin-xs + 2 0;
    width: 100%;
    height: 100%;
    min-height: 240px;
  }
}
.comment-history-time-wrapper {
  &.custom-time-wrap {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }
}
</style>
