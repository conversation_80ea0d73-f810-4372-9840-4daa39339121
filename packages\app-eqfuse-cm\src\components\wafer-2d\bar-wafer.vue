<script setup lang="ts">
import { watch, ref } from 'vue';
import type { Measuredata } from '@/utils/spline-interpolate';
import Chart from '../chart.vue';
import type { Specification } from '@/views/metrology-management/interface';
import { calcGroup, getToolbox } from './index';
import { debounce } from 'lodash-es';
import { colorList, formatterY } from '@futurefab/ui-sdk-ees-charts';
const props = defineProps<{
    data?: Measuredata;
    groupKey: string;
    groupNumber: number;
    specification?: Specification;
}>();
const options = ref<any>({});
const getSingleBar = (
    data: Measuredata,
    groupKey: string,
    groupNumber: number,
    specification: Specification,
) => {
    let groupList: number[];
    const xIndex = data.header.findIndex(item => item === specification.jsonObj.xField);
    const yIndex = data.header.findIndex(item => item === specification.jsonObj.yField);
    if (groupKey !== 'Radius') {
        const groupIndex = data.header.findIndex(item => item === groupKey);
        groupList = data.data.map(item => Number(item[groupIndex]));
    } else {
        groupList = data.data.map(item =>
            Math.sqrt(Number(item[xIndex]) ** 2 + Number(item[yIndex]) ** 2),
        );
    }

    const maxV = Math.max(...groupList);
    const minV = Math.min(...groupList);
    const gap = (maxV - minV) / groupNumber;

    // 按区间分组的对象
    const groupRes = new Array(groupNumber).fill(null).map((_: null, index: number) => ({
        start: minV + gap * index,
        end: index === groupNumber - 1 ? maxV : minV + gap * (index + 1),
        count: 0,
    }));

    groupList.forEach((value: number) => {
        const groupI = calcGroup(value, maxV, minV, gap, groupNumber);
        groupRes[groupI].count++;
    });
    return {
        color: colorList,
        grid: {
            left: 80,
            top: 30,
            right: 20,
            bottom: 30,
        },
        xAxis: {
            show: true,
            type: 'category',
            data: new Array(groupNumber).fill(null).map((item, index) => index + 1),
            splitArea: {
                show: true,
            },
        },
        yAxis: {
            show: true,
            splitArea: {
                show: false,
            },
        },
        toolbox: getToolbox(),
        tooltip: {
            show: true,
            formatter: (event: any) => {
                if (event?.data?.info) {
                    const info = event.data.info;
                    return [
                        `Range: ${formatterY(info.start)} ~ ${formatterY(info.end)}`,
                        `Count: ${info.count}`,
                    ].join('<br/>');
                }
            },
            appendToBody: true, 
        },
        series: [
            {
                name: 'bar',
                type: 'bar',
                barWidth: '100%',
                itemStyle: { borderWidth: 0.2, borderColor: 'white' },
                data: groupRes.map(item => ({
                    value: item.count,
                    info: item,
                })),

            },
        ],
    };
};
const tempFn = debounce(() => {
    options.value = getSingleBar(
        props.data!,
        props.groupKey,
        props.groupNumber,
        props.specification!,
    );
}, 100);
watch(
    [() => props.data, () => props.groupKey, () => props.groupNumber, () => props.specification],
    v => {
        if (v) {
            tempFn();
        }
    },
    { immediate: true },
);
</script>
<template>
    <div class="common-chart-container">
        <Chart :options="options" />
    </div>
</template>
<style scoped lang="less">
.common-chart-container {
    width: 100%;
    height: 100%;
}
</style>
