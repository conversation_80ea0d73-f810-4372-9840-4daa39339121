<script setup lang="ts">
import { ref } from 'vue';
import { CheckCircleFilled, CloseCircleFilled, InfoCircleFilled } from '@ant-design/icons-vue';

const props = defineProps(['matchingInfo']);
const emit = defineEmits(['expandOrFoldAll']);
const isExpand = ref<boolean>(true);
const expandOrFoldAll = (isExpandParam: boolean) => {
  isExpand.value = isExpandParam;
  emit('expandOrFoldAll', isExpandParam);
};
</script>

<template>
  <div class="total-container">
    <a-space>
      <a-tooltip placement="topLeft">
        <template #title>
          <span>
            {{ isExpand ? $t('cm.tips.allFold') : $t('cm.tips.allExpand') }}
          </span>
        </template>
        <i v-if="isExpand" class="iconfont icon-fold expand-icon" @click="expandOrFoldAll(false)" />
        <i v-else class="iconfont icon-expand expand-icon" @click="expandOrFoldAll(true)" />
      </a-tooltip>

      <span>
        <close-circle-filled class="error" />
        {{ matchingInfo?.unmatchingCountAll }}
      </span>
      <span>
        <info-circle-filled class="warning" />
        {{ matchingInfo?.warningCountAll }}
      </span>
      <span>
        <check-circle-filled class="success" />
        {{ matchingInfo?.matchingCountAll }}
      </span>
      <span v-if="matchingInfo?.missingCountAll > 0">
        <info-circle-filled class="info" />
        {{ matchingInfo?.missingCountAll }}
      </span>
      <span> Total {{ matchingInfo?.totalCountAll }} </span>
    </a-space>
  </div>
</template>

<style scoped lang="less">
@import url('@/assets/style/variable.less');
.total-container {
  padding: 4px 10px;
  display: flex;
  align-items: center;
  background-color: @bg-header-color;
  border-top: 1px solid @border-color;
  .expand-icon {
    cursor: pointer;
    &:hover {
      background: @bg-hover-2-color;
    }
  }
  .error {
    color: @error-color;
    font-size: 16px;
  }
  .warning {
    color: @warning-color;
    font-size: 16px;
  }
  .success {
    color: @success-color;
    font-size: 16px;
  }
  .info {
    color: @text-sub-color;
    font-size: 16px;
  }
}
</style>
