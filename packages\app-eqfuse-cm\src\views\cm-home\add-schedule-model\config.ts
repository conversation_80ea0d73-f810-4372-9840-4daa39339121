import dayjs from 'dayjs';
import { t } from '@futurefab/vxe-table';
import { type AddScheduleForm } from '@/utils/schedule';
export const getInitFormValue = ({
    isEdit,
    form,
    currentUser,
}: {
    isEdit: boolean;
    form: null | AddScheduleForm;
    currentUser: string;
}): AddScheduleForm => {
    const now = dayjs();
    const startDate = dayjs()
        .startOf('hour')
        .add(now.minute() >= 30 ? 1 : 0.5, 'hour')
        .add(1, 'day');
    if (isEdit) {
        return {
            ...form,
            startDate: dayjs(form?.startDate),
            endDate: dayjs(form?.endDate),
        } as AddScheduleForm;
    } else {
        return {
            jobName: null,
            startDate: startDate,
            recurrenceBy: 'never',
            dayInterval: 1,
            hourInterval: 8,
            endType: 'noEnd',
            afterOccurrences: null,
            endDate: startDate.add(1, 'day'),
            // contextFilter: null,
            // contextFilterRegex: false,
            emailNotification: false,
            notificationRecipients: currentUser ? [currentUser] : [],
            // attachReport: false,
            // topNColOutput: 4,
            // topNParamOutput: 20,
            // conditionalNotification: false,
            // threshold: null,
            // thresholdCount: null,
            // thresholdConjunction: 'and',
            // thresholdPercent: null,
            alarmConditions: [
                {
                    id:
                        'id-' +
                        new Date().getTime().toString(36) +
                        '-' +
                        Math.random().toString(36).substring(2, 9),
                    conditionType: 'TOTAL',
                    conditionRule: {
                        ruleParam: '',
                        ruleValue: 50,
                    },
                },
            ],
        };
    }
};

export const getValidationMsg = (form: AddScheduleForm, isEdit?: boolean): string[] => {
    console.log(form, 'getValidationMsg');
    const result: string[] = [];
    if (!form.jobName) {
        result.push(t('common.tip.inputName', { name: t('cm.label.jobName') }));
    }
    if (!form.startDate) {
        result.push(t('common.tip.inputName', { name: t('cm.label.startDate') }));
    } else {
        if (!isEdit && form.startDate.$d.getTime() < new Date().getTime()) {
            result.push(t('common.tip.startDateIsPast'));
        }
    }
    if (form.recurrenceBy !== 'never') {
        if (form.recurrenceBy === 'day' && !form.dayInterval) {
            result.push(t('common.tip.inputName', { name: t('Day Interval') }));
        }
        if (form.recurrenceBy === 'hour' && !form.hourInterval) {
            result.push(t('common.tip.inputName', { name: t('Hour Interval') }));
        }
        if (form.endType === 'after' && !form.afterOccurrences) {
            result.push(t('common.tip.inputName', { name: 'Occurrences' }));
        }
        if (form.endType === 'endBy') {
            if (!form.endDate) {
                result.push(t('common.tip.inputName', { name: 'End Date' }));
            }
        }
    }
    if (form.emailNotification) {
        if (!form.notificationRecipients?.length) {
            result.push(t('common.tip.inputName', { name: t('cm.label.notificationRecipients') }));
        }
        form.alarmConditions?.forEach((element: any) => {
            switch (element.conditionType) {
                case 'TOTAL':
                    if (
                        element?.conditionRule?.ruleValue !== 0 &&
                        !element?.conditionRule?.ruleValue
                    )
                        result.push(
                            t('common.tip.inputName', {
                                name: 'TOTAL ' + t('cm.label.matchingRate'),
                            }),
                        );
                    break;
                case 'PARAM':
                    if (!element?.conditionRule?.ruleParam)
                        result.push(
                            t('common.tip.selectOne', {
                                name: 'PARAM ' + t('cm.label.matchingParameter'),
                            }),
                        );
                    break;
                case 'PARAM_GROUP':
                    if (
                        element?.conditionRule?.ruleValue !== 0 &&
                        !element?.conditionRule?.ruleValue
                    )
                        result.push(
                            t('common.tip.inputName', {
                                name: 'PARAM_GROUP ' + t('cm.label.matchingRate'),
                            }),
                        );
                    if (!element?.conditionRule?.ruleParam)
                        result.push(
                            t('common.tip.selectOne', {
                                name: 'PARAM_GROUP ' + t('cm.label.groupName'),
                            }),
                        );
                    break;
            }
        });
        // if (form.attachReport) {
        //     if (!form.topNColOutput) {
        //         result.push('Please input a valid top N column count greater than 1 and up to 6.');
        //     }
        //     if (!form.topNParamOutput) {
        //         result.push('Please input a valid top N parameter count between 1 and 50.');
        //     }
        // }

        // if (form.conditionalNotification) {
        //     if (!form.threshold) {
        //         result.push(t('common.tip.inputName', { name: t('cm.label.matchThreshold') }));
        //     }
        //     if (form.thresholdConjunction === 'and') {
        //         if (!form.thresholdCount || !form.thresholdPercent) {
        //             result.push(
        //                 t(
        //                     'Please input both Minimum count of dataset and Minimum percent of dataset for AND condition.',
        //                 ),
        //             );
        //         }
        //     } else {
        //         if (!form.thresholdCount && !form.thresholdPercent) {
        //             result.push(
        //                 t(
        //                     'Please input either Minimum count of dataset or Minimum percent of dataset for OR condition.',
        //                 ),
        //             );
        //         }
        //     }
        // }
    }
    return result;
};
