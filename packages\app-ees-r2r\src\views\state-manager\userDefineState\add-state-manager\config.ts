import { VXETable, type VxeGridProps, t } from '@futurefab/vxe-table';
import { tableDefaultConfig } from '@futurefab/ui-sdk-ees-basic';
interface TableOptionsParam {
  editFlag?: boolean;
  itemList?: any[];
}
export const xGetConfig = (param: TableOptionsParam): VxeGridProps => {
  const { editFlag, itemList } = param;
  return tableDefaultConfig(
    {
      id: 'stateManagerAdd',
      border: true,
      stripe: false,
      toolbarConfig: {
        tableName: 'stateManager.title.stateKey',
        import: false,
        export: false,
        refresh: false,
        tools: editFlag
          ? [
              ...VXETable.tableFun.getToolsButton([
                { name: 'common.btn.add', icon: 'icon-btn-add', visible: true },
                {
                  name: 'common.btn.deleteData',
                  icon: 'icon-btn-delete',
                  visible: true
                }
              ])
            ]
          : []
      },
      columns: [
        ...(editFlag
          ? [
              {
                width: 80,
                field: 'order',
                title: 'common.field.order',
                align: 'center',
                fixed: 'left',
                slots: { default: 'dragBtn' },
                params: { selectable: false }
              }
            ]
          : []),
        {
          field: 'rawId',
          type: 'checkbox',
          width: 40,
          align: 'center'
        },
        {
          field: 'seq',
          width: 60,
          title: 'common.field.seq',
          align: 'center'
        },
        {
          field: 'itemName',
          minWidth: 200,
          title: 'common.field.item',
          align: 'left',
          editRender: {
            name: '$select',
            events: {
              change: (argus: any, event: any) => {
                // console.log(row.itemName, 11, event.value);
                const { data, row } = argus;
                row.isEdit = event.value === row.itemNameOld ? false : true;
              }
            },
            options: itemList || [],
            optionProps: { label: 'name', value: 'name' },
            enabled: editFlag,
            props: {
              readonly: false
            }
          }
        },
        {
          field: 'displayName',
          minWidth: 200,
          title: 'common.field.displayName',
          editRender: {
            name: '$input',
            // events: {
            //     keyup: (argus: any, event: any) => {
            //         // console.log('input', event.value);
            //         const { data, row } = argus;
            //         row.displayName = event.value;
            //         row.isEdit = event.value === row.descriptionOld ? false : true;
            //     },
            // },
            enabled: editFlag
          }
        },
        {
          field: 'description',
          minWidth: 200,
          title: 'common.field.description',
          align: 'left',
          editRender: {
            name: '$input',
            events: {
              keyup: (argus: any, event: any) => {
                // console.log('input', event.value);
                const { data, row } = argus;
                row.description = event.value;
                row.isEdit = event.value === row.descriptionOld ? false : true;
              }
            },
            enabled: editFlag
          }
        }
      ],
      checkboxConfig: {
        showHeader: true,
        checkMethod: () => {
          return editFlag ? true : null;
        }
      },
      editConfig: {
        enabled: editFlag
      }
    } as VxeGridProps,
    false
  );
};
export const dConfig = tableDefaultConfig(
  {
    id: 'stateManagerAddItem',
    stripe: false,
    border: true,
    toolbarConfig: {
      tableName: 'stateManager.title.stateItem',
      import: false,
      export: false,
      refresh: false,
      tools: [
        ...VXETable.tableFun.getToolsButton([
          { name: 'common.btn.add', icon: 'icon-btn-add', visible: true },
          {
            name: 'common.btn.deleteData',
            icon: 'icon-btn-delete',
            visible: true
          }
        ])
      ]
    },
    columns: [
      {
        width: 80,
        field: 'order',
        title: 'common.field.order',
        align: 'center',
        fixed: 'left',
        slots: { default: 'dragBtn' },
        params: { selectable: false }
      },
      {
        field: 'rawId',
        type: 'checkbox',
        width: 60,
        align: 'center'
      },
      {
        field: 'seq',
        width: 60,
        title: 'common.field.seq',
        align: 'center'
      },
      {
        field: 'itemName',
        minWidth: 200,
        title: 'common.field.item',
        align: 'left',
        editRender: {
          name: '$input',
          events: {
            keyup: (argus: any, event: any) => {
              const { data, row } = argus;
              // console.log(row.itemName, 11, event.value);
              row.itemName = event?.value;
              row.isEdit = event.value === row.itemNameOld ? false : true;
            }
          }
        }
      },
      {
        field: 'displayName',
        minWidth: 200,
        title: 'common.field.displayName',
        editRender: {
          name: '$input'
          // events: {
          //     keyup: (argus: any, event: any) => {
          //         // console.log('input', event.value);
          //         const { data, row } = argus;
          //         row.description = event.value;
          //         row.isEdit = event.value === row.descriptionOld ? false : true;
          //     },
          // },
        }
      },
      {
        field: 'description',
        minWidth: 200,
        title: 'common.field.description',
        align: 'left',
        editRender: {
          name: '$input',
          events: {
            keyup: (argus: any, event: any) => {
              // console.log('input', event.value);
              const { data, row } = argus;
              row.description = event.value;
              row.isEdit = event.value === row.descriptionOld ? false : true;
            }
          }
        }
      }
    ]
  } as VxeGridProps,
  false
);
