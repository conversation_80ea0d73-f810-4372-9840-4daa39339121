<template>
    <div ref="container" class="container">
        <div ref="chartBox" class="chartBox"></div>
    </div>
</template>
<script lang="ts" setup>
import * as echarts from '@xdatav/echarts';
import { debounce } from 'lodash-es';
import { defineProps, watch, nextTick, ref, onMounted, onUnmounted, shallowRef } from 'vue';
import { type ShallowRef } from 'vue';
const props = withDefaults(
    defineProps<{
        options: echarts.EChartsCoreOption;
        useZoom?: boolean;
        useBrush?: boolean;
    }>(),
    {
        options: () => ({} as echarts.EChartsCoreOption),
        useZoom: true,
        useBrush: false,
    },
);
const emits = defineEmits([
    'chartClick',
    'chartDatazoom',
    'instanceChange',
    'chartResize',
    'chartLegendselectchanged',
    'chartMouseover',
    'chartMouseout',
    'brush',
    'brushEnd',
    'brushselected',
]);
const setChartZoom = (myChart: echarts.ECharts) => {
    try {
        myChart.dispatchAction({
            type: 'dataZoom',
        });        
    } catch {
    }
    props.useZoom && myChart.dispatchAction({
        type: 'takeGlobalCursor',
        key: 'dataZoomSelect',
        dataZoomSelectActive: true,
    });
};
const setChartBrush = (myChart: echarts.ECharts) => {
    try {
        myChart.dispatchAction({
            type: 'brush',
            areas: [],
        });        
    } catch {
    }

    props.useBrush && myChart.dispatchAction({
        type: 'takeGlobalCursor',
        key: 'brush',
        brushOption: {
            brushType: 'rect',
            brushMode: 'single'
        }
    });
};
const chartInstance: ShallowRef<echarts.ECharts | null> = shallowRef(null);
const chartBox = ref(null);
const container = ref(null);
const callback = debounce(() => {
    nextTick(() => {
        if (chartInstance.value) {
            chartInstance.value.resize();
            setChartBrush(chartInstance.value);
            emits('chartResize', chartInstance.value);
        }
    });
}, 200);
const observe = new ResizeObserver(callback);

const initChart = (option: echarts.EChartsCoreOption) => {
    chartInstance.value = echarts.init(chartBox.value);
    chartInstance.value.setOption(option);
    chartInstance.value.on('click', (event: any) => {
        emits('chartClick', event);
    });
    chartInstance.value.on('datazoom', (event: any) => {
        emits('chartDatazoom', event);
    });
    chartInstance.value.on('legendselectchanged', (event: any) => {
        emits('chartLegendselectchanged', event);
    });
    chartInstance.value.on('mouseover', (event: any) => {
        emits('chartMouseover', event);
    });
    chartInstance.value.on('mouseout', (event: any) => {
        emits('chartMouseout', event);
    });
    chartInstance.value.on('brush', (event: any) => {
        emits('brush', event);
    });
    chartInstance.value.on('brushEnd', (event: any) => {
        emits('brushEnd', event);
    });
    chartInstance.value.on('brushselected', (event: any) => {
        emits('brushselected', event);
    });

    setChartZoom(chartInstance.value);
    setChartBrush(chartInstance.value);
};
watch(
    () => props.options,
    (option: any) => {
        if (chartInstance.value) {
            chartInstance.value.dispose();
            initChart(option);
            setChartZoom(chartInstance.value);
        }
    },
    { immediate: true },
);
watch(
    () => chartInstance.value,
    (    value: any) => {
        emits('instanceChange', value);
    },
    { immediate: true },
);

onMounted(() => {
    nextTick(() => {
        initChart(props.options);
    });
    observe.observe(container.value!);
});
onUnmounted(() => {
    if (chartInstance.value) {
        chartInstance.value.dispose();
    }
    emits('instanceChange', null);
    observe.disconnect();
});
defineExpose({
    getChartInstance: () => chartInstance.value
});
</script>
<style scoped>
.chartBox {
    width: 100%;
    height: 100%;
}
.container {
    width: 100%;
    height: 100%;
}
</style>
