import { interpolate, thinPlateSplineInterpolate, type SamplePoint } from '@/utils/spline-interpolate';
import type { Specification } from '@/views/metrology-management/interface';
import { formatterY } from '@futurefab/ui-sdk-ees-charts';
// 计算一个数乘以 10 的多少次方大于等于 1， （0.1）=? 1
export const minPowerOf10 = (num: number) => {
    return num >= 1 ? 0 : Math.ceil(-Math.log10(num));
};

export const formatterYByGap = (value: number, gap: number) => {
    if (gap >= 1) {
        return formatterY(value)
    } else if(gap == 0) {
        return value;
    } else {
        const d = minPowerOf10(gap);
        return formatterY(value, d + 3)
    }
}
export const getPicecs = (minV: number, maxV: number, colorList: string[], noLimit = true) => {
    const gap = maxV - minV;
    const colorLen = colorList.length;
    const itemGap = gap / colorLen;
    const result = colorList.map((color: string, i: number) => {
        const temp: any = {
            max: formatterYByGap(minV + itemGap * (i + 1), gap),
            min: formatterYByGap(minV + itemGap * i, gap),
            color,
        };
        if (noLimit) {
            if (i === 0) {
                delete temp.min;
            } else if (i === colorLen - 1) {
                delete temp.max;
            }
        }
        return temp;
    });
    return result;
};
export const getX = (specification: Specification, i: number, step: number) => {
    const radius = specification.jsonObj.diameter / 2;
    const x = i * step - radius;
    let offset = 0;
    switch (specification.jsonObj.originPosition) {
        case 'Bottom Left':
            offset = radius;
            break;
        case 'Top Left':
            offset = radius;
            break;
        case 'Bottom Right':
            offset = -radius;
            break;
        case 'Top Right':
            offset = -radius;
            break;
        default:
            offset = 0;
            break;
    }
    return x + offset;
};
export const getY = (specification: Specification, i: number, step: number) => {
    const radius = specification.jsonObj.diameter / 2;
    const y = i * step - radius;
    let offset = 0;
    switch (specification.jsonObj.originPosition) {
        case 'Bottom Left':
            offset = radius;
            break;
        case 'Bottom Right':
            offset = radius;
            break;
        case 'Top Left':
            offset = -radius;
            break;
        case 'Top Right':
            offset = -radius;
            break;
        default:
            offset = 0;
            break;
    }
    return y + offset;
};
export const getCenterPointer = (specification: Specification) => {
    let x = 0,
        y = 0;
    const radius = specification.jsonObj.diameter / 2;
    switch (specification.jsonObj.originPosition) {
        case 'Bottom Left':
            x = radius;
            y = radius;
            break;
        case 'Bottom Right':
            x = -radius;
            y = radius;
            break;
        case 'Center':
            break;
        case 'Top Left':
            x = radius;
            y = -radius;
            break;
        case 'Top Right':
            x = -radius;
            y = -radius;
    }
    return [x, y];
};
// 薄板样条插值函数（Thin Plate Spline, TPS）
export function generateWaferGrid(
    samplePoints: SamplePoint[],
    specification: Specification,
    gridSize: number,
) {
    const radius = specification.jsonObj.diameter / 2;
    const { weights, c } = thinPlateSplineInterpolate(samplePoints);
    const grid = [];
    const step = specification.jsonObj.diameter / (gridSize - 1);
    const center = getCenterPointer(specification) as any;
    for (let i = 0; i < gridSize; i++) {
        const x = getX(specification, i, step) as number;
        for (let j = 0; j < gridSize; j++) {
            const y = getY(specification, j, step) as number;
            const dist = Math.sqrt((x - center[0]) ** 2 + (y - center[1]) ** 2);
            if (i < 5 && j < 5) {
                // console.log(`i:${i}; j: ${j}; x: ${x}, y: ${y}; dist: ${dist}`);
            }
            if (dist > radius) {
                continue;
            } else {
                grid.push({ i, j, z: interpolate(x, y, samplePoints, weights, c) });
            }
        }
    }
    return grid;
}

/**
 * 左闭右开区间 [), 最后一个是 左闭右闭
 * 返回 分到哪一个组， 从 0 开始计数
 */
export const calcGroup = (
    v: number,
    maxV: number,
    minV: number,
    gap: number,
    groupNumber: number,
) => {
    if (v === maxV) {
        return groupNumber - 1;
    } else {
        return Math.floor((v - minV) / gap);
    }
};
export const getToolbox = () => {
    return {
        show: true,
        showTitle: false,
        feature: {
            dataZoom: {
            enableBrushBack: true
            },
            brush: {
            type: ['rect']
            }
        },
        iconStyle: {
            opacity: 0
        }
    };
}