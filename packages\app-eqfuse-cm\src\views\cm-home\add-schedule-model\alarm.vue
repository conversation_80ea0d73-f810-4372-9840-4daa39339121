<script setup lang="ts">
import { ref, h, watch } from 'vue';
// import { PlusOutlined, MinusOutlined } from '@ant-design/icons-vue';
import type { SelectProps } from '@futurefab/ant-design-vue';

interface Props {
  selectOption: any;
  parameterGroupOption: any;
  parameterOption: any;
  isEdit: boolean;
  isDisabled: boolean;
}
const props = withDefaults(defineProps<Props>(), {
  selectOption: [],
  parameterGroupOption: [],
  parameterOption: [],
  isEdit: false,
  isDisabled: false
});
const alarm = defineModel<any>('alarm');
const baseClass = 'emil-alarm-options';
const emailType = ref('');
const emailParameter = ref();
const emailParameterGroup = ref();
const matchingNumber = ref<number>(50);
const parameterMatchingNumber = ref<number>(50);
const anyOf = ref('ANY');
const anyOfOptions = ref<SelectProps['options']>([
  {
    label: 'Any of them',
    value: 'ANY'
  },
  {
    label: 'All of them',
    value: 'ALL'
  }
]);
const emits = defineEmits(['handleDeleteEmailOption']);
const handleDelete = () => {
  emits('handleDeleteEmailOption');
};
const handleChange = (val: string) => {
  emailType.value = val;
  alarm.value.conditionType = val;
  if (val === 'PARAM') alarm.value.conditionRule.ruleValue = 'ANY';
  if (val === 'PARAM_GROUP') alarm.value.conditionRule.ruleValue = 50;
};
const handleParamChange = (val: string[]) => {
  alarm.value.conditionRule.ruleParam = val.join(',');
};
// number
const handleNumberChange = (val: number) => {
  alarm.value.conditionRule.ruleValue = val;
};
// any or all
const handleAnyOfChange = (val: string) => {
  alarm.value.conditionRule.ruleValue = val;
};
watch(
  () => props.isEdit,
  () => {
    if (props.isEdit) {
      const conditionType = alarm.value?.conditionType;
      const ruleParam = alarm.value?.conditionRule?.ruleParam;
      const ruleValue = alarm.value?.conditionRule?.ruleValue;
      // 写入数据
      emailType.value = conditionType;
      switch (conditionType) {
        case 'TOTAL':
          matchingNumber.value = ruleValue;
          break;
        case 'PARAM':
          emailParameter.value = ruleParam?.split(',');
          anyOf.value = ruleValue;
          break;
        case 'PARAM_GROUP':
          emailParameterGroup.value = ruleParam?.split(',');
          parameterMatchingNumber.value = ruleValue;
          break;
      }
    } else {
      emailType.value = props.selectOption?.[0]?.label;
    }
  },
  { immediate: true }
);
</script>

<template>
  <div :class="baseClass">
    <a-button class="space" size="small" @click="handleDelete">
      <template #icon>
        <i class="iconfont icon-eq-substract" style="height: 100%; font-size: 15px" />
      </template>
    </a-button>
    <a-select
      v-model:value="emailType"
      class="space alarmSelect"
      :options="selectOption"
      :disabled="isDisabled"
      :show-arrow="true"
      @change="handleChange"
    />
    <div v-if="emailType === 'TOTAL'" :class="baseClass + '-item'">
      <span class="space">matching rate less than</span>
      <a-input-number
        id="inputNumber"
        v-model:value="matchingNumber"
        class="space"
        :bordered="true"
        :min="0"
        :max="100"
        style="width: 70px"
        :disabled="isDisabled"
        @change="handleNumberChange"
      /><span>%</span>
    </div>
    <div v-if="emailType === 'PARAM_GROUP'" :class="baseClass + '-item'">
      <a-select
        v-model:value="emailParameterGroup"
        mode="multiple"
        class="space alarmSelect"
        :options="parameterGroupOption"
        :disabled="isDisabled"
        @change="handleParamChange"
      />
      <span class="space">matching rate less than</span
      ><a-input-number
        id="inputNumber"
        v-model:value="parameterMatchingNumber"
        class="space"
        :bordered="true"
        :min="0"
        :max="100"
        style="width: 70px"
        :disabled="isDisabled"
        @change="handleNumberChange"
      /><span>%</span>
    </div>
    <div v-if="emailType === 'PARAM'" :class="baseClass + '-item'">
      <a-select
        v-model:value="emailParameter"
        mode="multiple"
        class="space alarmSelect"
        :options="parameterOption"
        :disabled="isDisabled"
        :show-arrow="true"
        @change="handleParamChange"
      />
      <a-select
        v-model:value="anyOf"
        class="space"
        :options="anyOfOptions"
        :disabled="isDisabled"
        @change="handleAnyOfChange"
      /><span>unmatched</span>
    </div>
  </div>
</template>

<style scoped lang="less">
@import url('@/assets/style/variable.less');
.emil-alarm-options {
  margin-top: 10px;
  display: flex;
  flex-direction: row;
  align-items: center;
  // 调整图标位置
  :deep(.ant-select-arrow) {
    top: 15px;
  }
  :deep(.ant-select-clear) {
    top: 15px;
  }
  button {
    &.space {
      color: @primary-color;
    }
  }
  .space {
    margin: 0 10px 0 0;
  }
  .alarmSelect {
    width: 160px;
  }
  &-item {
    display: flex;
    align-items: center;
    span {
      color: @text-subtitle-color;
      font-size: 14px;
    }
  }

  :deep(.ant-input-number-disabled .ant-input-number-input) {
    color: @text-disabled-color-1;
  }
}
</style>
