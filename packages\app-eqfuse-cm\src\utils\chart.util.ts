import { computed } from 'vue';
import { type AlignedData } from '@xchart/vue';
import { SERIES_COLORS } from '@/constant/charts';
import { GroupConfigVO } from '@/model/GroupConfigVO';
import moment from 'moment';
import { round as lodRound } from 'lodash-es';
import {
  useBaseStore,
  type ChartLegendConfig,
  type NewChartConfig
} from '@futurefab/ui-sdk-stores';
import type { CmFilterObject } from '@/views/cm-home/cm-main/components/cm-result-chart/interface';
import type { ChartConfig } from '@/views/cm-home/cm-main/components/group-custom-configuration/interface';
import { GroupIdGap } from '@/views/cm-home/cm-main/components/cm-result-chart/util';
import type { CmLegend } from '@/components/cm-group-legend/interface';

const baseStore = useBaseStore();

// 初始化trace chart每条chart线的数据
export const groupWebSocketData = (WebSocketData: any[]): any => {
  const baseTimeStamp: number = moment('2000/01/01 00:00:00', 'YYYY/MM/DD HH:mm:ss').valueOf();
  return WebSocketData.map((info) => {
    const {
      endDtts,
      fdtaFaultYn,
      groupConfigId,
      eqpModuleId,
      lotId,
      referenceYn,
      slot,
      specCount,
      startDtts,
      paramAlias,
      data,
      steps,
      traceParamDataId,
      chamber,
      eqpId,
      recipeId,
      waferId,
      loopNo,
      stationName,
      contextKey
    } = info;
    const { columns, rows, columnSize, rowSize } = data;
    const groupedData: any = {};
    const ROW_INDEX: any[] = [];
    const TIME_SLOT: number[] = [];
    columns.forEach((column: string | number) => {
      groupedData[column] = [];
    });
    rows.forEach((row: any[], rowIndex: any) => {
      ROW_INDEX.push(rowIndex);
      row.forEach((value, index) => {
        const columnName = columns[index];
        groupedData[columnName].push(value);
        // 手动计算时间差值
        if (columnName === 'TIME') {
          TIME_SLOT.push(value - rows[0][columns.indexOf('TIME')]);
        }
      });
    });
    return {
      data,
      endDtts,
      fdtaFaultYn,
      groupConfigId,
      eqpModuleId,
      lotId,
      substrateId: waferId,
      loopNo,
      stationName,
      referenceYn,
      slot,
      specCount,
      startDtts,
      paramAlias,
      steps,
      traceParamDataId,
      ROW_INDEX,
      ...groupedData,
      TIME_SLOT,
      columnSize,
      rowSize,
      chamber,
      eqpId: eqpId ? eqpId : eqpModuleId?.split(':')[1],
      recipeId,
      contextKey,
      waferId
    };
  });
};

/**
 * 计算数组的最大值和最小值
 * @returns [min, max]
 */
export const minAndMax = (arr: number[]): number[] => {
  return arr.reduce(
    (acc, val) => {
      acc[0] = Math.min(Number(acc[0]), Number(val));
      acc[1] = Math.max(Number(acc[1]), Number(val));
      return acc;
    },
    [Infinity, -Infinity]
  );
};
/**
 * 将chart线数组转换成XChart可用的数据
 * @param initChartData 源数据
 * @param [xKey, yKey] 图表x轴，y轴表示的数据
 * @param groupConfigColor id和其对应颜色的集合
 * @param normalizationValue 对比计算后的 VALUE 值集合
 * @returns 返回图表直接能用的数据和工艺步骤数据
 */
export interface StepTimeTooltip {
  count_slot: number[];
  ROW_INDEX: number[];
  time_slot: number[];
  TIME_SLOT: number[];
  TIME: number[];
  STEP: string[];
}
const filterData = (
  lotId: string,
  groupConfigId: number,
  filterInfo: CmFilterObject | string[],
  customLegendItems: CmLegend[],
  paramAlias: string,
  chamber: string,
  eqpId: string,
  recipeId: string,
  overlay?: boolean,
  selectWaferLoop?: any[],
  groupId?: string[],
  commonGroupConfigVO?: any,
  contextKey?: string,
  loopNo?: string | number,
  stationName?: string,
  waferId?: string
) => {
  if (overlay) {
    //const stationArr = substrateId.split(',');
    // loop stationArr?.[1], wafer station stationArr?.[0], example: 1;PM1_RECIPE-01.csv,A
    // commonGroupConfigVO?.stationCompare   steationame
    // is lopp , loopNo
    // default waferid
    //const id = commonGroupConfigVO.isLoop ? stationArr?.[1] : stationArr?.[0];
    let id: any = '';
    if (commonGroupConfigVO.isLoop) {
      id = loopNo;
    } else {
      id = waferId;
    }

    return (
      groupId?.includes(String(groupConfigId)) &&
      selectWaferLoop?.includes(id) &&
      (filterInfo as string[])?.includes(paramAlias)
    );
  } else {
    const baseFilter = cmChartContextFilter(filterInfo as CmFilterObject, {
      group: groupConfigId,
      eqpId,
      chamber,
      recipeId
    }) && cmChartLegendFilter(customLegendItems, groupConfigId);
    
    // 如果提供了selectWaferLoop，则额外进行wafer/loop过滤
    if (selectWaferLoop && commonGroupConfigVO) {
      let id: any = '';
      if (commonGroupConfigVO.isLoop) {
        id = loopNo;
      } else {
        id = waferId;
      }
      
      // 如果selectWaferLoop为空数组，则不显示任何数据
      if (selectWaferLoop.length === 0) {
        return false;
      }
      
      return baseFilter && selectWaferLoop.includes(id);
    }
    
    return baseFilter;
  }
};
const dealFilterStep = (filterStep: string[], newChartData: any[]) => {
  const stepSet = new Set(filterStep.map((item) => item + ''));
  newChartData.forEach((chartData: any) => {
    // 是否已经包含了这个数据的所有step
    const hasAllStep = chartData.steps.every((item: string) => stepSet.has(item + ''));
    if (!hasAllStep) {
      const ROW_INDEX: number[] = [];
      const TIME: string[] = [];
      const TIME_SLOT: number[] = [];
      const VALUE: number[] = [];
      const count_slot: number[] = [];
      const time_slot: number[] = [];
      const STEP: string[] = [];
      chartData.STEP.forEach((step: string, index: number) => {
        if (stepSet.has(step + '')) {
          ROW_INDEX.push(chartData.ROW_INDEX[index]);
          TIME.push(chartData.TIME[index]);
          TIME_SLOT.push(chartData.TIME_SLOT[index]);
          VALUE.push(chartData.VALUE[index]);
          count_slot.push(chartData.count_slot[index]);
          time_slot.push(chartData.time_slot[index]);
          STEP.push(step);
        }
      });
      chartData.ROW_INDEX = ROW_INDEX;
      chartData.TIME = TIME;
      chartData.TIME_SLOT = TIME_SLOT;
      chartData.VALUE = VALUE;
      chartData.count_slot = count_slot;
      chartData.time_slot = time_slot;
      chartData.STEP = STEP;
    }
  });
};
export interface formatToXChartOption {
  initChartData: any;
  xyKey: string[];
  groupConfigColor: any[];
  normalizationValue: number[][];
  filterInfo: CmFilterObject | string[];
  chartLegendConfig: ChartConfig | null | NewChartConfig;
  customLegendItems: CmLegend[];
  overlay?: boolean;
  selectWaferLoop?: any[];
  groupId?: string[];
  isLoop?: boolean;
  isDeleteBlank?: boolean;
  filterLotWaferData?: any[];
  filterStep?: any[];
  commonGroupConfigVO?: any;
}
export const formatToXChart = ({
  initChartData,
  xyKey: [xKey, yKey],
  groupConfigColor,
  normalizationValue,
  filterInfo,
  chartLegendConfig,
  customLegendItems,
  overlay,
  selectWaferLoop,
  groupId,
  commonGroupConfigVO,
  isDeleteBlank,
  filterLotWaferData,
  filterStep
}: formatToXChartOption): {
  xChartData: AlignedData;
  seriesColor: string[];
  xAxisMinMax: number[];
  yAxisMinMax: number[];
  tooltipData: StepTimeTooltip[];
  newChartData: any;
  xMap: any[];
} => {
  let xAxisData: number[] = [];
  let tempData: number[] = [];
  const seriesData: number[][] = [];
  const seriesColor: string[] = [];
  const tooltipData: StepTimeTooltip[] = [];
  const newChartData = initChartData.filter(
    ({
      groupConfigId,
      paramAlias,
      lotId,
      substrateId,
      chamber,
      eqpId,
      recipeId,
      waferId,
      contextKey,
      loopNo,
      stationName
    }: {
      groupConfigId: number;
      paramAlias: string;
      substrateId: string;
      chamber: string;
      eqpId: string;
      recipeId: string;
      lotId: string;
      waferId: string;
      contextKey: string;
      loopNo: string | number;
      stationName: string;
    }) => {
      const firstFilter = filterData(
        lotId,
        groupConfigId,
        filterInfo,
        customLegendItems,
        paramAlias,
        chamber,
        eqpId,
        recipeId,
        overlay,
        selectWaferLoop,
        groupId,
        commonGroupConfigVO,
        contextKey,
        loopNo,
        stationName,
        waferId
      );
      if (!filterLotWaferData?.length) {
        return firstFilter;
      } else {
        return (
          firstFilter &&
          filterLotWaferData.some((item) => {
            return (
              item.lotId === lotId &&
              item.substrateId === substrateId &&
              Number(item.dataSet) === Number(groupConfigId)
            );
          })
        );
      }
    }
  );
  // 处理 filterStep逻辑
  if (filterStep?.length) {
    dealFilterStep(filterStep, newChartData);
  }

  newChartData.forEach((chartData: any) => {
    tempData.push(...chartData[xKey]);
  });
  xAxisData = [...new Set(tempData)].sort((a, b) => a - b);
  newChartData.forEach((chartData: any, i: number) => {
    const tempTooltipData: StepTimeTooltip = {
      count_slot: [],
      ROW_INDEX: [],
      time_slot: [],
      TIME_SLOT: [],
      TIME: [],
      STEP: []
    };
    const maxLength = Math.max(
      xAxisData.length,
      yKey === 'VALUE' && normalizationValue?.length > 0
        ? normalizationValue[i].length
        : chartData?.[yKey]?.length
    );
    tempData = new Array(maxLength);


    for (const key in tempTooltipData) {
      if (Object.prototype.hasOwnProperty.call(tempTooltipData, key)) {
        tempTooltipData[key as keyof StepTimeTooltip] = new Array(maxLength);
      }
    }
    // 针对VALUE对齐x轴，以及页面上需要显示的STEP、TIME也需要进行对齐
    if (yKey === 'VALUE' && normalizationValue?.length > 0) {
      // normalizationValue于VALUE的长度一致
      normalizationValue[i].forEach((series: number, index: string | number) => {
        const xIndex = xAxisData.indexOf(chartData[xKey][index]);
        tempData[xIndex] = series;
        // 对齐需要显示的STEP和TIME
        ['STEP', 'TIME', 'count_slot', 'ROW_INDEX', 'time_slot', 'TIME_SLOT'].forEach((attr) => {
          if (chartData[attr]) {
            tempTooltipData[attr as keyof StepTimeTooltip][xIndex] = chartData[attr][index];
          }
        });
      });
    } else {
      chartData[yKey].forEach((series: number, index: string | number) => {
        const xIndex = xAxisData.indexOf(chartData[xKey][index]);
        tempData[xIndex] = series;
        // 对齐需要显示的STEP和TIME
        ['STEP', 'TIME', 'count_slot', 'ROW_INDEX', 'time_slot', 'TIME_SLOT'].forEach((attr) => {
          if (chartData[attr]) {
            tempTooltipData[attr as keyof StepTimeTooltip][xIndex] = chartData[attr][index];
          }
        });
      });
    }
    seriesData.push(tempData);
    tooltipData.push(tempTooltipData);

    // 获取并记录每条chart线的颜色
    if (overlay) {
      const colorConfig = groupConfigColor?.find(({ name }) => name === chartData['paramAlias']);
      const color = colorConfig?.chartLegendColor;
      seriesColor.push(color);
    } else {
      const color = groupConfigColor?.find(
        (item) => item.id == chartData['groupConfigId']
      )?.chartLegendColor;
      seriesColor.push(color);
    }
  });

  let xMap: any[] = [];
  if ((xKey === 'TIME' && isDeleteBlank) || filterStep?.length) {
    // 将 时间专为 index， 并且返回 xMap 从索引转为 时间
    xMap = xAxisData;
    xAxisData = xAxisData.map((item, index) => index);
  }


  const res: any = {
    xChartData: [xAxisData, ...seriesData],
    seriesColor,
    xAxisMinMax: minAndMax(xAxisData),
    yAxisMinMax: minAndMax(seriesData.flat()),
    tooltipData,
    newChartData,
    xMap
  };
  return res;
};

/**
 * 复制折线图y轴对象
 */
type SeriesConfig = {
  label: string;
  scale?: string;
  points?: object;
  width?: number;
};
export const useGenerateSeriesConfigs = (count: number, option: SeriesConfig): SeriesConfig[] => {
  // 创建一个计算属性，根据 count 的值生成配置数组
  const seriesConfigs = computed((): SeriesConfig[] => {
    return Array.from({ length: count }, () => option);
  });

  return seriesConfigs.value;
};
// 给数据分配颜色
export const useChartColor = (formatData: GroupConfigVO[]) => {
  const groupConfigColorMap = new Map();
  const seriesColorCount = SERIES_COLORS.length;
  let legendItemIndex = 0;
  // 基准色 light: #007FAC rgba(0, 127, 172) dark: #49c6f2 rgb(73, 198, 242)
  // const baseColor = baseStore.theme === 'light' ? 'rgba(0, 127, 172)' : 'rgb(73, 198, 242)';
  formatData
    .sort((a, b) => (a.reference ? -1 : 1))
    .forEach((gc: GroupConfigVO) => {
      gc.chartLegendColor = SERIES_COLORS[groupConfigColorMap.size % seriesColorCount];
      gc.color = gc.chartLegendColor?.replace('0.4', '1');
      groupConfigColorMap.set(Number(gc.id), {
        legendItemIndex: legendItemIndex++,
        selected: true,
        color: gc.chartLegendColor,
        groupConfig: gc,
        contexts: new Set<string>(gc?.contexts?.map((c) => c.lot + ',' + c.wafer))
      });
      // 存储颜色到headerInfo
      gc.headerInfo.groupColor = gc.color;
    });
  return formatData;
};
export const useParameterColor = (formatData: any[]) => {
  const groupConfigColorMap = new Map();
  const seriesColorCount = SERIES_COLORS.length;
  return formatData.map((item, i) => {
    groupConfigColorMap.set(item, item);
    const chartLegendColor = SERIES_COLORS[(groupConfigColorMap.size - 1) % seriesColorCount];
    return {
      id: i + 1,
      name: item,
      color: chartLegendColor?.replace('0.4', '1'),
      chartLegendColor
    };
  });
};

export const cmChartContextFilter = (
  filterInfo: CmFilterObject,
  context: { group: number | string; eqpId: string; chamber: string; recipeId: string }
) => {
  if (!filterInfo.filterKeys?.length) {
    return true;
  }
  const { group, eqpId, chamber, recipeId } = context;
  if (filterInfo.type === 'Group') {
    return filterInfo.filterKeys.includes(group);
  } else {
    const filterKeys = (filterInfo.filterKeys as string[]).map((k: string) => k.split(GroupIdGap));
    const keyMap = {
      EQP: eqpId,
      Chamber: chamber,
      Recipe: recipeId
    };
    return filterKeys.some(([groupId, contextId]) => {
      return (
        groupId === group + '' && contextId + '' === keyMap[filterInfo.type as keyof typeof keyMap]
      );
    });
  }
};

export const cmChartLegendFilter = (customLegendItem: CmLegend[], groupId: number) => {
  if (!customLegendItem?.length) {
    return true;
  } else {
    return customLegendItem.find((item) => item.id === groupId)?.show;
  }
};

export interface SummaryChartFormatParams {
  data: { segmentStatData: any[] };
  keysArr: any[];
  groupConfigColor: any[];
  step: string;
  filterInfo: CmFilterObject;
  contextIndex: string[];
  chartLegendConfig: NewChartConfig | null;
  customLegendItems: CmLegend[];
  deleteBlank: boolean;
}
/**
 * Summary Chart Data Format
 * @param data websocket源数据
 * @param keysArr 图表数据的含义key值集合
 * @returns 图表所需的数据
 */
export const getSummaryChartFormatData = ({
  data,
  keysArr,
  groupConfigColor,
  step,
  filterInfo,
  contextIndex,
  chartLegendConfig,
  customLegendItems,
  deleteBlank
}: SummaryChartFormatParams) => {
  // const time = ['START_TIME', 'END_TIME'];
  const seriesColor: string[] = [];
  const seriesGroupId: number[] = [];
  const xAxisData: number[][] = [];
  const seriesData: number[][] = [];
  const contextIndexMap: Record<string, number> = {};
  contextIndex.forEach((item, index) => {
    contextIndexMap[item] = index;
  });
  const headerMatch = data?.segmentStatData
    ?.filter(({ groupConfigId, recipestep_name }) => {
      let rcpName = recipestep_name;
      if (recipestep_name?.includes('_Norm')) {
        rcpName = recipestep_name?.split('_Norm')?.[0];
      }
      if (rcpName !== step) {
        return false;
      }
      const legendFilter = cmChartLegendFilter(customLegendItems, groupConfigId);
      if (!legendFilter) {
        return false;
      }
      if (filterInfo.type === 'Group' && filterInfo.filterKeys.length) {
        const filter = filterInfo.filterKeys.includes(groupConfigId);
        if (!filter) {
          return false;
        }
      }

      return true;
    })
    ?.map((chartTable) => {
      const tempObj: any = {};
      chartTable.header.forEach((key: string | number) => {
        tempObj[key] = [];
      });
      chartTable.valueList
        .filter((item: string[]) => {
          if (filterInfo.type === 'Group') {
            return true;
          }
          const context = item[0].split(',');
          const eqpModuleId = context[contextIndexMap.eqpModuleId];
          const eqpId = context[contextIndexMap.eqpId] || eqpModuleId?.split(':')[1];
          return cmChartContextFilter(filterInfo, {
            group: chartTable.groupConfigId,
            eqpId,
            chamber: context[contextIndexMap.chamber],
            recipeId: context[contextIndexMap.recipeId]
          });
        })
        .forEach((values: { [x: string]: any }) => {
          chartTable.header.forEach((key: string, index: string | number) => {
            tempObj[key].push(values[index]);
          });
        });
      const groupDefaultColor = groupConfigColor?.find(
        ({ id }) => Number(id) === chartTable.groupConfigId
      )?.chartLegendColor;

      let color = groupDefaultColor;
      seriesColor.push(color);
      seriesGroupId.push(Number(chartTable.groupConfigId));
      return tempObj;
    });
  const coordinate = headerMatch.map((values) =>
    keysArr.reduce((accumulator, current, index: number) => {
      // x轴内容
      if (index === 0) xAxisData.push(values?.[current]);
      // y轴内容
      if (index === 1) seriesData.push(values?.[current]);
      accumulator.push(values?.[current]);
      return accumulator;
    }, [])
  );

  const flatXAxisData = [...new Set(xAxisData.flat())].sort((a, b) => a - b);
  if (deleteBlank) {
    const timeIndexMap: any = {};
    flatXAxisData.forEach((item, index) => {
      timeIndexMap[item] = index;
    });
    coordinate.forEach((item) => {
      item[0] = item[0].map((time: string) => {
        // 时间转换为索引
        return timeIndexMap[time];
      });
    });
  }
  const res = {
    coordinate,
    seriesColor,
    xAxisMinMax: deleteBlank
      ? minAndMax(flatXAxisData.length ? [0, flatXAxisData.length - 1] : [])
      : minAndMax(
          flatXAxisData.length ? [flatXAxisData[0], flatXAxisData[flatXAxisData.length - 1]] : []
        ),
    yAxisMinMax: minAndMax(seriesData.flat()),
    xMap: flatXAxisData, // 索引 -> 时间
    seriesGroupId
  };

  return res;
};
//返回一个数据的最高位 
function getDigitPositionValue(num: number) {
  // 处理0的特殊情况
  if (num === 0) {
      return 0;
  }
  
  // 取数字的绝对值进行处理
  const absNum = Math.abs(num);
  const numStr = absNum.toString();
  
  // 检查是否为小数
  if (numStr.includes('.')) {
    // 处理小数情况（返回小数点后的位数信息）
    const parts = numStr.split('.');
    const integerPart = parts[0];
    const decimalPart = parts[1];
    
    // 找到第一个非零数字的位置
    let firstNonZeroIndex = 0;
    while (firstNonZeroIndex < decimalPart.length && decimalPart[firstNonZeroIndex] === '0') {
        firstNonZeroIndex++;
    }
    
    // 如果整数部分为0，则有效数字在小数部分
    if (integerPart === '0') {
        return -(firstNonZeroIndex + 1);
    } else {
        // 整数部分不为0，返回整数部分的位数
        return integerPart.length;
    }
  } else {
    // 处理整数情况（返回整数的位数）
    return numStr.length;
  }
}
/**
 * 获取chart坐标轴的范围
 * @param min
 * @param max
 * @param float 边距比例，如果为0则不添加边距
 * @param isCustomRange 是否为自定义范围，如果是则不添加边距
 */
export function getChartAxisRange(minData: number, maxData: number, float: number, isCustomRange: boolean = false) {
  const min = Number(minData.toFixed(5));
  const max = Number(maxData.toFixed(5));
  let range: [number, number] = [min, max];
  
  // 如果是自定义范围，不添加边距
  if (isCustomRange) {
    return range;
  }
  
  if (min === max) {
    if (min === 0) {
      range = [-1, 1];
    } else {
      range = [min * (1 - float), max * (float + 1)];
    }
  } else {
    const gap = max - min;
    const digiPos = getDigitPositionValue(gap / 10);
    const fixMin = min - (max - min) * float;
    const fixMax = max + (max - min) * float;
    if (digiPos > 0) {
      range = [Math.floor(fixMin), Math.ceil(max + (max - min) * float)];
    } else {
      range = [Number(fixMin.toFixed(Math.abs(digiPos))), Number(fixMax.toFixed(Math.abs(digiPos)))];
    }

  }
  return range;
}

export const getCount = (resultGroupInfos: any, key: string, id: number) => {
  return resultGroupInfos
    ?.filter((item: any) => item.id === id)
    ?.reduce((pre: any, current: any) => {
      return (pre += current[key]);
    }, 0);
};

// 判断所有组内的recipe是否全部一样
export const isOneRecipe = (isStation: boolean, checkData: any) => {
  if (isStation) {
    let firstRecipeId = null;
    for (const [key, valueArray] of checkData) {
      if (!valueArray.length) continue;
      if (firstRecipeId === null) {
        firstRecipeId = valueArray[0].RECIPE_ID;
      } else {
        for (const item of valueArray) {
          if (item.RECIPE_ID !== firstRecipeId) {
            return false;
          }
        }
      }
    }
  }
  return true;
};

/**
 * 数字转科学计数法
 * @param value
 * @returns string|number
 */
export const formatterYSM = (value: number, decimalPlace = 3): string | number => {
  const res = value.toString();
  if (res.toUpperCase().includes('E')) return res.toUpperCase();
  let numN1 = 0; //e前面的数值
  let numN2 = 1; //e后面的指数值
  let num1 = 0; //整数位数
  let num2 = 0; //小数位数
  let t1 = 1;
  const absValue = Math.abs(value);
  if (absValue < 10000 && absValue >= 1) {
    return lodRound(value, decimalPlace);
  } else if (absValue < 1 && absValue >= 0.0001) {
    return lodRound(value, 4);
  } else if (absValue == 0) {
    return value;
  }
  //计入小数点后有多少位
  for (let k = 0; k < res.length; k++) {
    if (res[k] === '.') {
      t1 = 0;
    }
    if (t1) {
      num1++;
    } else {
      num2++;
    }
  }
  if (Math.abs(value) < 1) {
    //小数点后一位开始计算
    for (let i = 2; i < res.length; i++) {
      if (res[i] == '0') {
        numN2++;
      } else if (res[i] == '.') {
        continue;
      } else {
        break;
      }
    }
    let v = parseFloat(value.toString());
    //10的numN2次方
    v = v * Math.pow(10, numN2);
    //四舍五入 仅保留3位小数位数
    v = Number(lodRound(v, decimalPlace));
    return v.toString() + ' *10 E-' + numN2;
  } else if (num1 > 1) {
    if (res[0] == '-') {
      numN1 = num1 - 2;
    } else {
      numN1 = num1 - 1;
    }
    let v = parseFloat(value.toString());
    v = v / Math.pow(10, numN1);
    v = Number(lodRound(v, decimalPlace));
    return v.toString() + ' *10 E+' + numN1;
  } else {
    return Math.round(value);
  }
};
