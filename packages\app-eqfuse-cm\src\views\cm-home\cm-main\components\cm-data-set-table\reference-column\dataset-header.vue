<script setup lang="ts">
import { ref } from 'vue';
import { CloseOutlined } from '@ant-design/icons-vue';
import EditGroupName from '@/views/cm-home/cm-main/add-dataset/edit-group-name/index.vue';
import { message } from '@futurefab/ant-design-vue';
import { t } from '@futurefab/vxe-table';
import ColorSelect from '@/components/color-select/index.vue';
import { updateGroupNameAndColor } from '@futurefab/ui-sdk-api';
import { showWarning, showError } from '@futurefab/ui-sdk-ees-basic';
import { datasetGroupStore } from '@/views/cm-home/cm-main/add-dataset/dataset-group-store';
const props = withDefaults(
  defineProps<{
    selectedReferenceType: string;
    isHistory: boolean;
    columnsData: any;
    tableInfo: any[];
    websocketParams: any;
  }>(),
  {
    selectedReferenceType: '',
    isHistory: false,
    columnsData: () => ({}),
    tableInfo: () => [],
    websocketParams: () => ({})
  }
);
const reference = defineModel<boolean>('reference');
const groupName = defineModel<string>('groupName');
const color = defineModel<string>('color');
const chartLegendColor = defineModel<string>('chartLegendColor');
const emits = defineEmits(['removeGroupConfig', 'nameColorChange']);

// 校验名称
const isVerify = ref<boolean>(false);
// 更新数据库分组名、颜色
const updateGroup = async (newGroupName?: string, newColor?: string) => {
  try {
    const res = await updateGroupNameAndColor({
      bodyParams: [
        {
          requestId: props.websocketParams.requestId,
          requestDt: props.websocketParams.requestDt,
          groupId: String(props.columnsData.id),
          groupNameAlias: newGroupName || groupName.value,
          groupColor: newColor || color.value
        }
      ]
    });
    if (res.status === 'SUCCESS') {
      if (newGroupName && groupName.value !== newGroupName) {
        // 同步到 新增 dataset 的弹窗中
        datasetGroupStore.handleRename(groupName.value!, newGroupName, true, props.isHistory);
      }
      groupName.value = newGroupName || groupName.value;
      color.value = newColor || color.value;
      chartLegendColor.value = newColor || chartLegendColor.value;
      message.success(t('cm.tips.modifySuccess'));
      emits('nameColorChange');
    } else {
      showError(res.msg);
    }
  } catch (error) {
    console.log('error:', error);
  }
};
const handleRename = (newGroupName: string, oldGroupName: string) => {
  if (newGroupName.trim() === '' || newGroupName === oldGroupName) return;
  // 获取所有分组名的名称
  const allGroupName = props.tableInfo?.[0]?.configColumns
    .filter((item: any) => !Array.isArray(item))
    .map((item: any) => item.name);
  if (allGroupName.includes(newGroupName)) {
    isVerify.value = true;
  } else {
    // 请求接口
    if (props.websocketParams.requestId) {
      updateGroup(newGroupName);
      return;
    }
    // 不请求接口
    if (newGroupName && groupName.value !== newGroupName) {
      // 同步到 新增的弹窗中
      datasetGroupStore.handleRename(groupName.value!, newGroupName, true, props.isHistory);
    }
    groupName.value = newGroupName;
    message.success(t('cm.tips.modifySuccess'));
  }
};
const removeGroupConfig = (data: any) => {
  emits('removeGroupConfig', data);
};
const uploadColor = (newColor: string) => {
  // 请求接口
  if (props.websocketParams.requestId) {
    updateGroup(groupName.value, newColor);
    return;
  }
  // 不请求接口
  color.value = newColor;
  message.success(t('cm.tips.modifySuccess'));
};
</script>

<template>
  <div class="dataset-header-box">
    <a-checkbox v-model:checked="reference" :disabled="selectedReferenceType === 'dynamic_trace'" />
    <EditGroupName
      :groupName="groupName || ''"
      v-model:isVerify="isVerify"
      @handleRename="handleRename"
    />
    <ColorSelect
      :type="'circle'"
      :showArrow="true"
      :tooltipText="$t('cm.tips.colorOptions')"
      :curColor="color || ''"
      @selectColor="uploadColor"
    />
    <div class="remove-box">
      <a-button
        type="link"
        class="remove-box-btn"
        :disabled="tableInfo?.length > 2"
        @click="removeGroupConfig(columnsData)"
      >
        <template #icon>
          <close-outlined class="remove-box-btn-icon" />
        </template>
      </a-button>
    </div>
  </div>
</template>

<style scoped lang="less">
@import url('@/assets/style/variable.less');
.dataset-header-box {
  display: flex;
  align-items: center;
  .remove-box {
    display: flex;
    align-items: center;
    &-btn {
      width: 15px;
      height: 22px;
    }
    &-btn-icon {
      color: @text-hint-color;
      font-size: 12px;
      vertical-align: 22px;
    }
  }
}
</style>
