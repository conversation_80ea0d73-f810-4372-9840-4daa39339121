import { type LocationQueryValue } from 'vue-router';
import { getPermissionButton } from '@futurefab/ui-sdk-ees-basic';

function toNoPermission() {
    window.history.replaceState({}, '', '/no-permission');
}
//处理从特定页面跳转
export const usePermissionFromPage = (fromRoute: string | LocationQueryValue[]) => {
    console.log('usePermissionFromPage');
    const permissionPageList = JSON.parse(window.localStorage.getItem('permissionPageList')!) || [];
    if (!permissionPageList.includes(fromRoute)) {
        toNoPermission();
    }
};

//处理在UserGroup页面注册的二级页面
export const usePermissionLevelTwoPage = async (
    pathname: string,
    pageId: string,
    baseUrl: string,
) => {
    const authorityInfo: any = await getPermissionButton(baseUrl + pathname);
    const _buttonList = authorityInfo.buttonList || '';
    //全选时false，并且button中没有对应页面的id
    if (!authorityInfo.allButtonFlag && !_buttonList.includes(pageId)) {
        toNoPermission();
    } else {
        // 有权限函数返回按钮信息，其他按钮权限不用重复请求
        return authorityInfo;
    }
};
