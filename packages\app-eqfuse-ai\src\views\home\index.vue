<script lang="ts" setup>
import VueMarkdown from '@futurefab/vue-markdown-plus';
import { ref, computed, nextTick, onMounted, watch, onUnmounted, h, reactive } from 'vue';
import ReferenceTip from '../../components/ReferenceTip/index.vue';
import { useScreenSize } from '../../hooks/useScreenSize';
import { replaceReferences } from '../../utils/index';
import { v4 as uuidv4 } from 'uuid';
import { getAgentList, getAiAnswer, getUserAgentsAndChats } from '@futurefab/ui-sdk-api';
import { showInfo, showWarning, showError } from '@futurefab/ui-sdk-ees-basic';
import { pdfPrefix } from '@/assets/const';
const ReferenceTipComponent = (props: any) => {
  return h(ReferenceTip, props);
};
const { isSmallScreen } = useScreenSize(1600);
const chatContentContainer = ref<HTMLElement | null>(null);

let inputValue = ref('');
let isFinish = ref(false);
let isEditorFocus = ref(false);
interface Message {
  role: 'user' | 'ai';
  content: string;
  createDtts: Date;
  id: number | string;
  thinking?: boolean;
  referenceList?: {
    doc_id: string;
    doc_name: string;
    url: string;
  }[];
}
let promptTips = ref<PromptTip[]>([]);
interface PromptTip {
  text: string;
  color: string;
  icon?: string;
}

interface SidebarExposed {
  loadHistoryList: (reset?: boolean) => Promise<void>;
}

let currentAgent: { id: any } | null = null;
let disconnectSSE: any = null;

const sseId = uuidv4();
onMounted(() => {
  initPage();
});

const initPage = async () => {
  try {
    // await getAgentData();
    await getUserAgentAndChatList();
    connectEventSource();
  } catch (err) {
    console.error(err);
  }
};

const getAgentData = async () => {
  try {
    const res = await getAgentList({
      bodyParams: {}
    });

    const { data } = res;
    if (data && data.length) {
      currentAgent = data[0];
    }
    return data;
  } catch (error) {
    console.error('Failed to get agent data:', error);
    throw error;
  }
};

const getUserAgentAndChatList = async () => {
  const res: any = await getUserAgentsAndChats({ bodyParams: {} });

  if (res && res.status === 200) {
    agentAndChatList.value = res.data;
  }
};

let eventSource: EventSource | null = null;
let reconnectAttempts = 0;
let maxReconnectAttempts = 5;
let reconnectDelay = 1000; // 初始重连延迟1秒
let reconnectTimer: NodeJS.Timeout | null = null;
let isReconnecting = false;

const connectEventSource = () => {
  if (isReconnecting) {
    console.log('正在重连中，跳过重复连接请求');
    return;
  }

  const url = '/apiAi/chat/sse/' + sseId;

  try {
    if (eventSource) {
      eventSource.close();
      eventSource = null;
    }

    eventSource = new EventSource(url);

    // 保存 SSE 实例
    currentSSE = eventSource;

    eventSource.onmessage = function (event) {
      handleServerData(event.data);
      reconnectAttempts = 0;
      reconnectDelay = 1000;
    };

    eventSource.onopen = function () {
      console.log('SSE 连接已建立, sseId:', sseId);
      reconnectAttempts = 0;
      reconnectDelay = 1000;
      isReconnecting = false;

      if (reconnectTimer) {
        clearTimeout(reconnectTimer);
        reconnectTimer = null;
      }
    };

    eventSource.onerror = function (err) {
      console.error('EventSource 连接失败:', err);

      // 关闭当前连接
      if (eventSource) {
        eventSource.close();
        eventSource = null;
      }

      // 错误时也要清理状态
      thinking.value = false;
      isFinish.value = true;
      currentSSE = null;

      // 尝试重连
      if (reconnectAttempts < maxReconnectAttempts) {
        scheduleReconnect();
      } else {
        console.error(`重连失败，已达到最大重试次数: ${maxReconnectAttempts}`);
        isReconnecting = false;
      }
    };
  } catch (error) {
    console.error('创建 EventSource 失败:', error);
    scheduleReconnect();
  }
};

const scheduleReconnect = () => {
  if (isReconnecting) return;

  isReconnecting = true;
  reconnectAttempts++;

  console.log(`准备第 ${reconnectAttempts} 次重连，延迟 ${reconnectDelay}ms`);

  reconnectTimer = setTimeout(() => {
    console.log(`开始第 ${reconnectAttempts} 次重连...`);
    connectEventSource();
  }, reconnectDelay);

  // 指数退避策略：延迟时间逐渐增加
  reconnectDelay = Math.min(reconnectDelay * 1.5, 10000); // 最大延迟10秒
};

const disconnectEventSource = () => {
  if (eventSource) {
    eventSource.close();
    eventSource = null;
  }

  if (reconnectTimer) {
    clearTimeout(reconnectTimer);
    reconnectTimer = null;
  }

  // 清理 SSE 连接
  if (currentSSE) {
    currentSSE.close();
    currentSSE = null;
  }

  isReconnecting = false;
  reconnectAttempts = 0;
  reconnectDelay = 1000;
};

// 缓存，在对话结束后进行文本替换
let lastCacheConent = '';
const handleServerData = (data: string) => {
  let newMsg = JSON.parse(data);
  const { finish, content, reference, ragSessionId } = newMsg;

  if (newMsg.ragChatSessionId) d.ragChatSessionId = newMsg.ragChatSessionId;
  if (newMsg.ragAgentSessionId) d.ragAgentSessionId = newMsg.ragAgentSessionId;

  isFinish.value = !!finish;

  const lastMessage = messages.value[messages.value.length - 1];
  if (!lastMessage) return;

  if (!finish) {
    // 流式生成过程中，保持 thinking 状态
    lastMessage.content = content;
    lastCacheConent = content;
    d.canSend = false;
  } else {
    // 生成完成时，才结束 thinking 状态
    lastMessage.thinking = false;
    thinking.value = false;

    if (!reference) {
      return;
    }
    const { doc_aggs, chunks } = reference;
    lastMessage.referenceList = (doc_aggs || []).map(
      (item: { doc_id: string; doc_name: string }) => ({
        ...item,
        url: `${pdfPrefix}${item.doc_id}`
      })
    );
    lastMessage.content = replaceReferences(lastCacheConent, chunks || []);
    d.canSend = true;
    // 清理 SSE 连接
    currentSSE = null;
  }

  // 滚动到最下方
  nextTick(() => {
    scrollToBottom();
  });
};

// agents and chats
interface AgentOrChatItem {
  createDtts: string;
  menu: boolean;
  name: string;
  permissionId: number;
  sort: number;
  type: string;
  updateDtts: string;
  value: string;
}
let agentAndChatList = ref<AgentOrChatItem[]>([]);
let selectedAgentOrChat = ref<AgentOrChatItem | null>(null);
let otherAgentOrChatSelected = false;
const handleClickAgentOrChat = (item: AgentOrChatItem) => {
  otherAgentOrChatSelected = false;
  if (selectedAgentOrChat.value && selectedAgentOrChat.value.value === item.value) {
    selectedAgentOrChat.value = null;
    d.askType = '';
    d.askId = '';
    return;
  }
  selectedAgentOrChat.value = item;
  d.askType = item.type;
  d.askId = item.value;
};

const toggleOtherAgentOrChat = (item: AgentOrChatItem) => {
  const isAlreadySelected = selectedAgentOrChat.value?.value === item.value;

  if (isAlreadySelected) {
    selectedAgentOrChat.value = null;
    d.askType = '';
    d.askId = '';
    otherAgentOrChatSelected = false;
  } else {
    selectedAgentOrChat.value = item;
    d.askType = item.type;
    d.askId = item.value;
    otherAgentOrChatSelected = true;
  }
};

// recomment
let recommentList: any = [
  // { label: '人事规章制度' },
  // { label: '财务报销制度' },
  // { label: 'Analysis Top Recipe only' }
];

const handleRecommentClick = (recomment: { label: any }) => {
  const { label } = recomment;
  inputValue.value = label;
  handleSend();
};

// quick option
let selectedOptionIdx = ref<number | null>(null);
const handleOptionClick = (index: number) => {
  selectedOptionIdx.value = index;
  inputValue.value = promptTips.value[index].text;
  handleSend();
};

// chat-editor
const handleInputChange = (event: Event) => {
  const target = event.target as HTMLDivElement;
  inputValue.value = target.innerText;
};

const handleKeyDown = (event: Event) => {
  const keyboardEvent = event as KeyboardEvent;
  if (keyboardEvent.key === 'Enter' && !keyboardEvent.shiftKey) {
    keyboardEvent.preventDefault();
    handleSend();
  }
};

let d = reactive({
  isUserScrolling: false,
  dialogueId: '',
  user_id: '',
  model: [],
  askType: 'NONE', // 询问类型
  askId: '', // agentId或者是chatId
  ragChatSessionId: '',
  ragAgentSessionId: '',
  ragAgentsOptions: [],
  fileList: [],
  models: [],
  currentItemKey: '',
  selectedKeys: [],
  incrementalOutput: true,
  enableThinking: false,
  content: inputValue.value,
  userCurrentHistoryContent: '',
  userCurrentMsgId: '',
  rows: 2,
  msgs: [] as Message[],
  canSend: true,
  enableRAG: true,
  ragData: [] as any[],
  mediaOutputOptions: {}
});

let messages = ref<Message[]>([]);
const hasMessages = computed(() => messages.value.length > 0);
let thinking = ref(false);
let currentSSE: any = null; // 保存当前的 SSE 连接

const handleSend = async () => {
  // 如果正在思考中，则停止生成
  if (thinking.value) {
    handleStopGeneration();
    return;
  }

  if (!inputValue.value.trim()) {
    return;
  }

  messages.value.push({
    role: 'user',
    content: inputValue.value,
    createDtts: new Date(),
    id: messages.value.length + 1
  });

  messages.value.push({
    role: 'ai',
    content: '',
    createDtts: new Date(),
    id: messages.value.length + 1,
    thinking: true
  });
  thinking.value = true;

  d.canSend = false;
  d.userCurrentHistoryContent = d.content;
  d.userCurrentMsgId = uuidv4();
  d.msgs = messages.value;
  let sendMsgs = d.msgs
    .map((e: any) => ({ content: e.content, role: e.role }))
    .slice(0, messages.value.length);

  getAiAnswer({
    msgs: sendMsgs, // 对话的上下文内容
    enableRAG: d.enableRAG, // 是否启用rag
    ragAgentSessionId: d.ragAgentSessionId, // rag代理人会话ID
    ragChatSessionId: d.ragChatSessionId, // rag聊天会话ID
    askType: selectedAgentOrChat.value ? d.askType : 'NONE', // 询问类型
    askId: selectedAgentOrChat.value ? d.askId : '', // agentId或者是chatId
    question: inputValue.value, // 用户的最后的提问
    dialogueId: d.dialogueId, // 对话ID
    enableThinking: d.enableThinking, // 是否开启思考模式
    user_id: d.user_id, // 用户id
    sseId: sseId // sseId
  }).then((res: any) => {
    if (!res || !res.res) {
      showError('获取回答失败');
      return;
    }

    console.log(res);

    d.dialogueId = res.data.dialogueId;

    // 清空输入框
    inputValue.value = '';
    const inputElement = document.querySelector('.placeholder-input');
    if (inputElement) {
      inputElement.innerHTML = '';
    }
  });
};

// 停止生成内容的函数
const handleStopGeneration = () => {
  thinking.value = false;
  isFinish.value = true;

  // 断开 SSE 连接
  if (currentSSE) {
    currentSSE.close();
    currentSSE = null;
  }

  // 更新最后一条 AI 消息的状态
  const lastMessage = messages.value[messages.value.length - 1];
  if (lastMessage && lastMessage.role === 'ai') {
    lastMessage.thinking = false;
    if (!lastMessage.content.trim()) {
      lastMessage.content = '已停止生成内容。';
    }
  }
};

// pdf-viewer
let pdfVisible = ref(false);
let pdfUrl = ref('');
// sidebar
const handleAddContent = async () => {
  messages.value = [];
  // 清理相关状态
  thinking.value = false;
  isFinish.value = true;
  if (currentSSE) {
    currentSSE.close();
    currentSSE = null;
  }
};
const handleSetMessages = (messageList: any) => {
  messages.value = messageList.map((item: any) => {
    const { content, metadata, userMessage } = item;
    let aiContent = '';
    let referenceList: Message['referenceList'] = []; // 单个消息的参考资料

    if (!userMessage && metadata) {
      const { reference } = metadata;
      const { chunks, doc_aggs } = reference;
      if (reference) {
        referenceList = (doc_aggs || []).map((doc: any) => ({
          doc_id: doc.doc_id,
          doc_name: doc.doc_name,
          url: `${pdfPrefix}${doc.doc_id}`
        }));
        aiContent = replaceReferences(content, chunks || []);
      }
    } else {
      aiContent = content;
    }

    return {
      role: item.createBy === 'system' ? 'ai' : 'user',
      content: userMessage ? item.content : aiContent,
      createDtts: item.createDtts,
      id: item.id,
      referenceList
    };
  });

  isFinish.value = true;
  // 清理相关状态
  thinking.value = false;
  if (currentSSE) {
    currentSSE.close();
    currentSSE = null;
  }
};

// to-bottom-btn
const scrollToBottom = () => {
  if (chatContentContainer.value) {
    chatContentContainer.value.scrollTo({
      top: chatContentContainer.value.scrollHeight,
      behavior: 'smooth'
    });
  }
};
let isAtBottom = ref(false);
let showGoBottomBtn = ref(false);
const checkScrollPosition = () => {
  if (!chatContentContainer.value) return;

  const { scrollTop, scrollHeight, clientHeight } = chatContentContainer.value;

  // 是否出现滚动条
  const hasScrollbar = scrollHeight > clientHeight;
  // 是否在底部（留10px缓冲区）
  isAtBottom.value = scrollHeight - scrollTop - clientHeight <= 10;
  showGoBottomBtn.value = hasScrollbar && !isAtBottom.value && isFinish.value;
};

let scrollListener: any = null;
watch(
  () => messages.value.length,
  async (newValue) => {
    if (newValue > 0 && !scrollListener) {
      await nextTick();
      if (chatContentContainer.value) {
        chatContentContainer.value.addEventListener('scroll', checkScrollPosition);
        scrollListener = () => {
          if (chatContentContainer.value) {
            chatContentContainer.value.removeEventListener('scroll', checkScrollPosition);
          }
        };
      }
    } else if (newValue === 0 && scrollListener) {
      if (chatContentContainer.value) {
        chatContentContainer.value.removeEventListener('scroll', checkScrollPosition);
        scrollListener = null;
      }
    }
  },
  { immediate: true }
);

onUnmounted(() => {
  if (scrollListener) {
    scrollListener();
    scrollListener = null;
  }

  if (disconnectSSE) {
    disconnectSSE();
  }
});

let isSidebarExpand = ref(false);
const toggleSidebar = (isExpand: boolean) => {
  isSidebarExpand.value = isExpand;
};

const sidebarRef = ref<SidebarExposed | null>(null);
const refreshSessions = () => {
  if (sidebarRef.value) {
    sidebarRef.value.loadHistoryList(true);
  }
};
let toBottomBtnHovered = ref(false);
</script>

<template>
  <div class="my-app">
    <!-- <Sidebar
      v-if="!pdfVisible"
      ref="sidebarRef"
      @addContent="handleAddContent"
      @setMessages="handleSetMessages"
      @toggleSidebar="toggleSidebar" /> -->
    <div class="main">
      <!-- 默认状态 -->
      <div v-if="!messages.length" class="layout-container scroll">
        <div
          class="home-page flex-center flex-column"
          width="30px"
          height="40px"
          :style="{ transform: isSidebarExpand && isSmallScreen ? 'translateX(160px)' : '' }"
        >
          <div class="home-logo flex-center">
            <SvgIcon name="logo" width="54px" height="54px" color="#ecf3ff" />
          </div>

          <div class="home-top">您好，我能为你做什么？</div>

          <div class="quick-options flex-center">
            <div
              class="option-item flex-center"
              v-for="(item, index) in promptTips"
              :key="index"
              :class="{ 'is-selected': selectedOptionIdx === index }"
              @click="handleOptionClick(index)"
            >
              <SvgIcon :name="item.icon" />
              <span class="label">{{ item.text }} </span>
              <SvgIcon name="swapRightOut" />
            </div>
          </div>

          <div class="chat-editor flex-column" :class="{ 'chat-editor-focus': isEditorFocus }">
            <div
              contenteditable="true"
              @focus="isEditorFocus = true"
              @blur="isEditorFocus = false"
              @keydown="handleKeyDown"
              @input="handleInputChange"
              placeholder="你好，今天我能为您做些什么？"
              class="placeholder-input"
            ></div>

            <div class="handler flex-between">
              <div class="flex-row">
                <a-tooltip placement="top" color="#4E5969">
                  <template #title>
                    <div>• 图片数量：最多10个</div>
                    <div>• 文件类型：pdf、txt、csv、docx.</div>
                  </template>
                  <div class="file-input flex-center">
                    <span
                      class="icon iconfont icon-upload-attachments"
                      style="font-size: 20px; color: #626c7b"
                    ></span>
                  </div>
                </a-tooltip>

                <div
                  v-for="(item, index) in agentAndChatList.slice(0, 3)"
                  :key="index"
                  class="agent-chat-item flex-center"
                  :class="{
                    selected: selectedAgentOrChat && selectedAgentOrChat.value === item.value
                  }"
                  @click="handleClickAgentOrChat(item)"
                >
                  {{ item.name }}
                  <span
                    class="iconfont icon-ai-skill"
                    style="color: #626c7b; margin-left: 8px"
                  ></span>
                </div>

                <a-popover
                  v-if="agentAndChatList.length > 3"
                  trigger="hover"
                  :arrow="false"
                  overlayClassName="agent-chat-popover"
                  :getPopupContainer="(triggerNode: HTMLElement) => triggerNode.parentNode"
                >
                  <template #content>
                    <div
                      v-for="(item, index) in agentAndChatList.slice(3, agentAndChatList.length)"
                      :key="index"
                      class="item flex-center"
                      :class="{
                        selected: selectedAgentOrChat && selectedAgentOrChat.value === item.value
                      }"
                      @click="toggleOtherAgentOrChat(item)"
                    >
                      {{ item.name }}
                    </div>
                  </template>

                  <div
                    class="agent-chat-item flex-center"
                    :class="{ selected: otherAgentOrChatSelected }"
                  >
                    其他
                    <span
                      class="iconfont icon-ai-skill"
                      style="color: #626c7b; margin-left: 8px"
                    ></span>
                  </div>
                </a-popover>
              </div>

              <a-tooltip placement="top" color="#4E5969">
                <template #title>
                  <span>{{ thinking ? '停止回答' : '发送' }}</span>
                </template>
                <div
                  class="send-btn flex-center"
                  @click="handleSend"
                  :class="{
                    'send-active': !!inputValue.trim() && !thinking,
                    'send-inactive': !inputValue.trim() && !thinking
                  }"
                >
                  <SvgIcon
                    v-if="!!inputValue.trim() && !thinking"
                    name="ai-sending-active"
                    width="16px"
                    height="16px"
                  />
                  <span
                    v-if="!inputValue.trim() && !thinking"
                    class="iconfont icon-sending"
                    style="width: 16px; height: 16px; font-size: 16px; color: #abb0b6"
                  ></span>
                </div>
              </a-tooltip>
            </div>
          </div>
        </div>
      </div>
      <!-- 对话状态 -->
      <div v-else class="chat-page chat">
        <div
          class="chat-detail-content"
          :style="{ transform: isSidebarExpand && isSmallScreen ? 'translateX(160px)' : '' }"
        >
          <div class="chat-detail-main" ref="chatContentContainer">
            <div class="chat-content-container">
              <div
                class="chat-content-list"
                :style="{ padding: pdfVisible ? '0 80px' : '6px 0 30px 4px' }"
              >
                <div
                  v-for="(message, index) in messages"
                  :key="index"
                  :class="['flex-row', 'message', message.role]"
                >
                  <div :class="['message-content flex-column', message.role]">
                    <div class="message-text">
                      <VueMarkdown
                        :source="message.content"
                        :components="{ reference: ReferenceTipComponent }"
                      />
                    </div>
                    <div
                      v-if="message.role === 'ai' && message.thinking"
                      class="thinking flex-center"
                    >
                      <SvgIcon name="logo" width="20px" height="20px" color="#ecf3ff" />
                      <span style="margin: 0 8px 0 4px">思考中....</span>
                      <span class="iconfont icon-arrow-down" style="font-size: 12px"></span>
                    </div>

                    <div
                      v-if="
                        isFinish &&
                        !message.thinking &&
                        message.role === 'ai' &&
                        message.referenceList &&
                        message.referenceList.length
                      "
                      class="reference-container"
                    >
                      <div class="reference-title">
                        为你找到 {{ message.referenceList.length }} 篇参考资料
                      </div>
                      <div
                        class="reference-item"
                        v-for="(item, index) in message.referenceList"
                        :key="index"
                      >
                        {{ index + 1 }}.
                        <span
                          style="color: #0065ff; cursor: pointer"
                          @click="
                            pdfUrl = item['url'];
                            pdfVisible = true;
                          "
                        >
                          {{ item['doc_name'] }}
                        </span>
                      </div>
                    </div>

                    <div class="toolbar" v-if="!thinking && message.role === 'ai'">
                      <a-tooltip placement="top">
                        <template #title>复制</template>
                        <span class="iconfont icon-ai-copy item"></span>
                      </a-tooltip>
                      <a-tooltip placement="top">
                        <template #title>重新生成</template>
                        <span class="iconfont icon-retry item"></span>
                      </a-tooltip>
                      <a-tooltip placement="top">
                        <template #title>喜欢</template>
                        <span class="iconfont icon-ai-like item"></span>
                      </a-tooltip>
                      <a-tooltip placement="top">
                        <template #title>不喜欢</template>
                        <span class="icon iconfont icon-ai-dislike item"> </span>
                      </a-tooltip>
                    </div>
                  </div>
                </div>
                <div v-if="isFinish && !thinking" class="recommend-list">
                  <div
                    v-for="recomment in recommentList"
                    class="recommend-item flex-center"
                    @click="handleRecommentClick(recomment)"
                  >
                    <SvgIcon name="recommend" />
                    <span>{{ recomment.label }}</span>
                    <SvgIcon name="swapRightOut" color="red" />
                  </div>
                </div>
              </div>
            </div>

            <div class="chat-action">
              <div v-if="showGoBottomBtn" class="bottom-action-container">
                <div
                  class="to-bottom flex-center"
                  :class="[showGoBottomBtn ? 'to-bottom-show' : '']"
                  @click="scrollToBottom"
                  @mouseenter="toBottomBtnHovered = true"
                  @mouseleave="toBottomBtnHovered = false"
                >
                  <span class="iconfont icon-arrow-down" style="color: #4e5969"></span>
                </div>
              </div>

              <div
                class="input-container flex-column"
                :class="{ 'has-message': hasMessages }"
                :style="{ width: pdfVisible ? '700px' : '900px' }"
              >
                <div
                  contenteditable="true"
                  @keydown="handleKeyDown"
                  @input="handleInputChange"
                  placeholder="你好，今天我能为您做些什么？"
                  class="placeholder-input"
                ></div>

                <div class="handler flex-between">
                  <div class="flex-row">
                    <a-tooltip placement="top" color="#4E5969">
                      <template #title>
                        <div>• 图片数量：最多10个</div>
                        <div>• 文件类型：pdf、txt、csv、docx.</div>
                      </template>
                      <div class="file-input flex-center">
                        <span
                          class="icon iconfont icon-upload-attachments"
                          style="font-size: 20px; color: #626c7b"
                        ></span>
                      </div>
                    </a-tooltip>

                    <div
                      v-for="(item, index) in agentAndChatList.slice(0, 3)"
                      :key="index"
                      class="agent-chat-item flex-center"
                      :class="{
                        selected: selectedAgentOrChat && selectedAgentOrChat.value === item.value
                      }"
                      @click="handleClickAgentOrChat(item)"
                    >
                      {{ item.name }}
                      <span
                        class="iconfont icon-ai-skill"
                        style="color: #626c7b; margin-left: 8px"
                      ></span>
                    </div>

                    <a-popover
                      v-if="agentAndChatList.length > 3"
                      trigger="hover"
                      :arrow="false"
                      overlayClassName="agent-chat-popover"
                      :getPopupContainer="(triggerNode: HTMLElement) => triggerNode.parentNode"
                    >
                      <template #content>
                        <div
                          v-for="(item, index) in agentAndChatList.slice(
                            3,
                            agentAndChatList.length
                          )"
                          :key="index"
                          class="item flex-center"
                          :class="{
                            selected:
                              selectedAgentOrChat && selectedAgentOrChat.value === item.value
                          }"
                          @click="toggleOtherAgentOrChat(item)"
                        >
                          {{ item.name }}
                        </div>
                      </template>

                      <div
                        class="agent-chat-item flex-center"
                        :class="{ selected: otherAgentOrChatSelected }"
                      >
                        其他
                        <span
                          class="iconfont icon-ai-skill"
                          style="color: #626c7b; margin-left: 8px"
                        ></span>
                      </div>
                    </a-popover>
                  </div>
                  <a-tooltip placement="top" color="#4E5969">
                    <template #title>
                      <span>{{ thinking ? '停止回答' : '发送' }}</span>
                    </template>
                    <div
                      class="send-btn flex-center"
                      @click="handleSend"
                      :style="{
                        cursor: !!inputValue.trim() || thinking ? 'pointer' : 'not-allowed',
                        'padding-left': thinking ? '0px' : '3px'
                      }"
                    >
                      <SvgIcon
                        v-if="!!inputValue.trim() && !thinking"
                        name="ai-sending-active"
                        width="16px"
                        height="16px"
                      />

                      <span
                        v-if="!inputValue.trim() && !thinking"
                        class="iconfont icon-sending"
                        style="width: 16px; height: 16px; font-size: 16px; color: #abb0b6"
                      ></span>

                      <span
                        v-if="thinking"
                        class="iconfont icon-stop"
                        style="color: #0065ff"
                      ></span>
                    </div>
                  </a-tooltip>
                </div>
              </div>
              <div class="chat-action-bottom"></div>
            </div>
          </div>
        </div>
        <PDFViewer
          v-if="pdfVisible"
          @close="pdfVisible = false"
          :style="{ width: '45%', 'max-width': isSidebarExpand ? '795px' : '' }"
          :src="pdfUrl"
        />
      </div>
    </div>
  </div>
</template>

<style lang="less" scoped>
@import url('@/assets/style/variable.less');
.my-app {
  min-width: 375px;
  display: flex;
  overflow: hidden;
  position: relative;
}
.main {
  display: flex;
  flex: 1;
  height: calc(100vh - 60px);
  width: 100%;
  background: #fff;
  .layout-container {
    display: flex;
    flex-direction: column;
    flex: 1;
    width: 100%;
    height: 100%;
    background-image: url('../../assets/images/bg.png');
    background-repeat: no-repeat;
    background-position: center center;
    background-size: cover;
    &.scroll {
      overflow-y: auto;
      scrollbar-gutter: stable;
    }

    .home-page {
      box-sizing: border-box;
      width: 100%;
      // height: calc(100% - 50px);
      flex: 1;
      position: relative;
      margin: 0 auto;

      .home-top {
        flex-shrink: 1;
        margin-bottom: 60px;
        display: flex;
        flex-direction: column;
        justify-content: flex-end;

        font-size: 48px;
        font-weight: bold;
        background: linear-gradient(279deg, #49c6f2 -1%, @primary-color 53%, #ff68a2 100%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }

      .quick-options {
        margin-bottom: 10px;

        .option-item {
          padding: 0 14px;
          height: 40px;
          border-radius: 100px;
          background: #ecf3ff;
          border: 1px solid #49c6f2;
          cursor: pointer;
          margin: 0 10px;
          color: #132035;
          .label {
            margin: 0 8px 0 4px;
            &:hover {
              color: @primary-color;
            }
          }

          &.is-selected {
            background: #e2f5ff;
            border-color: @primary-color;
            box-shadow: 0 0 0 2px rgba(0, 101, 255, 0.1);

            .label {
              color: @primary-color;
            }
          }

          &:active:not(.is-selected) {
            background: #e2f5ff;
          }
        }
      }

      .chat-editor {
        width: 900px;
        height: 160px;
        padding: 14px;
        border-radius: 16px;
        background: #fff;
        box-sizing: border-box;
        border: 1px solid #e2e4e7;
        box-shadow:
          0px 12px 60px 2px rgba(2, 12, 29, 0.06),
          0px 6px 12px -2px rgba(2, 12, 29, 0.08);

        .placeholder-input {
          min-height: 86px;
          outline: none;
          border: 0;
          &:empty:before {
            content: attr(placeholder);
            color: #bbb;
          }
        }

        .handler {
          height: 32px;
          margin-top: 14px;

          .agent-chat-popover {
            .item {
              border-radius: 8px;
              width: 180px;
              height: 40px;
              padding: 0 16px;
              color: #132035;
              margin-bottom: 4px;
              justify-content: flex-start;
              &:hover {
                background: #f5f7f9;
                cursor: pointer;
              }
              &.selected {
                background: #eceef0;
              }
            }
          }

          .agent-chat-item {
            border: 1px solid #e2e4e7;
            padding: 0 16px;
            color: #132035;
            border-radius: 8px;
            margin-left: 10px;
            &:hover {
              background: #f5f7f9;
              cursor: pointer;
            }
            &.selected {
              background: #eceef0;
            }
          }

          .file-input {
            width: 32px;
            height: 32px;
            box-sizing: border-box;
            border-radius: 8px;
            border: 1px solid #eceef0;
            cursor: pointer;
            // margin-right: 10px;
            &:hover {
              background: #f5f7fa;
            }
          }
          .send-btn {
            cursor: pointer;
            width: 32px;
            height: 32px;
            background: #f1f4f7;
            border-radius: 50%;
            transition: color 0.2s ease;
          }
          .send-active {
            cursor: pointer;
          }

          .send-active:hover {
            color: #f5f7f9;
          }

          .send-inactive {
            cursor: not-allowed;
          }

          .send-inactive:hover {
            background-color: #f5f7f9;
          }
        }
      }

      .chat-editor-focus {
        border: 1px solid @primary-color;
      }
    }
  }
  .chat-page {
    display: flex;
    flex: 1;
    height: calc(100vh - 14px);
    width: 100%;
    .chat-detail-content {
      display: flex;
      flex: 1;
      min-width: 0;
      height: 100%;
      position: relative;
      .chat-detail-main {
        flex: 1;
        min-width: 0;
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: auto;
        position: relative;
        box-sizing: border-box;
        scrollbar-gutter: stable;

        .chat-content-container {
          box-sizing: border-box;
          width: 100%;
          flex: 1;
          padding-bottom: 20px;
          .chat-content-list {
            margin: 0 auto auto;
            width: 100%;
            display: flex;
            flex-direction: column;
            box-sizing: border-box;
            max-width: 900px;

            .message {
              .message-content {
                padding: 14px;
                border-radius: 16px 2px 16px 16px;
                margin-top: 14px;
                &.ai {
                  background: #ecf3ff;
                  font-size: 16px;
                  line-height: 26px;
                  color: #132035;
                }
                &.user {
                  font-size: 16px;
                  background: #f2f7fa;
                }
                :deep(.vue-markdown) * {
                  font-size: 16px !important;
                }
                .thinking {
                  background: #ecf3ff;
                }

                .reference-container {
                  .reference-title {
                    margin: 20px 0;
                  }
                }

                .toolbar {
                  margin-top: 14px;
                  .item {
                    cursor: pointer;
                  }
                  .item:not(:first-child) {
                    margin-left: 10px;
                  }
                  .item:hover {
                    background: #fafafa;
                  }
                }
              }
            }

            .user {
              justify-content: flex-end;
            }

            .ai {
              justify-content: flex-start;
            }

            .recommend-list {
              margin-top: 14px;
              flex-direction: column;
              align-items: flex-start;
              width: 100%;
              .recommend-item {
                margin-bottom: 14px;
                height: 36px;
                line-height: 36px;
                width: fit-content;
                padding: 0 14px;
                border-radius: 100px;
                background: #ecf3ff;
                cursor: pointer;
                span {
                  margin: 0 4px;
                }

                &:hover {
                  background-color: #effbff;
                }
              }
            }
          }
        }

        .chat-action {
          background: #fff;
          width: 100%;
          display: flex;
          flex-shrink: 0;
          align-items: center;
          justify-content: center;
          flex-direction: column;
          box-sizing: border-box;
          z-index: 10;
          position: sticky;
          bottom: 36px;
          left: 0;
          .bottom-action-container {
            position: absolute;
            width: 100%;
            top: 0;
            max-width: 900px;

            .to-bottom {
              width: 44px;
              min-height: 44px;
              border-radius: 50%;
              background-color: #fff;
              z-index: 9;
              position: absolute;
              bottom: 24px;
              cursor: pointer;
              right: calc(50% - 22px);
              transform: translate(-50%);
              box-shadow: 0 4px 10px rgba(0, 0, 0, 0.25);
              transition: opacity 0.3s ease-in-out;
            }
          }
          .input-container {
            position: relative;
            top: -10px;
            height: 120px;
            padding: 14px;
            border-radius: 16px;
            background: #ffffff;
            box-sizing: border-box;
            border: 1px solid #e2e4e7;
            box-shadow:
              0px 12px 60px 2px rgba(2, 12, 29, 0.06),
              0px 6px 12px -2px rgba(2, 12, 29, 0.08);

            .placeholder-input {
              min-height: 46px;
              outline: none;
              border: 0;
              &:empty:before {
                content: attr(placeholder);
                color: #bbb;
              }
            }

            .handler {
              height: 32px;
              margin-top: 14px;

              .agent-chat-popover {
                .item {
                  border-radius: 8px;
                  width: 180px;
                  height: 40px;
                  padding: 0 16px;
                  color: #132035;
                  margin-bottom: 4px;
                  justify-content: flex-start;
                  &:hover {
                    background: #f5f7f9;
                    cursor: pointer;
                  }
                  &.selected {
                    background: #eceef0;
                  }
                }
              }

              .agent-chat-item {
                border: 1px solid #e2e4e7;
                padding: 0 16px;
                color: #132035;
                border-radius: 8px;
                margin-left: 10px;
                &:hover {
                  background: #f5f7f9;
                  cursor: pointer;
                }
                &.selected {
                  background: #eceef0;
                }
              }
              .file-input {
                width: 32px;
                height: 32px;
                box-sizing: border-box;
                border-radius: 8px;
                border: 1px solid #eceef0;
                cursor: pointer;
                margin-right: 10px;
                &:hover {
                  background: #f5f7fa;
                }
              }
              .send-btn {
                cursor: pointer;
                width: 32px;
                height: 32px;
                background: #f1f4f7;
                border-radius: 50%;
                transition: color 0.2s ease;
              }
              .send-active {
                cursor: pointer;
              }

              .send-active:hover {
                color: #f5f7f9;
              }

              .send-inactive {
                cursor: not-allowed;
              }

              .send-inactive:hover {
                background-color: #f5f7f9;
              }
            }
          }
          &-bottom {
            height: 20px;
          }
        }
      }
    }
  }
}

// 小屏适配
@media (max-width: 900px) {
  .layout-container .home-page {
    padding: 0 16px;
  }

  .home-top {
    font-size: 32px !important;
    height: auto !important;
    margin-bottom: 30px !important;
    padding: 20px 0;
  }

  .quick-options {
    flex-wrap: wrap !important;
    justify-content: center !important;
    gap: 10px !important;

    .option-item {
      margin: 0 !important;
      flex: 1 1 auto !important;
      min-width: 120px !important;
    }
  }

  .chat-editor {
    width: 100% !important;
    height: auto !important;
    min-height: 120px !important;
  }

  .chat-page .chat-detail-content {
    padding: 0 16px !important;
  }

  .chat-action .input-container {
    width: 100% !important;
    padding: 0 16px !important;
  }
}

@media (max-width: 1290px) {
  .chat-detail-content {
    // 小屏模式下仅侧边栏展开时应用transform
    &.sidebar-only-expand {
      transform: translateX(160px) !important;
    }

    // 双条件或其他情况时移除transform
    &:not(.sidebar-only-expand) {
      transform: none !important;
    }

    &.pdf-visible-and-sidebar-expand {
      width: calc(100% - 300px - 45%) !important;
      margin-left: 300px;
    }
  }

  .chat-action {
    padding: 0 80px !important;
  }

  .input-container:not(.pdf-visible) {
    width: 900px !important;
    max-width: 100% !important;
  }

  // 当pdf可见时保持现有宽度
  .input-container.pdf-visible {
    width: 740px !important;
  }
}

:deep(.agent-chat-popover .ant-popover-inner-content) {
  padding: 4px;
}
</style>
