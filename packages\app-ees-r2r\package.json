{"name": "@futurefab/ui-app-ees-r2r", "version": "1.0.0", "description": "app-ees-r2r TODO", "private": true, "type": "module", "main": "dist/index.js", "module": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"dev": "vite serve", "build": "vite build", "test:unit": "vitest", "test-only": "vitest --run", "prebuild-only": "rimraf tsconfig.tsbuildinfo", "build-only": "SKIP_CHECK=true npm run build", "type-check": "vue-tsc --noEmit --composite false", "prepublishOnly": "run-p test-only build-only", "build:watch": "run-p type-check & vite build --watch", "clean": "<PERSON><PERSON><PERSON> dist", "check-without-dts": "vue-tsc --noEmit --composite false"}, "devDependencies": {"@futurefab/ui-sdk-api": "workspace:*", "@futurefab/ui-sdk-ees-basic": "workspace:*", "@futurefab/ui-sdk-ees-charts": "workspace:*", "@futurefab/ui-sdk-stores": "workspace:*", "@futurefab/ui-sdk-ees-workflow": "workspace:*", "js-md5": "^0.8.3"}, "dependencies": {"xlsx": "^0.18.5"}, "files": ["dist"], "publishConfig": {"registry": "https://npm.dev.futurefab.cn/"}, "repository": {"type": "git", "url": "git+ssh://********************:30022/group-intelligence/advanced-ees/advanced-ees-frontend.git", "directory": "packages/app-ees-r2r"}, "futurefabUIMeta": {"type": "app", "stack": "vue", "externals": ["@futurefab/ant-design-vue", "@futurefab/echarts", "@futurefab/echarts/charts", "@futurefab/echarts/components", "@futurefab/echarts/core", "@futurefab/echarts/features", "@futurefab/echarts/renderers", "@futurefab/icons-vue", "@futurefab/ui-framework", "@futurefab/ui-sdk-api", "@futurefab/ui-sdk-ees-basic", "@futurefab/ui-sdk-ees-charts", "@futurefab/ui-sdk-ees-workflow", "@futurefab/ui-sdk-stores", "@futurefab/vxe-table", "@kangc/v-md-editor/lib/preview", "@kangc/v-md-editor/lib/style/preview.css", "@kangc/v-md-editor/lib/theme/style/vuepress.css", "@kangc/v-md-editor/lib/theme/vuepress.js", "@xchart/marker", "dayjs", "lodash-es", "sortablejs", "splitpanes", "vue", "vue-router", "xe-utils", "xlsx"]}}