<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick, shallowRef, watch } from 'vue';
import { setHeaderSelectFilter, showWarning } from '@futurefab/ui-sdk-ees-basic';
import { cloneDeep } from 'lodash-es';
import { DATA_GROUP } from '@/constant/charts';
import { t } from '@futurefab/vxe-table';

const props = withDefaults(
  defineProps<{
    id: string;
    data: any[];
    options: object;
    columnFieldList: string[];
    type?: string;
    runType?: string;
    groupStore?: any;
    clickRowToSelect?: boolean;
  }>(),
  {
    id: () => 'common-table-id',
    data: () => [],
    options: () => ({}),
    columnFieldList: () => [],
    type: 'checkbox',
    runType: 'CSV',
    groupStore: {},
    clickRowToSelect: true
  }
);
const emits = defineEmits(['specialHandle', 'clickGrouping']);
const checkData = defineModel<any[]>('checkData');

const gridRef = ref();

// Grid operations
const updateHeaderFilters = () => {
  setHeaderSelectFilter({
    xGrid: gridRef,
    tableData: gridRef.value.getTableData()?.fullData,
    columnFieldList: props.columnFieldList
  });
};

const refreshFilters = () => {
  setTimeout(() => updateHeaderFilters());
};

const updateCheckData = (row?: any, type?: string) => {
  if (row && type === 'radio') {
    checkData.value = [row];
    emits?.('specialHandle', row);
    return;
  }
  // 点击行触发的事件
  if (props.type === 'radio') {
    const selectedRow = cloneDeep(gridRef.value.getRadioRecord());
    checkData.value = selectedRow ? [selectedRow] : [];
    emits?.('specialHandle', selectedRow);
  } else {
    checkData.value = gridRef.value.getCheckboxRecords();
  }
};
const cellContextMenuEvent = (data: any) => {
  // console.log('context menu', data);
};
const contextMenuClickEvent = (data: any) => {
  // console.log('xxxxx menu click', data);
  // gridRef.value?.getCellAreas()
  const { menu, rows } = data;
  const selectRows = rows.filter(
    (item: any) => item !== undefined && item.existsTraceRow !== false
  );
  if (selectRows.length === 0) return;
  emits('clickGrouping', menu.code, selectRows);
};
const gridEvents = {
  checkboxAll: () => updateCheckData(),
  checkboxChange: () => updateCheckData(),
  radioChange: () => updateCheckData()
};

const onFilterChange = () => {
  const visibleData = gridRef.value.getTableData().tableData;
  gridRef.value.toggleAllCheckboxRow(visibleData);
  updateCheckData();
};

const getRowClassName = (rowInfo: any) => {
  if (props.runType === 'HIS' && !rowInfo.row?.existsTraceRow) {
    return 'disabled-row';
  }
  return '';
};

const isRowSelectable = (rowInfo: any) => {
  if (props.runType === 'HIS' && rowInfo.row?.existsTraceRow === false) {
    return false;
  }
  return true;
};

const setCheckRow = (row: any, type: string) => {
  if (type === 'radio') {
    gridRef.value.setRadioRow(row);
  } else {
    gridRef.value.toggleCheckboxRow(row);
  }
  updateCheckData(row, type);
};

const groupByMap = (map: Map<string, any[]>) => {
  const temp: any[] = [];
  map.forEach((data: any[], group: string) => {
    props.groupStore.handleAdd(group, data);
    temp.push(...data);
  });
  gridRef.value.remove(temp);
};

const handleAutoGroup = (column: any) => {
  const { visibleData } = gridRef.value.getTableData();
  const enabledRows = visibleData.filter((item: any) => item.existsTraceRow !== false);

  if (props.groupStore.checkData.size) {
    return showWarning(t('cm.tips.groupRepetition'));
  }

  if (enabledRows.length) {
    const uniqueValues = [...new Set(enabledRows.map((item: any) => item[column.field]))].slice(
      0,
      26
    );
    const groupMap = new Map();

    uniqueValues.forEach((value: any, index) => {
      groupMap.set(
        DATA_GROUP[index].value,
        enabledRows.filter((item: any) => item[column.field] === value)
      );
    });

    groupByMap(groupMap);

    nextTick(() => refreshFilters());
  }
};

const setupEventListeners = () => {
  nextTick(() => {
    // 控制右键菜单内的内容可以滚动
    window.addEventListener('wheel', function (event) {
      event.stopPropagation(); // 阻止事件冒泡至父级元素
    });
  });
};

const cleanupEventListeners = () => {
  window.removeEventListener('wheel', function (event) {
    event.stopPropagation();
  });
};

// Exposed methods
const getData = () => gridRef.value.getTableData();
const addTableData = (data: any[]) => {
  gridRef.value.loadData(data);
  // refreshFilters();
};
const deleteTableData = (deleteRows?: any[]) => {
  if (deleteRows && deleteRows.length > 0) {
    gridRef.value.remove(deleteRows);
  } else {
    gridRef.value.removeCheckboxRow();
  }
  // refreshFilters();
};

defineExpose({ getData, addTableData, deleteTableData, setCheckRow, groupByMap, refreshFilters });

// Lifecycle
watch(
  () => props.data,
  () => {
    nextTick(() => {
      if (gridRef.value) refreshFilters();
    });
  },
  { immediate: true }
);

onMounted(() => setupEventListeners());
onUnmounted(() => cleanupEventListeners());
</script>

<template>
  <div class="common-table-wrapper">
    <vxe-grid
      :id="props.id"
      ref="gridRef"
      class="modal-table-small-radius"
      height="auto"
      v-bind="options"
      :data="data"
      :row-class-name="getRowClassName"
      :radio-config="{
        checkMethod: isRowSelectable,
        trigger: clickRowToSelect ? 'row' : 'cell',
        // 允许单选取消
        strict: false
      }"
      :checkbox-config="{
        checkMethod: isRowSelectable,
        range: true,
        trigger: clickRowToSelect ? 'row' : 'cell'
      }"
      v-on="gridEvents"
      @filter-change="onFilterChange"
      @cell-menu="cellContextMenuEvent"
      @menu-click="contextMenuClickEvent"
    >
      <template #autoGroupHearder="{ column }">
        <div class="column-title">{{ column.title }}</div>
        <vxe-tooltip :content="$t('cm.label.autoGroup')" :use-h-t-m-l="true" theme="light">
          <i
            class="iconfont icon-eq-automatic-grouping cm-head-group-icon"
            @click="handleAutoGroup(column)"
          ></i>
        </vxe-tooltip>
      </template>
    </vxe-grid>
  </div>
</template>

<style lang="less" scoped>
@import url('@/assets/style/variable.less');
.common-table-wrapper {
  height: 100%;
  width: 100%;

  :deep(.disabled-row) {
    background-color: @check-default-br-color !important;
  }

  :deep(.vxe-cell--title) {
    display: flex;
    flex: 1;
    height: 100%;
    flex-direction: row;
    align-items: center;
    .column-title {
      display: flex;
      flex: 1;
    }
    .vxe-cell--checkbox {
      height: 16px;
      margin-left: 25%;
    }
  }
  :deep(.vxe-table .vxe-header--icon-wrapper) {
    margin-left: 0;
  }
}
.cm-head-group-icon {
  font-size: 14px;
  color: @bg-icon-active-color;
  margin: 0 4px 0 2px;
  width: 16px;
  height: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
  &:hover {
    cursor: pointer;
    color: @bg-icon-active-color;
    background-color: @bg-hover-2-color;
  }
}
</style>
