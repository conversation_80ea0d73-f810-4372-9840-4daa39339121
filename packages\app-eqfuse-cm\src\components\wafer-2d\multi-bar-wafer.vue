<script setup lang="ts">
import { watch, ref } from 'vue';
import { type Measuredata } from '@/utils/spline-interpolate';
import Chart from '../chart.vue';
import { calcGroup, getToolbox } from './index';
import { colorList, formatterY } from '@futurefab/ui-sdk-ees-charts';
const props = defineProps<{
    data: Measuredata[];
    valueKey: string;
    groupNumber: number;
}>();
const options = ref<any>(null);
const getMultiBar = (data: Measuredata[], valueKey: string, groupNumber: number) => {
    const values: number[] = [];
    data.forEach(measure => {
        const vIndex = measure.header.findIndex(item => item === valueKey);
        values.push(...measure.data.map(item => Number(item[vIndex])));
    });

    const maxV = Math.max(...values);
    const minV = Math.min(...values);
    const gap = (maxV - minV) / groupNumber;

    // 按区间分组的对象
    const groupRes = new Array(groupNumber).fill(null).map((_: null, index: number) => ({
        start: minV + gap * index,
        end: index === groupNumber - 1 ? maxV : minV + gap * (index + 1),
        count: 0,
    }));

    values.forEach((value: number) => {
        const groupI = calcGroup(value, maxV, minV, gap, groupNumber);
        groupRes[groupI].count++;
    });
    return {
        color: colorList,
        grid: {
            left: 80,
            top: 30,
            right: 20,
            bottom: 30,
        },
        toolbox: getToolbox(),
        xAxis: {
            show: true,
            type: 'category',
            data: new Array(groupNumber).fill(null).map((item, index) => index + 1),
            splitArea: {
                show: true,
            },
        },
        yAxis: {
            show: true,
            splitArea: {
                show: false,
            },
        },
        tooltip: {
            formatter: (event: any) => {
                if (event?.data?.info) {
                    const info = event.data.info;
                    return [
                        `Range: ${formatterY(info.start)} ~ ${formatterY(info.end)}`,
                        `Count: ${info.count}`,
                    ].join('<br/>');
                }
            },
            appendToBody: true, 
        },
        series: [
            {
                name: 'bar',
                type: 'bar',
                barWidth: '99%',
                itemStyle: { borderWidth: 2 },
                data: groupRes.map(item => ({
                    value: item.count,
                    info: item,
                })),

            },
        ],
    };
};

watch(
    [() => props.data, () => props.valueKey, () => props.groupNumber],
    v => {
        if (v) {
            options.value = getMultiBar(props.data, props.valueKey, props.groupNumber);
        }
    },
    { immediate: true },
);
</script>
<template>
    <div class="common-chart-container">
        <Chart :options="options" />
    </div>
</template>
<style scoped lang="less">
.common-chart-container {
    width: 100%;
    height: 100%;
}
</style>
