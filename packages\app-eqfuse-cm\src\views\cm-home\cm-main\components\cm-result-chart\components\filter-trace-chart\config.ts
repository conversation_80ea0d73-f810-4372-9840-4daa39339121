import { VXETable } from '@futurefab/vxe-table';

export const getFilterFieldList = () => {
    return ['key', 'lotId', 'wafer', 'startTime', 'endTime', 'processDuration', 'reference'];
};
export const getFilterTraceTableOptions = () => {
    const options = VXETable.tableFun.tableDefaultConfig({
        columns: [
            {
                type: 'checkbox',
            },
            {
                field: 'key',
                title: 'cm.field.dataSet',
                minWidth: 120,
                sortable: true,
                filters: [],
                filterRender: {},
            },
            {
                field: 'lotId',
                title: 'cm.field.lot',
                minWidth: 120,
                sortable: true,
                filters: [],
                filterRender: {},
            },
            {
                field: 'wafer',
                title: 'cm.field.wafer',
                minWidth: 120,
                sortable: true,
                filters: [],
                filterRender: {},
            },
            {
                field: 'startTime',
                title: 'cm.field.startTime',
                minWidth: 120,
                sortable: true,
                filters: [],
                filterRender: {},
            },
            {
                field: 'endTime',
                title: 'cm.field.endTime',
                minWidth: 120,
                sortable: true,
                filters: [],
                filterRender: {},
            },
            {
                field: 'processDuration',
                title: 'cm.field.processDuration',
                minWidth: 120,
                sortable: true,
                filters: [],
                filterRender: {},
            },
            {
                field: 'reference',
                title: 'cm.field.reference',
                minWidth: 120,
                sortable: true,
                filters: [],
                filterRender: {},
            },
        ],
    });
    options.columns.shift();
    return options;
};
