import BigNumber from 'bignumber.js';

// 加
export const add = (a: number, b: number) => {
    return Number(new BigNumber(a).plus(b).toString());
};

// 减
export const subtract = (a: number, b: number) => {
    return Number(new BigNumber(a).minus(b).toString());
};

// 乘
export const multiply = (a: number, b: number) => {
    return Number(new BigNumber(a).times(b).toString());
};

// 除
export const divide = (a: number, b: number) => {
    return Number(new BigNumber(a).div(b).toString());
};

// 计算小数位数
export const countDecimalPlaces = (num: number) => {
    const str = num.toString();
    const match = str.match(/\.(\d+)/);
    return match ? match[1].length : 0;
};

/**
 * 使用最小二乘法求解线性回归方程 y = kx + b 中的参数k和b
 * @param points 样本点数组，每个元素是[x, y]形式的数组
 * @returns 包含k和b的对象，如果样本不足或计算失败则返回null
 */
export function leastSquares(points: [number, number][]): { k: number; b: number | null } | null {
    // 验证输入
    if (!points || points.length < 2) {
        console.error("至少需要2个样本点才能进行线性回归计算");
        return null;
    }

    const n = points.length;
    let sumX = 0;    // 所有x的总和
    let sumY = 0;    // 所有y的总和
    let sumXY = 0;   // 所有x*y的总和
    let sumX2 = 0;   // 所有x²的总和

    // 计算各项总和
    for (const [x, y] of points) {
        sumX += x;
        sumY += y;
        sumXY += x * y;
        sumX2 += x * x;
    }

    // 计算分母，避免除以零
    const denominator = n * sumX2 - sumX * sumX;
    if (denominator === 0) {
        return { k: Infinity,  b: null};
    }

    // 计算斜率k和截距b
    const k = (n * sumXY - sumX * sumY) / denominator;
    const b = (sumY - k * sumX) / n;

    return { k, b };
}