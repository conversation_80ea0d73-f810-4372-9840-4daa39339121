import type { RouteRecordRaw } from 'vue-router';

export const routes: Array<RouteRecordRaw> = [
  {
    path: 'chamber-matching',
    name: 'chamber-matching',
    component: () => import('@/views/cm-home/chamber-matching/chamber-matching.vue')
  },
  {
    path: 'cm-main',
    name: 'cm-main',
    meta: {
      keepAlive: true
    },
    component: () => import('@/views/cm-home/cm-main/cm-main.vue')
  },
  {
    path: 'cm-screenshot',
    name: 'cm-screenshot',
    meta: {
      keepAlive: true
    },
    component: () => import('@/views/cm-screenshot/index.vue')
  },
  {
    path: 'cm-config',
    name: 'cm-config',
    component: () => import('@/views/cm-configuration/index.vue')
  },
  {
    path: 'hardware-config',
    name: 'hardware-config',
    component: () => import('@/views/hardware-configuration/index.vue')
  },
  {
    path: 'metrology-management',
    name: 'metrology-management',
    component: () => import('@/views/metrology-management/index.vue')
  },
  {
    path: 'wafer-moving-analysis',
    name: 'wafer-moving-analysis',
    component: () => import('@/views/wafer-moving-wrap/index.vue')
  },
  // rms
  {
    path: 'ec-station-parameter',
    name: 'ec-station-parameter',
    component: () => import('../views/ec-station-parameter/index.vue')
  }
];
export default routes;
