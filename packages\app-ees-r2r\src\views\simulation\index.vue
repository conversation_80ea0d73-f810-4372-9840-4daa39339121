<script lang="ts" setup>
import { onBeforeMount, provide, reactive, ref, watch } from 'vue';
import SettingTab from './setting/index.vue';
import ResultTab from './result/index.vue';
import { simulation as buttonIds } from '@/log-config';
import {
  getPermissionButton,
  EesLeftMenu,
  type EESMenuListType
} from '@futurefab/ui-sdk-ees-basic';
import { injectAuthKey } from './interface';
import type { BtnAuth } from '@/utils/permission';
import { useR2RSimulationStore } from '@futurefab/ui-sdk-stores';
const store = useR2RSimulationStore();

const curTab = ref('setting');
const tabList = ref([
  { name: 'r2rSimulation.title.setting', id: 'setting', disabled: false },
  { name: 'r2rSimulation.title.result', id: 'result', disabled: true }
]);

const btnAuth = ref({ allButtonFlag: false, buttonList: [] } as BtnAuth);
const settingTabDom = ref();
const params = reactive({
  showResult: false,
  actualData: [] as Record<string, string[]>[],
  area: '',
  modelGroup: ''
});
onBeforeMount(async () => {
  btnAuth.value = (await getPermissionButton('/r2r/simulation')) as BtnAuth;
  console.log(btnAuth.value, 'btnAuth');
});
function onClickMenu(menu: EESMenuListType) {
  if (menu.id == 'result') {
    const { actualData, isSimulated } = store.state;
    const { area, modelGroup } = store.state.templateJson;
    params.actualData = actualData;
    params.showResult = isSimulated;
    params.area = area;
    params.modelGroup = modelGroup;
  }
}
watch(
  () => store.state.isSimulated,
  (val) => {
    tabList.value[1].disabled = !val;
  },
  { immediate: true }
);
provide(injectAuthKey, { btnAuth });
</script>

<template>
  <div :id="buttonIds.pageId" class="r2r-simulation">
    <ees-left-menu v-model:active-key="curTab" :menu-list="tabList" @click-menu="onClickMenu">
      <template #right-content>
        <setting-tab v-show="curTab === 'setting'" ref="settingTabDom" />
        <result-tab
          v-show="curTab === 'result'"
          :actual-data="params.actualData"
          :area="params.area"
          :model-group="params.modelGroup"
        />
      </template>
    </ees-left-menu>
  </div>
</template>

<style scoped lang="less">
@import url('@/assets/style/variable.less');
.r2r-simulation {
  height: 100%;
}
</style>
