import { VXETable, type VxeGridProps, t } from '@futurefab/vxe-table';
import { stateManager as buttonIds } from '@/log-config/index';
import { boolFilters } from '@/utils/table-config';
import { tableDefaultConfig } from '@futurefab/ui-sdk-ees-basic';
export const xConfig = tableDefaultConfig(
  {
    id: 'stateManager',
    borderOutLine: [1, 0, 0, 0],
    exportConfig: { filename: 'ShareStateManager.xlsx', id: buttonIds.user.export },
    rowConfig: {
      isCurrent: true
    },
    toolbarConfig: {
      tableName: 'stateManager.title.stateManager',
      border: false,
      import: false,
      export: false,
      refresh: true,
      slots: {
        beforeTools: 'beforeTools',
        beforeButtonsList: 'search-condition'
      },
      tools: [
        ...VXETable.tableFun.getToolsButton([
          {
            name: 'common.btn.add',
            icon: 'icon-btn-add',
            id: buttonIds.user.add,
            visible: false
          },
          {
            name: 'common.btn.modify',
            icon: 'icon-btn-modify',
            id: buttonIds.user.modify,
            visible: false
          },
          {
            name: 'common.btn.deleteData',
            icon: 'icon-btn-delete',
            id: buttonIds.user.delete,
            visible: false
          },
          {
            name: 'common.btn.history',
            icon: 'icon-btn-history',
            id: buttonIds.user.history,
            visible: false
          }
        ])
      ]
    },
    columns: [
      {
        field: 'rawId',
        type: 'checkbox',
        width: 40,
        align: 'center'
      },
      {
        field: 'activityFunctionName',
        minWidth: 150,
        title: 'stateManager.field.function',
        align: 'left',
        sortable: true,
        filters: []
      },
      // {
      //     field: 'areaName',
      //     minWidth: 150,
      //     title: 'common.field.area',
      //     align: 'left',
      //     sortable: true,
      //     filters: [{ data: '' }],
      //     filterMethod,
      // },
      // {
      //     field: 'eqpModelName',
      //     minWidth: 150,
      //     title: 'common.field.eqpModel',
      //     align: 'left',
      //     sortable: true,
      //     filters: [{ data: '' }],
      //     filterMethod,
      // },
      {
        field: 'useYn',
        minWidth: 120,
        title: 'common.field.usedYn',
        align: 'left',
        type: 'columnCheckbox-default',
        sortable: true,
        filters: boolFilters
      },
      {
        field: 'activityClassName',
        minWidth: 150,
        title: 'stateManager.field.stateType',
        align: 'left',
        sortable: true,
        filters: []
      },
      {
        field: 'lastUpdateBy',
        minWidth: 150,
        title: 'common.field.lastUpdateBy',
        align: 'left',
        sortable: true,
        filters: []
      },
      {
        field: 'lastUpdateDtts',
        minWidth: 200,
        title: 'common.field.lastUpdateDtts',
        align: 'left',
        sortable: true,
        filters: [{ data: '' }],
        filterRender: {
          name: '$input'
        }
      },
      {
        field: 'createBy',
        minWidth: 150,
        title: 'common.field.createBy',
        align: 'left',
        sortable: true,
        filters: []
      },
      {
        field: 'createDtts',
        minWidth: 200,
        title: 'common.field.createDtts',
        align: 'left',
        sortable: true,
        filters: [{ data: '' }],
        filterRender: {
          name: '$input'
        }
      }
    ],
    checkboxConfig: {
      showHeader: true
    },
    columnCheckboxConfig: {
      checkMethod: () => false
    }
  } as VxeGridProps,
  true,
  [buttonIds.user.add, buttonIds.user.modify, buttonIds.user.delete, buttonIds.user.history]
);
export const kConfig = tableDefaultConfig({
  id: 'stateManagerDetail',
  borderOutLine: [1, 0, 0, 0],
  toolbarConfig: {
    tableName: 'stateManager.title.keys',
    import: false,
    export: false,
    refresh: false,
    border: false
  },
  columns: [
    {
      field: 'itemName',
      minWidth: 130,
      title: 'stateManager.field.itemName',
      align: 'left',
      sortable: true,
      filters: []
    },
    {
      field: 'displayName',
      minWidth: 200,
      title: 'common.field.displayName',
      filters: []
    },
    {
      field: 'description',
      minWidth: 150,
      title: 'common.field.description',
      align: 'left',
      sortable: true,
      filters: [{ data: '' }],
      filterRender: {
        name: '$input'
      }
    },
    {
      field: 'seq',
      minWidth: 80,
      title: 'common.field.seq',
      align: 'center',
      sortable: true,
      filters: [{ data: '' }],
      filterRender: {
        name: '$input'
      }
    }
  ]
} as VxeGridProps);
export const dConfig = tableDefaultConfig({
  id: 'stateManagerDetail',
  borderOutLine: [1, 0, 0, 0],
  toolbarConfig: {
    tableName: 'stateManager.title.items',
    import: false,
    export: false,
    refresh: false,
    border: false
  },
  columns: [
    // {
    //     field: 'type',
    //     minWidth: 80,
    //     title: 'common.field.item',
    //     align: 'left',
    //     sortable: true,
    //     filters: [{ data: '' }],
    // },
    {
      field: 'itemName',
      minWidth: 130,
      title: 'stateManager.field.itemName',
      align: 'left',
      sortable: true,
      filters: []
    },
    {
      field: 'displayName',
      minWidth: 200,
      title: 'common.field.displayName',
      filters: []
    },
    {
      field: 'description',
      minWidth: 150,
      title: 'common.field.description',
      align: 'left',
      sortable: true,
      filters: [{ data: '' }],
      filterRender: {
        name: '$input'
      }
    },
    {
      field: 'seq',
      minWidth: 80,
      title: 'common.field.seq',
      align: 'center',
      sortable: true,
      filters: [{ data: '' }],
      filterRender: {
        name: '$input'
      }
    }
  ]
} as VxeGridProps);
