import type { BtnAuth } from '@/utils/permission';
import type { VxeColumnProps } from '@futurefab/vxe-table';
import type { MenuInfo } from '@futurefab/ant-design-vue/es/menu/src/interface';
import type { InjectionKey, Ref } from 'vue';

export interface ASelectOptions {
  label: string;
  value: string;
}

export const injectKey: InjectionKey<{
  areaList: Ref<ASelectOptions[]>;
  area: Ref<string>;
}> = Symbol();

export const injectAuthKey: InjectionKey<{
  btnAuth: Ref<BtnAuth>;
}> = Symbol();

export interface MenuClickType {
  menu: MenuInfo;
  dataSourceType: string;
  extraParams: any;
}

export interface RightTable {
  rawId: number | null;
  columns: VxeColumnProps[];
  dataList: any[];
}

export interface LeftSettingForm {
  area: string;
  modelGroup: string;
  intervalTime: number;
  methodList: any[];
  selectedMethod: any[];
}
