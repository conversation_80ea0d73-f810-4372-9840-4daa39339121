import { VXETable, type VxeGridProps, t } from '@futurefab/vxe-table';
import { stateViewer as buttonIds } from '@/log-config/index';
import { tableDefaultConfig } from '@futurefab/ui-sdk-ees-basic';

export const xGetConfig = (type: any) => {
  console.log('StateResetHistory', type);
  return tableDefaultConfig({
    // class: 'scroll-hover-table border-full-not-last-cell-table',
    id: 'StateResetHistory',
    // borderOutLine: [0, 1, 0, 1], // 在.vue文件设置
    // border: true,
    toolbarConfig: {
      tableName: 'common.title.history',
      import: false,
      export: false,
      refresh: false,
      border: true,
      slots: { beforeButtonsList: 'beforeButtonsList' },
      // border: false,
      // custom: false,
      // zoom: false,
      tools: [
        ...VXETable.tableFun.getToolsButton([
          {
            name: 'common.btn.exportData',
            icon: 'icon-btn-export',
            id: buttonIds[type].export,
            visible: true
            // visible: false,
          }
        ])
      ]
    },
    columns: [
      /* {
            field: 'createDtts',
            minWidth: 150,
            title: 'common.field.createDtts',
            align: 'left',
            fixed: 'left',
            sortable: true,
            filters: [{ data: '' }],
            filterRender: {
                name: '$input',
            },
        },
        {
            field: 'stateTypeCd',
            minWidth: 150,
            title: 'stateViewer.field.stateType',
            align: 'left',
            sortable: true,
            filters: [{ data: '' }],
            filterRender: {
                name: '$input',
            },
        },
        {
            field: 'resetType',
            minWidth: 150,
            title: 'stateViewer.field.resetType',
            align: 'left',
            sortable: true,
            filters: [{ data: '' }],
            filterRender: {
                name: '$input',
            },
        },
        {
            field: 'createBy',
            minWidth: 150,
            title: 'common.field.createBy',
            align: 'left',
            sortable: true,
            filters: [{ data: '' }],
            filterRender: {
                name: '$input',
            },
        }, */
    ],
    checkboxConfig: {
      showHeader: true
    },
    columnCheckboxConfig: {
      checkMethod: () => false
    }
  } as VxeGridProps);
};
