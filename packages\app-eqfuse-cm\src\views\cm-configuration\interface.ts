export type TrimType = 'COUNT' | 'TIME' | 'PERCENT';

export interface TrimRuleOption {
  trimBy: TrimType;
  start: {
    count: number | null;
    time: number | null;
    percent: number | null;
  };
  end: {
    count: number | null;
    time: number | null;
    percent: number | null;
  };
}

export interface TrimOriginData {
  rawId: number | null;
  trimRuleOption: TrimRuleOption;
  trimRuleContext: Record<string, string>;
}
export interface TrimRuleData {
  rawId: number | null;
  trimRuleOption: TrimRuleOption;
  trimming?: string;
  [key: string]: any;
}

type LabelObjects<T extends string> = {
  [K in T]: { label: string; value: K };
}[T];

export type TypeTrimOption = LabelObjects<TrimType>[];

export interface TrimForm {
  startCount: null | number;
  startTime: null | number;
  startPercent: null | number;
  endCount: null | number;
  endTime: null | number;
  endPercent: null | number;
}

export type TrimFormMap = {
  [K in keyof TrimForm]: (type: TrimType) => {
    label: string;
    key: K;
    max?: number;
    precision?: number;
    fristK: string;
    secondK: string;
    unit?: string;
  };
};

export type TrimFormArr = {
  label: string;
  key: keyof TrimForm;
  max?: number;
  precision?: number;
  unit?: string;
}[];

export interface StationItem {
  // eqpId: string;
  // chamberStr: string;
  paramName: string;
  paramAlias?: string;
  createBy?: string;
  createDtts?: string;
  stationName?: string;
  [k: string]: any;
}
export interface ScreenshotRowItem {
  paramName: string;
  items: Array<{
    sort: number;
    imgType: string;
    base64ImgData: string;
    step: string | number;
  }>;
  traceChartImg: string;
}

export type SSEMsgType = 'Heartbeat' | 'Data' | 'Progress' | 'Error';

export interface ExportProgress {
  time: string | Date;
  log: string;
  status: 'DOING' | 'DONE' | 'ERROR';
  data: {
    screentshotTotalCount: number;
    currentScreentshotIndex: number;
  };
}
