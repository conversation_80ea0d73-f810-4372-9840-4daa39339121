export interface ExportFormat {
  value: string;
  label: string;
}

export interface CMAnalysisOption {
  value: string;
  label: string;
}

export interface LayoutOptionItem {
  value: string;
  label: string;
}

export interface LayoutOption {
  key: 'traceChart' | 'boxplotChart' | 'summaryChart' | 'metrologyChart';
  label: string;
  options: LayoutOptionItem[];
}

export interface ParameterItem {
  [x: string]: any;
  id: string | number;
  parameterName?: string;
  name?: string;
  children?: ParameterItem[];
  treeKey: string;
}

export interface CheckedItems {
  [groupId: string]: {
    [childId: string]: boolean;
  };
}

export interface ExportFilter {
  format: string[];
  filterParameter: boolean;
  useCMAnalysisResult: boolean;
  cmAnalysisResultList: string[];
  layoutSetting: {
    traceChart: string;
    boxplotChart: string;
    summaryChart: string;
    metrologyChart: string;
  };
}

export interface FilterParameter {
  checkAll: boolean;
  checkedItems: CheckedItems;
}

export interface ExportFormData {
  exportFilter: ExportFilter;
  filterParameter: FilterParameter;
}

export interface SearchParam {
  category: (string | number)[];
  inputValue: string;
}
