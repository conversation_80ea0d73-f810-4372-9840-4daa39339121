import {
  VXETable,
  t,
  VxeTableDefines,
  type VxeTableConstructor,
  type VxeTablePrivateMethods,
  type VxeGridConstructor
} from '@futurefab/vxe-table';
import { tableDefaultConfig } from '@futurefab/ui-sdk-ees-basic';
const h = '250px';
const toolbarConfig = {
  import: false,
  export: false,
  custom: false,
  refresh: false
};
export const options = tableDefaultConfig({
  columns: [
    {
      field: 'code',
      type: 'checkbox',
      headerClassName: 'table-show-checkbox-all-title',
      width: '40px',
      align: 'center'
    },
    {
      field: 'itemName',
      title: 'common.field.item',
      minWidth: '200px'
    },
    {
      field: 'code',
      title: 'common.field.details',
      minWidth: '100px',
      slots: {
        default: 'groupBtn'
      }
    }
  ],
  rowConfig: {
    keyField: 'code'
  },
  toolbarConfig: {
    // tableName: t('eesCharts.SeriesGF'),
    ...toolbarConfig
  },
  checkboxConfig: {
    showHeader: false,
    checkMethod: ({ row }: { row: any }) => {
      return row.filterList.length > 0;
    }
  },
  maxHeight: h
});

export const showOptions: any = {
  border: true,
  stripe: false,
  resizable: true,
  showHeaderOverflow: true,
  showOverflow: true,
  highlightHoverRow: true,
  keepSource: true,
  borderOutLine: [1, 0, 0, 1],
  size: 'small',
  columns: [
    {
      title: 'No.',
      type: 'seq',
      width: '48px',
      align: 'center',
      fixed: 'left'
    },
    {
      field: 'group',
      title: 'summary.colHeader.group',
      width: '80px',
      editRender: {
        name: '$select',
        options: [],
        optionProps: { value: 'value', label: 'label' }
      }
    },
    {
      field: 'name',
      sortable: true,
      minWidth: '200px'
      // filters: [{ data: '' }],
      // filterRender: {
      //     name: '$input'
      // }
    },
    {
      field: 'code',
      title: 'summary.colHeader.filter',
      type: 'columnCheckbox-default',
      minWidth: '100px',
      align: 'center'
    }
  ],
  rowConfig: {
    height: 34
  },
  toolbarConfig: {
    className: 'fdc_chart_group_show',
    ...toolbarConfig,
    border: false
  },
  maxHeight: h,
  // rowConfig: {
  //     keyField: 'code',
  // },
  checkboxConfig: {
    showHeader: false
  },
  editConfig: {
    trigger: 'dblclick',
    mode: 'cell'
    // showStatus: false,
    // showStatusBorder: true,
  },
  mouseConfig: {
    selected: true,
    area: true
  },
  clipConfig: {
    isCopy: true,
    isPaste: true,
    isCutBorder: true,
    isSameColumnPaste: false
  },
  keyboardConfig: {
    isArrow: true,
    isDel: true,
    isEnter: true,
    isTab: true,
    isEdit: true,
    isChecked: true,
    editMethod: ({
      row,
      column,
      $table
    }: {
      row: any;
      rowIndex: number;
      column: any;
      columnIndex: number;
      $table: any;
      $grid: any;
    }) => {
      // // 重写默认的覆盖式，改为追加式
      $table.setActiveCell(row, column);
    }
  }
};
