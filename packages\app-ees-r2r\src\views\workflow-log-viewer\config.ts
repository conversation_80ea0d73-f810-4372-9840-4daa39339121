import { VXETable } from '@futurefab/vxe-table';
import { workflowLogViewer as buttonIds } from '@/log-config/index';
import { tableDefaultConfig } from '@futurefab/ui-sdk-ees-basic';
export default tableDefaultConfig(
  {
    id: 'log-viewer-table',
    borderOutLine: [1, 0, 0, 0],
    columns: [
      {
        field: 'area',
        title: 'common.field.area',
        sortable: true,
        minWidth: '150px',
        filters: [{ data: '' }]
      },
      {
        field: 'eqpId',
        title: 'r2rSimulation.field.eqpId',
        sortable: true,
        minWidth: '150px',
        filters: [{ data: '' }]
      },
      {
        field: 'lotId',
        title: 'r2rSimulation.field.lotId',
        sortable: true,
        minWidth: '130px',
        filters: [{ data: '' }]
      },
      {
        field: 'substrateId',
        title: 'workflowLogViewer.field.substrateId',
        sortable: true,
        minWidth: '150px',
        filters: [{ data: '' }]
      },
      {
        field: 'recipeId',
        title: 'r2rSimulation.field.recipeId',
        sortable: true,
        minWidth: '130px',
        filters: [{ data: '' }]
      },
      {
        field: 'methodName',
        title: 'executionSetting.field.method',
        sortable: true,
        minWidth: '150px',
        filters: [{ data: '' }]
      },
      {
        field: 'modelGroupWorkflow',
        title: 'workflowLogViewer.field.modelGroup',
        sortable: true,
        minWidth: '300px',
        filters: [{ data: '' }]
      },
      {
        field: 'modelWorkflow',
        title: 'workflowLogViewer.field.model',
        sortable: true,
        minWidth: '250px',
        filters: [{ data: '' }]
      },
      {
        field: 'message',
        title: 'workflowLogViewer.field.message',
        sortable: true,
        minWidth: '150px',
        filters: [{ data: '' }],
        filterRender: {
          name: '$input'
        }
      },
      {
        field: 'eventDtts',
        title: 'workflowLogViewer.field.dtts',
        sortable: true,
        minWidth: '200px',
        filters: [{ data: '' }],
        filterRender: {
          name: '$input'
        }
      },
      {
        field: 'errorYn',
        title: 'workflowLogViewer.field.errorYn',
        type: 'columnCheckbox-default',
        sortable: true,
        minWidth: '150px',
        filters: [{ data: '' }]
      },
      {
        field: 'tid',
        title: 'workflowLogViewer.field.tId',
        sortable: true,
        minWidth: '130px',
        filters: [{ data: '' }],
        filterRender: {
          name: '$input'
        }
      },
      {
        field: 'fid',
        title: 'workflowLogViewer.field.fId',
        sortable: true,
        minWidth: '130px',
        filters: [{ data: '' }]
      },
      {
        field: 'mid',
        title: 'workflowLogViewer.field.mId',
        sortable: true,
        minWidth: '130px',
        filters: [{ data: '' }]
      }
    ],
    exportConfig: {
      id: buttonIds.export,
      filename: 'WorkflowLogViewer.xlsx'
    },
    columnCheckboxConfig: {
      checkMethod: () => {
        return false;
      }
    },
    rowClassName: ({ row }: { row: { errorYn: boolean } }) => {
      if (row.errorYn) {
        return 'error-red-row';
      }
    },
    rowConfig: {
      isCurrent: true
    },
    toolbarConfig: {
      border: false,
      tableName: 'workflowLogViewer.title.logViewer',
      refresh: true,
      import: false,
      export: false,
      slots: {
        beforeTools: 'help',
        beforeButtonsList: 'search-condition'
      },
      tools: [
        ...VXETable.tableFun.getToolsButton([
          {
            id: buttonIds.executeFlow,
            name: 'workflowLogViewer.btn.executeFlow',
            icon: 'icon-btn-flow-execute',
            isAuthority: true,
            visible: false
          },
          {
            id: buttonIds.backendExport,
            code: 'backendExport',
            name: 'common.btn.export',
            icon: 'icon-btn-export',
            isAuthority: true,
            visible: false
          }
        ])
      ]
    }
  },
  true,
  [buttonIds.executeFlow]
);
