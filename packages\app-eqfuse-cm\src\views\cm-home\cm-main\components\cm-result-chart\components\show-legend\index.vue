<script setup lang="ts">
import { ref, shallowRef, defineProps, withDefaults, watch, reactive, nextTick } from 'vue';
import type { LegendTree } from '../../interface';
import { EesButtonTip } from '@futurefab/ui-sdk-ees-basic';

interface Props {
  legendInfo: LegendTree[];
  metaData: any;
}
const props = withDefaults(defineProps<Props>(), {
  legendInfo: () => [],
  metaData: () => ({}),
});
const emits = defineEmits(['filterChart']);
const open = ref(false);
const legendButton = ref(null);
const filterValue = shallowRef<any>([]);
const selectValue = ref('');
const checkValue = ref<any>([]);
const checkOptions = ref<any>([]);
const checkAll = reactive({
  checkAll: false,
  indeterminate: false,
  change: (e: any) => {
    filterValue.value = e.target.checked ? checkValue.value.map((item: any) => item.id) : [];
    checkAll.indeterminate = false;
  }
});
const handleTypeChange = (value: string, option: LegendTree) => {
  checkValue.value = option.valueList;
  filterValue.value = [];
  checkAll.checkAll = false;
  checkAll.indeterminate = false;
};
const groupChange = (checkList: any[]) => {
  if (!checkList.length) {
    checkAll.checkAll = false;
    checkAll.indeterminate = false;
  } else {
    checkAll.checkAll = checkList.length === checkValue.value.length;
    checkAll.indeterminate = !checkAll.checkAll;
  }
}
const getPopupContainer = (trigger: HTMLElement) => {
  if (trigger) {
    return trigger?.parentNode;
  }
  return document.body;
};

watch(
  [() => props.legendInfo],
  () => {
    if (props.legendInfo && props.legendInfo.length > 0) {
      checkOptions.value = props.legendInfo;
      if (!selectValue.value) {
        selectValue.value = checkOptions.value?.[0]?.label || '';
        checkValue.value = checkOptions.value?.[0]?.valueList || [];
      } else {
        const i = checkOptions.value?.findIndex((item: { label: any; }) => item.label === selectValue.value);
        checkValue.value = checkOptions.value?.[i]?.valueList || [];
      }
      filterValue.value = [];
      checkAll.checkAll = false;
    }
  },
  { immediate: true }
);

const confirm = () => {
  emits('filterChart', {type: selectValue, filterKeys: filterValue.value});
  open.value = false;
};
defineExpose({
  setFilterGroup: (groups: number[]) => {
    selectValue.value = 'Group';
    handleTypeChange('Group', checkOptions.value?.[0]);
    nextTick(() => {
      groupChange(groups);
      filterValue.value = groups;
    });
  }
});
</script>

<template>
  <div>
    <a-popover
      v-model:open="open"
      trigger="click"
      overlay-class-name="sm-filter-legend"
      placement="bottomRight"
      :overlay-style="{ paddingRight: '12px' }"
    >
      <template #content>
        <a-row>
          <a-select
            v-model:value="selectValue"
            style="width: 200px"
            show-search
            placeholder="Select"
            :options="checkOptions"
            :get-popup-container="getPopupContainer"
            :show-arrow="true"
            @change="handleTypeChange"
          ></a-select>
        </a-row>
        <div class="cm-filter-checkbox-group-box scrollbar-wrap-hover">
          <a-checkbox
            v-model:checked="checkAll.checkAll"
            :indeterminate="checkAll.indeterminate"
            @change="checkAll.change"
          >Check all</a-checkbox>
          <a-checkbox-group v-model:value="filterValue" @change="groupChange" class="checkbox-group">
            <a-row v-for="info in checkValue" :key="info">
              <a-checkbox :value="info.id">
                <span class="label-circle" :style="`background-color: ${info.color}`"></span>
                <span class="label">{{ info.checkLabel }}</span>
              </a-checkbox>
            </a-row>
          </a-checkbox-group>
        </div>

        <div class="sm-filter-legend-btn-box" style="text-align: right; margin-top: 5px;">
            <a-button type="primary" @click="confirm">{{
                $t('common.btn.confirm')
            }}</a-button>
        </div>
      </template>
      <div ref="legendButton">
        <ees-button-tip
          is-border
          :marginRight="10"
          icon="#icon-btn-filter-legend"
          :text="$t('eesCharts.commonBtn.filterLegend')"
        />
      </div>
    </a-popover>
  </div>
</template>

<style scoped lang="less">
@import url('@/assets/style/variable.less');
:deep(.chart_icon) {
  &:focus {
    background-color: @primary-color-row-select;
  }
}
.sm-filter-legend {
  .cm-filter-checkbox-group-box {
    margin-top: 8px;
    max-height: 260px;
    overflow: auto;
  }
  .checkbox-group {
    padding: 0;
    margin: 0;
    display: flex;
    flex-direction: column;
    flex-wrap: nowrap;
    .label-circle {
      margin-left: -4px;
      display: inline-block;
      width: 10px;
      height: 10px;
      border-radius: 50px;
    }
    .label {
      line-height: 32px;
      margin-left: 4px;
    }
  }
}
</style>
