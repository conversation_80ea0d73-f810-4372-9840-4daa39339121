import { getChartColor } from '@futurefab/ui-sdk-ees-charts';
import { snapdom } from '@zumer/snapdom';
import domtoimage from 'dom-to-image';

export async function htmlToImgUrl(dom: HTMLElement, dep: 'snapdom' | 'domtoimage') {
  try {
    let img;
    const opt = {
      backgroundColor: getChartColor(),
      width: dom.offsetWidth,
      height: dom.clientHeight,
    }
    if(dep === 'snapdom') {
      const result = await snapdom(dom, {
        ...opt,
        type: 'jpg'
      });
      img = (await result.toPng()).src;
    } else {
      img = await domtoimage.toPng(dom, {
        ...opt,
        useCORS: true, // 允许跨域图片
      });
    }
    console.log(img, getChartColor())
    return img; // 返回 base64 url
  } catch(e) {
    console.log(e)
    return null;
  }
}

export async function htmlToImgBold(dom: HTMLElement, dep: 'snapdom' | 'domtoimage') {
  try {
    let img
    const opt = {
      backgroundColor: getChartColor(),
      width: dom.offsetWidth,
      height: dom.clientHeight,
    }
    if(dep === 'snapdom') {
      const result = await snapdom(dom, {
        ...opt,
        type: 'jpg'
      });
      img = await result.toBlob({ type: 'png' });
    } else {
      img = await domtoimage.toBlob(dom, {
        ...opt,
        useCORS: true, // 允许跨域图片
        type: 'png',
      });
    }
    return img; // 返回 base64 url
  } catch {
    return null;
  }
}
async function copyImageToClipboard(imageBlob: any) {
  try {
    // 检查浏览器支持
    if (!navigator.clipboard || !navigator.clipboard.write) {
        throw new Error('浏览器不支持Clipboard API');
    }
    
    // 创建ClipboardItem
    const item = new ClipboardItem({ [imageBlob.type]: imageBlob });
    // 写入剪贴板
    await navigator.clipboard.write([item]);
    return true;
  } catch (error) {
    console.error(error);
    return false;
  }
}
export async function copyChart(dom: HTMLElement, dep: 'snapdom' | 'domtoimage') {
  const bolb = await htmlToImgBold(dom, dep);
  if (!bolb) {
    return false;
  }
  return await copyImageToClipboard(bolb);
}

export async function downloadChart(dom: HTMLElement, dep: 'snapdom' | 'domtoimage', name: string) {
  const url = await htmlToImgUrl(dom, dep);
  if (!url) {
    return false;
  }

  if (name && !name?.endsWith('.png') && !name.endsWith('.jpg')) {
    name+= '.png'
  }

  let a = document.createElement('a');
  a.href = url;
  a.download = name || 'chart';
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
}
