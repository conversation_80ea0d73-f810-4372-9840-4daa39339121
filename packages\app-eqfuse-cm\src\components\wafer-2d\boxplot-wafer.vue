<script setup lang="ts">
import { watch, ref } from 'vue';
import type { Measuredata } from '@/utils/spline-interpolate';
import Chart from '../chart.vue';
import type { Specification } from '@/views/metrology-management/interface';
import { quantileSeq, mean, variance, std } from 'mathjs';
import { calcGroup, getToolbox } from './index';
import { debounce } from 'lodash-es';
import { colorList, formatterY } from '@futurefab/ui-sdk-ees-charts';
const props = defineProps<{
    data?: Measuredata;
    valueKey: string;
    groupKey: string;
    groupNumber: number;
    specification?: Specification;
}>();
const options = ref<any>({});
const getSingleBoxplot = (
    data: Measuredata,
    valueKey: string,
    groupKey: string,
    groupNumber: number,
    specification: Specification,
) => {
    let groupList: number[];
    const xIndex = data.header.findIndex(item => item === specification.jsonObj.xField);
    const yIndex = data.header.findIndex(item => item === specification.jsonObj.yField);
    const vIndex = data.header.findIndex(item => item === valueKey);
    const valueList = data.data.map(item => Number(item[vIndex]));
    if (groupKey !== 'Radius') {
        const groupIndex = data.header.findIndex(item => item === groupKey);
        groupList = data.data.map(item => Number(item[groupIndex]));
    } else {
        groupList = data.data.map(item =>
            Math.sqrt(Number(item[xIndex]) ** 2 + Number(item[yIndex]) ** 2),
        );
    }

    const maxV = Math.max(...groupList);
    const minV = Math.min(...groupList);
    const gap = (maxV - minV) / groupNumber;

    // 按区间分组的对象
    const groupRes = new Array(groupNumber).fill(null).map((_: null, index: number) => ({
        start: minV + gap * index,
        end: index === groupNumber - 1 ? maxV : minV + gap * (index + 1),
        data: [] as number[],
        count: 0,
    }));
    console.log(groupRes, 'groupRes');
    groupList.forEach((value: number, index: number) => {
        const groupI = calcGroup(value, maxV, minV, gap, groupNumber);
        groupRes[groupI].data.push(valueList[index]);
    });
    groupRes.forEach(item => {
        if (!item.data.length) {
            return;
        }
        item.count = item.data.length;
        // 将数据转换为 boxplot需要的格式  [min,  Q1,  median (or Q2),  Q3,  max]
        const q = quantileSeq(item.data, [0, 0.25, 0.5, 0.75, 1]) as number[];
        const iqr = q[3] - q[1];
        const upper = q[3] + 1.5 * iqr;
        const lower = q[1] - 1.5 * iqr;
        for (let i = 0; i < q.length; i++) {
            if (q[i] < lower) {
                q[i] = lower;
            } else {
                break;
            }
        }
        for (let i = q.length - 1; i >= 0; i++) {
            if (q[i] > upper) {
                q[i] = upper;
            } else {
                break;
            }
        }
        item.data = q;
    });
    return {
        color: colorList,
        grid: {
            left: 80,
            top: 30,
            right: 20,
            bottom: 30,
        },
        xAxis: {
            show: true,
            type: 'category',
            data: new Array(groupNumber).fill(null).map((item, index) => index + 1),
            splitArea: {
                show: true,
            },
        },
        yAxis: {
            show: true,
            scale: true,
            splitArea: {
                show: false,
            },
        },
        toolbox: getToolbox(),
        tooltip: {
            formatter: (event: any) => {
                if (event?.data?.info) {
                    const info = event.data.info;
                    return [
                        `Range: ${formatterY(info.start)} ~ ${formatterY(info.end)}`,
                        `Count: ${info.count}`,
                        `Max: ${event.data.value[5]}`,
                        `Q3: ${event.data.value[4]}`,
                        `Median: ${event.data.value[3]}`,
                        `Q1: ${event.data.value[2]}`,
                        `Min: ${event.data.value[1]}`
                    ].join('<br/>');
                }
            },
            appendToBody: true, 
        },
        series: [
            {
                name: 'boxplot',
                type: 'boxplot',
                data: groupRes.map(item => ({
                    value: item.data,
                    info: item,
                })),

            },
        ],
    };
};

const tempFn = debounce(() => {
    options.value = getSingleBoxplot(
        props.data!,
        props.valueKey,
        props.groupKey,
        props.groupNumber,
        props.specification!,
    );
}, 100);
watch(
    [
        () => props.data,
        () => props.valueKey,
        () => props.groupKey,
        () => props.groupNumber,
        () => props.specification,
    ],
    v => {
        if (props.data) {
            tempFn();
        }
    },
    { immediate: true },
);
</script>
<template>
    <div class="common-chart-container">
        <Chart :options="options" />
    </div>
</template>
<style scoped lang="less">
.common-chart-container {
    width: 100%;
    height: 100%;
}
</style>
