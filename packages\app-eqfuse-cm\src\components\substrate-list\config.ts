import { t, VXETable } from '@futurefab/vxe-table';
export const options = VXETable.tableFun.tableDefaultConfig({
    id: 'fdc-trace-model-simulation-select-data-table',
    toolbarConfig: {
        tableName: 'common.title.lotList',
        import: false,
        export: false,
        tools: [],
        slots: {
            beforeTools: 'searchList',
        },
    },
    columns: [
        {
            field: 'rawId',
            type: 'checkbox',
            headerClassName: 'sel',
        },
    ],
    checkboxConfig: {
        showHeader: false,
    },
});
export const setRowClassName = (e: any) => {
    let str = '';
    if (e.row.faultCount > 0) {
        str = 'error_row';
    } else if (e.row.warningCount > 0) {
        str = 'warning_row';
    }
    return str;
};
