<script lang="ts" setup>
// import { EesSidebar } from '@futurefab/ui-sdk-ees-basic';
import { ref, watch, computed } from 'vue';
import { Splitpanes, Pane } from 'splitpanes';
import { GroupConfigVO } from '@/model/GroupConfigVO';
import { getCmConfigRule } from '@/views/cm-home/cm-main/config';
import {
  handleGroupData,
  removeDuplicateByContext
} from '@/views/cm-home/cm-main/add-dataset/config';
import { datasetGroupStore } from '@/views/cm-home/cm-main/add-dataset/dataset-group-store';
import { cloneDeep, uniqueId } from 'lodash-es';
import { useCmstore } from '@futurefab/ui-sdk-stores';
import EqFuseSidebar from '@/components/eqfuse-sidebar/index.vue';
import { getGroupDataBefore } from '@futurefab/ui-sdk-api';
import { RUN_BY } from '@/constant/charts';
import { isOneRecipe } from '@/utils';
import { showWarning } from '@futurefab/ui-sdk-ees-basic';
import { t } from '@futurefab/vxe-table';

import LeftTopContent from './left-top-content/index.vue';
import LeftBottomContent from './left-bottom-content/index.vue';
import RightContent from './right-content/index.vue';

const cmStore = useCmstore();
const isLocal = computed(() => cmStore.smartMachineLocalMode);

const props = withDefaults(
  defineProps<{
    datasetEntry?: 'add' | 'modify';
    showCache?: boolean;
    isHistory?: boolean;
    websocketParams?: any;
    groupingData?: any;
  }>(),
  {
    datasetEntry: 'add',
    showCache: false,
    isHistory: false,
    websocketParams: () => ({}),
    groupingData: () => []
  }
);
const open = defineModel<boolean>('open');
const emits = defineEmits([
  'showAddDataset',
  'renderNewGroupConfigs',
  'confirm',
  'changeIsHistory',
  'setParameter',
  'setRunMode'
]);
const rightContentRef = ref();
const commonGroupConfig = defineModel<GroupConfigVO>('commonGroupConfig');
const showLeftTop = ref<boolean>(true);
/**
 * 关键信息
 * 1. 解析出来的data list
 * 2. 选择的run by模式
 * 3. 选择的dataset group
 * 4. 分组完成的所有数据
 * 5. 弹窗内的所有缓存数据
 */
const specialKey = ref<string>('wafer');
// run by
const runByModel = ref<string>('wafer');
const handleRunByModelChange = (value: string) => {
  if (value === 'station') {
    datasetGroupStore.groupTitle = 'Station';
  } else {
    datasetGroupStore.groupTitle = 'Dataset';
  }
};
// data list key
const datasetSelect = ref(
  localStorage.getItem('DATASET_SELECT') || (isLocal.value ? 'EQP' : 'FDC')
);
const datasetSelectKey = ref('');
// wafer table data
const waferData = ref<any>({});
const setWaferData = (data: any) => {
  waferData.value = data;
};
const group = ref<string>('A');
// 弹窗loading
const datasetLoading = ref<boolean>(false);
// 分组loading
const groupLoading = ref<boolean>(false);
// 传递给后端的runType 包含 CSV, TSV, DATABASE, HIS
const runType = ref('CSV');

const handleCancel = () => {
  emits('showAddDataset', props.datasetEntry);
};
const handleClick = (code: string) => {
  if (code == 'closeCancel' || code == 'cancel') {
    handleCancel();
  }
};
const leftBottomContentRef = ref();
// 回填分组数据进入data list，并处理掉重复数据
const handleOriginTableAdd = (data: any) => {
  leftBottomContentRef.value?.handleOriginTableAdd(data);
};
// 上传文件的所有名称，非文件上传类型的就为空Map
const fileNames = ref<any>([]);

// 数据备份
const backupData = () => {
  if (props.datasetEntry === 'add') {
    // Data Source Type
    datasetGroupStore.datasetSelect = datasetSelect.value;
    // Run By
    datasetGroupStore.runByModel = runByModel.value;
    // 分组数据
    datasetGroupStore.backupsCheckData = cloneDeep(datasetGroupStore.checkData);
    // Wafer/Loop/Station List Data
    datasetGroupStore.waferDataTemp = cloneDeep(waferData.value);
    // Loop Data List
    datasetGroupStore.loopStepTemp = datasetGroupStore.loopStep;
    // 缓存表格展示数据
    datasetGroupStore.loopDataTemp = leftBottomContentRef.value.getCruxTableData();
    datasetGroupStore.stepIdsTemp = datasetGroupStore.stepIds;
  } else {
    // 分组数据
    datasetGroupStore.historyBackupsCheckData = cloneDeep(datasetGroupStore.checkData);
    // Wafer/Loop/Station List Data
    datasetGroupStore.historyWaferDataTemp = cloneDeep(waferData.value);
  }
};
// 数据备份读取
const backupDataRestoration = () => {
  if (props.datasetEntry === 'add') {
    // reference group回显 // datasetGroupStore.referenceGroup
    datasetSelect.value = datasetGroupStore.datasetSelect;
    runByModel.value = datasetGroupStore.runByModel;
    datasetGroupStore.key = uniqueId('datasetGroupStore_');
    // Data List还原
    if (datasetGroupStore.runByModel === 'loop') {
      waferData.value = cloneDeep(datasetGroupStore.waferDataTemp);
      // loop模式下需要选择下拉内容，所有需要加上nextTick/setTimeout
      setTimeout(() => {
        leftBottomContentRef.value?.setWaferLoopTableData(datasetGroupStore.currentWafer, 'radio');
      });
    } else {
      const allCheck: any[] = [];
      datasetGroupStore.backupsCheckData.forEach((value) => allCheck.push(...value));
      const tempDeepArray = cloneDeep(datasetGroupStore.waferDataTemp?.data);
      waferData.value = Object.assign(datasetGroupStore.waferDataTemp, {
        data: tempDeepArray?.filter(
          (item: any) => !allCheck?.find((obj: any) => item._X_ROW_KEY === obj._X_ROW_KEY)
        )
      });
      leftBottomContentRef.value?.setWaferTableData(waferData.value.data);
      datasetGroupStore.checkData = cloneDeep(datasetGroupStore.backupsCheckData);
    }
  } else {
    // 历史run的数据备份读取
    datasetGroupStore.key = uniqueId('historyDatasetGroupStore_');
    const allCheck: any[] = [];
    datasetGroupStore.historyBackupsCheckData.forEach((value) => allCheck.push(...value));
    const tempDeepArray = cloneDeep(datasetGroupStore.historyWaferDataTemp?.data);
    waferData.value = Object.assign(datasetGroupStore.historyWaferDataTemp, {
      data: tempDeepArray?.filter(
        (item: any) => !allCheck?.find((obj: any) => item._X_ROW_KEY === obj._X_ROW_KEY)
      )
    });
    datasetGroupStore.checkData = cloneDeep(datasetGroupStore.historyBackupsCheckData);
  }
  setTimeout(() => {
    datasetLoading.value = false;
  }, 300);
};

// 提交分组信息，并处理缓存
const handleOk = async () => {
  if (datasetGroupStore.checkData.size < 2) {
    showWarning(t('cm.tips.noGroupTips'));
    return;
  }
  const isStation = runByModel.value === 'station';
  if (!isOneRecipe(isStation, datasetGroupStore.checkData)) {
    showWarning(t('cm.tips.onlyOneRecipe'));
    return;
  }
  // 非文件上传类型，fileNames置为空
  if (!['CSV', 'TSV'].includes(runType.value)) fileNames.value = [];
  // 处理缓存
  backupData();
  // 处理分组信息
  if (props.datasetEntry === 'add') {
    await handleGroupData(
      emits,
      commonGroupConfig.value,
      runByModel.value,
      waferData.value,
      fileNames.value,
      runType.value,
      handleCancel
    );
    // 设置header显示run info
    emits('setRunMode', {
      runTypeText: leftTopContentRef.value?.getDataSource()?.label,
      runModeText: RUN_BY.find((item: any) => item.value === runByModel.value)?.label
    });
  } else {
    // 判断历史是哪种类型的数据来源
    const runByModelHistory = commonGroupConfig.value?.isLoop
      ? 'loop'
      : commonGroupConfig.value?.stationCompare
        ? 'station'
        : 'wafer';
    await handleGroupData(
      emits,
      commonGroupConfig.value,
      runByModelHistory,
      waferData.value,
      fileNames.value,
      'HISTORY',
      handleCancel
    );
  }
  // isHistory只有历史run的modify情况下才是true
  emits('changeIsHistory', props.datasetEntry === 'modify' && props.isHistory);
  // 初始化filter参数弹窗
  emits('setParameter', props.datasetEntry);
};
// 最大分组组数
const maxGroupCount = ref<any>({});
const leftTopContentRef = ref();
// 初始化弹窗
const clearModalAllData = () => {
  // 重置left-top-content
  showLeftTop.value = false;
  // 清空model数据
  leftTopContentRef.value?.clearModalData();
  setTimeout(() => {
    showLeftTop.value = true;
  });
};
const allMetrologyCols = ref<string[]>([]);
const clearMetrologyCol = () => {
  allMetrologyCols.value = [];
};
watch(
  () => runType.value,
  () => {
    clearMetrologyCol();
  }
);
// history run
const handleHistoryRun = async () => {
  // 有缓存
  if (datasetGroupStore.historyBackupsCheckData.size > 0) {
    // 读取缓存数据
    setTimeout(() => {
      backupDataRestoration();
    });
    return;
  }
  // 无缓存
  const res = await getGroupDataBefore({
    bodyParams: {
      historyRequestId: props.websocketParams.requestId,
      historyRequestDt: props.websocketParams.requestDt,
      loop: props.websocketParams.loop
    }
  });
  if (res.status === 'SUCCESS') {
    datasetLoading.value = false;
    const allSelectRows: any[] = [];
    // 设置历史分组内容
    if (props.groupingData?.length > 0) {
      props.groupingData.forEach((item: any) => {
        // groupId groupName selectedRows
        datasetGroupStore.handleAdd(item.groupName, item.selectedRows);
        allSelectRows.push(...item.selectedRows);
      });
    }
    // 设定历史分组源数据，剔除了已经选择过的数据
    const originData = res.data.data
      ?.map((item: any) => ({
        ...item,
        wafer: item?.waferId?.split(';')?.[0]
      }))
      .concat(allSelectRows);
    setWaferData({
      columns: res.data.columns,
      csvMeta: res.data.csvMeta,
      data: removeDuplicateByContext(originData, datasetGroupStore.excludeFields)
    });
  } else {
    datasetLoading.value = false;
  }
};
const metrologyGrouping = () => {
  rightContentRef.value?.handleClear();
};
// 左侧列表options更新
const reloadLeftBottomTableOptions = () => {
  leftBottomContentRef.value?.reloadTableOptions();
};
// 右侧列表options更新
const reloadRightContentTableOptions = () => {
  rightContentRef.value?.reloadTableOptions();
};

watch([open, props.showCache], () => {
  if (open.value) {
    datasetLoading.value = true;
    if (props.datasetEntry === 'modify' && props.isHistory) {
      // history run
      handleHistoryRun();
    } else {
      // 普通run
      // 获取限制规则
      getCmConfigRule().then((data: any) => {
        maxGroupCount.value = data.maxGroupCount.value;
      });
      // 读取缓存数据
      setTimeout(() => {
        backupDataRestoration();
      });
    }
  }
});

defineExpose({ clearModalAllData });
</script>

<template>
  <EqFuseSidebar
    :key="'addDataset'"
    v-model:open="open"
    :title="$t('cm.btn.addDataset')"
    :has-zoom="true"
    class="dataset-modal sidebar-no-padding"
    @clickbutton="handleClick"
    drawerContainer=".dataset-modal"
  >
    <template #sidebar_content>
      <div
        class="dataset-modal-content"
        v-isLoading="{
          isShow: datasetLoading,
          hasButton: false,
          title: 'Loading...'
        }"
      >
        <splitpanes class="default-theme dataset-modal-content-splitpanes">
          <pane size="50">
            <div class="dataset-modal-content-left">
              <splitpanes class="default-theme" horizontal>
                <pane size="50" v-if="showLeftTop && !(datasetEntry === 'modify' && isHistory)">
                  <LeftTopContent
                    ref="leftTopContentRef"
                    v-model:runByModel="runByModel"
                    v-model:commonGroupConfig="commonGroupConfig"
                    v-model:groupLoading="groupLoading"
                    v-model:runType="runType"
                    v-model:fileNames="fileNames"
                    v-model:datasetSelect="datasetSelect"
                    v-model:datasetSelectKey="datasetSelectKey"
                    @handleRunByModelChange="handleRunByModelChange"
                    @setWaferData="setWaferData"
                    @clearMetrologyCol="clearMetrologyCol"
                  />
                </pane>
                <pane size="50">
                  <div
                    :key="datasetSelectKey"
                    v-isLoading="{
                      isShow: groupLoading,
                      hasButton: false,
                      title: 'Loading...'
                    }"
                    style="height: 100%; width: 100%"
                  >
                    <LeftBottomContent
                      :key="datasetSelectKey"
                      ref="leftBottomContentRef"
                      v-model:runByModel="runByModel"
                      v-model:group="group"
                      v-model:metrology-cols="allMetrologyCols"
                      :waferData="waferData"
                      :runType="runType"
                      :maxGroupCount="maxGroupCount"
                      :specialKey="specialKey"
                      @metrologyGrouping="metrologyGrouping"
                      @reloadRightContentTableOptions="reloadRightContentTableOptions"
                    />
                  </div>
                </pane>
              </splitpanes>
            </div>
          </pane>
          <pane size="50">
            <RightContent
              ref="rightContentRef"
              v-model:group="group"
              :runByModel="runByModel"
              :maxGroupCount="maxGroupCount"
              :columns="waferData?.columns"
              :allMetrologyCols="allMetrologyCols"
              @handleOriginTableAdd="handleOriginTableAdd"
              @reloadLeftBottomTableOptions="reloadLeftBottomTableOptions"
            />
          </pane>
        </splitpanes>

        <div class="apply-box">
          <a-button class="apply" type="primary" @click="handleOk">
            {{ $t('common.btn.apply') }}
          </a-button>
          <a-button @click="handleCancel">{{ $t('common.btn.close') }}</a-button>
        </div>
      </div>
    </template>
  </EqFuseSidebar>
</template>

<style lang="less" scoped>
@import url('@/assets/style/variable.less');
.dataset-modal {
  &-content {
    display: flex;
    flex-direction: column;
    height: 100%;

    &-left {
      height: 100%;
      border-right: 1px solid @border-color;
    }

    &-right {
      height: 100%;
      border-left: 1px solid @border-color;
    }

    &-splitpanes {
      height: calc(100% - 52px);
    }

    .apply-box {
      display: flex;
      flex-direction: row;
      justify-content: end;
      // border-top: 1px solid @border-color;
      padding: 10px 14px;
      .apply {
        margin: 0 10px 0 0;
      }
    }
  }
}
</style>
