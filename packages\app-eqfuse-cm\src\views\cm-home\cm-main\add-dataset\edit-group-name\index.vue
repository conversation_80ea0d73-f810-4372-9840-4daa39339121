<script lang="ts" setup>
import { ref, nextTick } from 'vue';

const props = withDefaults(
  defineProps<{
    groupName: string;
  }>(),
  {
    groupName: ''
  }
);
const isVerify = defineModel<boolean>('isVerify');
const emits = defineEmits(['handleRename']);

const isEdit = ref<boolean>(false);
const handleRename = () => {
  isEdit.value = true;
  nextTick(() => {
    inputRef.value?.focus();
  });
};
const inputRef = ref<any>(null);
const groupNameValue = ref<string>(props.groupName);
const changeGroupName = () => {
  emits('handleRename', groupNameValue.value, props.groupName);
  isEdit.value = false;
};
</script>

<template>
  <div class="edit-group-name-box">
    <a-popover v-model:open="isVerify" placement="bottomLeft" trigger="click">
      <template #content>
        <span style="color: var(--error-color)">{{ $t('cm.tips.groupNameExist') }}</span>
      </template>
      <span style="width: 0; height: 0"></span>
    </a-popover>
    <a-input
      v-show="isVerify || isEdit"
      ref="inputRef"
      class="group-name-input"
      :status="isVerify ? 'error' : ''"
      v-model:value="groupNameValue"
      @blur="changeGroupName"
      @press-enter="changeGroupName"
    />
    <div class="group-name" v-show="!isVerify && !isEdit">
      <span>{{ groupName }}</span>
    </div>
    <a-tooltip v-if="!isVerify && !isEdit">
      <template #title>
        <span>{{ $t('cm.tips.rename') }}</span>
      </template>
      <i class="iconfont icon-modify modify-icon" @click="handleRename" />
    </a-tooltip>
  </div>
</template>

<style scoped lang="less">
@import url('@/assets/style/variable.less');
.edit-group-name-box {
  display: inline-flex;
  align-items: center;
  flex-grow: 1;
  .group-name-input {
    margin: 0 4px;
    height: 24px;
    display: inline-flex;
    flex-grow: 1;
  }
  .group-name {
    display: inline-flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    height: 24px;
    font-size: 14px;
    font-weight: normal;
    border-radius: 2px;
    background: @border-group-color;
    box-sizing: border-box;
    border: 1px solid @border-color;
    color: @text-subtitle-color;
    padding: 0 7px;
    margin: 0 4px;

    > span {
      letter-spacing: 0;
      transform: translateX(0);
      white-space: nowrap;
    }
  }
  .modify-icon {
    width: 16px;
    height: 16px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    &:hover {
      background: @bg-hover-2-color;
    }
  }
}
</style>
