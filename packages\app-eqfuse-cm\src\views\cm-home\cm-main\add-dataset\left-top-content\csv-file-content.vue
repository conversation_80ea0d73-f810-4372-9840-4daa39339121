<script lang="ts" setup>
import { ref, reactive } from 'vue';
import type { UploadChangeParam } from '@futurefab/ant-design-vue';
import { uploadCsvXmlFile, getWaferListByCsv } from '@futurefab/ui-sdk-api';
import { showWarning, uuid } from '@futurefab/ui-sdk-ees-basic';
import { cmMainInfo as buttonIds } from '@/log-config';
import { createZipFromFiles } from '@futurefab/ui-sdk-eqfuse-common';
const runByModel = defineModel('runByModel');
const groupLoading = defineModel('groupLoading');
const emits = defineEmits(['setWaferData', 'setFileNames']);

const CSVFileTipsHeader = `Only CSV format is supported. The column order should be exactly the same as the example in the tooltip, `;
const CSVFileTipsFooter = `. A header line is required for each file. Only the “yyyy/MM/dd HH:mm:ss.SSS” format is supported.`;
const csvColumns = [
  'LOT',
  'WAFER',
  'TOOL',
  'CHAMBER',
  'OPERATION',
  'RECIPE ID',
  'RECIPE STEP',
  'TRACE TIME',
  'ORG STEP',
  'LOOP NO',
  'PARAMETER_1',
  'PARAMETER_2',
  'PARAMETER_...',
  'PARAMETER_N'
];

const defaultFileInfo = {
  fileList: [],
  names: new Map(),
  headers: {
    authorization: 'authorization-text'
  }
};
const csvFileInfo = reactive(defaultFileInfo);

const deRepeatFile = () => {
  let arr: any = [];
  csvFileInfo.fileList.forEach((e: any) => {
    let found = arr.findIndex((e2: any) => e2.name === e.name) > -1;
    if (!found) arr.push(e);
  });
  csvFileInfo.fileList = arr;
};
const customUpload = () => {
  // console.log('自定义上传d.fileList:', csvFileInfo.fileList);
};
let handleDrop = (e: DragEvent) => {
  // console.log(e);
};
const handleChange = (info: UploadChangeParam) => {
  const status = info.file.status;
  if (status !== 'uploading') {
    console.log(info.file, info.fileList);
  }
  deRepeatFile();
};
const handleUploadClearBox = (e: any) => {
  e.stopPropagation();
};
// 清除文件
const handleUploadClear = (e: any) => {
  e.stopPropagation();
  csvFileInfo.fileList = [];
  csvFileInfo.names = new Map();
};
// 解析文件,获取waferData
const getCsvFileWaferData = async () => {
  groupLoading.value = true;
  const isLoop = runByModel.value === 'loop';
  let promiseArray: any = [];
  let errorMsg: any = [];
  for (let i = 0; i < csvFileInfo.fileList.length; i++) {
    const file: any = csvFileInfo.fileList[i];
    let zipFile: any = file.originFileObj;
    try {
      zipFile = await createZipFromFiles([file.originFileObj], uuid() + new Date().getTime());
    } catch (e) {
    }
    let promise = uploadCsvXmlFile({
      bodyParams: {
        fileInfo: zipFile,
      },
      headerParams: {
        routeFrom: 'wees-gateway-cm',
        log: 'Y'
      }
    });
    promise.then((res: any) => {
      if (res?.status === 'FAIL') {
        errorMsg.push(res.msg);
      }
      promiseArray.push(res);
    });
    promiseArray.push(promise);
  };
  if (!promiseArray.length) {
    groupLoading.value = false;
    return showWarning('cm.tips.noFile');
  }
  if (errorMsg.length > 0) return showWarning(errorMsg?.join('，'), false);
  // 清空文件名，防止数据混乱
  csvFileInfo.names = new Map();
  Promise.all(promiseArray)
    .then(async () => {
      csvFileInfo.fileList.forEach(({ name }) => {
        if (csvFileInfo.names.get(runByModel.value)?.size > 0) {
          const tempData = csvFileInfo.names.get(runByModel.value);
          tempData.add(name);
          csvFileInfo.names.set(runByModel.value, tempData);
        } else {
          const tempSet = new Set();
          tempSet.add(name);
          csvFileInfo.names.set(runByModel.value, tempSet);
        }
      });
      const fileNames = [...csvFileInfo.names.get(runByModel.value)];
      const params = { loop: isLoop, fileNames };
      await getWaferListByCsv({
        bodyParams: params,
        headerParams: {
          log: 'Y',
          page: buttonIds.pageId,
          action: 'get-wafer-list-by-csv'
        }
      })
        .then((response: any) => {
          if (response.status === 'SUCCESS') {
            response.data.data = response.data?.data?.map((item: any) => ({
              ...item,
              wafer: item?.waferId?.split(';')?.[0]
            }));
            const tempData = response.data;
            emits('setWaferData', tempData);
            // 设置fileNames
            emits('setFileNames', [...csvFileInfo.names.get(runByModel.value)].flat());
          }
          groupLoading.value = false;
        })
        .catch(() => (groupLoading.value = false));
    })
    .catch(() => (groupLoading.value = false));
};

defineExpose({ getCsvFileWaferData });
</script>

<template>
  <div class="csv-file-content">
    <div class="csv-file-content-upload-box">
      <span class="label">{{ $t('cm.title.csvFileImport') }}</span>
      <a-upload
        v-model:file-list="csvFileInfo.fileList"
        accept=".csv, text/csv"
        :multiple="true"
        name="file"
        :custom-request="customUpload"
        :headers="csvFileInfo.headers"
        :before-upload="() => false"
        :show-upload-list="{ showRemoveIcon: true }"
        @change="handleChange"
        @drop="handleDrop"
      >
        <div class="upload-header">
          <a-button class="upload-header-left">
            <i class="iconfont icon-upload" style="margin-right: 8px" />
            {{ $t('cm.btn.addFiles') }}
          </a-button>
          <div
            v-if="csvFileInfo.fileList.length > 0"
            class="upload-header-right"
            @click="handleUploadClearBox"
          >
            <span class="count-text">
              <span class="hint-text">{{ $t('cm.title.fileCount') }}:</span>
              {{ csvFileInfo.fileList.length }}
            </span>
            <a @click="handleUploadClear" class="clear-btn">
              <i class="iconfont icon-clear hint-text" />{{ $t('cm.btn.clearFiles') }}
            </a>
          </div>
        </div>
        <a-typography-paragraph class="fileTips">
          {{ CSVFileTipsHeader }}
          <a-tooltip placement="bottom">
            <a>View Example</a>
            <template #title>
              <div>
                <label style="font-weight: bold">{{ $t('cm.label.csvFileCol') }}</label>
              </div>
              <p v-for="(column, index) in csvColumns" :key="index" :span="24">
                <span>· {{ column }}</span>
              </p>
            </template>
          </a-tooltip>
          {{ CSVFileTipsFooter }}
        </a-typography-paragraph>
      </a-upload>
    </div>
  </div>
</template>

<style lang="less" scoped>
@import url('@/assets/style/variable.less');
.csv-file-content {
  flex: 1;
  display: flex;
  overflow: hidden;
  &-upload-box {
    flex: 1;
    display: flex;
    overflow: hidden;
    flex-direction: row;
    align-items: flex-start;
    margin-bottom: 10px;
    .label {
      min-width: 106px;
      height: 32px;
      width: max-content;
      color: @text-title-color;
      font-weight: bold;
      display: flex;
      align-items: center;
    }
    .hint-text {
      color: @text-hint-color;
    }
    .upload-header {
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      &-left {
        height: 32px;
        border-radius: 4px;
        border: 1px solid @border-color;
        color: @text-subtitle-color;
        .icon-upload {
          margin-right: 8px;
        }
      }
      &-right {
        height: 32px;
        display: flex;
        flex: 1;
        justify-content: flex-end;
        align-items: center;
        .count-text {
          font-weight: bold;
          margin-right: 18px;
          color: @text-subtitle-color;
        }
        .clear-btn {
          color: @text-subtitle-color;
          padding: 0 8px;
          .hint-text {
            margin-right: 4px;
          }
          &:hover {
            border-radius: 4px;
            background-color: @bg-active-1-color;
          }
        }
      }
    }
    .fileTips {
      margin: 4px 0 0 0;
      font-size: 12px;
    }
    // 右侧选择文件区域
    :deep(.ant-upload-wrapper) {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      height: 100%;
      .ant-upload {
        width: 100%;
        flex: 0 0 auto;
      }
      .ant-upload-list {
        flex: 1;
        overflow-y: auto;
        // 显示滚动条
        &:hover {
          &::-webkit-scrollbar-thumb {
            background-color: @bg-scroll-color;
          }
        }
        &::-webkit-scrollbar-corner {
          background-color: @bg-scroll-color;
        }
      }
    }
  }
}
</style>
