import { VXETable } from '@futurefab/vxe-table';
export const getOptions = (isLoop: boolean) => {
  const options = [
    {
      type: 'checkbox'
      // slots: {
      //     header: 'checkAllHead',
      // },
    },
    {
      field: 'groupName',
      title: 'cm.field.dataSet',
      minWidth: 60,
      sortable: true,
      filters: [],
      filterRender: {}
    },
    {
      field: 'lotId',
      title: 'cm.field.lot',
      minWidth: 120,
      sortable: true,
      filters: [],
      filterRender: {}
    }
  ];
  if (isLoop) {
    options.push({
      field: 'loopNo',
      title: 'cm.field.loopNo',
      minWidth: 120,
      sortable: true,
      filters: [],
      filterRender: {}
    });
  } else {
    options.push({
      field: 'wafer',
      title: 'cm.field.wafer',
      minWidth: 120,
      sortable: true,
      filters: [],
      filterRender: {}
    });
  }
  return VXETable.tableFun.tableDefaultConfig({
    columns: options
  });
};
