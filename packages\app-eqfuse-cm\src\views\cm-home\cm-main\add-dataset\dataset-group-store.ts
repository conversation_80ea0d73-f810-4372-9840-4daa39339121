import { reactive, shallowReactive, computed } from 'vue';
import { useCmstore } from '@futurefab/ui-sdk-stores';
import {
  deduplicateByContext,
  getReplenishGroup
} from '@/views/cm-home/cm-main/add-dataset/config';

const cmStore = useCmstore();

const isLocal = computed(() => cmStore.smartMachineLocalMode);
export const datasetGroupStore = reactive({
  key: 'datasetGroupStore',
  checkData: new Map(),
  tempCheckData: new Map(),
  groupTitle: 'Dataset',
  loopStep: '',
  stepIds: [] as any[],
  // 数据备份
  runByModel: 'wafer',
  backupsCheckData: new Map(),
  waferDataTemp: shallowReactive<any>({}),
  loopStepTemp: '',
  stepIdsTemp: [] as any[],
  // history模式下的数据备份
  historyBackupsCheckData: new Map(),
  historyWaferDataTemp: shallowReactive<any>({}),
  // loop模式下当前选中wafer
  currentWafer: {},
  datasetSelect: localStorage.getItem('DATASET_SELECT') || (isLocal.value ? 'EQP' : 'FDC'),
  loopDataTemp: shallowReactive<any>([]),
  copySet: new Map(),
  // reference group
  referenceGroup: new Set(),
  excludeFields: [
    '_X_ROW_KEY',
    'ROW_INDEX',
    'StartTime',
    'TimeStamp',
    'EndTime',
    'fileName',
    'time'
  ],
  // 存储dataset name到localstorage
  saveDataSetName: (name: string) => {
    // 包含了默认A-Z分组名称的内容
    const allGroupNameList = getReplenishGroup();
    if (allGroupNameList.find((item: any) => item.value === name)) return;
    // 不存在新的名称的时候才需要新增
    const groupCache = localStorage.getItem('REPLENISH_GROUP') || '';
    if (groupCache) {
      const groupArr = groupCache.split(',');
      groupArr.push(name);
      localStorage.setItem('REPLENISH_GROUP', groupArr.join(','));
    } else {
      localStorage.setItem('REPLENISH_GROUP', name);
    }
  },
  // 更改分组名
  handleRename: (
    oldGroupName: string | string[],
    newGroupName: string | string[],
    isBackup = false,
    isHistory = false
  ) => {
    if (oldGroupName === newGroupName) return;
    // 创建新的Map来保持顺序
    const newCheckData = new Map();
    // 是否改的是备份的数据
    const checkKey = isBackup
      ? isHistory
        ? 'historyBackupsCheckData'
        : 'backupsCheckData'
      : 'checkData';
    const oldGroupNameList = Array.isArray(oldGroupName) ? oldGroupName : [oldGroupName];
    const newGroupNameList = Array.isArray(newGroupName) ? newGroupName : [newGroupName];
    datasetGroupStore[checkKey].forEach((value: any[], key: string) => {
      const oldGroupNameIndex = oldGroupNameList.findIndex((item) => item === key);
      if (oldGroupNameIndex !== -1) {
        newCheckData.set(newGroupNameList[oldGroupNameIndex], value);
      } else {
        newCheckData.set(key, value);
      }
    });
    datasetGroupStore[checkKey] = newCheckData;
    // 同时更新copySet中的引用
    oldGroupNameList.forEach((oldName, index) => {
      const newName = newGroupNameList[index];
      if (datasetGroupStore.copySet.has(oldName)) {
        const copyValue = datasetGroupStore.copySet.get(oldName);
        datasetGroupStore.copySet.delete(oldName);
        datasetGroupStore.copySet.set(newName, copyValue);
      }
      // 将新的dataset name存入缓存
      datasetGroupStore.saveDataSetName(newName);
    });
  },
  // 处理Station复制组的数据
  handleStationCopy: (group: string, values: any[]) => {
    // 确保 values 是一个数组
    if (!Array.isArray(values)) {
      throw new Error('Values must be an array.');
    }
    // 检查 group 是否已存在
    if (datasetGroupStore.checkData.has(group)) {
      const arr = datasetGroupStore.checkData.get(group);
      // 将 values 中的新元素添加到 arr 中
      values.forEach((value) => {
        if (!arr.includes(value)) arr.push(value);
      });
      // 更新 Map 中的数组
      datasetGroupStore.checkData.set(group, arr);
    } else {
      // 如果 group 不存在，则创建一个新的数组并添加 values
      datasetGroupStore.checkData.set(group, [...values]);
    }
  },
  handleAdd: (group: string, values: any[], excludeFields?: string[]) => {
    // 确保 values 是一个数组
    if (!Array.isArray(values)) {
      throw new Error('Values must be an array.');
    }
    // 检查 group 是否已存在
    if (datasetGroupStore.checkData.has(group)) {
      const arr = datasetGroupStore.checkData.get(group);
      // 将 values 中的新元素添加到 arr 中
      values.forEach((value) => {
        if (!arr.includes(value)) arr.push(value);
      });
      // 更新 Map 中的数组
      datasetGroupStore.checkData.set(
        group,
        deduplicateByContext(arr, excludeFields || datasetGroupStore.excludeFields)
      );
    } else {
      // 删除别的分组内存在的相同数据，根据context来区分是否为同一个数据
      datasetGroupStore.checkData.forEach((existingValues: any[], existingKey: string) => {
        if (existingKey !== group && !datasetGroupStore.copySet.has(existingKey)) {
          // 找出需要删除的相同context数据
          const toDelete = existingValues.filter((existingItem: any) => {
            return values.some((newItem: any) => {
              // 使用与deduplicateByContext相同的逻辑判断是否为相同数据
              const excludeFieldsToUse = excludeFields || datasetGroupStore.excludeFields;
              const getContext = (item: any) => {
                const contextKeys = Object.keys(item)
                  .filter((key) => !excludeFieldsToUse.includes(key))
                  .sort();
                return contextKeys.map((key) => `${key}:${item[key]}`).join(',');
              };
              return getContext(existingItem) === getContext(newItem);
            });
          });

          if (toDelete.length > 0) {
            datasetGroupStore.handleDelete(existingKey, toDelete);
          }
        }
      });
      // 清除数据为空的分组
      datasetGroupStore.handleClearEmpty();
      // 如果 group 不存在，则创建一个新的数组并添加 values
      datasetGroupStore.checkData.set(group, [...values]);
    }
  },
  handleDelete: (group: string, values: any[]) => {
    // 确保 values 是一个数组
    if (!Array.isArray(values)) {
      throw new Error('Values must be an array.');
    }
    // 获取当前组的数据
    const arr = datasetGroupStore.checkData.get(group);
    if (arr) {
      // 创建一个新的数组来保存删除后的结果
      const filteredArr = arr.filter((item: any) => !values.includes(item));
      // 如果过滤后的数组长度发生了变化，则更新 Map 中的数组
      if (filteredArr.length !== arr.length) {
        datasetGroupStore.checkData.set(group, filteredArr);
      }
    } else {
      // 如果 group 不存在，则无需做任何事情
      console.warn(`Group "${group}" does not exist.`);
    }
  },
  handleTempCheckData: (group: string, values: any[]) => {
    // 确保 value 是一个数组
    if (!Array.isArray(values)) {
      throw new Error('Value must be an array.');
    }
    datasetGroupStore.tempCheckData.set(group, [...values]);
  },
  handleLoopStep: (value: string) => {
    datasetGroupStore.loopStep = value;
  },
  handleCurrentWafer: (value: any) => {
    datasetGroupStore.currentWafer = value;
  },
  handleStepIds: (value: any[]) => {
    datasetGroupStore.stepIds = value;
  },
  // 删除内容为空的分组
  handleClearEmpty: () => {
    const keys = Array.from(datasetGroupStore.checkData.keys());
    keys.forEach((key) => {
      if (datasetGroupStore.checkData.get(key)?.length === 0) {
        datasetGroupStore.checkData.delete(key);
      }
    });
  },
  handleClear: () => {
    // 清空整个 Map
    datasetGroupStore.checkData.clear();
    datasetGroupStore.copySet.clear();
    // 清空reference group
    datasetGroupStore.referenceGroup.clear();
  },
  handleTempDataClear: () => {
    datasetGroupStore.tempCheckData.clear();
  },
  // 清空暂存
  handleBackupsClear: () => {
    // add模式下的数据备份
    datasetGroupStore.backupsCheckData.clear();
    datasetGroupStore.waferDataTemp = {};
    datasetGroupStore.loopStepTemp = '';
    datasetGroupStore.loopDataTemp = [];
    datasetGroupStore.stepIdsTemp = [];
    // history模式下的数据备份
    datasetGroupStore.historyBackupsCheckData.clear();
    datasetGroupStore.historyWaferDataTemp = {};
  }
});
