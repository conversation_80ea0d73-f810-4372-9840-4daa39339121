# @futurefab/ui-app-ees-r2r

FutureFab.AI UI Framework 子应用 app-ees-r2r, 有关 UI Framework 请查阅 https://git.futurefab.cn/apex/ui-framework

** 此脚手架由 `npm create @futurefab/ui` 快速创建 **

## 介绍

TODO

## 基本用法

### 安装

`npm i @futurefab/ui-app-ees-r2r -D --registry=https://npm.dev.futurefab.cn/`

### 门户 / Portal 注册

```ts
import { type AppConfig, AppManager } from '@futurefab/ui-platform';

const apps: AppConfig[] = [
  // ...
  {
    id: 'app-ees-r2r',
    route: '/xxx',
    entry: {
      type: vue,
      package: '@futurefab/ui-app-ees-r2r' // 直接在 Portal 安装此依赖
      // 或远程 url 加载
      // scripts: ['/unpkg/@futurefab/ui-app-ees-r2r/dist/index.js'],
      // styles: ['/unpkg/@futurefab/ui-app-ees-r2r/dist/style.css']
    }
  }
  // ...
];
const appManager = new AppManager({ apps });
```

## 调试

### 本地启动 vite serve

在子项目目录下执行

```sh
pnpm dev
```

### 在 Portal 页面上启用调试

如果 Portal 是本地 serve 模式，会自动启用代理；

如果要在远端的 Portal 启用代理，F12 console 执行以下代码

```js
devmode('@futurefab/ui-app-ees-r2r', 'http://localhost:5907/src/index.ts', []);
```

执行完后刷新页面

### 开启 debug 输出

```js
localStorage.debug = 'ui:app-ees-r2r*';
```
