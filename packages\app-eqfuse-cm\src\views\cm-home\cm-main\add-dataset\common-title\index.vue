<script setup lang="ts">
import { useSelfFullScreen } from '@/utils/tools';
import { t } from '@futurefab/vxe-table';
import { useBaseStore } from '@futurefab/ui-sdk-stores';

const baseStore = useBaseStore();

const props = withDefaults(
  defineProps<{
    title: string;
    count: number;
    border?: boolean;
    borderBottom?: boolean;
    checkType?: 'checkbox' | 'radio' | 'none';
    checkTooltip?: string;
  }>(),
  {
    title: '',
    count: 0,
    border: false,
    borderBottom: false,
    checkType: 'none',
    checkTooltip: 'Select'
  }
);
const emits = defineEmits(['changeChecked']);

const boxRef = defineModel('boxRef');
const { isFullscreen, toggle } = useSelfFullScreen(boxRef as any);

// 勾选
const isChecked = defineModel<boolean>('isChecked');
const changeChecked = (e: any) => {
  emits('changeChecked', e.target.checked);
};
</script>

<template>
  <div
    :class="
      border
        ? borderBottom
          ? 'title-warp title-warp-border-bottom'
          : 'title-warp title-warp-border'
        : 'title-warp'
    "
  >
    <div class="title-warp-left-box">
      <a-tooltip v-if="checkType === 'checkbox'">
        <template #title>
          <span>{{ checkTooltip }}</span>
        </template>
        <a-checkbox
          v-model:checked="isChecked"
          @change="changeChecked"
          class="title-warp-left-box-checkbox"
        />
      </a-tooltip>
      <span class="title-warp-left-box-title">{{ props.title }}</span>
      <slot name="left-icon-left-area"></slot>
      <slot name="left-icon"></slot>
      <vxe-tooltip
        :content="t(!isFullscreen ? 'common.title.zoomIn' : 'common.title.zoomOut')"
        :use-h-t-m-l="true"
        :theme="baseStore.theme"
      >
        <i
          v-if="boxRef"
          class="iconfont chart_icon_icon zoom-icon fullscreen-icon"
          :class="isFullscreen ? 'icon-screen-reduction' : 'icon-screen-full'"
          @click="toggle"
        ></i>
      </vxe-tooltip>
      <slot name="left-content"></slot>
    </div>
    <div class="title-warp-right-box">
      <span v-show="props.count > 0" class="title-warp-right-box-count-box">
        <span class="title-warp-right-box-count">{{ $t('common.title.count') }}：</span>
        {{ props.count }}
      </span>
      <slot name="right-icon"></slot>
    </div>
  </div>
</template>

<style scoped lang="less">
@import url('@/assets/style/variable.less');
.title-warp-border {
  border-top: solid 1px @border-color;
  border-left: solid 1px @border-color;
  border-right: solid 1px @border-color;
  border-radius: 4px 4px 0 0;
}
.title-warp-border-bottom {
  border-bottom: solid 1px @border-color;
  border-radius: 4px 4px 0 0;
}
.title-warp {
  padding: 8px 10px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;

  &-left-box {
    display: flex;
    flex-direction: row;
    flex: 1;
    align-items: center;
    &-checkbox {
      margin-right: 4px;
    }
    &-title {
      display: inline-flex;
      align-items: center;
      font-weight: bold;
      margin-right: 4px;
      font-size: 16px;
      color: @text-title-color;
    }
    .fullscreen-icon {
      color: @text-weak-text;
      width: 20px;
      height: 20px;
      display: inline-flex;
      justify-content: center;
      align-items: center;
      &:hover {
        cursor: pointer;
        color: @text-weak-text;
        background-color: @bg-hover-2-color;
      }
    }
  }
  &-right-box {
    display: flex;
    flex-direction: row;
    font-weight: bold;
    align-items: center;
    margin-left: 10px;
    &-count-box {
      margin-right: 10px;
    }
    &-count {
      color: @text-hint-color;
    }
  }
}
</style>
