<script setup lang="ts">
import { computed, nextTick, ref } from 'vue';
import { getAddMetrologyOptions } from './config';
import {
  EesButtonTip,
  showWarning,
  uuid,
  setHeaderSelectFilter
} from '@futurefab/ui-sdk-ees-basic';
import AddMetrologyChart from './add-metrology-chart.vue';
import { CloseOutlined } from '@futurefab/icons-vue';
import type { MetrologyGroup } from './interface';
import { t } from '@futurefab/vxe-table';
import { getGroupDataBefore } from '@futurefab/ui-sdk-api';
import { datasetGroupStore } from '@/views/cm-home/cm-main/add-dataset/dataset-group-store';
const props = withDefaults(
  defineProps<{
    data: any[];
    columns?: any[];
    beforeMetrologyCol?: string[];
    noAddDataset?: boolean;
    websocketParams?: any;
    checkData?: Map<string, any[]>;
  }>(),
  {
    data: () => [],
    columns: () => [],
    beforeMetrologyCol: () => [],
    noAddDataset: false,
    websocketParams: () => ({})
  }
);
const emits = defineEmits(['metrologyGroup']);
const buildTableData = () => {
  const temp = props.data.map((item: any) =>
    props.noAddDataset
      ? { ...item }
      : {
          ...item,
          datasetGroup: 'Ungrouped',
          originRow: item // 源数据的索引
        }
  );

  if (!props.noAddDataset) {
    // 处理之前分好组的数据
    const contextSet = new Set<string>(); // 记录未分组的数据有哪些不同的行， 用于判断是否和以分组的数据重复
    const splitStr = '@@';
    const keys = [
      'chamber',
      'eqpId',
      'eqpModuleId',
      'layerId',
      'loopNo',
      'lotId',
      'recipeId',
      'stationName',
      'step',
      'stepLoop',
      'waferId'
    ];
    temp.forEach((row) => {
      contextSet.add(keys.map(key => row[key]).join(splitStr));
    });

    if (props.checkData) {
      for (const [group, list] of props.checkData) {
        list.forEach((row) => {
          const contextKey = keys.map(key => row[key]).join(splitStr);
          if (!contextSet.has(contextKey)) { // 重复的情况下以未分组的 数据为准
            temp.push({
              ...row,
              datasetGroup: group,
              originRow: row
            });
          }
        });
      }
    }
  }
  return temp;
};
const waferColumns = ref<any[]>([]);
const getWaferColumns = async () => {
  if (!props.websocketParams.recipeId && !props.websocketParams.requestDt) {
    waferColumns.value = datasetGroupStore.waferDataTemp.columns;
    return;
  }
  try {
    loading.value = true;
    const res = await getGroupDataBefore({
      bodyParams: {
        historyRequestId: props.websocketParams.requestId,
        historyRequestDt: props.websocketParams.requestDt,
        loop: false
      }
    });
    if (res.status === 'SUCCESS') {
      console.log(res, 'rrr');
      waferColumns.value = res.data.columns;
    }
  } catch {
  } finally {
    loading.value = false;
  }
};
const waferMetrologyCol = computed(() => {
  if (props.noAddDataset && props.data.length) {
    return Object.keys(props.data[0]).filter((key) => key.endsWith('-metrology'));
  } else {
    return [];
  }
});
const _beforeMetrologyCol = computed(() =>
  props.noAddDataset ? waferMetrologyCol.value : props.beforeMetrologyCol
);
const _columns = computed(() => (props.noAddDataset ? waferColumns.value : props.columns));
const xGrid = ref();
const loading = ref(false);
const tableData = ref<any[]>([]);
const options = ref(getAddMetrologyOptions(_columns.value, [], [], [], props.noAddDataset));
const showModel = ref(false);
const handleHide = () => {
  showModel.value = false;
};

// 本次操作期间加的列
const curAddCol = ref<string[]>([]);
// 所有的量测 列
const allMetrologyCols = computed(() => [
  ...curAddCol.value,
  ..._beforeMetrologyCol.value.filter((item) => !curDelCol.value.includes(item))
]);
const rules = computed(() => {
  const temp: Record<string, any[]> = {};
  allMetrologyCols.value.forEach((item) => {
    temp[item] = [
      {
        required: true,
        message: t('common.tip.required', {
          name: item.slice(0, -'-metrology'.length)
        })
      }
    ];
  });
  return temp;
});
const columnInput = ref('');
const addMetrologyChartRef = ref();
const drawChart = () => {
  addMetrologyChartRef.value.show();
};
const cellClassName = ({ row, column }: any) => {
  if (column.field?.includes('-metrology')) {
    return 'add-metrology-cell';
  }
};
// 本次操作期间 删除的列
const curDelCol = ref<string[]>([]);
const deleteCol = (col: string) => {
  if (curAddCol.value.includes(col)) {
    // 本次操作期间加的列
    curAddCol.value.splice(
      curAddCol.value.findIndex((item) => (item === col)),
      1
    );
  } else {
    // 之前加的列
    curDelCol.value.push(col);
  }
  tableData.value.forEach((item) => {
    delete item.col;
  });
  options.value = getAddMetrologyOptions(
    _columns.value,
    _beforeMetrologyCol.value,
    curAddCol.value,
    curDelCol.value,
    props.noAddDataset
  );
};
const addCol = () => {
  if (columnInput.value?.trim?.()) {
    const temp = columnInput.value?.trim?.() + '-metrology';
    if (_beforeMetrologyCol.value.includes(temp) || curAddCol.value.includes(temp)) {
      // 不能重复添加
      return showWarning('Metrology Parameter cannot repeat add');
    }
    curAddCol.value.push(temp);
    options.value = getAddMetrologyOptions(
      _columns.value,
      _beforeMetrologyCol.value,
      curAddCol.value,
      curDelCol.value,
      props.noAddDataset
    );

    tableData.value.forEach((item) => {
      item[temp] = null;
    });
    columnInput.value = '';
  }
};
const exportTableData = () => {
  xGrid.value.exportData({
    type: 'csv'
  });
};
const save = async () => {
  const result = await xGrid.value.validate(tableData.value);
  if (result && Object.keys(result).length) {
    return;
  }
  // 处理新增的量测值
  curAddCol.value.forEach((key) => {
    tableData.value.forEach((item) => {
      item.originRow[key] = item[key];
    });
  });
  // 处理删除的列
  curDelCol.value
    .filter((col) => !curAddCol.value.includes(col)) // 过滤后续新增的 重名 的 key
    .forEach((key) => {
      tableData.value.forEach((item) => {
        delete item.originRow[key];
      });
    });
  // 处理其它列
  const updatedRows = xGrid.value.getRecordset()?.updateRecords;
  _beforeMetrologyCol.value
    .filter((key) => !curDelCol.value.includes(key) && !curAddCol.value.includes(key))
    .forEach((key) => {
      updatedRows.forEach((row: any) => {
        row.originRow[key] = row[key];
      });
    });
  // 分组数据
  const groupMap = new Map<string, any[]>();
  tableData.value
    .filter((item) => item.datasetGroup !== 'Ungrouped')
    .forEach((item) => {
      if (groupMap.has(item.datasetGroup)) {
        groupMap.get(item.datasetGroup)?.push(item.originRow);
      } else {
        groupMap.set(item.datasetGroup, [item.originRow]);
      }
    });
  const res: MetrologyGroup = {
    groupMap,
    allMetrologyCols: [...allMetrologyCols.value],
    curAddCol: [...curAddCol.value],
    curDelCol: [...curDelCol.value]
  };
  emits('metrologyGroup', res);
  showModel.value = false;
};
function setHeader() {
  setHeaderSelectFilter({
    xGrid: xGrid,
    tableData: xGrid.value.getTableData().fullData,
    columnFieldList: options.value.columns
      .filter((item: any) => item.field && item.field !== 'datasetGroup')
      .map((item: any) => item.field)
  });
}
defineExpose({
  show: async () => {
    showModel.value = true;
    tableData.value = buildTableData();
    if (props.noAddDataset) {
      await getWaferColumns();
    }
    options.value = getAddMetrologyOptions(
      _columns.value,
      _beforeMetrologyCol.value,
      [],
      [],
      props.noAddDataset
    );
    curAddCol.value = [];
    curDelCol.value = [];
    setTimeout(() => setHeader());
  }
});
const baseClass = 'add-metrology-data';
</script>

<template>
  <Teleport to="body">
    <vxe-modal
      v-model="showModel"
      :title="$t('cm.title.inputMetrologyData')"
      :mask-closable="false"
      :width="'90%'"
      :height="'90%'"
      :class-name="'sidebar-no-padding'"
      resize
      show-footer
      @hide="handleHide"
      @close="handleHide"
    >
      <div :class="baseClass + '-box'">
        <div :class="baseClass + '-title'">
          <div :class="baseClass + '-title-left'">
            <p class="single-line-ellipsis">{{ 'Add Column' }}</p>
            <a-input v-model:value="columnInput" style="margin-right: 10px"></a-input>
            <a-button
              type="primary"
              style="margin-right: 10px"
              :disabled="!columnInput?.trim?.() || !tableData?.length"
              @click="addCol"
              >{{ $t('common.btn.add') }}</a-button
            >
          </div>
          <div :class="baseClass + '-title-right'">
            <EesButtonTip
              is-border
              :icon="'#icon-btn-trend-chart-draw'"
              :text="$t('common.btn.drawChart')"
              :disabled="!tableData?.length && !allMetrologyCols.length"
              :margin-right="10"
              @click="drawChart"
            />
            <EesButtonTip
              is-border
              :icon="'#icon-btn-export'"
              :text="$t('common.btn.export')"
              :disabled="!tableData?.length"
              @click="exportTableData"
            />
          </div>
        </div>
        <div :class="baseClass + '-content'">
          <vxe-grid
            :id="baseClass + '-grid'"
            ref="xGrid"
            :class="[baseClass + '-grid']"
            v-bind="options"
            :data="tableData"
            :loading="loading"
            :cell-class-name="cellClassName"
            :edit-rules="rules"
          >
            <template v-for="value in allMetrologyCols" :key="value" #[value]="{ column }">
              <span class="single-line-ellipsis">{{ column.title }}</span>
              <CloseOutlined @click="deleteCol(column.field)" />
            </template>
          </vxe-grid>
        </div>
      </div>
      <template #footer>
        <a-button type="primary" style="margin-right: 10px" @click="save">{{
          $t('common.btn.save')
        }}</a-button>
        <a-button @click="handleHide">{{ $t('common.btn.close') }}</a-button>
      </template>
    </vxe-modal>
  </Teleport>
  <AddMetrologyChart
    ref="addMetrologyChartRef"
    :data="tableData"
    :curAddCol="curAddCol"
    :beforeMetrologyCol="noAddDataset ? waferMetrologyCol : beforeMetrologyCol"
  ></AddMetrologyChart>
</template>

<style scoped lang="less">
@import url('@/assets/style/variable.less');
.add-metrology-data {
  &-box {
    height: 100%;
    width: 100%;
  }
  &-title {
    padding: 0 10px;
    height: 52px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    &-left {
      display: flex;
      align-items: center;
      justify-content: space-between;
      p {
        margin-right: 10px;
        flex-shrink: 0;
      }
    }
    &-right {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
  }
  &-content {
    height: calc(100% - 52px);
  }
}
:deep(.metrology-col-header .vxe-cell--title) {
  display: flex;
  flex-grow: 1;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}
</style>
