<script lang="ts" setup>
import { ref, reactive, onMounted, inject, computed } from 'vue';
import {
  getStateViewerResetHistory,
  exportStateViewerResetHistory,
  getUserStateViewerResetHistory,
  exportUserStateViewerResetHistory,
  apiConfig
} from '@futurefab/ui-sdk-api';
import { stateViewer as buttonIds } from '@/log-config/index';
import { xGetConfig } from './config';
import { t, type VxeGridDefines, VXETable, type VxeTablePropTypes } from '@futurefab/vxe-table';
import { setHeaderSelectFilter, EesSidebar, EesCustomTime } from '@futurefab/ui-sdk-ees-basic';
import { filterMethod } from '@/utils/tools';
import { boolFilters } from '@/utils/table-config';
import { exportToExcel, showWarning } from '@futurefab/ui-sdk-ees-basic';
import dayjs from 'dayjs';
import type { GetButtonAuthority } from '@/views/setup-data-manager/interface';

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  selectedState: {
    type: Object,
    default: {}
  },
  type: String
});
const getButtonAuthority = inject('getButtonAuthority');
const buttonAuthority = computed(() =>
  (getButtonAuthority as GetButtonAuthority)().buttonAuthority()
);
const refTime = ref();
const dataParams = reactive<any>({
  startDate: dayjs().subtract(29, 'day').startOf('day'),
  endDate: dayjs().endOf('day'),
  timeAreaResult: null,
  timeConfigVal: '',
  timeIsConfig: '',
  timeAreaBind: [dayjs().subtract(29, 'day').startOf('day'), dayjs().endOf('day')]
});
const timePop = (val: [number, number]) => {
  console.log('timePop', val);
  dataParams.timeAreaResult = val;
  // dataParams.timeAreaBind = val; // recursive
  dataParams.startDate = dayjs(val[0]).format('YYYY-MM-DD HH:mm:ss');
  dataParams.endDate = dayjs(val[1]).format('YYYY-MM-DD HH:mm:ss');
  onSearch();
};
const timeStartEndChange = (val: [number, number]) => {
  console.log('timeStartEndChange', val);
  dataParams.timeAreaBind = val;
};
const timeTypeChange = ({ isConfig, val }: any) => {
  console.log('timeTypeChange', isConfig, val);
  dataParams.timeConfigVal = val;
  dataParams.timeIsConfig = isConfig;
};
const xGrid = ref();
const xTable = reactive({
  options: xGetConfig(props.type),
  data: [] as any[],
  loading: false,
  totalPage: 0,
  currentPage: 1,
  pageSize: 20
});
const inputData = reactive({
  stateName: '',
  stateItems: [],
  stateKey: []
});
const emits = defineEmits(['hideSidebar']);
const handleClick = async () => {
  emits('hideSidebar');
};
const exportMethod = async () => {
  if (!xTable.data.length) return showWarning(t(`common.tip.exportCommonTip`));
  const bodyParams = {
    exportFileName: `StateResetHistory - ${
      props.selectedState.stateName || props.selectedState.title
    }`,
    startDate: dayjs(dataParams.startDate).format('YYYY-MM-DD HH:mm:ss'),
    endDate: dayjs(dataParams.endDate).format('YYYY-MM-DD HH:mm:ss'),
    modelName: props.selectedState?.modelName,
    stateName: props.selectedState?.stateName || props.selectedState?.title
  };
  const res = await (props.type === 'user'
    ? exportUserStateViewerResetHistory({
        headerParams: {
          log: 'Y',
          page: buttonIds.pageId,
          action: buttonIds.user.export
        },
        bodyParams
      })
    : exportStateViewerResetHistory({
        headerParams: {
          log: 'Y',
          page: buttonIds.pageId,
          action: buttonIds.system.export
        },
        bodyParams
      }));
  if (res.status !== 'SUCCESS') return;
  exportToExcel(res.data);
};
const gridEvents = {
  toolbarToolClick: ({ code, tool }: any) => {
    console.log(code, tool);
    const { menuClick, row } = tool;
    switch (code) {
      case 'exportData':
        exportMethod();
        break;
    }
  },
  pageChange: ({ currentPage, pageSize }: any) => {
    console.log('pageChange', currentPage, pageSize);
    xTable.currentPage = currentPage;
    xTable.pageSize = pageSize;
    onSearch();
  }
};
const setColumn = async (resData: any) => {
  if (!resData.column) return;
  let cols: any = [];
  Object.entries(resData.column).forEach((item: any, index: number) => {
    console.log('resCols', item);
    if (!['STATE KEY', 'STATE VALUE', 'STATE KEY ALIAS', 'STATE VALUE ALIAS'].includes(item[0])) {
      let minWidth = item[1].length * 16 > 100 ? item[1].length * 16 : 100;
      if (item[0].toLowerCase().includes('time') || item[0].toLowerCase().includes('dtt')) {
        minWidth = 200;
      }
      cols.push({
        field: item[1],
        title: item[0],
        fixed: index ? '' : 'left',
        sortable: true,
        minWidth,
        filters: item[1] === 'RESET_YN' ? boolFilters : [],
        filterMethod,
        align: item[1] === 'RESET_YN' ? 'center' : 'left',
        type: item[1] === 'RESET_YN' ? 'columnCheckbox-default' : ''
      });
    } else {
      if (!['STATE KEY ALIAS', 'STATE VALUE ALIAS'].includes(item[0])) {
        cols.push({
          title: item[0],
          minWidth: 180,
          children: item[1].map((v: any, index: number) => {
            const minWidth = v.length * 16 > 100 ? v.length * 16 : 100;
            // 增加 displayName，后端字段的field需要处理(props.type == 'user')
            let stateKeyValueAlias = '';
            if (item[0] == 'STATE KEY') {
              stateKeyValueAlias = 'STATE KEY ALIAS';
            } else if (item[0] == 'STATE VALUE') {
              stateKeyValueAlias = 'STATE VALUE ALIAS';
            }
            const stateKeyValueAliasColumn = resData.column[stateKeyValueAlias];
            return {
              field: v,
              title: (stateKeyValueAliasColumn && stateKeyValueAliasColumn[index]) || v,
              sortable: true,
              minWidth,
              filters: [],
              filterMethod
            };
          })
        });
      }
    }
  });
  xTable.options.columns = cols;
  console.log('setColumn', cols);
  // xGrid.value?.reloadColumn(cols);
  setTimeout(() => {
    const columnFieldList = [
      ...Object.values(resData.column),
      ...resData.column['STATE KEY'],
      ...resData.column['STATE VALUE']
    ];
    // const columnFieldList = ['actionModeCd', ...resCols.key, ...resCols.value];
    xGrid.value &&
      setHeaderSelectFilter({
        xGrid: xGrid,
        columnFieldList,
        tableData: xTable.data
      });
  }, 100);
};
const getHistory = async (bodyParams: any) => {
  xTable.loading = true;
  const res = await (
    props.type === 'user'
      ? getUserStateViewerResetHistory({
          headerParams: {
            log: 'Y',
            page: buttonIds.pageId,
            action: buttonIds.user.resetHistory
          },
          bodyParams
        })
      : getStateViewerResetHistory({
          headerParams: {
            log: 'Y',
            page: buttonIds.pageId,
            action: buttonIds.system.resetHistory
          },
          bodyParams
        })
  ).finally(() => {
    xTable.loading = false;
  });
  if (res.status !== 'SUCCESS') return;
  const resData = res?.data;
  xTable.data = [];
  xTable.data =
    resData?.table ||
    resData?.map((v: any) => {
      return {
        ...v,
        ...v.stateKeyMap,
        ...v.stateValueMap
      };
    }) ||
    [];
  xTable.totalPage = resData?.count;
  setColumn(resData);
};
const onSearch = () => {
  getHistory({
    limit: xTable.pageSize,
    page: xTable.currentPage,
    startDate: dayjs(dataParams.startDate).format('YYYY-MM-DD HH:mm:ss'),
    endDate: dayjs(dataParams.endDate).format('YYYY-MM-DD HH:mm:ss'),
    modelName: props.selectedState?.modelName,
    stateName: props.selectedState?.stateName || props.selectedState?.title
  });
};
onMounted(() => {
  console.log('mounted', props);
  setTimeout(() => {
    xGrid.value &&
      xGrid.value.setVisibleTools({
        authorityList: buttonAuthority.value.buttonList,
        flag: buttonAuthority.value.allButtonFlag
      });
  }, 300);
});
</script>

<template>
  <ees-sidebar v-if="show" @clickbutton="handleClick">
    <template #sidebar_content>
      <div class="reset-history">
        <vxe-grid
          ref="xGrid"
          class="vxe-grid-pager-border-xs vxe-toolbar-top-left-radius-xs vxe-toolbar-top-right-radius-xs"
          v-bind="xTable.options"
          :border-out-line="[xTable.data?.length ? 1 : 1, 1, 0, 1]"
          :data="xTable.data"
          :show-header="xTable.data?.length > 0"
          :loading="xTable.loading"
          :pager-config="{
            layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes', 'FullJump'],
            align: 'right',
            pageSizes: [10, 15, 20, 25, 100],
            pageSize: xTable.pageSize,
            total: xTable.totalPage,
            currentPage: xTable.currentPage
          }"
          :export-config="{
            filename: `StateResetHistory - ${selectedState.stateName || selectedState.title}.xlsx`,
            id: buttonIds[props.type as string].export
          }"
          v-on="gridEvents"
        >
          <template #beforeButtonsList="{ row }">
            <ees-custom-time
              ref="refTime"
              class="data-history-time"
              :bordered="true"
              :time-area="dataParams.timeAreaBind"
              :api-config="apiConfig"
              @time-pop="timePop"
              @timeStartEndChange="timeStartEndChange"
              @time-type-change="timeTypeChange"
            ></ees-custom-time>
          </template>
        </vxe-grid>
      </div>
    </template>
  </ees-sidebar>
</template>

<style lang="less" scoped>
@import url('@/assets/style/variable.less');
@ns: reset-history;

.@{ns} {
  height: 100%;
  display: flex;
  flex-direction: column;

  :deep(.title-time) {
    display: none;
  }
  .data-history-time {
    padding-left: @padding-common;
  }

  :deep(.ant-form-item) {
    .ant-form-item-label {
      label {
        width: 80px;
      }
    }
  }

  .search-row {
    display: flex;
  }

  .search-btn {
    margin-left: @margin-btn;
  }
}
</style>
