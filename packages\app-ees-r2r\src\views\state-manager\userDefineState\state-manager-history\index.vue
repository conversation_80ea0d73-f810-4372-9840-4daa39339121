<script lang="ts" setup>
import { ref, reactive, onBeforeMount } from 'vue';
import { xConfig } from './config';
import { t, type VxeGridDefines, VXETable, type VxeTablePropTypes } from '@futurefab/vxe-table';
import { getUserDefineStateHistory } from '@futurefab/ui-sdk-api';
import { setHeaderSelectFilter, EesSidebar } from '@futurefab/ui-sdk-ees-basic';

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  selected: {
    type: Object,
    default: {}
  },
  cols: {
    type: Array,
    default: []
  }
});
const xGrid = ref();
const xTable = reactive({
  options: xConfig,
  data: [] as any[]
});
const emits = defineEmits(['hideSidebar']);
const handleClick = async () => {
  emits('hideSidebar');
};
const getHistory = async (bodyParams: any) => {
  const res = await getUserDefineStateHistory({ bodyParams });
  xTable.data = res.data || [];
  console.log(props.cols);
  xGrid.value &&
    xGrid.value.reloadColumn([...xTable.options.columns!.slice(0, 3), ...props.cols.slice(2)]);
  setTimeout(() => {
    xGrid.value &&
      setHeaderSelectFilter({
        xGrid: xGrid,
        tableData: xTable.data as any,
        columnFieldList: [
          'operateType',
          'activityClassName',
          'useYn',
          'areaName',
          'eqpModelName',
          'chamberName',
          'version',
          'activityFunctionName',
          'lastUpdateBy',
          'createBy'
        ],
        splitTypeMap: {
          areaName: ',',
          eqpModelName: ',',
          chamberName: ','
        }
      });
  }, 300);
};
const gridEvents = {
  toolbarToolClick: ({ code }: any) => {
    switch (code) {
      case 'refresh':
        getHistory(props.selected.selectData);
        break;
    }
  }
};
onBeforeMount(() => {
  console.log('mounted', props);
  getHistory(props.selected.selectData);
});
</script>

<template>
  <ees-sidebar v-if="show" class="sidebar-no-padding" @clickbutton="handleClick">
    <template #sidebar_content>
      <div class="state-history">
        <vxe-grid
          ref="xGrid"
          class="vxe-toolbar-top-left-radius-xs vxe-toolbar-top-right-radius-xs vxe-table-bottom-left-radius-xs vxe-table-bottom-right-radius-xs"
          v-bind="xTable.options"
          :data="xTable.data"
          :export-config="{
            filename: `ShareStateHistory - ${selected.selectData.activityFunctionName}.xlsx`
          }"
          v-on="gridEvents"
        >
          >
        </vxe-grid>
      </div>
    </template>
  </ees-sidebar>
</template>

<style lang="less" scoped>
@import url('@/assets/style/variable.less');
@ns: state-history;

.@{ns} {
  height: 100%;
}
</style>
