import { VXETable } from '@futurefab/vxe-table';
import { tableDefaultConfig } from '@futurefab/ui-sdk-ees-basic';
export default tableDefaultConfig({
  id: 'log-viewer-table',
  borderOutLine: [0, 0, 0, 0],
  columns: [
    {
      field: 'StartTime',
      title: 'workflowLogViewer.field.startTime',
      sortable: true,
      minWidth: '180px',
      filters: [{ data: '' }],
      filterRender: {
        name: '$input'
      }
    },
    {
      field: 'EndTime',
      title: 'workflowLogViewer.field.endTime',
      sortable: true,
      minWidth: '180px',
      filters: [{ data: '' }],
      filterRender: {
        name: '$input'
      }
    },
    {
      field: 'name',
      title: 'workflowLogViewer.field.workflowName',
      sortable: true,
      minWidth: '180px',
      filters: [{ data: '' }],
      filterRender: {
        name: '$input'
      }
    },
    {
      field: 'method',
      title: 'workflowLogViewer.field.eventName',
      sortable: true,
      minWidth: '150px',
      filters: [{ data: '' }],
      filterRender: {
        name: '$input'
      }
    },
    {
      field: 'ActivityName',
      title: 'workflowLogViewer.field.activityName',
      sortable: true,
      minWidth: '150px',
      filters: [{ data: '' }],
      filterRender: {
        name: '$input'
      }
    },
    {
      field: 'ActivityType',
      title: 'workflowLogViewer.field.activityType',
      sortable: true,
      minWidth: '150px',
      filters: [{ data: '' }],
      filterRender: {
        name: '$input'
      }
    },
    {
      field: 'Input',
      title: 'workflowLogViewer.field.input',
      sortable: true,
      minWidth: '130px',
      filters: [{ data: '' }],
      filterRender: {
        name: '$input'
      }
      // formatter: ({ cellValue }: { cellValue: any }) => {
      //     if (cellValue) {
      //         return JSON.stringify(cellValue);
      //     }
      // },
    },
    {
      field: 'CustomLog',
      title: 'workflowLogViewer.field.step',
      sortable: true,
      minWidth: '130px',
      filters: [{ data: '' }],
      filterRender: {
        name: '$input'
      }
    },
    {
      field: 'Output',
      title: 'workflowLogViewer.field.output',
      sortable: true,
      minWidth: '150px',
      filters: [{ data: '' }],
      filterRender: {
        name: '$input'
      }
    },
    {
      field: 'Result',
      title: 'workflowLogViewer.field.result',
      sortable: true,
      minWidth: '150px',
      filters: [{ data: '' }],
      filterRender: {
        name: '$input'
      }
    },
    {
      field: 'ErrorLog',
      title: 'workflowLogViewer.field.errorLog',
      sortable: true,
      minWidth: '150px',
      filters: [{ data: '' }],
      filterRender: {
        name: '$input'
      }
    }
  ],
  exportConfig: {
    filename: 'WorkflowLogViewer_ExecuteLogs.xlsx'
  },
  columnCheckboxConfig: {
    checkMethod: () => {
      return false;
    }
  },
  toolbarConfig: {
    tableName: 'workflowLogViewer.title.executeLogs',
    refresh: true,
    import: false,
    export: true,
    tools: [],
    border: false
  }
});
