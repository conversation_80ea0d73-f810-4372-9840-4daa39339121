<script setup lang="ts">
import { ref, defineProps, defineEmits, watch, computed, reactive, onMounted } from 'vue';
import { showError, showWarning, EesButtonTip } from '@futurefab/ui-sdk-ees-basic';
import { cmConfig as buttonIds } from '@/log-config';

import type {
  CMAnalysisOption,
  ExportFormat,
  LayoutOption,
  ParameterItem,
  ExportFilter,
  ExportFormData,
  FilterParameter,
  CheckedItems,
  SearchParam
} from './interface';
import { cloneDeep } from 'lodash-es';
import { getCodePriorityCodes } from '@futurefab/ui-sdk-api';
import type { CodeA3Entity } from '@/views/cm-home/parameter-categorization/interface';
import SpecialTag from '@/views/cm-home/cm-main/components/special-tag/index.vue';
import { t } from '@futurefab/vxe-table';

const exportFormats: ExportFormat[] = [
  { value: 'PPT', label: 'PPT' },
  { value: 'Image', label: 'Image' }
];

const cmAnalysisOptions: CMAnalysisOption[] = [
  { value: 'highlightStep', label: 'Highlight Step' },
  { value: 'summary', label: 'Summary Result' },
  { value: 'boxplot', label: 'Boxplot' },
  { value: 'metrology', label: 'Metrology Correlation Chart' }
];

const layoutOptions: LayoutOption[] = [
  {
    key: 'traceChart',
    label: 'Trace Chart',
    options: [
      { value: '1-1', label: '1x1' },
      { value: '2-2', label: '2x2' }
    ]
  },
  {
    key: 'summaryChart',
    label: 'Summary Chart',
    options: [
      { value: '1-1', label: '1x1' },
      { value: '2-2', label: '2x2' },
      { value: '3-2', label: '3x2' }
    ]
  },
  {
    key: 'boxplotChart',
    label: 'Boxplot Chart',
    options: [
      { value: '1-1', label: '1x1' },
      { value: '2-2', label: '2x2' },
      { value: '3-2', label: '3x2' }
    ]
  },
  {
    key: 'metrologyChart',
    label: 'Metrology Chart',
    options: [
      { value: '1-1', label: '1x1' },
      { value: '2-2', label: '2x2' },
      { value: '3-2', label: '3x2' }
    ]
  }
];

const props = defineProps<{
  treeData: ParameterItem[];
  loading?: boolean;
  clickedRow?: Object;
  hasMetrology?: boolean;
}>();

const emit = defineEmits<{
  (e: 'confirm', formData: ExportFormData): void;
  (e: 'cancel'): void;
}>();

onMounted(async () => {
  await getCategoryData();
});

let categories = reactive<CodeA3Entity[]>([]);
let categoiesLoading = ref(false);
async function getCategoryData() {
  try {
    categoiesLoading.value = true;
    const res = await getCodePriorityCodes({
      bodyParams: {},
      headerParams: {
        log: 'Y',
        page: buttonIds.pageId,
        action: 'get-categories'
      }
    });
    categories = [
      ...(res.data || []),
      {
        codeId: -1,
        code: 'Uncategorized',
        name: 'Uncategorized',
        codeOrder: null,
        default: true,
        used: true,
        description: 'Uncategorized',
        codeProperties: [{ codeId: 1058, propertyName: 'COLOR', propertyValue: '#BBBBBB' }]
      }
    ];
  } catch {
    categories = [];
    showError('获取分类失败');
  } finally {
    categoiesLoading.value = false;
  }
}
// 选择相关的状态
const expandedKeys = ref<string[]>([]);
const checkedKeys = ref<string[]>([]);

const formData = reactive<ExportFormData>({
  exportFilter: {
    format: ['PPT'],
    filterParameter: true,
    useCMAnalysisResult: false,
    cmAnalysisResultList: [],
    layoutSetting: {
      traceChart: '1-1',
      boxplotChart: '',
      summaryChart: '',
      metrologyChart: ''
    }
  },
  filterParameter: {
    checkAll: false,
    checkedItems: {}
  }
});

const searchCondition = reactive<SearchParam>({
  category: [],
  inputValue: ''
});

// 筛选后的总参数数量
const filteredParametersCount = computed(() => {
  let count = 0;
  filteredTreeData.value.forEach((group) => {
    count += group.children?.length || 0;
  });
  return count;
});

// 已选择的子节点数量
const selectedCount = computed(() => {
  let count = 0;

  // 遍历所有原始数据，检查是否在 checkedKeys 中
  props.treeData.forEach((group) => {
    if (group.children) {
      group.children.forEach((child) => {
        const childKey = child.treeKey?.toString();
        if (childKey && checkedKeys.value.includes(childKey)) {
          count++;
        }
      });
    }
  });

  return count;
});

// 检查是否存在搜索条件
const hasSearchCondition = computed(() => {
  return !!searchCondition.inputValue;
});

// 搜索状态提示文本，用于用户反馈
const searchStatusText = computed(() => {
  if (!hasSearchCondition.value) {
    return '';
  }

  if (filteredTreeData.value.length === 0) {
    return t('cm.tips.noMatchingData');
  }

  if (searchCondition.inputValue) {
    return t('cm.tips.foundMatchingParameters', { count: filteredParametersCount.value });
  }

  return '';
});

// 分组选中状态枚举
enum GroupCheckState {
  NONE = 'none', // 未选
  PARTIAL = 'partial', // 半选
  ALL = 'all' // 全选
}

/**
 * 检查分组的选中状态
 * 返回: GroupCheckState.NONE(未选), GroupCheckState.PARTIAL(半选), GroupCheckState.ALL(全选)
 */
const getGroupCheckState = (groupKey: string): GroupCheckState => {
  // 直接从treeDataForATree中找到对应的分组
  const group = treeDataForATree.value.find((g) => g.key === groupKey);
  if (!group || !group.children) return GroupCheckState.NONE;

  // 只检查当前可见的子项
  const visibleChildren = group.children || [];
  if (visibleChildren.length === 0) return GroupCheckState.NONE;

  // 统计选中的子项数量
  const checkedCount = visibleChildren.filter((child) => {
    return checkedKeys.value.includes(child.key);
  }).length;

  // 根据选中数量判断状态
  if (checkedCount === 0) {
    return GroupCheckState.NONE; // 未选
  } else if (checkedCount === visibleChildren.length) {
    return GroupCheckState.ALL; // 全选
  } else {
    return GroupCheckState.PARTIAL; // 半选
  }
};

/**
 * 处理分组checkbox变化事件
 */
const handleGroupCheckboxChange = (groupKey: string, event: any) => {
  const checked = event.target.checked;
  // 找到对应的分组
  const group = treeDataForATree.value.find((g) => g.key === groupKey);
  if (!group || !group.children) return;

  // 只操作当前可见的子项
  const visibleChildren = group.children || [];
  visibleChildren.forEach((child) => {
    const childKey = child.key;
    if (!childKey) return;

    if (checked.checked) {
      if (!checkedKeys.value.includes(childKey)) {
        checkedKeys.value.push(childKey);
      }
    } else {
      const index = checkedKeys.value.indexOf(childKey);
      if (index > -1) {
        checkedKeys.value.splice(index, 1);
      }
    }
  });

  updateCheckedItems();
  checkSelectedDataCount();
};

/**
 * 处理checkbox变化事件
 */
const handleCheckboxChange = (key: string, event: any) => {
  const checked = event.target.checked;

  if (checked) {
    // 如果选中且不在选择列表中，则添加
    if (!checkedKeys.value.includes(key)) {
      checkedKeys.value.push(key);
    }
  } else {
    // 如果取消选中且在选择列表中，则移除
    const index = checkedKeys.value.indexOf(key);
    if (index > -1) {
      checkedKeys.value.splice(index, 1);
    }
  }

  updateCheckedItems();

  checkSelectedDataCount();
};

/**
 * 处理 a-tree 组件的节点选择事件
 */
const handleTreeSelect = (selectedKeys: string[], info: any) => {
  const nodeKey = info.node.key;

  // 如果是父项（分组），忽略选择事件，让checkbox自己处理
  if (nodeKey.startsWith('group-')) {
    return; // 忽略分组节点的选择
  }

  // 只处理子项的选择
  const childKey = nodeKey;

  // 检查当前节点是否已经被选中
  const isCurrentlyChecked = checkedKeys.value.includes(childKey);

  if (isCurrentlyChecked) {
    // 如果已经选中，则取消选择
    const index = checkedKeys.value.indexOf(childKey);
    if (index > -1) {
      checkedKeys.value.splice(index, 1);
    }
  } else {
    // 如果未选中，则添加到选择列表
    checkedKeys.value.push(childKey);
  }

  // 更新选中项结构
  updateCheckedItems();

  checkSelectedDataCount();
};

/**
 * 更新选中项结构
 */
const updateCheckedItems = () => {
  const newCheckedItems: CheckedItems = {};

  props.treeData.forEach((group) => {
    const groupId = group.parameterName?.toString();
    if (!groupId) return;

    newCheckedItems[groupId] = {};

    group.children?.forEach((child) => {
      const childKey = child.treeKey?.toString();
      if (childKey) {
        // 检查是否在选中的键值中
        newCheckedItems[groupId][childKey] = checkedKeys.value.includes(childKey);
      }
    });
  });

  // 更新原来的数据结构
  formData.filterParameter.checkedItems = newCheckedItems;
};

/**
 * 处理CM分析结果复选框变化
 * - 启用时：选择所有分析选项并启用布局设置
 * - 禁用时：清空选择并禁用布局设置
 */
const handleCMAnalysisResultChange = () => {
  if (formData.exportFilter.useCMAnalysisResult) {
    formData.exportFilter.cmAnalysisResultList = [
      'highlightStep',
      'boxplot',
      'summary',
      props.hasMetrology ? 'metrology' : ''
    ];
    formData.exportFilter.layoutSetting.boxplotChart = '1-1';
    formData.exportFilter.layoutSetting.summaryChart = '1-1';
    formData.exportFilter.layoutSetting.metrologyChart = props.hasMetrology ? '1-1' : '';
  } else {
    formData.exportFilter.cmAnalysisResultList = [];
    formData.exportFilter.layoutSetting.boxplotChart = '';
    formData.exportFilter.layoutSetting.summaryChart = '';
    formData.exportFilter.layoutSetting.metrologyChart = '';
  }
};

/**
 * 处理CM分析选项变化
 * 根据选中的分析结果类型，自动设置对应的布局配置
 */
const handleCMAnalysisOptionChange = () => {
  const { cmAnalysisResultList, layoutSetting } = formData.exportFilter;

  // 定义图表类型与布局配置的映射关系
  const chartLayoutMap = [
    {
      optionValue: 'boxplot',
      layoutKey: 'boxplotChart' as keyof typeof layoutSetting,
      defaultLayout: '1-1'
    },
    {
      optionValue: 'summary',
      layoutKey: 'summaryChart' as keyof typeof layoutSetting,
      defaultLayout: '1-1'
    },
    {
      optionValue: 'metrology',
      layoutKey: 'metrologyChart' as keyof typeof layoutSetting,
      defaultLayout: '1-1'
    }
  ];

  chartLayoutMap.forEach(({ optionValue, layoutKey, defaultLayout }) => {
    if (cmAnalysisResultList.includes(optionValue)) {
      // 选项被选中：如果当前值为空，设置为默认值
      if (!layoutSetting[layoutKey]) {
        layoutSetting[layoutKey] = defaultLayout;
      }
    } else {
      // 选项未被选中：清空布局设置
      layoutSetting[layoutKey] = '';
    }
  });
};

/**
 * 判断布局选项是否应该被禁用
 * @param layoutKey 布局配置的键名
 * @returns 是否禁用
 */
const isLayoutDisabled = (layoutKey: string): boolean => {
  const { useCMAnalysisResult, cmAnalysisResultList } = formData.exportFilter;

  // 定义需要检查分析选项的布局键映射
  const analysisDependentLayouts: Record<string, string> = {
    boxplotChart: 'boxplot',
    summaryChart: 'summary',
    metrologyChart: 'metrology'
  };

  // 如果当前布局需要依赖特定的分析选项
  if (analysisDependentLayouts[layoutKey]) {
    const requiredOption = analysisDependentLayouts[layoutKey];
    return !cmAnalysisResultList.includes(requiredOption);
  }

  // 其他情况：当未启用CM分析结果且不是traceChart时禁用
  return !useCMAnalysisResult && layoutKey !== 'traceChart';
};

// 总参数数量
const totalParametersCount = computed(() => {
  let count = 0;
  props.treeData.forEach((group) => {
    count += group.children?.length || 0;
  });
  return count;
});

/**
 * 根据传入的 clickedRow 设置选择状态
 */
const setSelectedRowsFromProps = (clickedRow: any) => {
  console.log('props.treeData:', props.treeData);
  // 清空之前的选中状态
  formData.filterParameter.checkedItems = {};
  checkedKeys.value = [];
  expandedKeys.value = []; // 清空展开状态

  props.treeData.forEach((group, groupIndex) => {
    const groupName = group.parameterName;

    if (groupName && group.children) {
      const child = group.children.find(
        (child) =>
          child.parameterName === clickedRow.parameterName || child.treeKey === clickedRow.treeKey
      );

      if (child) {
        if (!formData.filterParameter.checkedItems[groupName]) {
          formData.filterParameter.checkedItems[groupName] = {};
        }
        // 选中对应的子项
        const childKey = child.treeKey?.toString();
        if (childKey) {
          formData.filterParameter.checkedItems[groupName][childKey] = true;

          // 设置选中状态
          checkedKeys.value.push(childKey);

          // 自动展开包含选中项的父级节点
          const groupKey = `group-${group.id}-${groupIndex}`;
          expandedKeys.value.push(groupKey);
        }
      }
    }
  });

  // 更新选中项结构
  updateCheckedItems();
};

/**
 * 检查选中的数据量，如果超过10则提示用户
 */
const checkSelectedDataCount = () => {
  if (selectedCount.value >= 10) {
    showWarning('选择数据过多，导出时间会耗时比较长');
  }
};

const setSpecialTag = (row: any) => {
  // 检查 row.results 是否存在
  if (!row?.results || !Array.isArray(row.results)) {
    return { type: 'success', text: t('cm.label.matching') };
  }
  // 循环 row.results
  for (const result of row.results) {
    // 检查 allColInfo 是否存在且为 Map 结构
    if (!result?.allColInfo || !(result.allColInfo instanceof Map)) {
      continue;
    }
    // 循环 allColInfo Map，先收集所有状态
    let hasWarning = false;
    for (const [groupConfigId, value] of result.allColInfo) {
      // 检查 unmatchingRecipeSteps 长度大于0，直接返回 error
      if (value?.unmatchingRecipeSteps && value.unmatchingRecipeSteps.length > 0) {
        return { type: 'error', text: t('cm.label.critical') };
      }
      // 检查 warningRecipeSteps 长度大于0，标记有警告
      if (value?.warningRecipeSteps && value.warningRecipeSteps.length > 0) {
        hasWarning = true;
      }
    }
    // 如果有警告，返回 warning
    if (hasWarning) return { type: 'warning', text: t('cm.label.warning') };
  }

  return { type: 'success', text: t('cm.label.matching') };
};

const findRawGroupByKey = (key: string): any => {
  const match = key?.match(/^group\-[^-]+\-(\d+)$/);
  if (match) {
    const index = parseInt(match[1], 10);
    if (index >= 0 && index < filteredTreeData.value.length) {
      return filteredTreeData.value[index];
    }
  }
  return null;
};

/**
 * 处理全局全选/取消全选
 * 更新所有分组和子项以匹配全局状态
 */
const handleCheckAllChange = ({ checked, $event }: any) => {
  formData.filterParameter.checkAll = checked;
  const checkedItems: CheckedItems = {};
  const selected: string[] = [];

  props.treeData.forEach((group) => {
    const groupId = group.parameterName?.toString();
    if (!groupId) return;

    checkedItems[groupId] = {};

    group.children?.forEach((child) => {
      const treeKey = child.treeKey?.toString();
      if (treeKey) {
        checkedItems[groupId][treeKey] = checked;
        if (checked) {
          selected.push(treeKey);
        }
      }
    });
  });

  formData.filterParameter.checkedItems = checkedItems;

  // 更新 a-tree 组件状态
  checkedKeys.value = selected;

  // 更新选中项结构
  updateCheckedItems();

  // 如果是全选操作，检查数据量并提示用户
  if (checked) {
    checkSelectedDataCount();
  }
};

/**
 * 提交前验证表单数据
 * @param showMsg 是否显示警告消息
 * @returns 验证通过返回true，否则返回false
 */
const validateForm = (showMsg: boolean = true): Boolean => {
  if (!formData.exportFilter.format.length) {
    if (showMsg) {
      showWarning('Please select at least one format');
    }
    return false;
  }

  let hasChecked = false;
  Object.values(formData.filterParameter.checkedItems).forEach((groupItems) => {
    Object.values(groupItems).forEach((isChecked) => {
      if (isChecked) hasChecked = true;
    });
  });

  if (!hasChecked) {
    if (showMsg) {
      showWarning('Please select at least one row item');
    }
    return false;
  }

  return true;
};

/**
 * 处理导出确认
 * 验证表单并发出确认事件
 */
const handleConfirm = () => {
  if (!validateForm(false)) return false;

  const cloneFormData = cloneDeep(formData);
  emit('confirm', cloneFormData);
  visible.value = false;
};

/**
 * 处理取消操作
 */
const handleCancel = () => {
  visible.value = false;
};

/**
 * 处理模态框关闭
 */
const handleClose = () => {
  visible.value = false;
};

/**
 * 打开模态框
 */
const open = () => {
  visible.value = true;
};

/**
 * 重置所有选择状态为初始值
 * 注意：保留export-filters区域的选项数据，只重置参数选择相关数据
 */
const resetSelections = () => {
  // 重置过滤参数选择状态
  formData.filterParameter = {
    checkAll: false,
    checkedItems: {}
  };

  // 保留导出过滤器数据，不进行重置
  // formData.exportFilter 保持当前值不变

  // 重置分组选中项结构
  props.treeData.forEach((group) => {
    const groupId = group.parameterName?.toString();
    if (groupId) {
      formData.filterParameter.checkedItems = {};
    }
  });

  // 重置搜索条件
  searchCondition.category = [];
  searchCondition.inputValue = '';

  // 重置 a-tree 组件状态
  expandedKeys.value = [];
  checkedKeys.value = [];

  // 应用CM分析选项的布局设置逻辑
  handleCMAnalysisOptionChange();
};

let visible = ref(false);

/**
 * 处理模态框可见性变化
 * 模态框关闭时重置选择
 */
const handleVisibleChange = (newVal: boolean) => {
  if (!newVal) {
    resetSelections();
  } else {
    if (props.clickedRow) {
      setSelectedRowsFromProps(props.clickedRow);
    }
  }
};

watch(() => visible.value, handleVisibleChange, { immediate: true });

/**
 * 隐藏前验证
 * 验证失败时阻止关闭
 */
const handleBeforeHide = async ({ type }: { type: string }) => {
  if (type === 'confirm' && !validateForm()) {
    return await new Error('');
  }
  return true;
};

/**
 * 根据搜索条件筛选的树形数据
 * - 显示包含匹配参数的分组
 * - 根据搜索条件筛选分组内的子项
 * - 隐藏没有匹配子项的分组
 * - 保持选择状态不受搜索影响
 */
const filteredTreeData = computed(() => {
  if (!searchCondition.inputValue) {
    return props.treeData;
  }

  const searchValue = searchCondition.inputValue?.toLowerCase().trim() || '';

  return props.treeData
    .map((group) => {
      let filteredChildren = group.children || [];

      // 根据搜索值筛选
      if (searchValue) {
        filteredChildren = filteredChildren.filter((child) => {
          const parameterName = child.parameterName?.toLowerCase() || '';
          const treeKey = child.treeKey?.toLowerCase() || '';
          return parameterName.includes(searchValue) || treeKey.includes(searchValue);
        });
      }

      // 如果分组下没有匹配的子项，隐藏整个分组
      if (filteredChildren.length === 0) {
        return null;
      }

      // 返回筛选后的分组
      return {
        ...group,
        children: filteredChildren
      };
    })
    .filter((group): group is NonNullable<typeof group> => group !== null);
});

const treeDataForATree = computed(() => {
  return filteredTreeData.value.map((group, index) => {
    // 为每个分组生成唯一的键值，避免键值冲突
    const groupKey = `group-${group.id}-${index}`;
    const groupTitle = group.parameterName || `Group ${group.id}`;

    const children =
      group.children?.map((child) => {
        const childKey = child.treeKey?.toString() || '';
        const childTitle = child.parameterName || child.treeKey || '';
        const childColor = child.categoryColor || '';
        const isSelected = checkedKeys.value.includes(childKey);
        const allColInfo = child.allColInfo || new Map();
        // 获取 cellClassName 的结果
        const cellClass = cellClassName(false, allColInfo);
        // 组合基础类名和动态类名
        const nodeClass = `tree-child-node ${cellClass}`.trim();

        return {
          title: childTitle,
          key: childKey,
          color: childColor,
          isSelected: isSelected,
          disabled: !formData.exportFilter.filterParameter,
          allColInfo,
          isGroup: false,
          class: nodeClass // 为子节点添加CSS类（包含cellClassName的结果）
        };
      }) || [];

    return {
      title: groupTitle,
      key: groupKey,
      children: children,
      disabled: !formData.exportFilter.filterParameter,
      allColInfo: null,
      isGroup: true,
      class: 'tree-parent-node' // 为父节点添加CSS类
    };
  });
});

/**
 * 根据子项的 key 获取对应的颜色
 */
const getChildColor = (key: string): string => {
  // 遍历所有分组和子项，找到对应的颜色
  for (const group of filteredTreeData.value) {
    const child = group.children?.find((child) => child.treeKey?.toString() === key);
    if (child && child.categoryColor) {
      return child.categoryColor;
    }
  }
  return '';
};

/**
 * 处理category选择变化，自动勾选对应category的所有子项
 */
const handleCategoryChange = (selectedCategoryIds: number[]) => {
  // 清空之前的选择
  checkedKeys.value = [];
  formData.filterParameter.checkedItems = {};
  expandedKeys.value = []; // 清空展开状态

  // 如果没有选择任何category，直接返回
  if (!selectedCategoryIds || selectedCategoryIds.length === 0) {
    return;
  }

  // 遍历所有分组和子项
  props.treeData.forEach((group, groupIndex) => {
    const groupId = group.parameterName?.toString();
    if (!groupId) return;

    if (!formData.filterParameter.checkedItems[groupId]) {
      formData.filterParameter.checkedItems[groupId] = {};
    }

    let hasSelectedChild = false; // 标记该分组是否有选中的子项

    group.children?.forEach((child) => {
      const childKey = child.treeKey?.toString();
      if (childKey) {
        // 检查子项是否属于选中的category
        const childCategoryId = child.categoryCodeId;
        if (childCategoryId && selectedCategoryIds.includes(childCategoryId)) {
          // 自动勾选该子项
          formData.filterParameter.checkedItems[groupId][childKey] = true;
          checkedKeys.value.push(childKey);
          hasSelectedChild = true; // 标记有选中的子项
        } else {
          // 取消勾选
          formData.filterParameter.checkedItems[groupId][childKey] = false;
        }
      }
    });

    // 如果该分组有选中的子项，自动展开该分组
    if (hasSelectedChild) {
      const groupKey = `group-${group.id}-${groupIndex}`;
      if (!expandedKeys.value.includes(groupKey)) {
        expandedKeys.value.push(groupKey);
      }
    }
  });

  // 更新选中项结构
  updateCheckedItems();

  // 检查选中的数据量并提示用户
  checkSelectedDataCount();
};

const cellClassName = (isGroup: boolean, allColInfo: any) => {
  if (!isGroup) {
    let hasWarning = false;
    for (const [groupConfigId, value] of allColInfo) {
      if (value?.unmatchingRecipeSteps && value.unmatchingRecipeSteps.length > 0) {
        return 'un-match-recipe'; // ### 返回错误样式类名
      }
      if (value?.warningRecipeSteps && value.warningRecipeSteps.length > 0) {
        hasWarning = true;
      }
    }
    if (hasWarning) return 'warning-recipe'; // ### 返回警告样式类名
    return 'col-red';
  }
  // ... 更多条件判断
  return 'col-red';
};

defineExpose({
  open,
  GroupCheckState
});
</script>
<template>
  <vxe-modal
    v-model="visible"
    :title="$t('common.btn.exportNew')"
    :loading="categoiesLoading"
    @confirm="handleConfirm"
    @cancel="handleCancel"
    :before-hide-method="handleBeforeHide"
    @close="handleClose"
    :confirmLoading="props.loading"
    confirm-button-type="primary"
    :maskClosable="false"
    width="1000px"
    show-footer
    destroy-on-close
    type="confirm"
    :confirm-button-text="$t('common.btn.exportNew')"
    resize
    className="cm-export-modal"
  >
    <div class="export-modal-content">
      <div class="export-filters">
        <div class="section-header">Export Filter</div>

        <div class="filter-options">
          <!-- Format -->
          <div class="filter-option">
            <span class="option-label">Format</span>
            <vxe-checkbox-group v-model="formData.exportFilter.format">
              <vxe-checkbox
                v-for="format in exportFormats"
                :key="format.value"
                :label="format.value"
                :content="format.label"
                style="line-height: 32px; height: 32px"
              ></vxe-checkbox>
            </vxe-checkbox-group>
          </div>

          <!-- Filter Parameter -->
          <!-- <div class="filter-option">
            <span class="option-label">Filter Parameter</span>
            <vxe-checkbox v-model="formData.exportFilter.filterParameter"></vxe-checkbox>
          </div> -->

          <!-- CM Analysis Result -->
          <div class="filter-option">
            <span class="option-label">Use CM Analysis Result</span>
            <vxe-checkbox
              v-model="formData.exportFilter.useCMAnalysisResult"
              @change="handleCMAnalysisResultChange"
            ></vxe-checkbox>
          </div>

          <!-- Analysis Result option -->
          <div class="analysis-result-options">
            <vxe-checkbox-group
              v-model="formData.exportFilter.cmAnalysisResultList"
              class="checkbox-group"
              @change="handleCMAnalysisOptionChange"
            >
              <vxe-checkbox
                v-for="item in cmAnalysisOptions"
                :key="item.value"
                :label="item.value"
                :disabled="item.value === 'metrology' && !hasMetrology"
              >
                <template v-if="item.value === 'metrology'">
                  <div class="flex-row flex-row flex-items-center">
                    <span class="custom-text">Metrology Correlation Chart</span>

                    <vxe-tooltip
                      :content="$t('cm.tips.exportMetrologyTip')"
                      trigger="hover"
                      :theme="'light'"
                      :enter-delay="0"
                      :leave-delay="0"
                    >
                      <span
                        class="icon iconfont icon-help"
                        style="color: #878e98; margin-left: 4px"
                      ></span
                    ></vxe-tooltip>
                  </div>
                </template>
                <template v-else>
                  {{ item.label }}
                </template>
              </vxe-checkbox>
            </vxe-checkbox-group>
          </div>

          <!-- Layout Setting -->
          <div class="filter-option">
            <span class="option-label">Layout settings</span>
          </div>

          <div class="layout-settings">
            <div class="layout-setting-item" v-for="layout in layoutOptions" :key="layout.key">
              <span class="layout-label">{{ layout.label }}</span>
              <vxe-radio-group
                v-model="formData.exportFilter.layoutSetting[layout.key]"
                class="radio-group"
              >
                <vxe-radio
                  v-for="option in layout.options"
                  :key="option.value"
                  :label="option.value"
                  :content="option.label"
                  :disabled="isLayoutDisabled(layout.key)"
                ></vxe-radio>
              </vxe-radio-group>
            </div>
          </div>
        </div>
      </div>

      <div class="filter-parameters">
        <div class="filter-parameters-header">
          <div class="filter-parameters-title">
            Select Parameter
            <span class="section-count"> ({{ selectedCount }} / {{ totalParametersCount }}) </span>
          </div>

          <div style="flex: 1"></div>
          <div class="search-controls">
            <a-select
              v-model:value="searchCondition.category"
              mode="multiple"
              :placeholder="$t('cm.tips.selectCategory')"
              style="width: 170px"
              :maxTagCount="1"
              allow-clear
              class="cm-export-category-select"
              @change="handleCategoryChange"
            >
              <a-select-option
                v-for="category in categories"
                :key="category.codeId"
                :value="category.codeId"
              >
                <div class="flex flex-items-center" :title="category.name">
                  <span
                    :style="{
                      width: '12px',
                      height: '12px',
                      borderRadius: '2px',
                      backgroundColor: category.codeProperties?.[0]?.propertyValue || '#ccc',
                      flex: 'none',
                      marginRight: '8px'
                    }"
                  ></span>
                  <span
                    style="
                      display: inline-block;
                      width: 100px;
                      overflow: hidden;
                      text-overflow: ellipsis;
                      white-space: nowrap;
                    "
                    >{{ category.name }}
                  </span>
                </div>
              </a-select-option>
            </a-select>
            <vxe-input
              v-model="searchCondition.inputValue"
              :placeholder="$t('cm.tips.searchParameterName')"
              clearable
              type="search"
              style="margin-left: 10px; width: 140px"
            ></vxe-input>
          </div>
        </div>

        <!-- 搜索状态提示 -->
        <div v-if="searchStatusText" class="search-status">
          <i class="fa fa-search" style="margin-right: 8px; color: #1890ff"></i>
          <span>{{ searchStatusText }}</span>
        </div>

        <div class="parameters-content-wrapper">
          <div class="parameters-content">
            <div class="check-all">
              <vxe-checkbox
                v-model="formData.filterParameter.checkAll"
                :content="$t('common.btn.selectAll')"
                @change="handleCheckAllChange"
                :disabled="!formData.exportFilter.filterParameter"
              ></vxe-checkbox>
            </div>
            <div class="parameters-tree" v-if="filteredTreeData.length > 0">
              <a-tree
                v-model:expandedKeys="expandedKeys"
                :tree-data="treeDataForATree"
                @select="handleTreeSelect"
                :selectedKeys="[]"
                :defaultSelectedKeys="[]"
              >
                <template #title="{ title, key, children, isSelected, isGroup, allColInfo }">
                  <span class="parameter-text-box">
                    <!-- 父项checkbox -->
                    <vxe-checkbox
                      v-if="children"
                      :model-value="getGroupCheckState(key) === GroupCheckState.ALL"
                      :indeterminate="getGroupCheckState(key) === GroupCheckState.PARTIAL"
                      @change="
                        (checked: any) => handleGroupCheckboxChange(key, { target: { checked } })
                      "
                      @click.stop
                      style="margin-right: 8px"
                    ></vxe-checkbox>

                    <!-- 子项checkbox -->
                    <vxe-checkbox
                      v-if="!children"
                      :model-value="checkedKeys.includes(key)"
                      @change="(checked: any) => handleCheckboxChange(key, { target: { checked } })"
                      style="margin-right: 8px"
                    ></vxe-checkbox>

                    <span
                      v-if="!children"
                      :style="{
                        width: '12px',
                        height: '12px',
                        borderRadius: '2px',
                        backgroundColor: getChildColor(key),
                        flex: 'none',
                        marginRight: '4px'
                      }"
                    ></span>

                    <span class="parameter-name">{{ title }}</span>
                    <SpecialTag
                      v-if="children"
                      :type="setSpecialTag(findRawGroupByKey(key)).type"
                      :text="setSpecialTag(findRawGroupByKey(key)).text"
                      :custom-style="{
                        padding: '0 8px',
                        width: 'auto',
                        height: '18px',
                        fontSize: '12px',
                        lineHeight: '18px'
                      }"
                      :marginLeft="6"
                    />
                  </span>
                </template>
              </a-tree>
            </div>

            <!-- 空状态  -->
            <div v-if="filteredTreeData.length === 0" class="empty-state">
              <div class="empty-icon">
                <i class="fa fa-table"></i>
              </div>
              <div class="empty-text">
                {{
                  searchCondition.inputValue
                    ? t('cm.tips.noMatchingData')
                    : t('cm.tips.noAvailableData')
                }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </vxe-modal>
</template>

<style lang="less">
@import url('@/assets/style/variable.less');

@border-color: #e2e4e7;
@spacing-sm: 14px;
@spacing-xs: 6px;
@font-weight-bold: 700;
@font-size-lg: 16px;
@font-size-md: 14px;
@bg-light: #fafbfc;
@text-secondary: #4e5969;
@text-disabled: #999;
@font-color: #021a23;

.cm-export-modal {
  .vxe-modal--content {
    padding: 0;
    max-height: 80vh;
    overflow: hidden;
  }

  .export-modal-content {
    display: flex;
    min-height: 400px;
    max-height: calc(80vh - 120px);
    overflow: hidden;
  }

  .section-header {
    font-size: @font-size-lg;
    font-weight: @font-weight-bold;
    color: @font-color;
    padding: @spacing-sm;
    border-bottom: 1px solid @border-color;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 52px;
    flex-shrink: 0;
  }

  .export-filters {
    width: 360px;
    border-right: 1px solid @border-color;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    /* 左侧区域不收缩 */

    .filter-options {
      padding: @spacing-sm;
      flex: 1;

      /* 自定义滚动条样式 */
      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;

        &:hover {
          background: #a8a8a8;
        }
      }
    }
  }

  .filter-parameters {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;

    .filter-parameters-header {
      display: flex;
      align-items: center;
      padding: @spacing-sm;
      height: 52px;
      border-bottom: 1px solid @border-color;
      flex-shrink: 0;

      .filter-parameters-title {
        font-size: @font-size-lg;
        font-weight: @font-weight-bold;
        color: @font-color;
        display: flex;
        align-items: center;

        .section-count {
          margin-left: 4px;
          font-weight: normal;
          font-size: @font-size-md;
          color: @text-secondary;
        }
      }

      .search-controls {
        display: flex;
        align-items: center;
      }
    }

    .search-status {
      padding: 8px @spacing-sm;
      background-color: #f0f9ff;
      border-bottom: 1px solid @border-color;
      color: #1890ff;
      font-size: @font-size-md;
      display: flex;
      align-items: center;
    }

    .parameters-content-wrapper {
      flex: 1;
      overflow: hidden;
      /* 防止内容溢出 */
      min-height: 0;
      /* 允许收缩 */
      display: flex;
      flex-direction: column;
    }

    .parameters-content {
      flex: 1;
      overflow-y: auto;
      //padding: 8px 14px;
      padding: 8px 0px;
      /* 自定义滚动条样式 */
      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 3px;

        &:hover {
          background: #a8a8a8;
        }
      }
      .parameter-text-box {
        display: inline-flex;
        align-items: center;
        width: 100%;
        overflow: hidden;

        .parameter-name {
          flex: 1;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          min-width: 0;
        }

        .warning-recipe {
          cursor: pointer;
          color: @warning-color;
          background-color: @fcm-import-changed-bg;
        }

        .un-match-recipe {
          cursor: pointer;
          color: @error-color;
          background-color: @fcm-import-deleted-bg;
        }
      }

      // 禁用树形组件的默认选中样式
      :deep(.ant-tree-node-selected) {
        background-color: transparent !important;

        .ant-tree-title {
          color: inherit !important;
          font-weight: inherit !important;
        }
      }

      :deep(.ant-tree-node-content-wrapper:hover) {
        background-color: transparent !important;
      }

      :deep(.ant-tree-node-content-wrapper.ant-tree-node-selected) {
        background-color: transparent !important;
      }
    }
  }

  .filter-option {
    height: 32px;
    max-width: 332px;
    display: flex;
    align-items: center;
    margin-bottom: 10px;

    .option-label {
      font-size: @font-size-md;
      font-weight: @font-weight-bold;
      display: inline-block;
      min-width: 166px;
    }
  }

  .analysis-result-options,
  .layout-settings {
    padding: @spacing-sm;
    width: 332px;
    border-radius: 4px;
    background: @bg-light;
  }

  .layout-setting-item {
    height: 32px;
    display: flex;
    align-items: center;
    margin-bottom: @spacing-xs;

    .layout-label {
      font-weight: @font-weight-bold;
      width: 110px;
      display: inline-block;
      color: @text-secondary;
      margin-right: 10px;
    }

    .radio-group {
      display: flex;
      gap: 10px;
    }
  }

  // 复选框组
  .checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 8px;

    .vxe-checkbox {
      height: 32px;
      line-height: 32px;
      display: flex;
      align-items: center;
    }

    .vxe-checkbox + .vxe-checkbox {
      margin-left: 0;
    }
  }

  .parameter-group {
    margin-bottom: @spacing-sm;
    border-radius: 4px;
    border: 1px solid transparent;
    transition: border-color 0.2s;
  }

  .group-header {
    height: 32px;
    display: flex;
    align-items: center;
  }

  .group-children {
    padding-left: 24px;
  }

  .parameter-item {
    height: 32px;
    display: flex;
    align-items: center;
    padding: 0 @spacing-xs;

    .parameter-name {
      margin-left: 8px;
    }
  }

  .check-all {
    height: 32px;
    line-height: 32px;
    margin-left: 28px;
  }

  .empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 0;
    color: #ccc;

    .empty-icon {
      font-size: 48px;
      margin-bottom: 16px;

      & i {
        color: #ddd;
      }
    }

    .empty-text {
      font-size: 14px;
      color: #999;
    }
  }
}
</style>

<style lang="less">
@import url('@/assets/style/variable.less');

.vxe-modal--wrapper .tree-child-node {
  width: 100% !important;

  .ant-tree-node-content-wrapper {
    width: 100% !important;
  }
}

/* 禁用 Ant Design 的默认 hover 效果 */
.vxe-modal--wrapper .ant-tree .ant-tree-node-content-wrapper:hover {
  background-color: transparent !important;
}

.vxe-modal--wrapper .tree-child-node {
  &.un-match-recipe {
    color: @error-color;
    background-color: @fcm-import-deleted-bg;
  }

  &.warning-recipe {
    color: @warning-color;
    background-color: @fcm-import-changed-bg;
  }
}

/* 限制弹窗内category select标签的最大宽度 */
.vxe-modal--wrapper .cm-export-category-select {
  .ant-select-selector {
    .ant-select-selection-overflow-item {
      max-width: 80px !important;
    }
  }
}
</style>
