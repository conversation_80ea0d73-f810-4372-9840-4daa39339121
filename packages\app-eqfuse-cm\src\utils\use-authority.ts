import { cloneDeep } from 'lodash-es';
import { nextTick, onMounted, type Ref, ref, watch } from 'vue';
export const useAuthority = (
    xGrid: Ref<any>,
    option: Ref<any>,
    authorityInfo: Ref<any>,
    buttonIds: Record<string, string>,
    hasExport = false,
    exportId = 'export-current-page',
) => {
    function setTableAuthority() {
        if (xGrid.value && authorityInfo?.value?.buttonList) {
            try {
                xGrid.value.setVisibleTools({
                    authorityList: authorityInfo.value.buttonList || [],
                    flag: authorityInfo.value.allButtonFlag,
                });
                option.value.columns = cloneDeep(option.value.columns);
                if (hasExport) {
                    option.value.toolbarConfig.export =
                        authorityInfo.value.allButtonFlag ||
                        authorityInfo.value.buttonList.includes(buttonIds[exportId]);
                }
            } catch {}
        }
    }
    watch(
        [() => option.value, () => authorityInfo.value],
        () => {
            nextTick(setTableAuthority);
        },
        { immediate: true },
    );
    onMounted(() => {
        setTableAuthority();
    });
};
