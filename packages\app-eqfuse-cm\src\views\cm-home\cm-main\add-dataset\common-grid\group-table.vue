<script lang="ts" setup>
import { ref, watch, nextTick } from 'vue';
import { getGroupTableOptions } from '@/views/cm-home/cm-main/add-dataset/config';
import type { ContextMap } from '@/views/cm-home/cm-main/add-dataset/interface';
import { setHeaderSelectFilter, showWarning, EesButtonTip } from '@futurefab/ui-sdk-ees-basic';
import { DATA_GROUP } from '@/constant/charts';
import CommonTitle from '@/views/cm-home/cm-main/add-dataset/common-title/index.vue';
import { t } from '@futurefab/vxe-table';
import { getReplenishGroup } from '@/views/cm-home/cm-main/add-dataset/config';
import EditGroupName from '@/views/cm-home/cm-main/add-dataset/edit-group-name/index.vue';
import { message } from '@futurefab/ant-design-vue';

interface Props {
  group: string;
  data: any;
  runByModel: string;
  columns: ContextMap[];
  groupStore: any;
  maxGroupCount?: number;
  allMetrologyCols: string[];
}
const props = withDefaults(defineProps<Props>(), {
  group: '',
  data: [],
  runByModel: 'wafer',
  columns: () => [],
  groupStore: {},
  maxGroupCount: 0,
  allMetrologyCols: () => []
});
const groupStore = props.groupStore;
const emits = defineEmits(['clickGrouping', 'reloadLeftBottomTableOptions']);
const shouldReload = defineModel<boolean>('shouldReload');

const groupXGrid = ref();
const option = ref(
  getGroupTableOptions(props.columns, 'wafer', [props.group], props.allMetrologyCols)
);
watch(
  () => props.allMetrologyCols,
  () => {
    option.value = getGroupTableOptions(
      props.columns,
      'wafer',
      [props.group],
      props.allMetrologyCols
    );
  },
  { deep: true }
);
const setHeader = () => {
  setTimeout(() => {
    setHeaderSelectFilter({
      xGrid: groupXGrid,
      tableData: groupXGrid.value.getTableData().fullData,
      columnFieldList: props.columns?.map((item) => item.key)
    });
  });
};
const handleCheckData = () => {
  const checkRecords = groupXGrid.value.getCheckboxRecords();
  groupStore.handleTempCheckData(props.group, checkRecords);
};
const tableEvent = {
  checkboxAll: (val: any) => {
    handleCheckData();
  },
  checkboxChange: (val: any) => {
    handleCheckData();
  }
};
const cellClick = (rowInfo: any) => {
  // 防止disabled状态也可以点击
  if (groupStore.copySet.has(props.group)) return;
  const { row, column } = rowInfo;
  if (column.type !== 'checkbox') {
    groupXGrid.value.toggleCheckboxRow(row);
    handleCheckData();
  }
};
const setDisabledRows = (rowInfo: any) => {
  if (groupStore.copySet.has(props.group)) return false;
  return true;
};
const count = ref(0);
const verifyMaxGroupCount = () => {
  if (props.maxGroupCount && groupStore.checkData?.size >= props.maxGroupCount) {
    showWarning(t('cm.tips.maxGroupCount', { count: props.maxGroupCount }));
    return false;
  }
  return true;
};
const handleCopy = (key: string, value: any) => {
  // 校验最大分组数
  if (!verifyMaxGroupCount()) return;
  if (groupStore.copySet.has(key)) {
    // delete
    count.value--;
    groupStore.handleDelete(key, value);
    groupStore.handleClearEmpty();
    groupStore.copySet.delete(key);
  } else {
    // copy
    if (count.value >= 9) {
      showWarning('cm.tips.maxCopyGroup');
      return;
    }
    let sub = 0;
    while (groupStore.checkData.has(DATA_GROUP[sub].label)) {
      sub++;
      if (sub > 26) return;
    }
    groupStore.handleStationCopy(DATA_GROUP[sub].label, value);
    groupStore.copySet.set(DATA_GROUP[sub].label, key);
    count.value++;
  }
};
const cellContextMenuEvent = (data: any) => {};
const contextMenuClickEvent = (data: any) => {
  const { menu, rows } = data;
  emits('clickGrouping', menu.code, rows, props.group);
};
// 改变整租数据的分组
const handleMoveDataset = (newGroupName: string) => {
  emits('clickGrouping', newGroupName, props.data, props.group);
};
// 设置分组为reference
const referenceChecked = ref<boolean>(false);
const setReferenceGroup = (checked: boolean) => {
  if (checked) {
    groupStore.referenceGroup.add(props.group);
  } else {
    groupStore.referenceGroup.delete(props.group);
  }
};
// 更改分组名
const isVerify = ref<boolean>(false);
const handleRename = (newGroupName: string, oldGroupName: string) => {
  if (newGroupName.trim() === '' || newGroupName === oldGroupName) return;
  if (groupStore.checkData.has(newGroupName)) {
    isVerify.value = true;
  } else {
    groupStore.handleRename(oldGroupName, newGroupName);
    message.success(t('cm.tips.modifySuccess'));
    // 更新右键菜单内容
    reloadTableOptions();
    emits('reloadLeftBottomTableOptions');
  }
};
// 更新右键菜单内容
const reloadTableOptions = () => {
  option.value = getGroupTableOptions(
    props.columns,
    'wafer',
    [props.group],
    props.allMetrologyCols
  );
};
// 没办法通过ref的方式调用，所有通过监听的方式实现table的刷新
watch(
  () => shouldReload.value,
  () => {
    reloadTableOptions();
  }
);
watch(
  () => props.data,
  () => {
    nextTick(() => {
      groupXGrid.value.loadData(props.data);
      setHeader();
    });
  },
  { deep: true, immediate: true }
);
watch(
  () => groupStore.key,
  () => {
    referenceChecked.value = groupStore.referenceGroup.has(props.group);
  },
  { immediate: true }
);
</script>

<template>
  <div class="group-table-warp">
    <common-title
      :title="`${runByModel === 'station' ? 'Station:' : 'Dataset:'}`"
      :count="props.data.length"
      :border="true"
      :borderBottom="false"
      :checkType="'checkbox'"
      :checkTooltip="
        referenceChecked ? $t('cm.tips.cancelReference') : $t('cm.tips.setAsReference')
      "
      v-model:isChecked="referenceChecked"
      @changeChecked="setReferenceGroup"
    >
      <template #left-icon-left-area>
        <EditGroupName
          :group-name="group"
          v-model:isVerify="isVerify"
          @handleRename="handleRename"
        />
      </template>
      <template #right-icon>
        <EesButtonTip
          v-if="runByModel === 'station'"
          is-border
          :marginRight="10"
          :icon="groupStore.copySet.has(group) ? '#icon-btn-delete' : '#icon-btn-copy'"
          :text="groupStore.copySet.has(group) ? $t('common.btn.delete') : $t('common.btn.copy')"
          @click="handleCopy(group, data)"
        />
        <a-dropdown
          :trigger="['click']"
          :placement="'bottomRight'"
          :arrow="{ pointAtCenter: true }"
          overlayClassName="move-dataset-dropdown"
        >
          <EesButtonTip
            is-border
            :icon="'#icon-btn-move-dataset'"
            :text="'Move ' + groupStore.groupTitle"
            @click.prevent
            :disabled="runByModel === 'station' && groupStore.copySet.has(group)"
          />
          <template #overlay>
            <a-menu>
              <p style="padding: 5px 14px; font-weight: bold">
                {{ 'Move ' + groupStore.groupTitle }}
              </p>
              <a-menu-divider />
              <div style="max-height: 300px; overflow: scroll">
                <template v-for="groupName in getReplenishGroup()">
                  <a-menu-item
                    :disabled="groupName.value === group"
                    @click="handleMoveDataset(groupName.value)"
                  >
                    <a>{{ groupName.label }}</a>
                  </a-menu-item>
                </template>
              </div>
            </a-menu>
          </template>
        </a-dropdown>
      </template>
    </common-title>
    <div class="group-table">
      <vxe-grid
        id="group-table-grid"
        :key="props.data"
        ref="groupXGrid"
        class="group-table-grid modal-table-small-radius"
        :checkbox-config="{
          checkMethod: setDisabledRows
        }"
        v-bind="option"
        :data="props.data"
        v-on="tableEvent"
        @cell-click="cellClick"
        @cell-menu="cellContextMenuEvent"
        @menu-click="contextMenuClickEvent"
      >
      </vxe-grid>
    </div>
  </div>
</template>

<style lang="less" scoped>
@import url('@/assets/style/variable.less');
.group-table-warp {
  border-radius: 0 0 4px 4px;
  height: 100%;
  .group-table {
    height: calc(100% - 53px);
  }
}
</style>
