export interface ContextMap {
  key: string;
  name: any;
}
export interface Attribute {
  columnName: string;
  csvIndex: number;
  groupKey: boolean;
  hide: boolean;
  loop: boolean;
  loopGroup: boolean;
  loopNo: boolean;
  loopStep: boolean;
  name: string;
  replaceColumn: string | null;
  schemaName: string | null;
  sort: number;
  sysName: string | null;
  time: boolean;
  timeFormat: string | null;
  trimming: boolean;
  chamber: boolean;
}
export interface MetaInputObject {
  attributeMap: { [key: string]: Attribute };
  contextMap: { [key: string]: Attribute };
  descriptorMap: { [key: string]: Attribute };
  parameterMap: { [key: string]: Attribute };
}
export interface MetaResult {
  key: string;
  name: string;
  loop: boolean;
  loopStep: boolean;
}
