/* 主要存放公共的样式 */
@import './variable.less';
@import './border-radius.less';
@import './flex.less';

.full-modal,
.modal-max-height {
  .ant-modal {
    max-width: 100%;
    top: 0;
    padding-bottom: 0;
    margin: 0;
  }

  .ant-modal-content {
    display: flex;
    flex-direction: column;
    height: calc(100vh);
  }

  .ant-modal-body {
    flex: 1;
  }

  .ant-modal-close {
    // color: @primary-font-color-3;
    color: var(--vxe-ui-table-icon-color);

    :hover {
      color: var(--primary-color);
    }
  }

  .ant-modal-close-icon {
    font-size: 16px !important;
  }
}

.ant-popover {
  z-index: 99999 !important;
}

.ant-picker-dropdown,
.ant-select-dropdown {
  z-index: 99999 !important;
}

.vxe-button.type--button.icon-type--iconButton,
.vxe-button.type--button.icon-type--svgButton {
  color: @primary-color;
}

.ant-input-borderless {
  background-color: @border-special-color !important;

  .ant-input-disabled {
    background-color: @border-special-color !important;
  }
}

// searchbar里面的Condition小圆角
.search-br-xs {
  border-radius: @border-radius-basis !important;
}

.sidebar-no-padding {
  .ant-drawer-body {
    padding: 0 !important;
  }
  .vxe-modal--content {
    padding: 0 !important;
  }
}

//在弹窗中，vxe-table的标题的左padding为0
.vxe-toolbar-padding-left-zero {
  .vxe-toolbar {
    padding-left: 0 !important;
  }
}

.ant-tooltip {
  z-index: 99999;
}

.eq-fuse-group-table-menu {
  max-height: 210px !important;
  overflow: auto !important;

  // 显示滚动条
  &:hover {
    &::-webkit-scrollbar-thumb {
      background-color: @bg-scroll-color;
    }
  }
  &::-webkit-scrollbar-corner {
    background-color: @bg-scroll-color;
  }

  &-title {
    background: none !important;
    .vxe-context-menu--link {
      font-weight: bold !important;
      cursor: default !important;
    }
  }

  &-item {
    .vxe-context-menu--link {
      font-weight: 400 !important;
    }
    .vxe-context-menu--link .vxe-context-menu--link-suffix{
      top: 0 !important;
    }
  }
}

.single-line-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.add-metrology-cell {
  background-color: @success-color-light !important;
}

.color-tip {
  margin-left: 10px;
  display: flex;
  align-items: center;
  &-item {
    border-radius: 11px;
    display: flex;
    align-items: center;
    padding: 2px 8px;
    margin-right: 10px;
    &-circle {
      height: 8px;
      width: 8px;
      border-radius: 50%;
      margin-right: 6px;
    }
    &-font {
      font-size: 12px;
      height: 18px;
      font-weight: normal;
      line-height: 18px;
    }
  }
}