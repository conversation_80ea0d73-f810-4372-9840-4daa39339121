{"name": "@futurefab/ui-app-eqfuse-ai", "version": "1.0.0", "description": "app-eqfuse-ai TODO", "private": true, "type": "module", "main": "dist/index.js", "module": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"dev": "vite serve", "build": "vite build", "test:unit": "vitest", "test-only": "vitest --run", "build-only": "vite build", "type-check": "vue-tsc --noEmit --composite false", "prepublishOnly": "run-p test-only build-only"}, "devDependencies": {"@futurefab/ui-sdk-api": "workspace:*", "@futurefab/ui-sdk-ees-basic": "workspace:*"}, "files": ["dist"], "publishConfig": {"registry": "https://npm.dev.futurefab.cn/"}, "repository": {"type": "git", "url": "git+https://git.futurefab.cn/tool-solution/eqfuse/frontend/fabsyn-frontend.git", "directory": "packages/app-eqfuse-ai"}, "futurefabUIMeta": {"type": "app", "stack": "vue", "externals": ["@futurefab/ant-design-vue", "@futurefab/ui-framework", "@futurefab/ui-sdk-api", "@futurefab/ui-sdk-ees-basic", "@futurefab/vxe-table", "vue", "vue-router"]}, "dependencies": {"@futurefab/vue-markdown-plus": "^0.1.6", "pdfjs-dist": "2.16.105", "unplugin-vue-components": "^28.8.0", "vite-plugin-static-copy": "^3.1.1", "vite-plugin-svg-icons": "^2.0.1"}}