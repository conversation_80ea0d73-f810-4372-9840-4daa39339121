<script lang="ts" setup>
import { ref, reactive, watch, computed, onBeforeMount, onUnmounted, nextTick } from 'vue';
import { xGetConfig, dConfig } from './config';
import {
  t,
  type VxeGridDefines,
  VXETable,
  VxeTableDefines,
  type VxeTablePropTypes
} from '@futurefab/vxe-table';
import { successInfo, showConfirm, showWarning, EesSidebar } from '@futurefab/ui-sdk-ees-basic';
import { Splitpanes, Pane } from 'splitpanes';
import {
  getKeyItemList,
  getUserDefineStateDetail,
  getUserDefineStateKeyList,
  getUserDefineStateItemList,
  getTableConfig,
  validateStateActivity,
  saveStateManager,
  updateStateManager
} from '@futurefab/ui-sdk-api';
import { stateManager as buttonIds } from '@/log-config/index';
import Sortable from 'sortablejs';
import { cloneDeep } from 'lodash-es';
import type { ConditionItem } from '@/components/search/interface';
import { getOmittedTooltip } from '@/utils/tools';

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  selected: {
    type: Object,
    default: {}
  },
  conditionParams: {
    type: Object,
    default: () => {
      return {} as ConditionItem;
    }
  }
});
const emits = defineEmits(['hideSidebar', 'doRequest']);
const params = reactive({
  areaList: [],
  modelList: [],
  chamberTypeList: [],
  rules: {
    activityClassName: [
      {
        required: true,
        message: t('common.form.selectValidation', {
          name: t('stateManager.label.activityClassType')
        })
      }
    ],
    activityFunctionName: [
      {
        required: true,
        message: t('common.form.inputValidation', {
          name: t('stateManager.label.activityFunctionName')
        })
      }
    ]
  }
});
const xFormRef = ref();
const xGrid = ref();
const dGrid = ref();
const xTable = reactive({
  options: {} as any,
  data: [] as any[],
  loading: false,
  rules: {} as VxeTablePropTypes.EditRules
});
const dTable = reactive({
  options: dConfig,
  data: [] as any[],
  loading: false,
  rules: {} as VxeTablePropTypes.EditRules
});
const onDelete = async (isKey = false) => {
  const thisTable = isKey ? xTable : dTable;
  const $table: any = isKey ? xGrid.value : dGrid.value;
  const removeList = $table.getCheckboxRecords();
  console.log('removeList', removeList);
  if (!removeList.length) {
    return showWarning('common.tip.selectData');
  }
  const type = await showConfirm({
    msg: t('common.tip.deleteConfirm', { total: removeList.length })
  });
  if (type !== 'confirm') return;
  $table.remove(removeList);
  const { tableData } = $table.getTableData();
  thisTable.data = tableData;
};
const onAddRow = async (isKey = false) => {
  const thisTable = isKey ? xTable : dTable;
  thisTable.data.forEach((v, i) => {
    v.seq = i;
  });
  const $table: any = isKey ? xGrid.value : dGrid.value;
  const record = {
    isAdd: true,
    rawId: '',
    itemName: '',
    itemNameOld: '',
    description: '',
    descriptionOld: '',
    seq: thisTable.data.length
  };
  // const { row: newRow } = await $table.insert(record);
  // await $table.setActiveRow(newRow);
  thisTable.data.push(record);
  console.log(thisTable.data);
  $table.loadData(thisTable.data);
};
const handleRowClassName = (argus: any) => {
  const { row } = argus;
  if (row.isAdd) {
    return 'is--new  row--new--border row--new';
  } else if (row.isEdit) {
    return 'row-edit--border';
  } else {
    return '';
  }
};
const onSave = async () => {
  console.log(inputData, xTable.data, dTable.data);
  xFormRef.value &&
    xFormRef.value
      .validate()
      .then(async () => {
        if (!xTable.data.length) {
          showWarning(t('common.tip.isRequired', { name: t('stateManager.title.stateKey') }));
          return;
        }
        VXETable.tableFun.useValid({
          data: xTable.data,
          xGird: xGrid,
          callback: async () => {
            if (!dTable.data.length) {
              showWarning(
                t('common.tip.isRequired', {
                  name: t('stateManager.title.stateItem')
                })
              );
              return;
            }
            VXETable.tableFun.useValid({
              data: dTable.data,
              xGird: dGrid,
              callback: async () => {
                const bodyParams = {
                  activityMstR2r: {
                    rawId: code.value === 'modify' ? rawId.value : null,
                    activityFunctionName: inputData.activityFunctionName,
                    activityClassName: inputData.activityClassName,
                    useYn: code.value === 'modify' ? props.selected?.selectData?.useYn : null
                  },
                  setupKeyExtMstR2rs: xTable.data.map((v) => {
                    return {
                      rawId: v.rawId,
                      // itemValue: v.itemName,
                      itemName: v.itemName,
                      displayName: v.displayName,
                      description: v.description,
                      seq: v.seq
                    };
                  }),
                  activityExtMstR2rs: dTable.data.map((v) => {
                    return {
                      rawId: v.rawId,
                      itemName: v.itemName,
                      displayName: v.displayName,
                      description: v.description,
                      seq: v.seq
                    };
                  }),
                  areaList: inputData.area,
                  eqpModelList: inputData.eqpModel,
                  chamberList: inputData.chamberType
                };
                // console.log(bodyParams, 222);
                const res = await (code.value === 'add'
                  ? saveStateManager({
                      headerParams: {
                        log: 'Y',
                        page: buttonIds.pageId,
                        action: buttonIds.user.add
                      },
                      bodyParams
                    })
                  : updateStateManager({
                      headerParams: {
                        log: 'Y',
                        page: buttonIds.pageId,
                        action: buttonIds.user.modify
                      },
                      bodyParams
                    }));
                if (res.status !== 'SUCCESS') return;
                successInfo('common.tip.saveSuccess');
                emits('hideSidebar', true);
              }
            });
          }
        });
      })
      .catch((err: any) => {
        console.log(err);
      });
};
const handleClick = async (code: string) => {
  code === 'save' ? onSave() : emits('hideSidebar');
};
const gridEvents = (isKey = false) => {
  return {
    toolbarToolClick: ({ code }: VxeGridDefines.ToolbarToolClickEventParams) => {
      console.log(isKey, code);
      switch (code) {
        case 'add':
          onAddRow(isKey);
          break;
        case 'deleteData':
          onDelete(isKey);
          break;
        // case 'save':
        //     onSave();
        //     break;
      }
    },
    editActived: (argus: any) => {
      const { row, column } = argus;
      const { field } = column;
      console.log('editActived:', field);
      if (!isKey && ['itemName', 'description'].includes(field)) {
        column.editRender.enabled = row.rawId && props.selected?.selectData?.useYn ? false : true;
      }
    }
  };
};
let sortable1: any;
const rowDrop = () => {
  const $grid = xGrid.value;
  sortable1 = Sortable.create(
    $grid.$el.querySelector('.body--wrapper>.vxe-table--body tbody') as HTMLElement,
    {
      handle: '.drag-btn',
      onStart: () => {
        if (!editFlag.value) return showWarning('common.tip.notModify');
      },
      onEnd: ({ newIndex, oldIndex }: Sortable.SortableEvent) => {
        const { fullData } = $grid.getTableData();
        const currRow = fullData.splice(oldIndex, 1)[0];
        console.log(currRow);
        fullData.splice(newIndex, 0, currRow);
        fullData.forEach((v: any, i: any) => {
          v.seq = i;
        });
        xTable.data = [];
        setTimeout(() => {
          xTable.data = cloneDeep(fullData);
        }, 1);
        console.log(xTable.data);
      }
    }
  );
};
let sortable2: any;
const rowDrop2 = () => {
  const $grid = dGrid.value;
  sortable2 = Sortable.create(
    $grid.$el.querySelector('.body--wrapper>.vxe-table--body tbody') as HTMLElement,
    {
      handle: '.drag-btn',
      onStart: () => {
        // if (!editFlag.value) return showWarning('common.tip.notModify');
      },
      onEnd: ({ newIndex, oldIndex }: Sortable.SortableEvent) => {
        const { fullData } = $grid.getTableData();
        const currRow = fullData.splice(oldIndex, 1)[0];
        console.log(currRow);
        fullData.splice(newIndex, 0, currRow);
        fullData.forEach((v: any, i: any) => {
          v.seq = i;
        });
        dTable.data = [];
        setTimeout(() => {
          dTable.data = cloneDeep(fullData);
        }, 1);
        console.log(dTable.data);
      }
    }
  );
};
let initTime1: any;
nextTick(() => {
  // 加载完成之后在绑定拖动事件
  initTime1 = setTimeout(() => {
    rowDrop();
  }, 500);
});
let initTime2: any;
nextTick(() => {
  // 加载完成之后在绑定拖动事件
  initTime2 = setTimeout(() => {
    rowDrop2();
  }, 500);
});
onUnmounted(() => {
  clearTimeout(initTime1);
  if (sortable1) sortable1.destroy();
  clearTimeout(initTime2);
  if (sortable2) sortable2.destroy();
});
const inputData = reactive({
  activityClassName: 'Share',
  activityFunctionName: '',
  area: [],
  eqpModel: [],
  chamberType: []
});
const typeOptions = [
  {
    label: 'Share',
    value: 'Share'
  }
];
const code = ref('add');
const rawId = ref();
const editFlag = ref(true);
const getDetail = (rawId: number) => {
  getUserDefineStateDetail({
    headerParams: {
      log: 'Y',
      page: buttonIds.pageId,
      action: 'user-add-detail'
    },
    bodyParams: { rawId }
  }).then((res: any) => {
    const resData = res?.data;
    if (resData?.areaList) handleChange(resData?.areaList, 'area', showList.value.indexOf('area'));
    if (resData?.eqpModelList)
      handleChange(resData?.eqpModelList, 'eqpModel', showList.value.indexOf('eqpModel'));
    inputData.area = resData?.areaList;
    inputData.eqpModel = resData?.eqpModelList;
    inputData.chamberType = resData?.chamberList;
  });
  getUserDefineStateKeyList({
    headerParams: {
      log: 'Y',
      page: buttonIds.pageId,
      action: 'user-add-key-list'
    },
    bodyParams: { rawId }
  }).then((res: any) => {
    xTable.data = res?.data?.map((v: any) => {
      return {
        ...v,
        itemNameOld: v.itemName
      };
    });
  });
  getUserDefineStateItemList({
    headerParams: {
      log: 'Y',
      page: buttonIds.pageId,
      action: 'user-add-state-item-list'
    },
    bodyParams: { rawId }
  }).then((res: any) => {
    dTable.data = res?.data?.map((v: any) => {
      return {
        ...v,
        itemNameOld: v.itemName
      };
    });
    /* if (!res?.data?.length) return;
        const col = dGrid.value.columns.find((v: any) => v.editRender);
        col.editRender.name = '$select';
        const options = res?.data?.map((v: any) => {
            return {
                label: v.itemName,
                value: v.itemName,
            };
        });
        console.log('options', options);
        col.editRender.options = options;
        col.editRender.events = {
            change: (argus: any, event: any) => {
                const { data, row } = argus;
                console.log(row.itemName, 22, row.itemNameOld, 22, event.value);
                row.itemName = event?.value;
                row.isEdit = event.value === row.itemNameOld ? false : true;
            },
        }; */
  });
};
const setRules = () => {
  getTableConfig({
    headerParams: {
      log: 'Y',
      page: buttonIds.pageId,
      action: 'user-add-table-config-key'
    },
    bodyParams: {
      requestUrl: '/stateManager/userDefineState/viewUserDefineStateKeyList'
    }
  }).then((responents: any) => {
    xTable.rules = VXETable.tableFun.setRules({
      responents,
      columns: xTable.options.columns as Array<VxeTableDefines.ColumnInfo>
    });
    (xTable.rules as any)['displayName'] = [
      {
        trigger: 'blur',
        type: 'string',
        pattern: new RegExp(/^[a-zA-Z0-9_]+$/),
        message: t('common.tip.inputRestriction')
      }
    ];
    console.log('xTable.rules key----', xTable.rules);
  });
  getTableConfig({
    headerParams: {
      log: 'Y',
      page: buttonIds.pageId,
      action: 'user-add-table-config-item'
    },
    bodyParams: {
      requestUrl: '/stateManager/userDefineState/viewUserDefineStateItemList'
    }
  }).then((responents: any) => {
    dTable.rules = VXETable.tableFun.setRules({
      responents,
      columns: dTable.options.columns as Array<VxeTableDefines.ColumnInfo>
    });
    const initValidator = dTable.rules['itemName'][0].validator;
    dTable.rules['itemName'][0].validator = (val: VxeTableDefines.ValidatorErrorParams) => {
      const result = initValidator!(val);
      // console.log('rules--', initValidator, val, result);
      if (result) return result;
      const { row } = val;
      const reg = new RegExp(/[^0-9a-zA-Z_]/g);
      if (reg.test(row.itemName)) {
        return new Error(t('common.tip.patternSetupKeyName'));
      }
    };
    (dTable.rules as any)['displayName'] = [
      {
        trigger: 'blur',
        type: 'string',
        pattern: new RegExp(/^[a-zA-Z0-9_]+$/),
        message: t('common.tip.inputRestriction')
      }
    ];
  });
};
const getOptions = async () => {
  const res = await getKeyItemList({
    headerParams: {
      log: 'Y',
      page: buttonIds.pageId,
      action: 'user-add-key-item-list'
    },
    bodyParams: {}
  });
  const resData = res.data;
  console.log(resData);
  console.log('editFlag', editFlag.value);
  xTable.options = xGetConfig({
    editFlag: editFlag.value,
    itemList: resData?.map((v: any) => {
      return {
        label: v,
        name: v
      };
    })
  });
  /* if (props.selected.code === 'add') {
        const col = dGrid.value.columns.find((v: any) => v.editRender);
        col.editRender.name = '$input';
        col.editRender.events = {
            keyup: (argus: any, event: any) => {
                const { data, row } = argus;
                console.log(row.itemName, 11, row.itemNameOld, 11, event.value);
                row.itemName = event?.value;
                row.isEdit = event.value === row.itemNameOld ? false : true;
            },
        };
    } */
};
onBeforeMount(() => {
  console.log('mounted', props.selected);
  editFlag.value = props.selected.code === 'add';
  getOptions();
  setRules();
  if (props.selected.code === 'add') return;
  code.value = props.selected.code;
  rawId.value = props.selected.selectData.rawId || null;
  const { activityFunctionName, activityClassName } = props.selected.selectData;
  inputData.activityFunctionName = activityFunctionName;
  inputData.activityClassName = activityClassName;
  getDetail(rawId.value);
});
const checkName = async () => {
  if (!inputData.activityFunctionName) return;
  await validateStateActivity({
    headerParams: {
      log: 'Y',
      page: buttonIds.pageId,
      action: 'user-add-validate'
    },
    bodyParams: {
      activityClassName: 'Share',
      activityFunctionName: inputData.activityFunctionName
    }
  });
};
const sidebar = reactive({
  buttonList: [
    { name: 'common.btn.save', code: 'save', status: 'primary' },
    { name: 'common.btn.cancel', code: 'cancel' }
  ]
});
const showList = computed(() => {
  const keys = Object.keys(props.conditionParams);
  if (keys) {
    params.areaList = props.conditionParams['area'] ? props.conditionParams['area'].list : [];
    params.modelList = props.conditionParams['eqpModel']
      ? props.conditionParams['eqpModel'].list2
      : [];
    params.chamberTypeList = props.conditionParams['chamberType']
      ? props.conditionParams['chamberType'].list2
      : [];
  }
  console.log('showList', props.conditionParams);
  return keys;
});
const handleChange = (value: string | string[], item: string, index: number) => {
  (inputData as any)[item] = value;
  if (item === 'area') {
    inputData['eqpModel'] = [];
    inputData['chamberType'] = [];
  }
  if (item === 'eqpModel') {
    inputData['chamberType'] = [];
  }
  emits('doRequest', { value, item, index, keys: showList.value, isSidebar: true });
};
watch(
  () => inputData.activityFunctionName,
  (val) => {
    if (val) inputData.activityFunctionName = val?.replace(/[^0-9a-zA-Z_]/g, '');
  }
);
const checkMethod = ({ row, column }: any) => {
  return row.rawId && props.selected?.selectData?.useYn ? false : true;
};
</script>

<template>
  <ees-sidebar v-if="show" :button-list="sidebar.buttonList" @clickbutton="handleClick">
    <template #sidebar_content>
      <div class="add-state-manager">
        <a-form
          ref="xFormRef"
          auto-complete="off"
          :colon="false"
          label-align="left"
          :rules="params.rules"
          :model="inputData"
        >
          <a-form-item
            :label="t('stateManager.label.activityFunctionName')"
            name="activityFunctionName"
          >
            <a-input
              v-model:value="inputData.activityFunctionName"
              :disabled="['modify'].includes(code)"
              @blur="checkName"
            />
          </a-form-item>
          <a-form-item
            :label="t('stateManager.label.activityClassType')"
            class="margin-form"
            name="activityClassName"
          >
            <a-select
              v-model:value="inputData.activityClassName"
              :options="typeOptions"
              :disabled="['modify'].includes(code)"
            />
          </a-form-item>
          <a-form-item
            v-if="showList.length > 0 && showList.includes('area')"
            :label="t('common.title.area')"
          >
            <a-select
              v-model:value="inputData.area"
              :not-found-content="t('eesBasic.tips.noData')"
              mode="multiple"
              :max-tag-count="10"
              :allow-clear="true"
              @change="
                (value: string | string[]) => {
                  handleChange(value, 'area', showList.indexOf('area'));
                }
              "
            >
              <a-select-option
                v-for="item in params.areaList"
                :key="item"
                :value="item"
                :title="item"
                >{{ item }}</a-select-option
              >
              <template #maxTagPlaceholder="omittedValues">
                <a-tooltip :title="getOmittedTooltip(omittedValues)" placement="bottom">
                  <span style="font-weight: normal">+{{ omittedValues.length }}</span>
                </a-tooltip>
              </template>
            </a-select>
          </a-form-item>
          <a-form-item
            v-if="showList.length > 0 && showList.includes('eqpModel')"
            class="margin-form"
            :label="t('common.title.eqpModel')"
          >
            <a-select
              v-model:value="inputData.eqpModel"
              :not-found-content="t('eesBasic.tips.noData')"
              mode="multiple"
              :max-tag-count="10"
              :allow-clear="true"
              @change="
                (value: string | string[]) => {
                  handleChange(value, 'eqpModel', showList.indexOf('eqpModel'));
                }
              "
            >
              <a-select-option
                v-for="item in params.modelList"
                :key="item"
                :value="item"
                :title="item"
                >{{ item }}</a-select-option
              >
              <template #maxTagPlaceholder="omittedValues">
                <a-tooltip :title="getOmittedTooltip(omittedValues)" placement="bottom">
                  <span style="font-weight: normal">+{{ omittedValues.length }}</span>
                </a-tooltip>
              </template>
            </a-select>
          </a-form-item>
          <a-form-item
            v-if="showList.length > 0 && showList.includes('chamberType')"
            :label="t('common.title.chamberType')"
          >
            <a-select
              v-model:value="inputData.chamberType"
              :not-found-content="t('eesBasic.tips.noData')"
              mode="multiple"
              :max-tag-count="10"
              :allow-clear="true"
              @change="
                (value: string | string[]) => {
                  handleChange(value, 'chamberType', showList.indexOf('chamberType'));
                }
              "
            >
              <a-select-option
                v-for="item in params.chamberTypeList"
                :key="item"
                :value="item"
                :title="item"
                >{{ item }}</a-select-option
              >
              <template #maxTagPlaceholder="omittedValues">
                <a-tooltip :title="getOmittedTooltip(omittedValues)" placement="bottom">
                  <span style="font-weight: normal">+{{ omittedValues.length }}</span>
                </a-tooltip>
              </template>
            </a-select>
          </a-form-item>
        </a-form>
        <div class="add-state-manager-content">
          <splitpanes
            class="default-theme no-bottom-border no-top-border"
            style="height: 100%; width: 100%"
            horizontal
          >
            <pane class="cardItem">
              <vxe-grid
                ref="xGrid"
                class="vxe-toolbar-top-left-radius-xs vxe-toolbar-top-right-radius-xs vxe-table-bottom-left-radius-xs vxe-table-bottom-right-radius-xs"
                v-bind="xTable.options"
                :data="xTable.data"
                :edit-rules="xTable.rules"
                :row-class-name="handleRowClassName"
                v-on="gridEvents(true)"
              >
                <template #dragBtn>
                  <span class="drag-btn iconfont icon-sort icon-normal-color"> </span>
                </template>
              </vxe-grid>
            </pane>
            <pane class="cardItem">
              <vxe-grid
                ref="dGrid"
                class="vxe-toolbar-top-left-radius-xs vxe-toolbar-top-right-radius-xs vxe-table-bottom-left-radius-xs vxe-table-bottom-right-radius-xs"
                v-bind="dTable.options"
                :data="dTable.data"
                :edit-rules="dTable.rules"
                :row-class-name="handleRowClassName"
                :checkbox-config="{ checkMethod }"
                v-on="gridEvents()"
              >
                <template #dragBtn>
                  <span class="drag-btn iconfont icon-sort icon-normal-color"> </span>
                </template>
              </vxe-grid>
            </pane>
          </splitpanes>
        </div>
      </div>
    </template>
  </ees-sidebar>
</template>

<style lang="less" scoped>
@import url('@/assets/style/variable.less');
@ns: add-state-manager;
.@{ns} {
  height: 100%;
  display: flex;
  flex-direction: column;
  :deep(.ant-form) {
    display: flex;
    flex-wrap: wrap;
  }
  :deep(.ant-form-item) {
    .ant-form-item-label {
      label {
        width: 170px;
      }
    }
    width: calc(50% - 10px);
  }
  &-content {
    flex-grow: 1;
    overflow: auto;
  }
}
.drag-btn {
  cursor: move;
}
.margin-form {
  margin-left: @margin-btn;
  &.ant-form-item {
    &:deep(.ant-form-item-label) {
      label {
        width: 142px;
      }
    }
  }
}
</style>
