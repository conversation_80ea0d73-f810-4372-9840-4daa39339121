import { timeArr, countArr, percentArr } from '../../config';
import type { TrimType, TypeTrimOption, TrimFormMap, TrimFormArr } from '../../interface';

export const TrimOptions: TypeTrimOption = [
    { value: 'COUNT', label: 'COUNT' },
    { value: 'TIME', label: 'TIME' },
    { value: 'PERCENT', label: '%' },
];

export const getLabels = (type: TrimType): TrimFormArr => {
    switch (type) {
        case 'TIME':
            return timeArr;
        case 'COUNT':
            return countArr;
        case 'PERCENT':
            return percentArr;
    }
};
