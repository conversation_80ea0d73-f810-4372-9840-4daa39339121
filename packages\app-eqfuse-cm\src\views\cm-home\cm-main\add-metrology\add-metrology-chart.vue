<script setup lang="ts">
import { computed, ref, nextTick, reactive, type Ref } from 'vue';
import Chart from '@/components/chart.vue';
import { useBaseStore } from '@futurefab/ui-sdk-stores';
import { formatterYSM, getChartAxisRange } from '@/utils';
import { getReplenishGroup } from '../add-dataset/config';
import { colorList } from '@futurefab/ui-sdk-ees-charts';
import CmGroupLegend from '@/components/cm-group-legend/index.vue';
import type { CmLegend } from '@/components/cm-group-legend/interface';
import dayjs from 'dayjs';
import { EesButtonTip } from '@futurefab/ui-sdk-ees-basic';
const props = withDefaults(defineProps<{
    beforeMetrologyCol: string[];
    curAddCol: string[];
    data: any[];
}>(), {
});
const baseStore = useBaseStore();
const chartData = ref<any[]>([]);
const parameterOptions = computed(() => 
    [...props.curAddCol, ...props.beforeMetrologyCol]
    .map(item => ({value: item, label: item.slice(0, -('-metrology'.length))}))
);
const parameter = ref('');
const showModel = ref(false);
const handleHide = () => {
    showModel.value = false;
};
const isDeleteBlank = ref(false);
const chooseGroup = ref(true);
const options = ref({});
const chartLegend: Ref<CmLegend[]> = ref([]);
const apply = () => {
    chartData.value.forEach((item: { originRow: { datasetGroup: any; }; datasetGroup: any; }) => {
        item.originRow.datasetGroup = item.datasetGroup;
    });
    showModel.value = false;
};
const groupsList = [...getReplenishGroup(), {label: 'Ungrouped', value: 'Ungrouped'}]
const chartRef = ref();
const chartContentRef = ref();
const getLineColor = () => {
  return baseStore.theme === 'light' ? '#ECEEF0' : '#556070';
};
const getLabelColor = () => {
  return baseStore.theme === 'light' ? '#4e5969' : '#e2e4e7';
};
const getBrushBorderColor = () => {
  return baseStore.theme === 'light' ? '#007FAC' : '#49C6F2';
};
const getBrushColor = () => {
  return baseStore.theme === 'light' ? '#007FAC40' : '#49C6F240';
};
let beforeSortAxis: string[] = [];
const setOptions = (legend?: CmLegend[]) => {
    const data = chartData.value
        .filter((item: { [x: string]: any; }) =>
            item[parameter.value] 
            && !isNaN(Number(item[parameter.value]))
        );
    const groupMap: Record<string, any[]> = {};
    data.forEach((item: { datasetGroup: any; }) => {
        const group = item.datasetGroup;
        if (groupMap[group]) {
            groupMap[group]!.push(item);
        } else {
            groupMap[group] = [item];
        }
    });
    const series: any[] = [];
    const sortGroup = Object.entries(groupMap).sort((a, b) => {
        const aIndex = groupsList.findIndex(item => item.value == a[0]);
        const bIndex = groupsList.findIndex(item => item.value == b[0]);
        return aIndex - bIndex;
    });
    const cLen = colorList.length;
    if (!legend) {
        const lengedData = sortGroup.map(item => item[0]);
        chartLegend.value = lengedData.map((item, index) => ({
            id: index,
            name: item,
            color: item === 'Ungrouped' ? 'grey' : colorList[index % cLen],
            show: true,
        }));
    }
    const allAxisSet = new Set<string>();
    let sortAxisList: string[] = [];
    let aAxisToIndex: Record<string, number> = {};
    sortGroup.forEach(([group, list], index) => {
        if (!legend || legend.find(item => item.name === group)?.show) {
            list.forEach(item => {
                allAxisSet.add(item.StartTime);
            });
            series.push({
                name: group,
                type: 'scatter',
                data: list.map(item => ({
                    info: item,
                    value: [item.StartTime, item[parameter.value]],
                })),
                itemStyle: {
                    color: group === 'Ungrouped' ? 'grey' : colorList[index % cLen],
                }
            });
        }
    });
    if (isDeleteBlank.value) {
        sortAxisList = [...allAxisSet]
            .sort((a, b) => new Date(a).getTime() - new Date(b).getTime());
        sortAxisList.forEach((time, index) => {
            aAxisToIndex[time] = index;
        });
        series.forEach(seriesItem => {
            seriesItem.data.forEach((item: any) => {
                item.value[0] = aAxisToIndex[item.value[0]];
            });
        });
        beforeSortAxis = sortAxisList;
    }
    const brush = chooseGroup.value ?  {
        outOfBrush: {
          colorAlpha: 0.5
        },
        brushStyle: {
            borderColor: getBrushBorderColor(),
            color: getBrushColor()
        },
        transformable: false,
        iconStyle: {
            opacity: 0
        },
        xAxisIndex: 0,
        yAxisIndex: 1,
      } : {};
    const toolbox = chooseGroup.value ? {
        left: '100%',
        iconStyle: {
            opacity: 0
        }
    } : {
        show: true,
        showTitle: false,
        feature: {
            dataZoom: {
            enableBrushBack: true
            },
            brush: {
            type: ['rect']
            }
        },
        iconStyle: {
            opacity: 0
        }
    };
    const res = {
        animation: false,
        grid: { left: 20, right: 30, top: 10, bottom: 0, containLabel: true },
        legend: { show: false },
        xAxis: {
            // type: 'category',
            type: isDeleteBlank.value ? 'category' : 'time',
            data: isDeleteBlank.value ? sortAxisList.map(item => dayjs(item).format('YYYY/MM/DD\nHH:mm')) : undefined,
            id: 'xTime',
            axisLabel: {
                color: getLabelColor(),
                formatter: isDeleteBlank.value ? undefined : '{yyyy}/{MM}/{dd}\n{HH}:{mm}',
                hideOverlap: true,
                fontFamily: 'Nunito Sans',
            },
            position: 'bottom',
            boundaryGap: ['3%', '3%'],
            axisLine: {
                onZero: false,
                show: true,
                lineStyle: { width: 2, color: getLineColor() }
            },
            axisTick: {
                show: true,
                lineStyle: { width: 2, color: getLineColor() }
            }
        },
        yAxis: {
            splitNumber: 5,
            axisLabel: {
                color: getLabelColor(),
                hideOverlap: true,
                formatter: (v: any) => formatterYSM(v, 4)
            },
            axisLine: {
                show: true,
                lineStyle: { width: 2, color: getLineColor() }
            },
            splitLine: {
                show: true,
                showMaxLine: false,
            },
            axisTick: {
                show: true,
                lineStyle: { width: 2, color: getLineColor() }
            },
            scale: true,
            name: '',
            min: ({ min, max }: any) => {
                return getChartAxisRange(min, max, 0.1)[0];
            },
            max: ({ min, max }: any) => {
                return getChartAxisRange(min, max, 0.1)[1];
            }
        },
        series,
        brush,
        toolbox
    };
    options.value = res;
    moveList.show = false;
};


const buildChartData = () => {
    return props.data.map((item: any) => ({...item, originRow: item}));
};
const chartClick = (event: any) => {
    console.log(event);
}
const moveList = reactive({
    show: false,
    left: 0 as (number | null),
    top: 0 as (number | null),
});
const beforeRange = reactive({
    x: [],
    y: [],
})
const brushEnd = (event: any) => {
    if (event?.areas?.length) {
        let xRange = event.areas[0].coordRange[0];
        const yRange = event.areas[0].coordRange[1];
        const instance = chartRef.value.getChartInstance();
        const xPixel = xRange.map((item: any) => instance.convertToPixel({xAxisIndex: 0}, item))
        const yPixel = yRange.map((item: any) => instance.convertToPixel({yAxisIndex: 0}, item))
        const clientWidth = chartContentRef.value.clientWidth;
        const clientHeight = chartContentRef.value.clientHeight;
        const gap = 10;
        const paneWidth = 170;

        if (clientWidth - xPixel[1] > gap  + paneWidth) {
            // 右边位置够
            moveList.left = xPixel[1] + 10;
        } else {
            moveList.left = Math.max(xPixel[0] - (gap  + paneWidth), gap * 2);
        }
        const paneHeight = 366;
        if (clientHeight - yPixel[0] > paneHeight - gap) {
            // 下边位置够
            moveList.top = yPixel[0] - gap;
        } else {
            moveList.top = Math.max(clientHeight - (paneHeight + 3 * gap), 0);
        }

        beforeRange.x = xRange;
        beforeRange.y = yRange;
        moveList.show = true;
    } else {
        moveList.show = false;
    }
}
const moveGroup = (group: string) => {
    const chooseData = chartData.value.filter((item: { [x: string]: any; StartTime: string|number|Date; }) => {
        const time = new Date(item.StartTime).getTime();
        const y = item[parameter.value];
        const beforeX0 = isDeleteBlank.value ? new Date(beforeSortAxis[beforeRange.x[0]]).getTime() : beforeRange.x[0];
        const beforeX1 = isDeleteBlank.value ? new Date(beforeSortAxis[beforeRange.x[1]]).getTime() : beforeRange.x[1];
        return time >= beforeX0
            && time <= beforeX1
            && y >= beforeRange.y[0]
            && y <= beforeRange.y[1];
    });
    if (chooseData.length) {
        chooseData.forEach((item: { datasetGroup: string; }) => item.datasetGroup = group);
        setOptions();
    }
    moveList.show = false;
}
const changeMode =  () => {
    setOptions();
};

const chartResize = (event: any) => {
    console.log(event, 'brush');
    moveList.show = false;
};

const reset = () => {
    chartData.value = buildChartData();
    setOptions();
};
const changeParam = () => {
    setOptions();
}
const legendChange =() => {
    setOptions(chartLegend.value);
};
const onDeleteBlank = () => {
    isDeleteBlank.value = !isDeleteBlank.value;
     setOptions(chartLegend.value);
}
defineExpose({
    show: () => {
        parameter.value = parameterOptions.value?.[0].value;
        showModel.value = true;
        chartData.value = buildChartData();
        setOptions();
    }
});
const baseClass = 'add-metrology-chart';
</script>

<template>
    <Teleport to="body">
        <vxe-modal
            v-model="showModel"
            :title="$t('cm.title.inputMetrologyData')"
            :mask-closable="false"
            :width="'90%'"
            :height="'90%'"
            :class-name="'sidebar-no-padding' + ' ' + baseClass "
            resize
            show-footer
            @hide="handleHide"
            @close="handleHide"
        > 
            <div :class="baseClass + '-box'">
                <div :class="baseClass + '-title'">
                    <div :class="baseClass + '-title-left'">
                        <p class="single-line-ellipsis">{{ 'Parameter' }}</p>
                        <a-select
                            v-model:value="parameter"
                            :options="parameterOptions" 
                            style="margin-right: 10px; width: 200px"
                            @change="changeParam"
                        ></a-select>
                    </div>
                    <div :class="baseClass + '-title-right'" >
                        <p  :class="baseClass + '-title-right-mode'">{{ 'Grouping Mode' }}</p>
                        <a-tooltip
                            placement="leftTop"
                        >
                            <template #title>
                                <p>{{ 'Using the “Grouping Mode”, you can adjust the grouping of data points by selecting them with the mouse box.' }}</p>
                            </template>
                            <i class="iconfont icon-help "></i>
                        </a-tooltip>

                        <a-switch v-model:checked="chooseGroup" @change="changeMode"></a-switch>
                        <p :class="baseClass + '-title-right-reset'" @click="reset">{{ 'Reset' }}</p>
                        <EesButtonTip
                            :margin-right="0"
                            :is-border="true"
                            icon="#icon-btn-blank-delete"
                            :text="$t('eesCharts.commonBtn.deleteBlank')"
                            :is-active="isDeleteBlank"
                            style="margin-left: 10px;"
                            @on-click="onDeleteBlank"
                        />
                    </div> 
                </div>
                <div ref="chartContentRef" :class="baseClass + '-content'">
                    <CmGroupLegend
                        :legend="chartLegend"
                        :title="parameterOptions.find(item => item.value === parameter)?.label"
                        @legendChange="legendChange"
                    >
                        <template #chart>
                            <div ref="chartContentRef" :class="baseClass + '-content-chart-box'">
                                <div
                                    v-if="moveList.show"
                                    :class="baseClass + '-content-group-list'"
                                    :style="{
                                        top: moveList.top + 'px',
                                        left: moveList.left + 'px',
                                    }"    
                                >
                                    <p>{{ 'Move Dataset' }}</p>
                                    <ul class="scrollbar-wrap-hover">
                                        <li
                                            v-for="item in groupsList"
                                            :key="item.label"
                                            @click="moveGroup(item.value)"
                                        >
                                            {{ `Dataset ${item.label}` }}
                                        </li>
                                    </ul>
                                </div>
                                <Chart ref="chartRef"
                                    :options="options"
                                    :useZoom="!chooseGroup"
                                    :use-brush="chooseGroup"
                                    @chartClick="chartClick"
                                    @brush-end="brushEnd"
                                    @chart-resize="chartResize"
                                />
                            </div>
                        </template>
                    </CmGroupLegend>    
                </div>
            </div>

            <template #footer>
                <a-button type="primary" style="margin-right: 10px" @click="apply">{{
                    $t('common.btn.apply')
                }}</a-button>
                <a-button @click="handleHide">{{ $t('common.btn.cancel') }}</a-button>
            </template>
        </vxe-modal>
    </Teleport>
</template>

<style scoped lang="less">
@import url('@/assets/style/variable.less');
.add-metrology-chart {
    overflow: hidden;
    &-box {
        height: 100%;
        width: 100%;
    }
    &-title {
        height: 52px;
        width: 100%;
        padding: 0 14px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        &-left {
            display: flex;
            align-items: center;
            justify-content: space-between;
            p {
                margin-right: 10px;
                flex-shrink: 0;
                font-size: 16px;
                font-weight: bold;
                line-height: 22px;
                color: @text-title-color; 
            }
        }
        &-right {
            display: flex;
            align-items: center;
            justify-content: space-between;
            p {
                font-size: 14px;
                font-weight: normal;
                line-height: 22px;
                color: @text-title-color;
            }
            &-mode {
                margin-right: 4px;
            }
            .icon-help {
                color: @text-hint-color;
                margin-right: 10px;
            }
            &-reset {
                margin-left: 18px;
                cursor: pointer;
                &:hover {
                    color: @primary-color;
                }
            }
        }
    }
    &-content {
        height: calc(100% - 52px);
        &-chart-box {
            position: relative;
            height: 100%;
            width: 100%;
        }
        overflow: hidden;
        :deep(.add-metrology-cell) {
            background-color: @success-color-light !important;
        }
        &-group-list {
            position: absolute;
            width: 170px;
            padding: 0px;
            background-color: @bg-block-color;
            z-index: 2;
            max-height: 364px;
            box-shadow: 0px 2px 24px 0px rgba(2, 12, 29, 0.1);
            p {
                height: 44px;
                padding: 11px 14px;
                font-family: Nunito Sans;
                font-size: 16px;
                font-weight: bold;
                line-height: 22px;
                letter-spacing: 0px;
                color: @text-title-color;
                border-bottom: 1px solid @border-color;
            }
            ul {
                width: 100%;
                padding: 4px;
                max-height: 320px;
                overflow-y: auto;
                li {
                    height: 32px;
                    padding: 6px;
                    font-family: Nunito Sans;
                    font-size: 14px;
                    font-weight: normal;
                    line-height: 20px;
                    color: @text-subtitle-color;
                    cursor: pointer;
                    &:hover {
                        background-color: @bg-hover-1-color;
                    }
                }
            }
        }
    }
}

</style>