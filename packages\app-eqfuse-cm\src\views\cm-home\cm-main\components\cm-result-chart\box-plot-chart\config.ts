import type { BoxPlotResult, CmFilterObject } from '../interface';
import { VXETable } from '@futurefab/vxe-table';
import { CHART_CONSTANT, DATA_GROUP } from '@/constant/charts';
import { add, subtract, multiply, divide, countDecimalPlaces } from '@/utils/math';
import moment from 'moment';
import { cmChartContextFilter, cmChartLegendFilter } from '@/utils';
import type { CmLegend } from '@/components/cm-group-legend/interface';
/**
 * 计算箱状图最大值、最小值、中位值、起始值、结束值
 */
export const percentile = (datas: number[], p: number) => {
  if (datas.length === 1) return datas?.[0];

  if (p >= 100) return datas[datas.length - 1];

  const position = ((datas.length + 1) * p) / 100.0;
  let leftNumber = 0.0;
  let rightNumber = 0.0;

  const n = (p / 100.0) * (datas.length - 1) + 1.0;

  if (position >= 1) {
    leftNumber = datas[Math.floor(n) - 1];
    rightNumber = datas[Math.floor(n)];
  } else {
    leftNumber = datas?.[0]; // first data
    rightNumber = datas?.[1]; // first data
  }

  if (leftNumber === rightNumber) {
    return leftNumber;
  } else {
    const part = n - Math.floor(n);
    return leftNumber + part * (rightNumber - leftNumber);
  }
};
// 计算盒须图数据
export const getBoxPlotData = (datas: number[]): BoxPlotResult => {
  // 输入验证
  if (!datas || datas.length === 0) {
    throw new Error('Data array cannot be empty');
  }

  let sum = 0;
  const result: BoxPlotResult = {
    min: 0,
    firstQuartile: 0,
    median: 0,
    thirdQuartile: 0,
    max: 0,
    average: 0,
    range: 0,
    stdDev: 0,
    lowerWhisker: 0,
    upperWhisker: 0,
    outliers: []
  };

  let sumOfESquare = 0;
  const squares = [];
  let lowerFence = 0;
  let upperFence = 0;

  // 先排序数据，确保后续计算的一致性
  const sortedData = [...datas].sort((a, b) => a - b);

  // 从排序后的数据计算真实的最小值和最大值
  result.min = sortedData[0];
  result.max = sortedData[sortedData.length - 1];

  // 计算总和
  for (let i = 0; i < datas.length; i++) {
    sum = add(sum, datas[i]);
    squares[i] = Math.pow(datas[i], 2);
  }

  // 保留小数位根据数据来决定
  const place = countDecimalPlaces(sum);
  result.average = Number(divide(sum, datas.length).toFixed(place));
  result.range = subtract(result.max, result.min);

  // 计算方差和标准差
  let m2 = 0.0;
  for (let i = 0; i < datas.length; i++) {
    const m = subtract(datas[i], result.average);
    const mPow2 = multiply(m, m);
    m2 = add(m2, mPow2);
  }

  sumOfESquare = m2;
  const variance = datas.length - 1 ? divide(sumOfESquare, datas.length - 1) : 0;
  result.stdDev = Number(Math.sqrt(Number(variance)).toFixed(place));

  // 计算四分位数
  result.firstQuartile = percentile(sortedData, 25);
  result.thirdQuartile = percentile(sortedData, 75);
  result.median = percentile(sortedData, 50);

  // 计算IQR和围栏
  const iqr = result.thirdQuartile - result.firstQuartile;
  lowerFence = result.firstQuartile - 1.5 * iqr;
  upperFence = result.thirdQuartile + 1.5 * iqr;

  // 基于排序后的数据识别异常值
  result.outliers = sortedData.filter((d) => d > upperFence || d < lowerFence);

  // 根据标准IQR算法计算whiskers
  // Lower Whisker: 在[lowerFence, Q1]范围内的最小值
  let lowerWhisker = result.firstQuartile;
  for (let i = 0; i < sortedData.length; i++) {
    if (sortedData[i] >= lowerFence && sortedData[i] <= result.firstQuartile) {
      lowerWhisker = sortedData[i];
      break; // 找到第一个符合条件的就是最小值
    }
  }

  // Upper Whisker: 在[Q3, upperFence]范围内的最大值
  let upperWhisker = result.thirdQuartile;
  for (let i = sortedData.length - 1; i >= 0; i--) {
    if (sortedData[i] >= result.thirdQuartile && sortedData[i] <= upperFence) {
      upperWhisker = sortedData[i];
      break; // 从后往前找到第一个符合条件的就是最大值
    }
  }

  result.lowerWhisker = lowerWhisker;
  result.upperWhisker = upperWhisker;

  return result;
};
// 计算异常点，且不重复
const getOutlierPoint = (oneLineData: any[], max: number, min: number, startTime: string) => {
  if (!Array.isArray(oneLineData) || oneLineData.length === 0) {
    return [];
  }

  const uniqueOutliers = new Set<number>();

  oneLineData.forEach((d) => {
    const value = Number(d);
    if (!isNaN(value) && (value > max || value < min)) {
      uniqueOutliers.add(value);
    }
  });

  return Array.from(uniqueOutliers)
    .sort((a, b) => a - b)
    .map((outlier) => [startTime, outlier]);
};
// 按照前端数据进行绘制
export const getBoxPlotFormatData = (
  groupData: any,
  chartData: any[],
  commonGroupConfigVO: any,
  activeStep: string,
  filterInfo: CmFilterObject,
  customLegendItems: CmLegend[],
  groupColumns: any[],
) => {
  const maxCandidates: Array<number> = [];
  const minCandidates: Array<number> = [];
  const chartTableData: any[] = [];
  const allChartData: any[] = [];
  // 先排序
  chartData.sort((d1, d2) => {
    if (d1.referenceYn === 'Y') return -1;
    if (d2.referenceYn === 'Y') return 1;
    return 1;
  });
  let filterChartData;
  if (filterInfo.filterKeys?.length > 0) {
    filterChartData = chartData.filter(({ groupConfigId, chamber, eqpId, recipeId }) => {
      return (
        cmChartContextFilter(filterInfo as CmFilterObject, {
          group: groupConfigId,
          eqpId,
          chamber,
          recipeId
        }) && cmChartLegendFilter(customLegendItems, groupConfigId)
      );
    });
  } else {
    filterChartData = chartData.filter(({ groupConfigId }) => {
      return cmChartLegendFilter(customLegendItems, groupConfigId);
    });
  }
  // 数据处理
  filterChartData.forEach((paramData) => {
    const columns = paramData.data.columns;
    const filteredTraceData = paramData.data.rows
      .filter((row: { [x: string]: string }) => row[columns.indexOf('STEP')] === activeStep)
      .map((row: { [x: string]: any }) => Number(row[columns.indexOf('VALUE')]));

    if (filteredTraceData.length === 0) return;

    const max = Math.max.apply(null, filteredTraceData);
    const min = Math.min.apply(null, filteredTraceData);
    maxCandidates.push(max);
    minCandidates.push(min);

    allChartData.push(filteredTraceData);

    const boxPloResult = getBoxPlotData(filteredTraceData);
    const startTime = moment(paramData.startDtts).format(CHART_CONSTANT.TraceTimeFormat);
    // 筛选出超出的散点图
    const pointValue = getOutlierPoint(
      filteredTraceData,
      boxPloResult.upperWhisker,
      boxPloResult.lowerWhisker,
      startTime
    );
    const { chamber, eqpId, eqpModuleId, lotId, recipeId, substrateId, loopNo, stationName } =
      paramData;
    const groupCol = groupColumns?.find(
      ({ id }: any) => id === paramData?.groupConfigId
    );
    const oneSeriesData = {
      key: groupCol?.name,
      dataSet: paramData.groupConfigId,
      eqp: eqpId || eqpModuleId?.split(':')?.[1],
      chamber,
      lot: lotId,
      recipeId,
      wafer: substrateId?.split(';')?.[0],
      loopNo,
      stationName,
      substrateId: substrateId,
      startTime: startTime,
      endTime: moment(paramData.endDtts).format(CHART_CONSTANT.TraceTimeFormat),
      min: boxPloResult.lowerWhisker,
      q1: boxPloResult.firstQuartile,
      median: boxPloResult.median,
      q3: boxPloResult.thirdQuartile,
      max: boxPloResult.upperWhisker,
      stdev: boxPloResult.stdDev,
      range: boxPloResult.range,
      avg: boxPloResult.average
    };
    if (pointValue?.length > 0) {
      chartTableData.push({
        ...oneSeriesData,
        pointValue: pointValue
      });
    } else {
      chartTableData.push(oneSeriesData);
    }
  });

  return { chartTableData, allChartData };
};
/**
 * box diagram table options
 */
export const getViewDataTableOptions = () => {
  return VXETable.tableFun.tableDefaultConfig({
    toolbarConfig: {
      tableName: 'common.title.viewData',
      import: false,
      export: true,
      refresh: true // 为了显示 custom;  import, export refresh至少有一个true, 用样式隐藏
    },
    columns: [
      {
        field: 'key',
        title: 'cm.field.dataSet',
        minWidth: 120,
        sortable: true,
        filters: [],
        filterRender: {}
      },
      {
        field: 'eqp',
        title: 'cm.field.eqp',
        minWidth: 120,
        sortable: true,
        filters: [],
        filterRender: {}
      },
      {
        field: 'chamber',
        title: 'cm.field.chamber',
        minWidth: 120,
        sortable: true,
        filters: [],
        filterRender: {}
      },
      {
        field: 'lot',
        title: 'cm.field.lot',
        minWidth: 120,
        sortable: true,
        filters: [],
        filterRender: {}
      },
      {
        field: 'wafer',
        title: 'cm.field.wafer',
        minWidth: 120,
        sortable: true,
        filters: [],
        filterRender: {}
      },
      {
        field: 'startTime',
        title: 'cm.field.startTime',
        minWidth: 120,
        sortable: true,
        filters: [],
        filterRender: {}
      },
      {
        field: 'endTime',
        title: 'cm.field.endTime',
        minWidth: 120,
        sortable: true,
        filters: [],
        filterRender: {}
      },
      {
        field: 'min',
        title: 'cm.field.min',
        minWidth: 120,
        sortable: true,
        filters: [],
        filterRender: {}
      },
      {
        field: 'q1',
        title: 'cm.field.q1',
        minWidth: 120,
        sortable: true,
        filters: [],
        filterRender: {}
      },
      {
        field: 'median',
        title: 'cm.field.median',
        minWidth: 120,
        sortable: true,
        filters: [],
        filterRender: {}
      },
      {
        field: 'q3',
        title: 'cm.field.q3',
        minWidth: 120,
        sortable: true,
        filters: [],
        filterRender: {}
      },
      {
        field: 'max',
        title: 'cm.field.max',
        minWidth: 120,
        sortable: true,
        filters: [],
        filterRender: {}
      }
    ]
  });
};
/**
 * box diagram table filter column
 * @returns 需要增加过滤的列
 */
export const getFilterFieldList = () => {
  return [
    'key',
    'eqp',
    'chamber',
    'lot',
    'wafer',
    'startTime',
    'endTime',
    'min',
    'q1',
    'median',
    'q3',
    'max',
    'range',
    'stdev',
    'avg'
  ];
};
