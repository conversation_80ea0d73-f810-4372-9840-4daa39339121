import { generateViteDefineConfig } from '@futurefab/ui-dev-utils';
import { fileURLToPath } from 'node:url';
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons';
import path from 'path';
import { viteStaticCopy } from 'vite-plugin-static-copy';
import Components from 'unplugin-vue-components/vite';
const defineConfig = generateViteDefineConfig({
  target: 'app',
  stack: 'vue',
  jsx: true
});

// https://vitejs.dev/config/
export default defineConfig({
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },
  server: {
    port: 5648
  },
  plugins: [
    Components({
      dirs: ['src/components'], // 指定组件目录
      extensions: ['vue'], // 只处理 .vue 文件
      dts: 'src/components.d.ts', // 自动生成类型声明文件
      deep: true // 递归扫描子目录
    }),
    createSvgIconsPlugin({
      iconDirs: [path.resolve(process.cwd(), 'src/assets/icons')],
      symbolId: 'icon-[dir]-[name]'
    }),
    viteStaticCopy({
      targets: [
        {
          src: 'node_modules/pdfjs-dist/cmaps/*',
          dest: 'cmaps'
        },
        {
          src: 'node_modules/pdfjs-dist/build/pdf.worker*.js',
          dest: 'assets/js'
        }
      ]
    })
  ]
});

