<script setup lang="ts">
import { watch, ref } from 'vue';
import { formatterYByGap, generateWaferGrid, getPicecs, getToolbox, minPowerOf10 } from './index';
import type { Measuredata } from '@/utils/spline-interpolate';
import Chart from '../chart.vue';
import { visualMapColors } from '@/utils/custom-color';
import type { Specification } from '@/views/metrology-management/interface';
import { debounce } from 'lodash-es';
const props = defineProps<{
    data?: Measuredata[] | Measuredata;
    size: number;
    valueKey: string;
    gridSize: number;
    specification?: Specification;
    isShrinkChart?: boolean;
}>();
const options = ref<any>({});
const getInterpolateChart = (
    data: Measuredata[] | Measuredata,
    visualColors: string[],
    valueKey: string,
    gridSize: number,
    specification: Specification,
) => {
    type Site = string;
    const temp: Record<Site, { x: number; y: number; value: number }> = {};
    (Array.isArray(data) ? data : [data]).forEach(item => {
        const xIndex = item.header.findIndex(item => item === specification.jsonObj.xField);
        const yIndex = item.header.findIndex(item => item === specification.jsonObj.yField);
        const vIndex = item.header.findIndex(item => item === valueKey);
        const siteIndex = item.header.findIndex(item => item === specification.jsonObj.siteField);
        item.data.forEach(dataRow => {
            const site = dataRow[siteIndex];
            const value = Number(dataRow[vIndex]);
            if (!temp[site]) {
                const x = Number(dataRow[xIndex]);
                const y = Number(dataRow[yIndex]);
                temp[site] = { x, y, value };
            } else {
                temp[site].value += value;
            }
        });
    });

    const samplePoints = Object.values(temp);
    const sampleValueList = samplePoints.map(item => item.value);
    const originMax = Math.max(...sampleValueList);
    const originMin = Math.min(...sampleValueList);
    const originGap = originMax - originMin;
    const grid = generateWaferGrid(samplePoints, specification, gridSize);
    const notchInfo = {
        position: [0, 0],
        rotate: 0
    }
    const symbolSize = 14;
    const radius = Math.floor(gridSize / 2)
    switch (specification.jsonObj.notchPosition) {
        case 'Bottom':
            notchInfo.position = [radius, 0];
            notchInfo.rotate = 0;
            break;
        case 'Left':
            notchInfo.position = [0, radius];
            notchInfo.rotate = -90;
            break;
        case 'Right':
            notchInfo.position = [gridSize, radius];
            notchInfo.rotate = 90;
            break;
        case 'Top':
            notchInfo.position = [radius, gridSize];
            notchInfo.rotate = 180;
    }
    return {
        tooltip: {
            position: 'top',
        },
        grid: props.isShrinkChart
            ? {
                  left: 0,
                  top: 0,
                  right: 0,
                  bottom: 0,
              }
            : {
                  left: 10,
                  top: 10,
                  right: 10,
                  bottom: 10,
              },
        toolbox: props.isShrinkChart ? null : getToolbox(),
        xAxis: {
            show: false,
            type: 'category',
            data: new Array(gridSize).fill(null).map((_, i) => i + 1),
            splitArea: {
                show: true,
            },
        },
        yAxis: {
            show: false,
            type: 'category',
            data: new Array(gridSize).fill(null).map((_, i) => i + 1),
            splitArea: {
                show: true,
            },
        },
        visualMap: {
            type: 'piecewise',
            show: false,
            calculable: true,
            orient: 'vertical',
            right: 10,
            top: 10,
            align: 'bottom',
            pieces: getPicecs(originMin, originMax, visualColors),
            seriesIndex: 0,
        },
        series: [
            {
                name: 'wafer chart',
                type: 'heatmap',
                data: grid.map(item => ({
                    value: [item.i, item.j, item.z],
                    info: item,
                })),
                label: {
                    show: false,
                },
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowColor: 'rgba(0, 0, 0, 0.5)',
                    },
                },
                tooltip: {
                    show: !props.isShrinkChart,
                    formatter: (event: any) => {
                        if ((event?.value?.[2] ?? false) !== false) {
                            return [`value: ${formatterYByGap(event.value[2], originGap)}`].join('<br/>');
                        }
                    },
                    appendToBody: true, 
                },
                markPoint: props.isShrinkChart ? null : {
                    symbol: 'triangle',
                    symbolSize,
                    symbolRotate: notchInfo.rotate,
                     tooltip: {
                        show: false, // 强制显示
                    },
                    borderWidth: 0,
                    data: [
                        {
                            name: 'Notch',
                            coord: notchInfo.position,
                            label: {show: false},
                            itemStyle : {
                                color: '#ff3366',
                            },
                        }
                    ]
                }
            },
        ],
    };
};
const tempFn = debounce(() => {
    options.value = getInterpolateChart(
        props.data!,
        visualMapColors,
        props.valueKey,
        props.gridSize,
        props.specification!,
    );
    console.log(options.value);
}, 100);
watch(
    [() => props.data, () => props.valueKey, () => props.gridSize, () => props.specification],
    v => {
        if (props.data) {
            tempFn();
        }
    },
    { immediate: true },
);
</script>
<template>
    <div
        :style="{ height: size + 'px', width: size + 'px' }"
        class="common-chart-container"
    >
        <Chart :options="options" />
    </div>
</template>
<style scoped lang="less">
.common-chart-container {
    display: inline-block;
}
</style>
