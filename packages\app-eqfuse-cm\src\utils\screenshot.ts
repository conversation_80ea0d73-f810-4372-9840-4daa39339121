import { cloneDeep } from 'lodash-es';
import { snapdom } from '@zumer/snapdom';

/**
 * 将使用 `<use>` 标签引用的 SVG 图标转换为普通内联 SVG 元素。
 * 该函数会查找页面中所有符合 `useLabel` 选择器的 `<use>` 元素，
 * 并将其引用的 SVG 内容克隆并插入到 DOM 中，同时进行缩放和平移以适配容器尺寸。
 * 解决html2canvas截图无法截图iconfont的问题
 * @param useLabel - 用于选取 `<use>` 元素的 CSS 选择器字符串
 */
export const toSimpleSvg = (useLabel: string) => {
  // 获取所有匹配的use标签
  const useElements = document.querySelectorAll(useLabel);
  const arr = [...useElements];

  arr.forEach((useElement) => {
    // 获取use元素引用的ID
    let href =
      useElement.getAttributeNS('http://www.w3.org/1999/xlink', 'href') ||
      useElement.getAttribute('href');

    if (href && href.startsWith('#')) {
      const id = href.substring(1);
      // 跳过特定ID的处理
      if (['icon-btn-close'].includes(id)) {
        return;
      }

      // 查找对应的实际SVG元素
      const svgElement = document.getElementById(id);
      const parentNode = useElement.parentNode as HTMLElement;

      // 验证必要元素是否存在
      if (!svgElement || !parentNode) {
        return;
      }

      const paths = svgElement.querySelectorAll('path');
      if (paths.length === 0) {
        console.warn(`SVG元素#${id}中未找到path，跳过处理`);
        return;
      }

      // 获取并验证SVG宽度
      let svgWidth = parentNode.clientWidth;
      if (svgWidth === 0) {
        const widthAttr = parentNode.getAttribute('width') || '';
        const widthStyle = parentNode.style.width || '';
        svgWidth = parseFloat(widthAttr) || parseFloat(widthStyle) || 0;
      }

      // 获取并验证SVG高度（增加更完善的高度处理逻辑）
      let svgHeight = parentNode.clientHeight;
      if (svgHeight === 0) {
        const heightAttr = parentNode.getAttribute('height') || '';
        const heightStyle = parentNode.style.height || '';
        // 尝试从宽度推断高度（如果高度仍无法获取）
        svgHeight = parseFloat(heightAttr) || parseFloat(heightStyle) || svgWidth || 0;
      }

      // 校验宽高有效性，避免后续计算出现NaN
      if (isNaN(svgWidth) || svgWidth <= 0 || isNaN(svgHeight) || svgHeight <= 0) {
        console.warn(`SVG宽高无效 (width: ${svgWidth}, height: ${svgHeight})，使用默认尺寸`);
        // 使用默认尺寸作为备选方案
        svgWidth = svgWidth || 24;
        svgHeight = svgHeight || 24;
      }

      // 计算所有path的包围框
      const totalBBox = { x: Infinity, y: Infinity, width: 0, height: 0 };
      paths.forEach(function (path) {
        try {
          const bbox = path.getBBox();
          totalBBox.x = Math.min(totalBBox.x, bbox.x);
          totalBBox.y = Math.min(totalBBox.y, bbox.y);
          totalBBox.width = Math.max(totalBBox.width, bbox.x + bbox.width);
          totalBBox.height = Math.max(totalBBox.height, bbox.y + bbox.height);
        } catch (error) {
          console.warn('获取path包围盒失败:', error);
        }
      });

      // 处理包围盒可能为零的情况
      if (totalBBox.width <= 0 || totalBBox.height <= 0) {
        console.warn('无法计算有效的path包围盒，使用默认变换');
        // 直接克隆路径，不应用变换
        paths.forEach((path) => {
          const clonedElement = path.cloneNode(true);
          parentNode.appendChild(clonedElement);
        });
        // 移除原始use元素
        parentNode.removeChild(useElement);
        return;
      }

      // 计算缩放比例
      const scaleX = svgWidth / totalBBox.width;
      const scaleY = svgHeight / totalBBox.height;
      const scale = Math.min(scaleX, scaleY) || 1;

      // 计算平移距离，确保结果为有效数字
      const translateX = (svgWidth - totalBBox.width * scale) / 2 - totalBBox.x * scale;
      const translateY = (svgHeight - totalBBox.height * scale) / 2 - totalBBox.y * scale;

      // 确保平移值有效
      const safeTranslateX = isNaN(translateX) ? 0 : translateX;
      const safeTranslateY = isNaN(translateY) ? 0 : translateY;

      // 应用变换到每个path
      paths.forEach((path) => {
        const clonedElement = path.cloneNode(true) as SVGElement;
        // 构建安全的transform字符串
        const transform = `scale(${scale}) translate(${safeTranslateX / scale}, ${safeTranslateY / scale})`;
        clonedElement.setAttribute('transform', transform);
        parentNode.appendChild(clonedElement);
      });

      // 移除原始的use元素
      parentNode.removeChild(useElement);
    }
  });
};

/**
 * 下载PPT文件函数
 * @param fileData - PPT文件的Base64编码数据
 * @param fileName - 下载文件的名称
 * @returns 无返回值
 */
export const downloadPPTFile = (fileData: any, fileName: string) => {
  const pptBase64 = fileData;

  // 1. 解码Base64为二进制字符串
  const binaryStr = atob(pptBase64);

  // 2. 转换为Uint8Array（二进制数组）
  const uint8Array = new Uint8Array(binaryStr.length);
  for (let i = 0; i < binaryStr.length; i++) {
    uint8Array[i] = binaryStr.charCodeAt(i);
  }

  // 3. 创建Blob对象（指定PPTX类型）
  const blob = new Blob([uint8Array], {
    type: 'application/vnd.openxmlformats-officedocument.presentationml.presentation'
  });

  // 4. 生成下载链接
  const downloadUrl = URL.createObjectURL(blob);

  // 5. 创建a标签触发下载
  const a = document.createElement('a');
  a.href = downloadUrl;
  a.download = fileName;
  document.body.appendChild(a);
  a.click();

  // 6. 释放临时URL（避免内存泄漏）
  URL.revokeObjectURL(downloadUrl);
  document.body.removeChild(a);
};
export const downloadImage = (dataUrl: string, name: string, step: string, type: string) => {
  const link = document.createElement('a');
  link.download = `${name}_step${step}_${type}_${Date.now()}.png`;
  link.href = dataUrl;
  link.click();
  URL.revokeObjectURL(link.href);
};

/**
 * 下载ZIP文件函数
 * @param fileData - ZIP文件的Base64编码数据
 * @param fileName - 下载文件的名称
 * @returns 无返回值
 */
export const downloadZipFile = (fileData: any, fileName: string) => {
  const zipBase64 = fileData;

  // 1. 解码Base64为二进制字符串
  const binaryStr = atob(zipBase64);

  // 2. 转换为Uint8Array（二进制数组）
  const uint8Array = new Uint8Array(binaryStr.length);
  for (let i = 0; i < binaryStr.length; i++) {
    uint8Array[i] = binaryStr.charCodeAt(i);
  }

  // 3. 创建Blob对象（指定ZIP类型）
  const blob = new Blob([uint8Array], {
    type: 'application/zip'
  });

  // 4. 生成下载链接
  const downloadUrl = URL.createObjectURL(blob);

  // 5. 创建a标签触发下载
  const a = document.createElement('a');
  a.href = downloadUrl;
  a.download = fileName;
  document.body.appendChild(a);
  a.click();

  // 6. 释放临时URL（避免内存泄漏）
  URL.revokeObjectURL(downloadUrl);
  document.body.removeChild(a);
};

/**
 * 将扁平数组转换为树形结构
 * @param data  源数组数据
 * @returns 树形结构数组
 */
export function transferTree(data: any[]): any[] {
  if (!Array.isArray(data) || data.length < 2) {
    return [];
  }

  // Step1: 忽略数组前2项，对剩余数据深拷贝
  const nodes = cloneDeep(data.slice(2));
  const result: any[] = [];
  // 用Map缓存顶级节点
  const topNodeMap = new Map();

  // Step2：筛选顶级节点
  nodes.forEach((node: any) => {
    if (node.groupId == null) {
      // 构造顶级节点，初始化children和扩展属性
      const topNode = {
        ...node,
        children: [],
        treeKey: node.parentGroupId,
        expand: false
      };
      result.push(topNode);
      topNodeMap.set(node.parentGroupId, topNode);
    }
  });

  // Step3：处理子节点，关联到对应顶级节点
  nodes.forEach((node: any) => {
    // 跳过顶级节点，只处理有groupId的子节点
    if (node.groupId == null) return;

    // 从缓存中找父节点
    const parentNode = topNodeMap.get(node.groupId);
    if (parentNode) {
      // 构造子节点并添加到父节点children
      parentNode.children.push({
        ...node,
        treeKey: `${node.groupConfigId}-${node.parameterName}`
      });
    } else {
      console.warn(`子节点${node.groupConfigId}未找到对应父节点`);
    }
  });

  return result;
}

/**
 * 等待DOM元素尺寸稳定的工具函数
 *
 * 在进行截图或其他依赖元素尺寸的操作前，确保目标元素的尺寸已经稳定，
 * 避免因为动画、异步渲染等因素导致的尺寸变化影响最终结果。
 *
 * @param selector - CSS选择器字符串，用于定位目标元素
 * @param stableDurationMs - 尺寸保持稳定的持续时间（毫秒），默认300ms
 * @param maxTries - 最大尝试次数，避免无限等待，默认10次
 * @returns Promise<void> - 当元素尺寸稳定或达到最大尝试次数时resolve
 *
 * @example
 * ```typescript
 * // 等待图表元素尺寸稳定后再截图
 * await waitForElementStable('.chart-container', 500);
 * ```
 */
export const waitForElementStable = async (selector: string, stableDurationMs = 800) => {
  const element = document.querySelector(selector) as HTMLElement | null;
  if (!element) return;
  await new Promise((resolve) => setTimeout(resolve, stableDurationMs));
};

/**
 * 截图排序枚举
 */
export const ScreenshotSort = {
  TRACE: 1,
  BOXPLOT: 2,
  SUMMARY: 3,
  METROLOGY: 4
} as const;

/**
 * 截图配置选项接口
 */
export interface ScreenshotOptions {
  scale?: number;
  useCORS?: boolean;
  logging?: boolean;
  backgroundColor?: string;
  scrollX?: number;
  scrollY?: number;
  allowTaint?: boolean;
}

/**
 * 图表截图数据结构
 */
export interface ChartScreenshotResult {
  sort: number;
  imgType: string;
  base64ImgData: string;
  step: string;
}

/**
 * 截取指定图表元素并生成图片
 * @param selector - 图表元素的选择器字符串
 * @param step - 当前步骤标识，用于日志记录
 * @param chartType - 图表类型，用于命名下载文件和返回数据标识
 * @param options - 截图配置选项
 * @returns 截图成功时返回包含图片信息的对象，失败时返回 null
 */
export const captureChartElement = async (
  selector: string,
  step: string,
  chartType: string,
  options: ScreenshotOptions = {}
): Promise<ChartScreenshotResult | null> => {
  const defaultOptions: ScreenshotOptions = {
    scale: 2,
    useCORS: true,
    logging: false,
    backgroundColor: '#ffffff',
    scrollX: 0,
    scrollY: 0,
    allowTaint: true,
    ...options
  };

  const chartElement = document.querySelector(selector) as HTMLElement;
  if (!chartElement) {
    console.warn(`未找到选择器为 "${selector}" 的元素`);
    return null;
  }

  try {

    // 等待元素稳定
    await new Promise((resolve) => setTimeout(resolve, 1000));

    const result = await snapdom(chartElement, defaultOptions);
    const img = await result.toPng();
    const chartDataUrl = img.src;


    const chartTypeToSortMap = new Map([
      ['boxplot', ScreenshotSort.BOXPLOT],
      ['metrology', ScreenshotSort.METROLOGY],
      ['summary', ScreenshotSort.SUMMARY]
    ]);

    return {
      sort: chartTypeToSortMap.get(chartType) ?? ScreenshotSort.SUMMARY,
      imgType: chartType + 'Chart',
      base64ImgData: chartDataUrl,
      step
    };
  } catch (error) {
    console.error(`步骤${step}截图失败:`, error);
    return null;
  } finally {
    // captureModeRef.value = false;
  }
};

/**
 * 截取轨迹图
 * @param selector - 轨迹图元素的选择器
 * @param options - 截图配置选项
 * @returns 截图成功时返回base64图片数据，失败时返回null
 */
export const captureTraceChart = async (
  selector: string = '.trace-chart',
  options: ScreenshotOptions = {}
): Promise<string | null> => {
  const defaultOptions: ScreenshotOptions = {
    scale: 2,
    useCORS: true,
    logging: false,
    backgroundColor: '#ffffff',
    scrollX: 0,
    scrollY: 0,
    allowTaint: true,
    ...options
  };

  const tracechart = document.querySelector(selector) as HTMLElement;
  if (!tracechart) {
    console.warn(`未找到选择器为 "${selector}" 的轨迹图元素`);
    return null;
  }
  console.log('###tracechart:', tracechart);

  try {
    const result = await snapdom(tracechart, defaultOptions);
    const img = await result.toPng();
    return img.src;
  } catch (error) {
    console.error('轨迹图截图失败:', error);
    return null;
  }
};
