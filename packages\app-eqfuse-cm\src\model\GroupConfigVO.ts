/* eslint-disable @typescript-eslint/no-non-null-assertion */
/* eslint-disable @typescript-eslint/no-this-alias */
/* eslint-disable @typescript-eslint/no-explicit-any */
import dayjs from 'dayjs';
import { Context } from '@/model/context';
import { TimePeriod } from '@/model/TimePeriod';
import { type SplitDatasetDataResponse } from '@/model/split-dataset-data-response';
import { Constants } from '@/model/constants';
import { getAttributesConfigs, getEesDbFileData } from '@futurefab/ui-sdk-api';
import { AttributeItem } from '@/model/attribute-item';
import { ResultGroupInfo } from '@/model/ResultGroupInfo';
import type { MetaResult } from '@/views/cm-home/cm-main/add-dataset/interface';
import { type MatchingInfo } from '@/views/cm-home/cm-main/components/cm-data-set-table/interface';
import {
  getMatchingRatio,
  getUnmatchingWarningRatio,
  getUnmatchingRatio,
  getWarningRatio,
  getMissingRatio
} from '@/views/cm-home/cm-main/components/cm-data-set-table/config';
import { cmMainInfo as buttonIds } from '@/log-config';
export interface TableDataAndColumns {
  tableData: any[];
  tableColumns: any[];
}

const fileNecessaryAttribute = [Constants.EQUIPMENT, Constants.CHAMBER, Constants.PROCESS];

export class GroupConfigVO {
  tool?: string; // 设备
  chamber?: string; // 腔室；如果按照设备分，那么腔室是按照逗号分割的字符串
  route?: string; // 路由，逗号分割的字符串
  recipe?: string; // 工艺配方，逗号分割的字符串
  product?: string; // 产品，逗号分割的字符串
  timeFrame: string; // 几个枚举，例如static这样的
  from: dayjs.Dayjs;
  to: dayjs.Dayjs;
  count?: string; // lot和wafer的分别求和
  reference: boolean; // 是否是参考
  id?: number; // 序号
  key?: string;
  fromToType: string; // 时间范围类型
  attributeConfigs: any[]; // 所有属性配置
  attributes?: any[]; // 所有属性，上传参数需要。
  selectedAttributeConfigs: any[]; // 被选中的属性配置
  createDatasetByEachChamber: boolean; // 是否为每个chamber创建一个对象
  contexts: Context[]; // 对应的Context集合
  selectedRows: any[];
  filteredContexts?: string; // 过滤字符串
  lotCount?: number; // lot Count
  waferCount?: number; // wafer Count
  isFileData: boolean; // 是否是文件数据
  file?: File | null; // 对应的文件
  relativeTime: number; // 相对时间
  relativeUnit: string; // 相对时间单位
  color: string; // 颜色
  chartLegendColor?: string;
  groupConfigs: GroupConfigVO[]; // 当这个对象作为公共的组对象的时候，这个属性描述的是有多少个组对象，用于计算新生成的groupConfig的id.
  resultRows?: any[]; //  比较分析的结果集
  resultGroups?: any;
  resultGroupInfos: ResultGroupInfo[];
  resultGroupInfo?: ResultGroupInfo; // 是针对单个GroupConfig的结果集信息
  fileNames?: string[];
  analysisOptions: any;
  isLoop: boolean;
  loopGroup: string;
  contextMap: { key: string; name: string; loopStep?: boolean }[];
  allContextMap: MetaResult[];
  waferLoops: any;
  runType: string;
  allLocations: any;
  eqpModuleIds: any;
  recipeId: string;
  recipeIds: string[];
  toolChamberIds: any;
  chamberIds: any;
  stationCompare: boolean;
  headerInfo: any;

  constructor() {
    this.fromToType = 'relative';
    this.createDatasetByEachChamber = true;
    this.isFileData = false;
    this.reference = false;
    this.contexts = [];
    this.attributeConfigs = [];
    this.selectedAttributeConfigs = [];
    this.relativeUnit = 'hours';
    this.relativeTime = 8;
    this.from = dayjs().subtract(1, 'day');
    this.to = dayjs();
    this.timeFrame = 'Static';
    this.color = '';
    this.chartLegendColor = '';
    this.groupConfigs = [];
    this.analysisOptions = {};
    this.resultGroupInfos = [];
    this.selectedRows = [];
    this.isLoop = false;
    this.loopGroup = '';
    this.contextMap = [];
    this.allContextMap = [];
    this.runType = '';
    this.allLocations = new Set();
    this.eqpModuleIds = [];
    this.recipeId = '';
    this.recipeIds = [];
    this.toolChamberIds = [];
    this.chamberIds = [];
    this.stationCompare = false;
    this.headerInfo = {};
  }

  setLoop(data: boolean) {
    this.isLoop = data;
  }

  setRunType(data: string) {
    this.runType = data;
  }

  setStationCompare(data: boolean) {
    this.stationCompare = data;
  }

  setTimeInfo(type: string, value: any) {
    switch (true) {
      case type === 'fromToType':
        this.fromToType = value;
        break;
      case type === 'relativeTime':
        this.relativeTime = value;
        break;
      case type === 'relativeUnit':
        this.relativeUnit = value;
        break;
      case type === 'from':
        this.from = value;
        break;
      case type === 'to':
        this.to = value;
        break;
    }
  }

  getAllLocations() {
    return this.allLocations;
  }
  getChamberIds() {
    return this.chamberIds;
  }

  setContextMap(data: { key: string; name: string; loopStep?: boolean }[]) {
    this.contextMap = data;
  }

  setAllContextMap(data: MetaResult[]) {
    this.allContextMap = data;
  }

  addGroupConfig(gc: any[]) {
    gc?.forEach((item: any) => delete item?.groupConfigs);
    this.groupConfigs = gc;
  }

  setFileNames(values: string[]) {
    this.fileNames = values;
  }

  remove(groupConfig: GroupConfigVO) {
    this.groupConfigs.splice(
      this.groupConfigs.findIndex((e) => e.id == groupConfig.id),
      1
    );
  }

  getNewGroupConfigId(groupConfigs?: any) {
    if (!groupConfigs || groupConfigs.length < 1) {
      return 1;
    }
    const ids: any = groupConfigs.map((gc: { sortId: number }) => gc.sortId);
    return Math.max(...ids) + 1;
  }

  getTimePeriod(scheduledAnalysis?: boolean): TimePeriod {
    let timePeriod: TimePeriod;
    if (this.fromToType === 'relative') {
      timePeriod = new TimePeriod(null, null, this.relativeTime, this.relativeUnit);
      if (scheduledAnalysis) {
        timePeriod = new TimePeriod(null, null, this.relativeTime, this.relativeUnit);
      } else {
        timePeriod = new TimePeriod(
          this.from.valueOf(),
          this.to.valueOf(),
          this.relativeTime,
          this.relativeUnit
        );
      }
    } else {
      timePeriod = new TimePeriod(this.from.valueOf(), this.to.valueOf(), null, null);
    }
    return timePeriod;
  }

  // 根据属性名称获得选中的属性值，是一个数组
  getAttributeValueArray(attrName: string) {
    const fieldName = GroupConfigVO.getFieldNameByAliasOrAttributeName(attrName);
    let v = this[fieldName as keyof typeof this] as any;
    if (v) {
      if (v && (fieldName == 'from' || fieldName == 'to')) {
        v = dayjs(v).format(Constants.TraceTimeFormat);
      }
      return v.split(',');
    }
    return [];
  }

  // 获取选中的属性二维数组
  getDataSplitSelection = () => {
    let arr: any[] = [];
    this.selectedAttributeConfigs
      .sort((a, b) => a.sequence - b.sequence)
      .filter((e: any) => e.value && e.value.length > 0)
      .forEach((s) => {
        let a: any = [];
        a.push(s.name);
        if (Array.isArray(s.value)) {
          s.value.forEach((i: any) => {
            if (i && i != 'NaN') {
              a = a.concat(i);
            }
          });
          arr.push(a);
        } else {
          if (s.value && s.value != 'NaN') {
            a = a.concat(s.value);
            arr.push(a);
          }
        }
      });
    const necessaryAttrArr = this.getNecessaryAttrArr();
    arr = arr.concat(necessaryAttrArr);
    return arr;
  };

  getNecessaryAttrArr() {
    const resArr: any = [];
    const attrArray = [Constants.TimeFrame, Constants.From, Constants.To, Constants.Count];
    attrArray.forEach((i) => {
      let a = [];
      a.push(i);
      a = a.concat(this.getAttributeValueArray(i));
      if (a.length > 1) {
        ``;
        resArr.push(a);
      }
    });
    return resArr;
  }

  deepCopy = () => {
    const splitGroupConfig: any = new GroupConfigVO();
    const _this: any = this;
    Object.keys(_this).forEach((k) => {
      splitGroupConfig[k] = _this[k];
    });
    splitGroupConfig.from = this.from.clone();
    splitGroupConfig.to = this.to.clone();
    splitGroupConfig.attributeConfigs = JSON.parse(JSON.stringify(this.attributeConfigs));
    splitGroupConfig.selectedAttributeConfigs = JSON.parse(
      JSON.stringify(this.selectedAttributeConfigs)
    );
    splitGroupConfig.contexts = JSON.parse(JSON.stringify(this.contexts));
    return splitGroupConfig;
  };

  getRouteOrProductText(routeOrProduct: string) {
    return this.selectedAttributeConfigs
      .filter((e) => e.name == routeOrProduct)
      .map((e) => {
        if (e.multiSelection) {
          return e.value ? e.value.join(',') : '';
        } else {
          return e.value ? e.value : '';
        }
      })[0];
  }

  getWaferCount() {
    return new Set(this.selectedRows.map((e) => e['waferId'])).size;
  }

  getLotCount() {
    return new Set(this.selectedRows.map((e) => e['lotId'])).size;
  }

  setDynamicCounts(dynamicSets: { [key: string]: Set<any> }) {
    // console.log('this.selectedRows.length', this.selectedRows.length);
    const counts: string[] = [];
    let loopCount = '';
    for (const [key, set] of Object.entries(dynamicSets)) {
      const loopStep = this.contextMap.find((item) => item.name === key)?.loopStep;
      if (loopStep) {
        // counts.push(`${key} ${Math.min(...set) + '-' + Math.max(...set)}`);
      } else if (key === 'Loop No') {
        loopCount = `Loop ${set.size}`;
      } else {
        counts.push(`${key} ${set.size}`);
      }
    }
    if (loopCount) {
      counts.push(loopCount);
    }
    this.count = counts.join(', ');
  }

  // 通过选中的名称数组去设置已经被选中的要出现的
  setSelectedConfigsByNameArray(names: any[]) {
    this.selectedAttributeConfigs = this.attributeConfigs.filter((e1: any) =>
      names.includes(e1.name)
    );
  }

  setFileNecessaryAttributeConfigs(gc: GroupConfigVO) {
    this.selectedAttributeConfigs = gc.attributeConfigs
      .filter((e) => fileNecessaryAttribute.includes(e.name))
      .map((e) => {
        // e.value = this.getAttributeValueArray(e.name);
        return e;
      });
  }

  getTableHeaderColumnsName() {
    const arr = this.selectedAttributeConfigs
      .sort((a, b) => a.sequence - b.sequence)
      .map((e) => e.alias);
    // 删除了'Time Frame'
    if (this.isLoop) {
      arr.push('Loop Group', 'From', 'To', 'Count');
    } else {
      arr.push('From', 'To', 'Count');
    }
    return arr;
  }

  static getFieldNameByAliasOrAttributeName(alias: string): string | undefined {
    // 特殊字段的处理保持不变
    switch (alias) {
      case Constants.EQUIPMENT:
        return 'Tool';
      case Constants.ToolAlias:
        return 'Tool';
      case Constants.EqpAlias:
        return 'EQP';
      case Constants.CHAMBER:
        return 'Chamber';
      case Constants.RouteAlias:
        return 'Route';
      case Constants.PRODUCT:
        return 'Product';
      case Constants.RECIPE:
        return 'Recipe';
      case Constants.TimeFrame:
        return 'timeFrame';
      case Constants.From:
        return 'from';
      case Constants.To:
        return 'to';
      case Constants.Count:
        return 'count';
      case 'Loop Group':
        return 'loopGroup';
    }
    return alias;
  }

  static getFieldValueByAlias(alias: string, groupConfig: any) {
    const fieldName = GroupConfigVO.getFieldNameByAliasOrAttributeName(alias);
    const value = groupConfig[`${fieldName}`];
    if (value && (fieldName == 'from' || fieldName == 'to')) {
      return dayjs(value).format(Constants.TraceTimeFormat);
    }
    return value;
  }

  // 根据服务接口响应的SplitDatasetDataResponse 去创建多个configs
  createNewConfigsBySplitDatasetResponse = (response?: SplitDatasetDataResponse) => {
    if (response) {
      const configs: GroupConfigVO[] = [];
      response.splitDatasetKeys.forEach((k, index) => {
        const splitGroupConfig = this.deepCopy();
        const keyTokens = k.split(response.splitDatasetKeyDelimiter); // tool和chamber
        splitGroupConfig.tool = keyTokens[0];
        splitGroupConfig.chamber = this.getAttributeShortValue(keyTokens[1], '/');
        const datasetContexts = response.datasetContexts[k].map(
          (ctx) =>
            new Context(
              ctx.isSelected,
              ctx.lot,
              ctx.wafer,
              ctx.datasetId,
              ctx.tool,
              ctx.chamber,
              ctx.recipe,
              ctx.startTime,
              ctx.endTime,
              ctx.descriptor_1,
              ctx.descriptor_2,
              ctx.isVisible
            )
        );
        splitGroupConfig.contexts = datasetContexts;
        splitGroupConfig.recipe = [...new Set(datasetContexts.map((e) => e.recipe))].join(',');
        splitGroupConfig.waferCount = splitGroupConfig.getWaferCount();
        splitGroupConfig.lotCount = splitGroupConfig.getLotCount();
        splitGroupConfig.count = `LOT ${splitGroupConfig.lotCount}, WAFER ${splitGroupConfig.waferCount}`;
        splitGroupConfig.id = index + 1;
        splitGroupConfig.route = this.getRouteOrProductText(Constants.PROCESS);
        splitGroupConfig.product = this.getRouteOrProductText(Constants.PRODUCT);
        configs.push(splitGroupConfig);
      });
      return configs;
    } else {
      return this.groupConfigs;
    }
  };

  // 组排序
  groupAndSort(arr: any[]) {
    // 如果出现整租参数都是noSelected或者零飘inActive，则将其排在最后，其他的按照分数排序
    const groups = [];
    let currentGroup = null;
    for (const obj of arr) {
      if (obj.groupId === null) {
        currentGroup = { header: obj, members: [] };
        groups.push(currentGroup);
      } else if (currentGroup !== null) {
        currentGroup.members.push(obj as never);
      }
    }
    groups.forEach((group) => {
      if (group.header.totalCount > 0) {
        group.header.ratio = group.header.matchingCount / group.header.totalCount;
      } else {
        group.header.ratio = Infinity;
      }
    });
    groups.sort((a, b) => {
      // noSelected和inActive的排在最后
      if (a.header.missingCount === a.header.totalCount) {
        return 1;
      }
      if (b.header.missingCount === b.header.totalCount) {
        return -1;
      }
      return a.header.ratio - b.header.ratio;
    });
    const sortedArray = [];
    for (const group of groups) {
      sortedArray.push(group.header);
      sortedArray.push(...group.members);
    }

    return sortedArray;
  }

  createGroupConfigTableDataAndColumn(response?: SplitDatasetDataResponse): TableDataAndColumns {
    const row3toRowN = this.groupAndSort(this.computedRow3Data()); // 先计算第三行到第N行的数据
    const row2 = this.computedRow2Data(); // 再计算第2行的数据
    // 最后计算第三行的数据
    const configColumns = this.createGroupConfigTableColumns(response);
    const row1: any = {};
    // 第一行数据。根据表格的列展示数据
    row1.configColumns = configColumns;
    row1.parentId = null;
    row1.id = 'configColumns';
    // 组装数据
    const records = [row1].concat(row2).concat(row3toRowN);
    return { tableData: records, tableColumns: configColumns };
  }

  sortRecipeSteps(arr: any[]): any[] {
    return arr.sort(
      (
        r1: { matchingPercentage: number; recipeStepId: number },
        r2: { matchingPercentage: number; recipeStepId: number }
      ) =>
        r1.matchingPercentage === r2.matchingPercentage
          ? r1.recipeStepId > r2.recipeStepId
            ? 1
            : -1
          : r1.matchingPercentage - r2.matchingPercentage
    );
  }

  setMatchInfo(data: any) {
    return data.map(
      (item: {
        matchingPercentage: number;
        recipeStepSummaryItems: any[];
        unmatchingRecipeSteps: any[];
        unmatchingRecipes: string;
        warningRecipeSteps: any[];
        warningRecipes: string;
        matchingRecipeSteps: any[];
        missingRecipeSteps: any[];
        recipeText: any;
      }) => {
        // totalCount === 0 时为 missingRecipeSteps
        item.matchingPercentage = 0;
        item.matchingRecipeSteps = [];
        item.missingRecipeSteps = [];
        const unMatchingRecipeSteps: any[] = [];
        const warningRecipeSteps: any[] = [];
        // noSelected的数据item.recipeStepSummaryItems = []
        item.recipeStepSummaryItems.forEach((e) => {
          if (e.totalCount === 0) {
            item.missingRecipeSteps.push(e);
          } else if (e.matchingPercentage < this.analysisOptions.criticalThreshold) {
            unMatchingRecipeSteps.push(e);
          } else if (
            e.matchingPercentage >= this.analysisOptions.criticalThreshold &&
            e.matchingPercentage <= this.analysisOptions.warningThreshold
          ) {
            warningRecipeSteps.push(e);
          } else {
            // 存入匹配的步骤
            item.matchingRecipeSteps.push(e);
          }
        });
        item.unmatchingRecipeSteps = this.sortRecipeSteps(unMatchingRecipeSteps);
        item.warningRecipeSteps = this.sortRecipeSteps(warningRecipeSteps);
        if (item.unmatchingRecipeSteps && item.unmatchingRecipeSteps.length > 0) {
          item.unmatchingRecipes =
            'Rcp. Step ' + item.unmatchingRecipeSteps.map((a) => a.recipeStepId).join(', ');
          item.recipeText = item.unmatchingRecipes;
        } else if (item.warningRecipeSteps && item.warningRecipeSteps.length > 0) {
          item.warningRecipes =
            'Rcp. Step ' + item.warningRecipeSteps.map((a) => a.recipeStepId).join(', ');
          item.recipeText = item.warningRecipes;
        }
        return item;
      }
    );
  }

  // 数据分组
  groupByField(obj: { [x: string]: any[] }) {
    const groupedArray: any = new Map();
    for (const key in obj) {
      obj[key].forEach((item) => {
        if (!groupedArray.has(item.groupId)) {
          groupedArray.set(item.groupId, [{ ...item, groupConfigId: Number(key) }]);
        } else {
          groupedArray.get(item.groupId).push({ ...item, groupConfigId: Number(key) });
        }
      });
    }
    return groupedArray;
  }

  // 计算第3行的数据
  computedRow3Data() {
    const records: any[] = [];
    const resultGroupInfos: ResultGroupInfo[] = [];
    if (this.resultGroups && Object.keys(this.resultGroups).length > 0) {
      const groupInfo: any = this.groupByField(this.resultGroups);
      groupInfo.forEach((groupValue: any) => {
        // 分组后每列数据合并
        groupValue.forEach((rg: any) => {
          const g = new ResultGroupInfo();
          g.parameterName = rg.groupName ? rg.groupName : 'Ungrouped';
          g.id = rg.groupConfigId;
          g.groupConfigId = rg.groupConfigId;
          // 分组排序
          g.categoryOrder = rg.order;
          // 根据groupId进行数据行的分组，分组id自己维护
          g.parentGroupId = rg.groupId;
          g.parentId = null;
          g.isGroup = true; // 是分组行
          g.isAnalysisResultRow = true; // 是分组结果行
          g.results = this.setMatchInfo(rg.items);
          g.resultRequestId = rg.resultRequestId;
          g.setUnMatchingTop5(); // 设置最差的五个
          const unMatchingTop5ParameterNames = g.getUnMatchingTop5ParameterNames();
          // 计算 分组的 匹配、警告、未匹配的数量
          this.computeResultGroupNumber(g);
          g.results.forEach((i) => {
            i.groupConfigId = g.groupConfigId;
            i.isAnalysisResultRow = true;
            i.isUnMatchingTop5 = unMatchingTop5ParameterNames.includes(i.parameterName);
          });
          resultGroupInfos.push(g);
        });

        this.resultGroupInfos = resultGroupInfos;
        this.computedRow2Data();
        const sortRule = this.groupConfigs?.map((item: any) => item?.id);

        resultGroupInfos
          ?.filter((item: any) => sortRule.includes(item.id))
          ?.forEach((groupHead: any) => {
            const isCopy = records.find(
              (record: any) => record.parameterName === groupHead.parameterName
            );
            if (!isCopy) records.push(groupHead);
            groupHead?.results.forEach((item: any) => {
              const allColInfo = new Map();
              resultGroupInfos.forEach((colInfo: any) => {
                colInfo.results.forEach((rowInfo: any) => {
                  if (rowInfo.parameterName === item.parameterName) {
                    allColInfo.set(rowInfo.groupConfigId, rowInfo);
                  }
                });
              });
              // 处理多列数据，根据groupConfigId分组，方便在渲染的时候拿到数据
              item.allColInfo = allColInfo;
              const isCopyItem = records.find(
                (record: any) => record.parameterName === item.parameterName
              );
              if (!isCopyItem) records.push(item);
            });
          });
      });
    }
    this.resultGroupInfos = resultGroupInfos;

    return records;
  }

  // 得到第 2行 的数据
  computedRow2Data() {
    // 就是第二行的数据
    const arr = [];
    const a: any = {};
    a.name = 'ParameterAndRecipe';
    a.id = 'ParameterAndRecipe';
    a.parentId = null;
    a.showMatchingInfo = false;
    const matchingInfo: MatchingInfo = {
      unmatchingCountAll: 0,
      warningCountAll: 0,
      matchingCountAll: 0,
      missingCountAll: 0,
      totalCountAll: 0
    };
    if (this.resultGroupInfos.length > 0) {
      const firstItemGroupConfigId = this.resultGroupInfos?.[0].groupConfigId;
      this.resultGroupInfos
        ?.filter((item: any) => item?.groupConfigId === firstItemGroupConfigId)
        .forEach((info) => {
          matchingInfo.unmatchingCountAll += Number(info.unmatchingCount);
          matchingInfo.warningCountAll += Number(info.warningCount);
          matchingInfo.matchingCountAll += Number(info.matchingCount);
          matchingInfo.missingCountAll += Number(info.missingCount);

          matchingInfo.totalCountAll += Number(info.totalCount);
        });
    }
    a.matchingInfo = matchingInfo;
    // 给configs设置上数据
    this.groupConfigs = this.groupConfigs
      .map((gc) => {
        gc.resultGroupInfo = this.resultGroupInfos.find((e) => e.groupConfigId == gc.id);
        gc.resultGroupInfos = this.resultGroupInfos;
        return gc;
      })
      .sort((a: any, b: any) => {
        // 首先按 reference 排序，为 true 的排在前面
        if (a.reference !== b.reference) return a.reference ? -1 : 1;
        // 按 matchingRatio 升序排列
        const aMatchingRatio = Number(getMatchingRatio(a?.resultGroupInfos, a?.id)) ?? Infinity;
        const bMatchingRatio = Number(getMatchingRatio(b?.resultGroupInfos, b?.id)) ?? Infinity;
        if (aMatchingRatio !== bMatchingRatio) {
          return aMatchingRatio - bMatchingRatio;
        }
        // 按 getUnmatchingWarningRatio 降序排列
        const aUnmatchingWarningRatio =
          Number(getUnmatchingWarningRatio?.(a?.resultGroupInfos, a?.id)) ?? -Infinity;
        const bUnmatchingWarningRatio =
          Number(getUnmatchingWarningRatio?.(b?.resultGroupInfos, b?.id)) ?? -Infinity;
        if (aUnmatchingWarningRatio !== bUnmatchingWarningRatio) {
          return bUnmatchingWarningRatio - aUnmatchingWarningRatio;
        }
        // 按 getUnmatchingRatio 降序排列
        const aUnmatchingRatio =
          Number(getUnmatchingRatio?.(a?.resultGroupInfos, a?.id)) ?? -Infinity;
        const bUnmatchingRatio =
          Number(getUnmatchingRatio?.(b?.resultGroupInfos, b?.id)) ?? -Infinity;
        if (aUnmatchingRatio !== bUnmatchingRatio) {
          return bUnmatchingRatio - aUnmatchingRatio;
        }
        // 按 getWarningRatio 降序排列
        const aWarningRatio = Number(getWarningRatio(a?.resultGroupInfos, a?.id)) ?? -Infinity;
        const bWarningRatio = Number(getWarningRatio(b?.resultGroupInfos, b?.id)) ?? -Infinity;
        if (aWarningRatio !== bWarningRatio) {
          return bWarningRatio - aWarningRatio;
        }
        // 按 getMissingRatio 升序排列
        const aMissingRatio = Number(getMissingRatio(a?.resultGroupInfos, a?.id)) ?? -Infinity;
        const bMissingRatio = Number(getMissingRatio(b?.resultGroupInfos, b?.id)) ?? -Infinity;
        return aMissingRatio - bMissingRatio;
      });
    if (this.resultGroupInfos.length > 0) {
      a.showMatchingInfo = true;
    }
    arr.push(a);
    return arr;
  }

  computeResultGroupNumber(rg: ResultGroupInfo) {
    let rgUnmatchingParameterCount = 0;
    let rgWarningParameterCount = 0;
    let rgMatchingParameterCount = 0;
    // 未分析
    let rgMissingParameterCount = 0;
    let rgTotalParameterCount = 0;
    rg.results.forEach((r) => {
      if (r.unmatchingRecipeSteps && r.unmatchingRecipeSteps.length > 0) {
        rgUnmatchingParameterCount++;
      } else if (r.warningRecipeSteps && r.warningRecipeSteps.length > 0) {
        rgWarningParameterCount++;
      } else if (r.matchingRecipeSteps && r.matchingRecipeSteps.length > 0) {
        rgMatchingParameterCount++;
      } else if (r.missingRecipeSteps && r.missingRecipeSteps.length > 0) {
        rgMissingParameterCount++;
      }
      rgTotalParameterCount++;
    });
    // Calculate Column(GroupConfig) Unmatching Ratio
    rg.unmatchingCount = rgUnmatchingParameterCount;
    rg.warningCount = rgWarningParameterCount;
    rg.matchingCount = rgMatchingParameterCount;
    rg.missingCount =
      rgTotalParameterCount -
      (rgUnmatchingParameterCount + rgWarningParameterCount + rgMatchingParameterCount);
    rg.totalCount = rgTotalParameterCount;
  }

  // 根据响应的SplitDatasetDataResponse 去创建 表格数据
  createGroupConfigEmptyColumns() {
    const tableColumns: any = [];
    const tableHeaderColumns = this.getTableHeaderColumnsName();
    tableColumns.push(tableHeaderColumns);
    tableColumns.push({});
    return tableColumns;
  }

  // 先获取数据，然后创建表格的列数据
  createGroupConfigTableColumns(response?: SplitDatasetDataResponse) {
    this.groupConfigs = this.createNewConfigsBySplitDatasetResponse(response);
    return this.generateTableColumns();
  }

  generateTableColumns() {
    let tableColumns: any = [];
    const tableHeaderColumns = this.getTableHeaderColumnsName();
    tableColumns.push(tableHeaderColumns);
    tableColumns = tableColumns.concat(this.groupConfigs);
    return tableColumns;
  }

  getAttributeShortValue(value: string, delimiter: string): string {
    if (!value || !delimiter) {
      return value;
    }
    const tokens = value.split(delimiter);
    return tokens[tokens.length - 1];
  }

  // 从服务器获取 属性配置，并且实现初始化
  initAttributesAndConfigsFromServer() {
    // 初始化 全部配置
    return getAttributesConfigs({
      bodyParams: {},
      headerParams: {
        log: 'Y',
        page: buttonIds.pageId,
        action: 'get-attribute-configs'
      }
    }).then((res: { data: any[] }) => {
      // console.log('初始化全部配置getAttributesConfigs', res);
      this.attributeConfigs = res?.data?.map(
        (e: {
          key: string;
          id: string;
          title: any;
          alias: any;
          dropdownVisibleChange: (open: any, i: any) => void;
          loadedData: boolean;
          disabled: boolean;
        }) => {
          e.key = e.id + '';
          e.title = e.alias;
          e.dropdownVisibleChange = this.getFormItemData;
          e.loadedData = false;
          if (!['Tool', 'EQP'].includes(e.title)) e.disabled = true;
          return e;
        }
      );
      const shortValueDelimiter = '/';
      const hide = true;
      let sequence: number;
      const attributes: AttributeItem[] = this.attributeConfigs.map(
        (attribute) =>
          new AttributeItem(
            attribute.sequence,
            attribute.name,
            attribute.alias,
            !attribute.multiSelection, // isSingle
            !attribute.isOptional, // isMandatory
            !attribute.isOptional, // visibleOnHeader
            attribute.name === Constants.CHAMBER, // supportShortValue
            attribute.name === Constants.CHAMBER ? shortValueDelimiter : null,
            !hide, // hide
            true, // visibleOnDashboard
            attribute.regex,
            attribute.isDescriptor,
            false
          )
      );
      sequence = Math.max.apply(
        [],
        attributes.map((i) => i.sequence)
      );
      attributes.push(
        new AttributeItem(
          sequence++,
          Constants.TimeFrame,
          Constants.TimeFrame,
          true,
          false,
          true,
          false,
          null,
          hide,
          true,
          false,
          false,
          false
        )
      );
      attributes.push(
        new AttributeItem(
          sequence++,
          Constants.From,
          Constants.From,
          true,
          false,
          true,
          false,
          null,
          hide,
          true,
          false,
          false,
          false
        )
      );
      attributes.push(
        new AttributeItem(
          sequence++,
          Constants.To,
          Constants.To,
          true,
          false,
          true,
          false,
          null,
          hide,
          true,
          false,
          false,
          false
        )
      );
      attributes.push(
        new AttributeItem(
          sequence++,
          Constants.Count,
          Constants.Count,
          true,
          false,
          true,
          false,
          null,
          hide,
          true,
          false,
          false,
          false
        )
      );
      this.attributes = attributes;
    });
  }

  getAllSelectData() {
    const toolLists: any = [];
    const chamberLists: any = [];
    const routesIds: string[] = [];
    const recipeIds: string[] = [];
    const productIds: string[] = [];
    this.selectedAttributeConfigs
      ?.filter((e: any) => e.value && e.value.length > 0)
      ?.forEach((item: any) => {
        switch (true) {
          case item.title === 'Tool' || item.title === 'EQP':
            if (Array.isArray(item.value)) {
              toolLists.push(...item.value);
            } else {
              toolLists.push(item.value);
            }
            break;
          case item.title === 'Chamber':
            if (item.value) {
              if (Array.isArray(item.value)) {
                chamberLists.push(...item.value);
              } else {
                chamberLists.push(item.value);
              }
            }
            break;
          case item.title === 'Route':
            if (Array.isArray(item.value)) {
              routesIds.push(...item.value);
            } else {
              routesIds.push(item.value);
            }
            break;
          case item.title === 'Recipe':
            if (Array.isArray(item.value)) {
              recipeIds.push(...item.value);
            } else {
              recipeIds.push(item.value);
            }
            break;
          case item.title === 'Product':
            if (Array.isArray(item.value)) {
              productIds.push(...item.value);
            } else {
              productIds.push(item.value);
            }
            break;
        }
      });
    return { toolLists, chamberLists, routesIds, recipeIds, productIds };
  }
  // 计算eqpModuleIds
  setOneEqpModuleIds(chamberLists: any, toolLists: any, Recipe: string) {
    this.eqpModuleIds = this.setEqpModuleIds(chamberLists, toolLists, this.allLocations);
    // this.recipeId = Recipe;
    this.recipeIds = Recipe?.split(',');
  }
  setEqpModuleIds(chamberLists: any, toolLists: any, allLocations: any) {
    const eqpModuleIds: string[] = [];
    chamberLists?.forEach((chamber: any) => {
      if (toolLists?.includes(chamber?.split('/')?.[0])) {
        allLocations?.forEach((location: any) => {
          eqpModuleIds.push(`${location}:${chamber}`);
        });
      }
    });
    return eqpModuleIds;
  }
  setOneEqpModuleIdsVal(eqpModuleIds: any, Recipe: string) {
    this.eqpModuleIds = eqpModuleIds;
    // this.recipeId = Recipe;
    this.recipeIds = Recipe?.split(',');
  }
  // 获取表单下拉选项的数据
  getFormItemData = async (
    open: any,
    i: {
      locations: any;
      loadedData: boolean;
      name: any;
      sequence: number;
      options: any;
      title: string;
    }
  ) => {
    if (open && !i.loadedData) {
      // console.log('重新加载一次数据吧');
      i.loadedData = true;
      const req: any = {};
      req.attributeName = i.name;
      req.timePeriod = this.getTimePeriod(false);
      let eqpModuleIds: string[] = [];
      this.selectedAttributeConfigs.sort((a, b) => a.sequence - b.sequence);
      // .filter((e: any) => e.sequence < i.sequence)
      const { toolLists, chamberLists, routesIds, recipeIds, productIds } = this.getAllSelectData();
      let chamberValues = [];
      if (chamberLists.length > 0) {
        chamberValues = chamberLists;
      } else {
        switch (true) {
          case i.title === 'Chamber':
            chamberValues = this.toolChamberIds;
            break;
          default:
            chamberValues = this.chamberIds;
            break;
        }
      }
      eqpModuleIds = this.setEqpModuleIds(chamberValues, toolLists, this.allLocations);
      let from: any = '';
      let to: any = '';
      if (this.fromToType === 'specific') {
        from = req.timePeriod.from;
        to = req.timePeriod.to;
      } else {
        if (this.relativeUnit === 'hours') {
          from = dayjs().subtract(this.relativeTime, 'hour').valueOf();
        } else {
          from = dayjs().subtract(this.relativeTime, 'day').valueOf();
        }
        to = dayjs().valueOf();
      }
      const isDev = process.env.NODE_ENV === 'development';
      const path = window.location.pathname;
      await getEesDbFileData({
        bodyParams: {
          traceFilterType: i.title === 'EQP' ? 'Tool' : i.title,
          from,
          to,
          eqpModuleIds,
          routesIds,
          recipeId: recipeIds[0],
          productIds,
          routePath: isDev ? '/cm/cm-main' : path
        },
        headerParams: {
          log: 'Y',
          page: buttonIds.pageId,
          action: 'get-ees-db-file-data'
        }
      }).then((res: { data: any }) => {
        let resultData = [];
        switch (true) {
          case i.title === 'Tool' || i.title === 'EQP':
            resultData = res?.data?.toolIds;
            this.toolChamberIds = res?.data?.chamberIds;
            break;
          case i.title === 'Chamber':
            resultData = res?.data?.chamberIds;
            this.chamberIds = res?.data?.chamberIds;
            break;
          default:
            resultData = res?.data?.dataIds;
            break;
        }
        res.data?.locations?.forEach((location: string) => this.allLocations.add(location));
        i.options = resultData?.map((e: any) => {
          // recipe增加每个里面有多少运行过的wafer
          if (typeof e === 'object') {
            return {
              label: e.key + '(' + e.value + ')',
              title: e.key + '(' + e.value + ')',
              value: e.key,
              name: i.title
            };
          }
          return { title: e, value: e, name: i.title };
        });
      });
    } else {
      console.log('已经加载过数据啦');
    }
  };

  isDynamicReferenceByTrace(selectedReferenceType: string): boolean {
    return (
      selectedReferenceType === 'dynamic_trace' || selectedReferenceType === 'dynamic_trace_norm'
    );
  }

  isDynamicReferenceByQualityDataType(selectedReferenceType: string) {
    return (
      selectedReferenceType === 'dynamic_metrology' || selectedReferenceType === 'dynamic_yield'
    );
  }

  async isAnalysisSettingInvalid(selectedReferenceType: string): Promise<any> {
    const isDynamic = this.isDynamicReferenceByTrace(selectedReferenceType);
    if (!this.groupConfigs || this.groupConfigs.length < 2) {
      throw new Error('At least two datasets are required to run analysis.');
    }
    if (this.groupConfigs.some((gc) => gc.isFileData && !gc.file)) {
      throw new Error('Please select file(s) again for security reason.');
    }
    if (!isDynamic && this.groupConfigs.every((gc) => !gc.reference)) {
      throw new Error('At least one reference is required to run analysis.');
    }
    if (!isDynamic && this.groupConfigs.every((gc) => gc.reference)) {
      throw new Error('At least one non-reference dataset is required to run analysis.');
    }
  }

  hasResults(): boolean {
    return (this.resultRows &&
      this.resultRows.length > 0 &&
      this.groupConfigs &&
      this.groupConfigs.length > 0) as boolean;
  }

  // 获取 descriptorValues
  getSelectedDescriptorValues() {
    let descriptorValues: any[] = [];
    if (this.groupConfigs.length > 0) {
      const gc = this.groupConfigs[0];
      gc.selectedAttributeConfigs.filter((i) => i.isDescriptor);
      gc.selectedAttributeConfigs
        .filter((i) => i.isDescriptor)
        .forEach((attr) => {
          const fieldValueByAlias = GroupConfigVO.getFieldValueByAlias(attr.alias, gc);
          const tempArr = fieldValueByAlias.split(',');
          descriptorValues = descriptorValues.concat(tempArr);
        });
    }
    return descriptorValues;
  }

  getSelectedContexts(): Context[] {
    return !this.contexts || this.contexts.length < 1
      ? []
      : this.contexts
          .filter((c) => c.isSelected)
          .map((c) => {
            return new Context(
              c.isSelected,
              c.lot,
              c.wafer,
              c.datasetId,
              c.tool,
              c.chamber,
              c.recipe,
              c.startTime,
              c.endTime,
              c.descriptor_1,
              c.descriptor_2,
              c.isVisible
            );
          });
  }

  getSelectedAttributes(): any {
    return this.attributeConfigs
      .filter((attr) => !attr.hide || attr.isReferenceAttribute)
      .map((attribute) => ({
        sequence: attribute.sequence,
        name: attribute.name,
        alias: attribute.alias,
        visibleOnHeader:
          this.selectedAttributeConfigs.findIndex((i) => i.name === attribute.name) >= 0,
        isSelected: this.getAttributeValueArray(attribute.name).length > 0,
        isSingle: attribute.isSingle,
        regex: attribute.regex,
        isMandatory: attribute.isMandatory,
        isDescriptor: attribute.isDescriptor,
        isReferenceAttribute: attribute.isReferenceAttribute
      }));
  }

  setSelectedAttributeConfigs(data: any) {
    this.selectedAttributeConfigs = data;
  }

  // 设置Dataset第一行内容
  setHeaderInfo(headerInfo: any) {
    this.headerInfo = headerInfo;
  }
  getHeaderInfo(headerInfo: any) {
    return this.headerInfo;
  }

  getRequestInstance(scheduledAnalysis?: boolean): any {
    return {
      id: this.id,
      name: this.key,
      waferLoops: this.waferLoops,
      reference: this.reference,
      filteredContexts: this.filteredContexts,
      selectedRows: this.selectedRows,
      headerInfo: this.headerInfo

      // fromToType: this.fromToType,
      // selection: this.getDataSplitSelection(),
      // userSelection: [],
      // selectedAttributes: this.getSelectedAttributes(),
      // timePeriod: this.getTimePeriod(scheduledAnalysis),
      // fileName: this.file ? this.file.name : null,
    };
  }

  isReferenceColumn(columnIndex: number) {
    if (columnIndex < 0) {
      return false;
    }
    if (columnIndex > this.groupConfigs.length - 1) {
      return false;
    }
    return this.groupConfigs[columnIndex].reference;
  }
  clearResult() {
    // 重置属性
    this.tool = undefined;
    this.chamber = undefined;
    this.route = undefined;
    this.recipe = undefined;
    this.product = undefined;
    this.count = undefined;
    this.reference = false;
    this.id = undefined;
    this.key = undefined;
    this.createDatasetByEachChamber = true;
    this.filteredContexts = undefined;
    this.lotCount = undefined;
    this.waferCount = undefined;
    this.isFileData = false;
    this.file = null;
    this.color = '';
    this.chartLegendColor = '';
    this.resultRows = undefined;
    this.resultGroups = undefined;
    this.resultGroupInfos = [];
    this.resultGroupInfo = undefined;
    this.analysisOptions = {};
  }
  clear() {
    // 重置属性
    this.tool = undefined;
    this.chamber = undefined;
    this.route = undefined;
    this.recipe = undefined;
    this.product = undefined;
    this.timeFrame = 'Static';
    this.from = dayjs().subtract(1, 'day');
    this.to = dayjs();
    this.count = undefined;
    this.reference = false;
    this.id = undefined;
    this.key = undefined;
    this.fromToType = 'relative';
    this.createDatasetByEachChamber = true;
    this.contexts = [];
    this.selectedRows = [];
    this.filteredContexts = undefined;
    this.lotCount = undefined;
    this.waferCount = undefined;
    this.isFileData = false;
    this.file = null;
    this.relativeTime = 8;
    this.relativeUnit = 'hours';
    this.color = '';
    this.chartLegendColor = '';
    this.groupConfigs = [];
    this.resultRows = undefined;
    this.resultGroups = undefined;
    this.resultGroupInfos = [];
    this.resultGroupInfo = undefined;
    this.fileNames = undefined;
    this.analysisOptions = {};
    this.isLoop = false;
    this.loopGroup = '';
    this.runType = '';
    this.stationCompare = false;
    this.headerInfo = {};
  }
}
