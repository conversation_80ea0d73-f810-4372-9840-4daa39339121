<script lang="ts" setup>
import { defineProps, watch, nextTick, reactive, ref, onBeforeUnmount, onMounted } from 'vue';
import * as echarts from '@futurefab/echarts/core';
import { LineChart, ScatterChart } from '@futurefab/echarts/charts';
import { stateViewer as buttonIds } from '@/log-config/index';
import {
  TitleComponent,
  // 组件类型的定义后缀都为 ComponentOption
  TooltipComponent,
  GridComponent,
  // 数据集组件
  DatasetComponent,
  // 内置数据转换器组件 (filter, sort)
  TransformComponent,
  ToolboxComponent,
  LegendComponent,
  BrushComponent
} from '@futurefab/echarts/components';
import { LabelLayout, UniversalTransition } from '@futurefab/echarts/features';
import { CanvasRenderer } from '@futurefab/echarts/renderers';
import {
  getSeries,
  getToday,
  setOptions,
  getTooltip,
  getScatterData,
  exportFun,
  clearMutilTooltip,
  dispatchZoom,
  dispatchBrush,
  removeBrush,
  handleGroupChartData,
  handleFilterChartData,
  getLegendTree,
  getLegendConfig,
  serieConfig
} from './tools';
import dayjs from 'dayjs';
import ChartGroup from './chart-group/index.vue';
import { Marker } from '@xchart/marker';
import {
  formatterY,
  showConfirm,
  showError,
  showWarning,
  getSingleButtonAuthority,
  exportXlsxMoreSheets,
  EesButtonTip
} from '@futurefab/ui-sdk-ees-basic';
import { setColor } from '@/utils/tools';
import { useRouter } from 'vue-router';
import { cloneDeep } from 'lodash-es';
// import { getTraceParamSpec } from '@futurefab/ui-sdk-api';
import { t } from '@futurefab/vxe-table';
import type { ButtonAuthority, DataIndex } from './interface';
import { baseUrl } from '@/assets/const';
import {
  EesCopyChart,
  EesShowRightY,
  EesAutoY,
  EesLegendFilter
} from '@futurefab/ui-sdk-ees-charts';
// 注册必须的组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  GridComponent,
  DatasetComponent,
  TransformComponent,
  ToolboxComponent,
  LegendComponent,
  BrushComponent,
  LineChart,
  ScatterChart,
  LabelLayout,
  UniversalTransition,
  CanvasRenderer
]);
const props = defineProps({
  searchCondition: {
    type: String,
    default: ''
  },
  startDate: {
    type: Number,
    default: getToday().start
  },
  endDate: {
    type: Number,
    default: getToday().end
  },
  isSplit: {
    type: Boolean,
    default: false
  },
  isShowPoint: {
    type: Boolean,
    default: true
  },
  selectedPointLimit: {
    type: Number,
    default: 100
  },
  maxExportLimit: {
    type: Number,
    default: 30000
  },
  buttonAuthority: {
    type: Object,
    default: () => {
      return {} as ButtonAuthority;
    }
  },
  exportName: {
    type: String,
    default: 'Chart'
  }
});
const emits = defineEmits(['hideLoading', 'showLoading']);
//ref 获取dom
const xMutilChart = ref();
const xChart = ref();
const xScatterChart = ref();
const xGroup = ref();
const xFilter = ref();
const xTraceDetail = ref();
let chartShowRightY = ref();
let chartAutoY = ref();

const router = useRouter();

const params = reactive({
  legendTree: [] as any[],
  legendData: [] as string[] | string[][],
  filterLegendData: [] as string[] | string[][],
  afterGroupData: [] as any[],
  yMin: undefined as number | undefined,
  yMax: undefined as number | undefined,
  yRightMin: undefined as number | undefined,
  yRightMax: undefined as number | undefined,
  yLog: false,
  yRightLog: false,
  yScale: 10,
  yRightScale: 10,
  isBaseLine: false,
  isMultiTooltip: false,
  isDeleteBlank: false,
  sumModuleNameList: [] as string[],
  hasRightY: false,
  rightParamList: [] as string[], //右侧Y轴总的list
  rightYList: [] as string[], //被选中的右侧Y轴list
  isSelectArea: false,
  selectedData: [] as any[],
  isShowTraceData: false,
  layout: 1,
  isSplit: false,
  chartType: 1,
  showChart: true,
  isScatter: false,
  scatterData: [] as any[],
  specParamList: [] as string[],
  paramAliasList: [] as string[],
  totalCount: 0,
  multiTooltipInfo: [] as DataIndex[] | DataIndex[][]
});
//chart对象
let myChart: echarts.ECharts;
let chartList = [] as echarts.ECharts[];
//tooltip marker
let marker = null as any;
//保留一份初始数据
let totalData = [] as any[];
//filter legend 全选处理 只有在单chart中用到
const toggleAllLegend = (checked: boolean) => {
  console.log('allLegend', checked);
  onMultiToolTip(true);
  handleRemoveBrush();
  params.filterLegendData = [];
  myChart.dispatchAction({
    type: 'legendAllSelect'
  });
  if (!checked) {
    params.filterLegendData = params.legendTree;
    myChart.dispatchAction({
      type: 'legendInverseSelect'
    });
  }
};
//filter legend 点击单选处理 只有在单chart中用到
const toggleOneLegend = (argus: {
  list: string[];
  value: string;
  checked: boolean;
  label: string;
  index: number;
  indexList: number[];
}) => {
  const { list, value, checked, index, indexList } = argus;
  console.log('oneLegend', argus, params.legendData);
  onMultiToolTip(true);
  handleRemoveBrush();
  params.filterLegendData = [];
  (params.legendData as string[]).forEach((name: string) => {
    const curName = name.split('^')[index];
    if (checked && curName === value.toString()) {
      myChart.dispatchAction({
        type: 'legendSelect',
        name
      });
    }
    if (list.length) {
      list.forEach((it, i) => {
        const n = name.split('^')[indexList[i]];
        if (n === it.toString()) {
          (params.filterLegendData as string[]).push(name);
          myChart.dispatchAction({
            type: 'legendUnSelect',
            name
          });
        }
      });
    }
  });
};
//点击chart的legend时， 处理与filter legend数据同步
const filterLegend = (argus: any) => {
  const { selected, clickName, chartIndex } = argus;
  if (typeof chartIndex == 'number' && params.isBaseLine) {
    handleRemoveBrush(chartIndex);
  } else {
    handleRemoveBrush();
  }
  const list: string[] = [];
  for (const key in selected) {
    if (!selected[key]) list.push(key);
  }
  if (typeof chartIndex == 'number') {
    params.filterLegendData[chartIndex] = list;
    return false;
  } else {
    params.filterLegendData = list;
  }
  const keyList = [] as string[];
  params.legendTree.forEach((item: any) => {
    if (item.children.length > 0) {
      keyList.push(item.label);
    }
  });

  const legendNameList = Object.keys(selected);
  const flagList = Object.values(selected);
  const isChecked = selected[clickName];
  const clickNameList = clickName.split('^');
  clickNameList.forEach((currentName: string, index: number) => {
    let flag = true; //判断是否存在同名 但选择状态不同的
    for (let i = 0; i < legendNameList.length; i++) {
      const nameList = legendNameList[i].split('^');
      if (isChecked) {
        xFilter.value &&
          xFilter.value.setChecked({
            label: keyList[index],
            name: currentName,
            checked: isChecked
          });
      } else {
        if (nameList[index] === currentName && flagList[i] != isChecked) {
          flag = false;
          return false;
        }
      }
    }
    if (flag) {
      xFilter.value &&
        xFilter.value.setChecked({
          label: keyList[index],
          name: currentName,
          checked: isChecked
        });
    }
  });
};
//将chart的legend转为filter legend需要的数据格式，并传值给filter legend组件 只有在单chart中用到
const setFilterLegend = (data: any[], groupingMap?: any) => {
  params.legendTree = getLegendTree(data, groupingMap);
  xFilter.value && xFilter.value.setLegendTree(params.legendTree);
};
//将包裹mutilTooltip的div id传值给copy chart组件
const getCopyChartId = () => {
  let id = '';
  id = !params.showChart
    ? params.isScatter
      ? 'viewer_chart-scatter-content'
      : 'viewer_chart-mutil-content'
    : 'viewer_chart';
  return id;
};
//处理右侧所有需要异步处理的操作按钮(需要将loading显示出来之后，再执行后面的操作)
const handleShowLoading = (callback: Function) => {
  emits('showLoading');
  handleDispose();
  handleMutilDispose();
  setTimeout(() => {
    callback && callback();
  }, 50);
};
//group&filter
const doGrouping = (data: any) => {
  const callbackFun = () => {
    console.time('handle grouping data');
    onMultiToolTip(true);
    params.filterLegendData = [];
    params.isSelectArea = false;
    params.selectedData = [];
    params.afterGroupData = [];
    const { filterMap, groupingMap, chartItem } = data;
    let newData = cloneDeep(totalData);
    if (Object.keys(filterMap).length > 0) {
      for (const key in filterMap) {
        newData = handleFilterChartData({
          data: newData,
          name: key,
          value: filterMap[key]
        });
      }
    }
    if (newData.length == 0) {
      params.afterGroupData = [];
      emits('hideLoading');
      return false;
    }
    if (Object.keys(groupingMap).length > 0) {
      newData = handleGroupChartData({
        data: newData,
        groupingMap
      });
    }
    if (newData.length == 0) {
      params.afterGroupData = [];
      emits('hideLoading');
      return false;
    }
    setFilterLegend(newData, groupingMap);
    params.isSplit = chartItem.isSplitParam;
    params.isScatter = chartItem.kind == 'scatter';
    params.layout = chartItem.layout;
    params.chartType = chartItem.type;
    console.timeEnd('handle grouping data');
    if (params.isScatter) {
      console.time('handle grouping scatterData');
      params.showChart = false;
      newData = getScatterData({
        chartItem,
        data: newData,
        isStepGroup: groupingMap.step,
        chartType: chartItem.type
      });
      console.timeEnd('handle grouping scatterData');
    } else {
      if (chartItem.type == 1 && chartItem.isSplitParam) {
        //sumParam相同的参数放在同一个chart中
        params.showChart = false;
        let dataInfo = {} as any;
        newData.forEach((item: any) => {
          if (!dataInfo[item.paramInfo.sumModuleName]) {
            dataInfo[item.paramInfo.sumModuleName] = [item];
            return false;
          }
          dataInfo[item.paramInfo.sumModuleName].push(item);
        });
        let list = Object.values(dataInfo);
        newData = list;
      } else if (chartItem.type == 1 && !chartItem.isSplitParam) {
        //全部在一个chart中
        params.showChart = true;
      } else if (chartItem.type == 2 && chartItem.isSplitParam) {
        // 根据分组单独画chart
        params.showChart = false;
      } else if (chartItem.type == 2 && !chartItem.isSplitParam) {
        // 不同sumParam的同一个分组放在同一个chart中
        params.showChart = false;
        const myData = {} as any;
        newData.forEach((item: any) => {
          const legend = item.paramInfo.legend.split(item.paramInfo.sumParamAlias).join('');
          const keys = Object.keys(myData);
          if (keys.length == 0) {
            myData[legend] = [item];
          } else {
            if (keys.includes(legend)) {
              myData[legend].push(item);
            } else {
              myData[legend] = [item];
            }
          }
        });
        newData = Object.values(myData);
      }
    }

    params.afterGroupData = newData;
    reDraw();
  };
  handleShowLoading(callbackFun);
};
const applyAutoY = (
  min: number | undefined,
  max: number | undefined,
  minRight: number | undefined,
  maxRight: number | undefined,
  log: boolean,
  rightLog: boolean,
  scale: number,
  rightScale: number
) => {
  const callbackFun = () => {
    params.yMin = min;
    params.yMax = max;
    params.yRightMin = minRight;
    params.yRightMax = maxRight;
    params.yLog = log;
    params.yRightLog = rightLog;
    params.yScale = scale;
    params.yRightScale = rightScale;
    reDraw();
  };
  handleShowLoading(callbackFun);
};
//显示规格线
const showBaseLine = () => {
  params.isBaseLine = !params.isBaseLine;
  reDraw();
};
//切换为刷选状态
const doSelectArea = () => {
  onMultiToolTip(true);
  params.isSelectArea = !params.isSelectArea;
  params.selectedData = [];
  if (params.showChart) {
    handleSelectArea(myChart);
  } else {
    chartList.forEach((target: echarts.ECharts) => {
      handleSelectArea(target);
    });
  }
};
const handleSelectArea = (target: any) => {
  if (params.isSelectArea) {
    params.selectedData = [];
    dispatchBrush(target);
  } else {
    removeBrush(target);
    dispatchZoom(target);
  }
};
//获取刷选状态下，已选择的数据
const getSelectData = (data: any[], chartIndex?: number) => {
  // if (data.length == 0) {
  //     params.isSelectArea = false;
  // }
  if (typeof chartIndex == 'number') {
    data.forEach((item: any) => {
      item.chartIndex = chartIndex;
      const existData = params.selectedData.filter(
        (val: any) =>
          val.serieIndex == item.serieIndex &&
          val.dataIndex == item.dataIndex &&
          val.chartIndex == chartIndex
      );
      existData.length == 0 && params.selectedData.push(item);
    });
  } else {
    params.selectedData = data;
  }
};
//取消刷选状态，切换为缩放状态
const handleRemoveBrush = (chartIndex?: number) => {
  params.isSelectArea = false;
  params.selectedData = [];
  if (typeof chartIndex == 'number') {
    chartList[chartIndex] && removeBrush((chartList as any[])[chartIndex]);
    return false;
  }
  doRemoveBrush(myChart);
  chartList.length > 0 &&
    chartList.forEach((target: echarts.ECharts) => {
      doRemoveBrush(target);
    });
};
const doRemoveBrush = (target: any) => {
  target && removeBrush(target);
  target && dispatchZoom(target);
};
//显示右侧Y轴
const onShowRightY = (list: string[]) => {
  const callbackFun = () => {
    params.rightYList = list;
    params.hasRightY = list.length > 0;
    reDraw();
  };
  handleShowLoading(callbackFun);
};
//切换为tooltip模式
const onMultiToolTip = (flag?: boolean | Event) => {
  if (flag) {
    //清空tooltip
    params.isMultiTooltip = false;
  } else {
    params.isMultiTooltip = !params.isMultiTooltip;
  }
  if (params.isMultiTooltip) {
    marker = new Marker({
      wrapper: params.showChart
        ? xChart.value
        : params.isScatter
          ? xScatterChart.value
          : xMutilChart.value,
      closable: true,
      closeByRightClick: false,
      canMoveOutside: false,
      copyable: true
    });
  } else {
    marker && marker.clear();
    marker = null;
    params.multiTooltipInfo = [];
  }
  // xChart.value && clearMutilTooltip(xChart.value);
  // xMutilChart.value && clearMutilTooltip(xMutilChart.value);
  // xScatterChart.value && clearMutilTooltip(xScatterChart.value);
};
//点击chart上的数据点，显示tooltip
const handleMutilTooltip = ({
  type,
  value,
  chartIndex
}: {
  type: string;
  value?: any;
  chartIndex?: number;
}) => {
  if (type == 'hide') {
    onMultiToolTip(true);
    return false;
  }
  if (!params.isMultiTooltip) return false;
  const { event, seriesIndex, dataIndex, color } = value;
  let x = event.offsetX;
  let y = event.offsetY;
  const flag = typeof chartIndex == 'number';
  if (flag) {
    const height = params.isScatter ? 360 : 370;
    const width = document.getElementById(
      params.isScatter ? 'viewer_chart-scatter-content' : 'viewer_chart-mutil-content'
    )?.clientWidth;
    if (params.layout == 1) {
      y = y + height * chartIndex;
    } else {
      x = x + ((chartIndex % params.layout) * width!) / params.layout;
      y = y + Math.floor(chartIndex / params.layout) * height;
    }
  }
  let dataValue: any = '';
  if (flag) {
    if (Array.isArray(params.afterGroupData[chartIndex])) {
      dataValue = params.afterGroupData[chartIndex][seriesIndex];
    } else {
      dataValue = params.afterGroupData[chartIndex];
    }
    if (params.multiTooltipInfo[chartIndex]) {
      (params.multiTooltipInfo[chartIndex] as DataIndex[]).push({
        seriesIndex,
        dataIndex
      });
    } else {
      params.multiTooltipInfo[chartIndex] = [
        {
          seriesIndex,
          dataIndex
        }
      ];
    }
  } else {
    dataValue = params.afterGroupData[seriesIndex];
    (params.multiTooltipInfo as DataIndex[]).push({
      seriesIndex,
      dataIndex
    });
  }
  marker.addMark({
    key: `${seriesIndex}-${dataIndex}${flag ? '-' + chartIndex : ''}`,
    x,
    y,
    data: [
      {
        color,
        label: '',
        // point value
        value: getTooltip({ value: dataValue, dataIndex })
      }
    ]
  });
};
const resetMultiTooltip = (chartIndex?: number) => {
  const flag = typeof chartIndex == 'number';
  const list = flag
    ? (params.multiTooltipInfo[chartIndex] as DataIndex[])
    : (params.multiTooltipInfo as DataIndex[]);
  const target = flag ? chartList[chartIndex] : myChart;
  const data = flag ? params.afterGroupData[chartIndex] : params.afterGroupData;
  list.forEach((item: DataIndex) => {
    if (!data[item.seriesIndex]) return false;
    const pixData =
      target &&
      target.convertToPixel({ seriesIndex: item.seriesIndex }, [
        data[item.seriesIndex]['timestamp'][item.dataIndex],
        data[item.seriesIndex]['seriesValue'][item.dataIndex]
      ]);
    let x = pixData[0],
      y = pixData[1];
    if (flag) {
      const height = params.isScatter ? 360 : 370;
      const width = document.getElementById(
        params.isScatter ? 'viewer_chart-scatter-content' : 'viewer_chart-mutil-content'
      )?.clientWidth;
      if (params.layout == 1) {
        y = y + height * chartIndex;
      } else {
        x = x + ((chartIndex % params.layout) * width!) / params.layout;
        y = y + Math.floor(chartIndex / params.layout) * height;
      }
    }
    marker.updateMark({
      key: `${item.seriesIndex}-${item.dataIndex}${flag ? '-' + chartIndex : ''}`,
      x,
      y
    });
  });
};
//删除空白区域
const onDeleteBlank = () => {
  const callbackFun = () => {
    params.isDeleteBlank = !params.isDeleteBlank;
    reDraw();
  };
  handleShowLoading(callbackFun);
};
//导出数据
const onExport = async () => {
  const handleFun = () => {
    emits('showLoading', 'common.loading.text');
    setTimeout(() => {
      const data = exportFun(totalData);
      console.time('exprorting');
      exportXlsxMoreSheets(data, `${props.exportName}.xlsx`);
      console.timeEnd('exprorting');
      emits('hideLoading');
    }, 50);
  };
  //数据总量大于后端配置的值，显示提示框告知用户
  if (params.totalCount > props.maxExportLimit) {
    const type = await showConfirm({
      msg: t('common.tip.exportTips', { count: params.totalCount })
    });
    if (type == 'confirm') {
      handleFun();
    }
  } else {
    handleFun();
  }
};
//还原chart到初始状态
const onInitialize = () => {
  const callbackFun = () => {
    onMultiToolTip(true);
    resetParams();
    setFilterLegend(totalData);
    params.showChart = !props.isSplit;
    reDraw();
  };
  handleShowLoading(callbackFun);
};
//处理chart的option
const handleOptions = ({
  data,
  chartIndex,
  colorIndex
}: {
  data: any;
  chartIndex?: number;
  colorIndex?: number | null;
}) => {
  const chartData = data;
  let totalTimeList: number[] = [];
  let legendList: string[] = [];
  chartData.forEach((item: any) => {
    //添加Y轴索引
    item['paramInfo']['yAxisIndex'] = 0;
    //含有右侧Y轴的数据
    if (params.hasRightY) {
      params.rightYList.forEach((val: string) => {
        // console.log(item, val);
        // if (item['paramInfo']['sumParamAlias'] === val) {
        if (item['itemName'] === val) {
          item['paramInfo']['yAxisIndex'] = 1;
        }
      });
    }
    //是deleteblank模式
    if (params.isDeleteBlank) {
      item.timestamp.forEach((it: number) => totalTimeList.push(it));
    }
    //处理legend
    const name = item['paramInfo']['legend'];
    if (!legendList.includes(name)) {
      legendList.push(name);
    }
  });
  let isShowLeftY = true,
    isShowRightY = false;
  if (typeof chartIndex == 'number') {
    params.legendData[chartIndex] = legendList;
    if (params.hasRightY) {
      //处理多chart判断是否显示左右Y轴
      if ((params.chartType == 1 && params.isSplit) || params.chartType == 2) {
        const isExist =
          chartData.filter((item: any) => params.rightYList.includes(item.paramInfo.sumParamAlias))
            .length != chartData.length;
        isShowLeftY = (params.rightYList.length > 0 && isExist) || params.rightYList.length == 0;
      } else if (params.chartType == 2 && params.isSplit) {
        isShowLeftY = !params.rightYList.includes(chartData.paramInfo.sumParamAlias);
      }
      if (params.chartType == 2 && !params.isSplit) {
        isShowRightY = params.rightYList.length > 0;
      } else {
        isShowRightY = !isShowLeftY;
      }
    }
  } else {
    params.legendData = legendList;
    if (params.hasRightY) {
      //单个chart判断是否显示左右Y轴
      isShowLeftY =
        (params.rightYList.length > 0 &&
          params.rightYList.length != params.rightParamList.length) ||
        params.rightYList.length == 0;
      isShowRightY = true;
    }
  }
  //时间戳去重排序
  totalTimeList = [...new Set(totalTimeList)].sort();
  //用于deleteblank，X轴时间戳List对应的indexList
  const xIndex = [] as number[];
  totalTimeList.forEach((val, index) => xIndex.push(index));

  //设置左侧Y轴最大最小值
  const yAxis = [
    {
      show: isShowLeftY,
      min: () => {
        return params.yMin;
      },
      max: () => {
        return params.yMax;
      },
      type: params.yLog ? 'log' : 'value',
      logBase: params.yScale,
      minorSplitLine: params.yLog
    }
  ];
  //设置右侧Y轴最大最小值
  if (params.hasRightY) {
    yAxis.push({
      show: isShowRightY,
      min: () => {
        return params.yRightMin;
      },
      max: () => {
        return params.yRightMax;
      },
      type: params.yRightLog ? 'log' : 'value',
      logBase: params.yRightScale,
      minorSplitLine: params.yRightLog
    });
  }
  //处理series(由于需要使用totalTimeList，因此不能放到同一个循环内)
  console.time('handle series');
  const series = getSeries({
    list: chartData,
    paramName: { x: 'timestamp', y: 'seriesValue' },
    isDeleteBlank: params.isDeleteBlank,
    totalTimeList: totalTimeList,
    isShowPoint: props.isShowPoint,
    colorIndex
  });
  console.timeEnd('handle series');
  //配置options
  const options = setOptions({
    grid: {
      left: 75,
      right: 75
    },
    legend: getLegendConfig(legendList, params.layout),
    xAxis: {
      type: params.isDeleteBlank ? 'category' : 'time',
      axisLabel: {
        formatter: (value: any, index: number) => {
          const item = params.isDeleteBlank ? totalTimeList[index] : value;
          let val = dayjs(item).format('MM-DD HH:mm:ss');
          // //可使用lotid展示在x轴;
          // chartData.forEach((d: any) => {
          // 	d.timestamp.forEach((t: number, i: number) => {
          // 		if (t <= item) {
          // 			val = d['lotid'][i];
          // 		}
          // 	});
          // });
          return val;
        },
        // params.isDeleteBlank
        //     ? (value: any, index: number) => {
        //           return dayjs(totalTimeList[index]).format('MM-DD HH:mm:ss');
        //       }
        //     : '{MM}-{dd} {HH}:{mm}:{ss}',
        hideOverlap: true
      },
      axisPointer: {
        show: params.isBaseLine,
        label: {
          formatter: (argus: any) => {
            const item = params.isDeleteBlank ? totalTimeList[argus.value] : argus.value;
            let val = dayjs(item).format('MM-DD HH:mm:ss');
            // chartData.forEach((d: any) => {
            // 	d.timestamp.forEach((t: number, i: number) => {
            // 		if (t == item) {
            // 			val = d['lotid'][i];
            // 		}
            // 	});
            // });
            return val;
          }
        }
      },
      data: xIndex
    },
    yAxis,
    series: Object.freeze(series),
    axisPointer: {
      show: !props.isShowPoint
    },
    tooltip: {
      formatter: (val: any) => {
        const { dataIndex, seriesName } = val;
        const value = chartData.filter(
          (item: any) => item['paramInfo']['legend'] === seriesName
        )[0];
        return getTooltip({
          value,
          dataIndex
        });
      }
    }
  });
  console.log(1111, 'options', options);
  return options;
};
const getMin = (value: { min: number; max: number }) => {
  return value.min - (value.max - value.min) * 0.01;
};
const getMax = (value: { min: number; max: number }) => {
  return value.max + (value.max - value.min) * 0.01;
};
const handleScatterOptions = ({
  data,
  chartIndex,
  colorIndex
}: {
  data: any;
  chartIndex: number;
  colorIndex?: number | null;
}) => {
  const legendList: string[] = [];
  const series = [] as any[];
  data.forEach((v: any, i: number) => {
    series.push({
      ...serieConfig({ isShowPoint: false }),
      emphasis: {
        scale: true
      },
      name: v['paramInfo']['legend'].split(`${v['paramInfo']['sumParamAlias']}^`)[1],
      type: 'scatter',
      legendHoverLink: false,
      data: v.xyValue,
      color: typeof colorIndex == 'number' ? setColor(chartIndex) : setColor(i),
      large: true
    });
    const name = v['paramInfo']['legend'];
    legendList.indexOf(name) < 0 &&
      legendList.push(name.split(`${v['paramInfo']['sumParamAlias']}^`)[1]);
  });
  params.legendData[chartIndex] = legendList;
  const options = setOptions({
    grid: {
      top: 60,
      right: 40
    },
    legend: getLegendConfig(legendList, params.layout),
    xAxis: {
      type: 'value',
      axisLabel: {
        hideOverlap: true,
        formatter: (value: number) => {
          const newValue = formatterY(value);
          return newValue;
        }
      },
      axisPointer: {
        show: false
      },
      min: getMin,
      max: getMax
    },
    yAxis: {
      axisLabel: {
        formatter: (value: any) => {
          const newValue = formatterY(value);
          return newValue;
        }
      },
      type: 'value',
      min: getMin,
      max: getMax
    },
    axisPointer: {
      show: false
    },
    series,
    tooltip: {
      formatter: (val: any) => {
        const { dataIndex, seriesName } = val;
        const value = data.filter(
          (item: any) =>
            item['paramInfo']['legend'].split(`${item['paramInfo']['sumParamAlias']}^`)[1] ===
            seriesName
        )[0];
        return getTooltip({
          value,
          dataIndex
        });
      }
    }
  });
  return options;
};
//销毁chart实例
const handleDispose = () => {
  if (myChart && !myChart.isDisposed()) myChart.dispose();
};
const handleMutilDispose = () => {
  chartList.forEach((target: echarts.ECharts) => {
    if (target && !target.isDisposed()) target.dispose();
  });
  chartList = [];
};
//用于多chart显示隐藏legend
const handleLegend = ({
  legendData,
  filterLegendData,
  chartIndex
}: {
  legendData: string[];
  filterLegendData: any[];
  chartIndex?: number;
}) => {
  const selectedObj = {} as any;
  legendData.forEach((name: string) => {
    if (!filterLegendData) {
      filterLegendData = [];
    }
    const num = filterLegendData.filter((item: string) => name === item).length;
    if (num == 0) {
      selectedObj[name] = true;
    } else {
      selectedObj[name] = false;
    }
  });
  return selectedObj;
};
//画图
const reDraw = (isInit?: boolean) => {
  if (totalData.length == 0) return false;
  onMultiToolTip(true);
  params.isSelectArea = false;
  params.selectedData = [];
  if (params.showChart) {
    handleSingleChart();
  } else {
    handleMutilChart(isInit);
  }
  if (!isInit) {
    emits('hideLoading');
  }
  console.timeEnd('drawing');
};
//绘制单chart
const handleSingleChart = () => {
  xChart.value && clearMutilTooltip(xChart.value);
  handleDispose();
  nextTick(() => {
    myChart = echarts.init(document.getElementById('viewer_chart')!);
    console.time('handle option');
    let options = handleOptions({
      data: params.afterGroupData
    });
    // console.log('options', JSON.stringify(options, null, 2));
    // let options = params.afterGroupData as any;
    console.timeEnd('handle option');
    console.time('drawing');
    myChart && myChart.setOption(options);
    // !!! Bind chat event after chart rendered in idle time
    requestIdleCallback(() => {
      keepLegendStatus(myChart);
      bindChartEvent(myChart);
    });
  });
};
//绘制多chart
const handleMutilChart = (isInit?: boolean) => {
  xMutilChart.value && clearMutilTooltip(xMutilChart.value);
  xScatterChart.value && clearMutilTooltip(xScatterChart.value);
  handleMutilDispose();
  params.afterGroupData.forEach((item: any, index: number) => {
    nextTick(() => {
      const target = echarts.init(document.getElementById(`viewer_chart${index}`)!);
      let options: Record<string, unknown> = {};
      if (params.isScatter) {
        options = handleScatterOptions({
          data: Array.isArray(item) ? item : [item],
          chartIndex: index,
          colorIndex: Array.isArray(item) ? null : index //用于多个chartItem.isSplit=true&chartItem.type=2的情况;
        });
      } else {
        options = handleOptions({
          data: Array.isArray(item) ? item : [item],
          chartIndex: index,
          colorIndex: Array.isArray(item) || isInit ? null : index //用于多个chartItem.isSplit=true&chartItem.type=2的情况;
        });
        if (params.isBaseLine) {
          (options.legend as Record<string, unknown>).selected = handleLegend({
            legendData: params.legendData[index] as string[],
            filterLegendData: params.filterLegendData[index] as any[]
          });
        }
      }
      console.time('drawing');
      target && target.setOption(options);
      chartList[index] = target;
      if (!params.isBaseLine || params.isScatter) {
        keepLegendStatus(target, index);
      }
      bindChartEvent(target, index);
    });
  });
  nextTick(() => {
    if (params.isBaseLine && !params.isScatter) {
      echarts.connect(chartList);
    } else {
      echarts.connect('');
    }
  });
};
const bindChartEvent = (target: any, chartIndex?: number) => {
  //切换为zoom模式
  dispatchZoom(target);
  //取消刷选模式
  removeBrush(target);
  /* let moveX = 0;
	let isClickBaseLine = false; //用于判断是都点击baseline上
	//鼠标按下事件，记录鼠标X轴位置
	target.getZr().on('mousedown', (event: MouseEvent) => {
		moveX = event.offsetX;
		isClickBaseLine = event.target && event.target.draggable;
	});
	//鼠标抬起事件，鼠标位置小于初始位置，还原chart
	target.getZr().on('mouseup', (event: MouseEvent) => {
		if (event.offsetX < moveX && !isClickBaseLine) {
			if (params.isBaseLine && !params.showChart) {
				reDraw();
			} else {
				keepLegendStatus(target, chartIndex);
			}
			// !!!Dispatch zoom action in idle time, because it may cause performance problems
			requestIdleCallback(() => {
				handleRemoveBrush(params.isBaseLine && !params.showChart ? chartIndex : undefined);
				dispatchZoom(target);
			});
		}
	});
	//鼠标移出标记点，处理tooltip样式
	target.on('mouseout', () => {
		const dom = document.getElementsByClassName('echarts-tooltip-hide');
		Array.from(dom).forEach((element: any) => {
			element.style.display = 'none';
		});
	}); */
  //点击chart标记点，如果是mutilTooltip模式，显示tooltip
  target.on('click', (params: any) => {
    handleMutilTooltip({ type: 'show', value: params, chartIndex });
  });
  //缩放chart，移除mutilTooltip
  target.on('datazoom', () => {
    handleMutilTooltip({ type: 'hide' });
  });
  /* //点击chart legend，记录legend状态
	target.on('legendselectchanged', function (argus: any) {
		const { selected, name } = argus;
		filterLegend({ selected, clickName: name, chartIndex });
	});
	//刷选模式下，记录选择的标记点
	target.on('brushend', (params: any) => {
		//brushType 为 'rect' range 和 coordRange 的格式为：[[minX, maxX], [minY, maxY]]
		const areas = params.areas;
		const selectedSeries = [] as any;
		areas.forEach((item: any) => {
			item.coordRanges.forEach((coordRange: any) => {
				const min = [coordRange[0][0], coordRange[1][0]];
				const max = [coordRange[0][1], coordRange[1][1]];
				selectedSeries.push({
					min,
					max,
				});
			});
		});
		const options = target.getOption();
		const series = options.series as any[];
		const legend = options.legend as any[];
		const legendData = legend[0].selected;
		const findData = [] as any[];
		series.forEach((item: any, index: number) => {
			if (legendData[item.name] !== undefined && !legendData[item.name]) {
				return false;
			}
			item.data.forEach((val: any, idx: number) => {
				const xVal = val[0];
				const yVal = val[1];
				selectedSeries.forEach((value: any) => {
					const min = value.min;
					const max = value.max;
					if (xVal >= min[0] && xVal <= max[0] && yVal >= min[1] && yVal <= max[1]) {
						findData.push({ serieIndex: index, dataIndex: idx });
					}
				});
			});
		});
		getSelectData && getSelectData(findData, chartIndex);
	}); */
};
// 保持legend状态
const keepLegendStatus = (target: echarts.ECharts, chartIndex?: number) => {
  const flag = typeof chartIndex == 'number';
  const myLegendData = flag
    ? (params.legendData[chartIndex] as string[])
    : (params.legendData as string[]);
  const myFilterLegendData = flag
    ? (params.filterLegendData[chartIndex] as string[])
    : (params.filterLegendData as string[]);
  myLegendData &&
    myLegendData.forEach((name) => {
      if (myFilterLegendData && myFilterLegendData.includes(name)) {
        target.dispatchAction({
          type: 'legendUnSelect',
          name
        });
      } else {
        target.dispatchAction({
          type: 'legendSelect',
          name
        });
      }
    });
};
//重置变量params为初始值
const resetParams = () => {
  params.yMin = undefined;
  params.yMax = undefined;
  params.yRightMax = undefined;
  params.yRightMin = undefined;
  params.yLog = false;
  params.yRightLog = false;
  params.yScale = 10;
  params.yRightScale = 10;
  params.isBaseLine = false;
  params.isMultiTooltip = false;
  params.isDeleteBlank = false;
  params.hasRightY = false;
  params.rightYList = [];
  params.isSelectArea = false;
  params.selectedData = [];
  params.isSplit = props.isSplit;
  if (totalData.length == 0) {
    params.afterGroupData = [];
  } else {
    params.afterGroupData = cloneDeep(totalData);
  }
  params.isScatter = false;
  params.layout = 1;
  params.scatterData = [];
  params.filterLegendData = [];
  params.legendData = [];
  console.log('chartAutoY.value', chartAutoY.value);
  chartAutoY.value && chartAutoY.value.resetValue();
  chartAutoY.value.autoY.active = false;
  chartShowRightY.value.showRight.value = [];
  chartShowRightY.value.showRight.active = false;
  xGroup.value && xGroup.value.handleReset(props.isSplit, true);
};
//销毁所有chart，将总数据置空
const clearChart = () => {
  handleDispose();
  handleMutilDispose();
  totalData = [];
  resetParams();
};
//处理chart的resize
const resize = () => {
  // onMultiToolTip(true);
  chartList.length > 0 &&
    chartList.forEach((target: echarts.ECharts, chartIndex: number) => {
      target && target.resize();
      if (
        params.multiTooltipInfo[chartIndex] &&
        (params.multiTooltipInfo[chartIndex] as DataIndex[]).length > 0
      ) {
        resetMultiTooltip(chartIndex);
      }
    });

  if (myChart) {
    myChart.resize();
    if (params.multiTooltipInfo.length > 0) {
      resetMultiTooltip();
    }
  }
};
//获取总数据，设置params属性值
const getData = ({
  data,
  rightParamList,
  sumModuleNameList,
  count,
  isEnd
}: {
  data: any[];
  rightParamList: string[];
  sumModuleNameList: string[];
  count: number;
  isEnd: boolean;
}) => {
  console.time('handle data');
  clearChart();
  if (data.length == 0) return false;
  params.totalCount = count;
  totalData = data;
  params.afterGroupData = cloneDeep(data);
  params.showChart = props.isSplit;
  params.isSplit = props.isSplit;
  params.rightParamList = rightParamList;
  params.sumModuleNameList = sumModuleNameList;
  // xGroup.value &&
  //     xGroup.value.getData({
  //         data: totalData,
  //         paramsAlias: params.sumModuleNameList,
  //         isSplit: params.isSplit,
  //     });
  if (params.isSplit) {
    params.showChart = false;
  } else {
    params.showChart = true;
    setFilterLegend(totalData);
  }
  console.timeEnd('handle data');
  reDraw(true);
  if (isEnd) {
    emits('hideLoading');
  }
};
const getAuthority = (buttonId: string) => {
  if (Object.keys(props.buttonAuthority).length == 0) {
    return false;
  }
  return getSingleButtonAuthority({ ...(props.buttonAuthority as ButtonAuthority) })(buttonId);
};
defineExpose({
  resize,
  getData,
  clearChart
});
onMounted(() => {
  window.onresize = () => {
    resize();
  };
});
onBeforeUnmount(() => {
  myChart && myChart.clear();
});
</script>
<template>
  <div class="viewer_chart_wrapper">
    <div v-show="params.totalCount > 0" class="viewer_toolbox">
      <ees-show-right-y
        v-show="!params.isScatter"
        ref="chartShowRightY"
        :param-list="params.rightParamList"
        :text="t('eesCharts.commonBtn.rightY')"
        @on-show-right-y="onShowRightY"
      />
      <ees-auto-y v-show="!params.isScatter" ref="chartAutoY" @apply-auto-y="applyAutoY" />
      <ees-button-tip
        v-show="!params.isScatter"
        icon="#icon-btn-blank-delete"
        :text="t('eesCharts.commonBtn.deleteBlank')"
        :is-active="params.isDeleteBlank"
        @on-click="onDeleteBlank"
      />
      <ees-button-tip
        icon="#icon-btn-multiple-tooltip"
        :text="t('eesCharts.commonBtn.multipleTooltip')"
        :is-active="params.isMultiTooltip"
        @on-click="onMultiToolTip"
      />
      <ees-legend-filter
        v-show="params.showChart"
        ref="xFilter"
        @toggle-all-legend="toggleAllLegend"
        @toggle-one-legend="toggleOneLegend"
      />
      <ees-copy-chart :container="getCopyChartId()" :chart-name="props.exportName" />
      <ees-button-tip
        icon="#icon-btn-export"
        :text="t('eesCharts.commonBtn.exportData')"
        @on-click="onExport"
      />
      <ees-button-tip
        icon="#icon-btn-initialize"
        :text="t('eesCharts.commonBtn.initialize')"
        @on-click="onInitialize"
      />
    </div>
    <div id="viewer_chart" ref="xChart"></div>
    <!-- v-if="params.showChart && params.afterGroupData.length > 0" -->
  </div>
</template>
<style lang="less" scoped>
@import url('@/assets/style/variable.less');

.viewer_chart_wrapper {
  width: 100%;
  height: 100%;
  overflow: hidden;
  border-radius: 0 0 @border-radius-xs @border-radius-xs;
  padding-top: @padding-lg + 6px; //30px
  // box-sizing: border-box;
  position: relative;

  .viewer_toolbox {
    // width: 100%;
    // height:40px;
    // display: inline-flex;
    // justify-content: flex-end;
    // align-items: center;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    position: absolute;
    // right: 40px;
    top: 0;
    z-index: 101;
    width: 100%;
    background: @bg-block-color;
  }

  #viewer_chart {
    width: 100%;
    height: 100%;
    position: relative;
    // overflow-x: hidden;
    // overflow-y: hidden;
  }
}

:deep(.xchart-marker__data) {
  align-items: flex-start !important;
  justify-content: space-between !important;
  white-space: nowrap;
}
</style>
