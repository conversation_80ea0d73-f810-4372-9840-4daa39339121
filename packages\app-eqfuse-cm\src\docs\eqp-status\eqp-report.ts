import { setHtml } from '../tools';
import VMdPreview, { xss } from '@kangc/v-md-editor/lib/preview';
const paramUsHtml = xss.process(
    VMdPreview.vMdParser.themeConfig.markdownParser.render(`
## Parameters Color by Types
<p class="left-point red">Red: The parameters have fault.</p>
<p class="left-point orange">Orange: The parameters have warning.</p>
<p class="left-point blue">Blue: The parameters have trace model.</p>
<p class="left-point purple">Purple: The parameters have chamber type trace model.</p>
`),
);
const paramCnHtml = xss.process(
    VMdPreview.vMdParser.themeConfig.markdownParser.render(`
## 颜色定义
<p class="left-point red">相应的参数发生异常</p>
<p class="left-point orange">相应的参数发生告警</p>
<p class="left-point blue">蓝色：相应的参数已经建立 Trace 模型</p>
<p class="left-point purple">紫色：相应的参数建立了 Chamber 类型的 Trace 模型</p>
`),
);

export const paramAliasUsText = setHtml(paramUsHtml);
export const paramAliasCnText = setHtml(paramCnHtml);

const paramLongtermUsHtml = xss.process(
    VMdPreview.vMdParser.themeConfig.markdownParser.render(`
## Parameters Color by Types
<p class="left-point blue">Blue: The parameters have trace model.</p>
<p class="left-point purple">Purple: The parameters have chamber type trace model.</p>
`),
);

const paramLongtermCnHtml = xss.process(
    VMdPreview.vMdParser.themeConfig.markdownParser.render(`
## 颜色定义
<p class="left-point blue">蓝色：相应的参数已经建立 Trace 模型</p>
<p class="left-point purple">紫色：相应的参数建立了 Chamber 类型的 Trace 模型</p>
`),
);
export const paramAliasLongtermUsText = setHtml(paramLongtermUsHtml);
export const paramAliasLongtermCnText = setHtml(paramLongtermCnHtml);
