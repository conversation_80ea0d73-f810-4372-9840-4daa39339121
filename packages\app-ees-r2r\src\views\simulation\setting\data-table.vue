<script lang="ts" setup>
import {
  type VxeGridDefines,
  VxeSelectDefines,
  type VxeSelectPropTypes,
  t,
  VXETable,
  type VxeColumnProps
} from '@futurefab/vxe-table';
import { inject, reactive, shallowRef, onUnmounted, computed } from 'vue';
import {
  dataFormRules,
  formatTableData,
  getDataOptions,
  onValidationData,
  onValidationTemp,
  aliasOptions,
  _methodOptions,
  methodOptionsNoAll,
  isMultiSelect,
  onChange
} from './config';
import DataSource from '../data-source/index.vue';
import {
  getSimulationValidatedDsList,
  saveSimulationDataSource,
  checkSimulation,
  startSimulation,
  sendSimulation,
  endSimulation,
  getSimulationTid
} from '@futurefab/ui-sdk-api';
import { type RightTable, injectAuthKey, injectKey } from '../interface';
import { isPermission } from '@/utils/permission';
import { simulation as buttonIds } from '@/log-config';
import { groupBy, differenceBy, cloneDeep, intersectionBy } from 'lodash-es';
import ExecuteFlow from '@/views/workflow-log-viewer/execute-flow/index.vue';
import { useR2RSimulationStore } from '@futurefab/ui-sdk-stores';
const store = useR2RSimulationStore();
const { showWarning, successInfo } = VXETable.tableFun;

const { btnAuth } = inject(injectAuthKey)!;
const { area } = inject(injectKey)!;

const data = reactive({
  list: [] as any[],
  rawId: null as null | number,
  area: '',
  visible: false,
  name: '',
  options: getDataOptions(btnAuth.value, []),
  sourceList: [] as any[],
  gridLoading: false,
  loading: false,
  dataVisible: false,
  groupOptions: [] as VxeSelectPropTypes.OptionGroups,
  saveObj: {} as any,
  simulationColumns: [] as VxeColumnProps[],
  actionIndex: 0, //Simulation的位置
  simulationId: null as number | null, //正在进行的Simulation ID
  isShow: false,
  logRow: null as any
});

// 使用模板init table
async function initTable({ rawId, columns, dataList }: RightTable) {
  await getDataSource(area.value);
  const len = data.groupOptions.filter(
    ({ options }) => !!options?.filter((it: any) => it.rawId === rawId).length
  ).length;
  if (len) {
    data.rawId = rawId;
    data.options = getDataOptions(btnAuth.value, columns || []);
    data.list = delSimulationRes(dataList);
  } else {
    data.rawId = null;
    data.options = getDataOptions(btnAuth.value, []);
    data.list = [];
  }
  store.setTempJson({
    ...store.state.templateJson,
    columns,
    rawId,
    dataList: data.list
  });
  xGrid.value.clearCheckboxRow();
  const start = store.state.templateJson.selectedMethod.length;
  data.saveObj.columnSetting = JSON.stringify(columns.slice(start));
  data.simulationColumns = columns.slice(0, start);
}

// check method时动态增列
function setTableConfig() {
  data.simulationColumns = store.state.templateJson.selectedMethod.map((item) => {
    let alias = '';
    item.methodName.split('_').forEach((str: string) => (alias += str[0]));
    return {
      title: item.alias || alias,
      field: item.methodName,
      width: 80,
      align: 'center',
      slots: {
        default: 'is-simulation'
      }
    };
  });
  store.setTempJson({
    ...store.state.templateJson,
    columns: [...data.simulationColumns, ...JSON.parse(data.saveObj.columnSetting || '[]')]
  });
  setBtnStatus();
}
const showSimulationIcon = (column: any, row: any, rowIndex: number) => {
  const len = data.simulationColumns.filter(({ title }) => title === column.title).length;
  return rowIndex > 1 && !!len && row[`${column.field}_tid`];
};

// get data-source-tree
async function getDataSource(_area: string, isClose?: boolean) {
  if (!_area) return;
  if (!isClose) {
    data.rawId = null;
    data.area = _area;
  } else {
    area.value = _area;
  }
  data.loading = true;
  let options: VxeSelectPropTypes.OptionGroups = [];
  const res = await getSimulationValidatedDsList({
    bodyParams: { area: _area },
    headerParams: {
      log: 'Y',
      page: buttonIds.pageId,
      action: buttonIds.dataSource.search
    }
  });
  data.loading = false;
  if (res.data) {
    const list: any[] = [];
    Object.keys(res.data).forEach((key) => {
      options.push({
        label: key,
        options: res.data[key]
      });
      list.push(...res.data[key]);
    });
    data.groupOptions = options;
    data.sourceList = list;
  }
  if (isClose) {
    const len = data.sourceList.filter((item) => item.rawId === data.rawId).length;
    if (!len) {
      data.rawId = null;
      data.list = [];
      data.options = getDataOptions(btnAuth.value, []);
    } else {
      changeData({ value: data.rawId } as VxeSelectDefines.ChangeEventParams);
    }
  }
}

// get data-source-grid list
async function changeData(e: VxeSelectDefines.ChangeEventParams) {
  if (!e.value) {
    data.list = [];
    return;
  }
  data.gridLoading = true;
  const { columnSetting, dataJson, dataSourceType, conditions } =
    data.sourceList.find((item) => item.rawId === e.value) || {};
  const timer = setTimeout(() => {
    data.saveObj = { columnSetting, dataSourceType, dataJson, conditions };
    data.list = formatTableData(JSON.parse(dataJson), JSON.parse(columnSetting));
    setTableConfig();
    store.setTempJson({
      ...store.state.templateJson,
      columns: [...data.simulationColumns, ...JSON.parse(columnSetting)],
      rawId: e.value,
      dataList: delSimulationRes(data.list)
    });
    setBtnStatus();
    xGrid.value.clearCheckboxRow();
    data.gridLoading = false;
    clearTimeout(timer);
  }, 50);
}

const gridEvents = {
  toolbarToolClick: async ({ code }: VxeGridDefines.ToolbarToolClickEventParams) => {
    switch (code) {
      case 'saveData':
        if (!data.list.length) return showWarning(t('r2rSimulation.tip.noSave'));
        const _res = await onValidationData({
          list: xGrid.value.getTableData().fullData,
          isSave: true
        });
        data.name = data.sourceList.find((item) => item.rawId === data.rawId).name;
        data.visible = _res.result;
        break;
      case 'validation':
        const res = await onValidationTemp();
        setBtnStatus(res.result ? { btnCode: 'start', status: true } : undefined);
        break;
      case 'start':
        if (store.state.isSimulating) {
          onSend();
        } else {
          const rs = await checkSimulation();
          if (rs.data === true) {
            const type = await VXETable.modal.confirm(t('r2rSimulation.tip.overWriteSimul'));
            if (type === 'confirm') onStart();
          } else if (rs.data === false) {
            onStart();
          }
        }
        break;
      case 'stop':
        onStop();
        await endSimulation();
        break;
      case 'pause':
        onPause();
        break;
      case 'refresh':
        if (!store.state.isSimulating) {
          changeData({ value: data.rawId } as VxeSelectDefines.ChangeEventParams);
        }
        break;
    }
  }
};

// 设置start/stop/pause状态，及还原初始状态
type btnCodeType = 'start' | 'stop' | 'pause' | 'other';
function setBtnStatus(params?: { btnCode: btnCodeType; status?: boolean }) {
  const actionCodes = ['start', 'start_gray', 'stop', 'stop_gray', 'pause', 'pause_gray'];
  if (params) {
    const { btnCode, status } = params;
    data.options.toolbarConfig?.tools?.forEach((item) => {
      if (item.code === btnCode) {
        item.visible = status ? isPermission(btnAuth.value, buttonIds.dataSource[btnCode]) : false;
      } else if (item.code === `${btnCode}_gray`) {
        item.visible = status ? false : isPermission(btnAuth.value, buttonIds.dataSource[btnCode]);
      }
      if (btnCode === 'other' && !actionCodes.includes(item.code!)) {
        item.disabled = !status;
        // if (status) {
        //     item.icon = item.icon?.split('_gray')[0];
        // } else {
        //     item.icon = item.icon?.includes('gray')
        //         ? item.icon
        //         : `${item.icon?.trim()}_gray`;
        // }
      }
    });
  } else {
    data.options = getDataOptions(btnAuth.value, store.state.templateJson.columns);
  }
}

// 剔除掉simulation的结果
function delSimulationRes(list: any[]) {
  const fieldList = data.simulationColumns.map(({ field }) => `${field}_tid`);
  const dataList = cloneDeep(list);
  dataList.forEach((item: any) => {
    Object.keys(item).forEach((key) => {
      if (fieldList.includes(key)) item[key] = null;
    });
  });
  return dataList;
}

// Save simulation data
const dataForm = shallowRef();
const xGrid = shallowRef();
async function onSave(rawId: number | null) {
  const obj = JSON.parse(data.saveObj.conditions || '{}');
  const conditions = JSON.stringify({ ...obj, name: data.name });
  const dataList = delSimulationRes(xGrid.value.getTableData().fullData);
  const res = await saveSimulationDataSource({
    headerParams: {
      log: 'Y',
      page: buttonIds.pageId,
      action: data.rawId ? buttonIds.dataSource.save : buttonIds.dataSource.add
    },
    bodyParams: {
      rawId,
      area: data.area,
      name: data.name,
      dataSourceType: data.saveObj.dataSourceType,
      conditions,
      columnSetting: data.saveObj.columnSetting,
      dataJson: JSON.stringify(dataList),
      validateYn: true
    }
  });
  if (res.status === 'SUCCESS') {
    successInfo(t('common.tip.saveSuccess'));
    data.visible = false;
    getDataSource(data.area, true);
  }
}
function beforeHideDataModal({ type }: any) {
  return new Promise<void>(async (resolve, reject) => {
    if (type === 'confirm') {
      const err = await dataForm.value.validate();
      if (err) {
        reject();
      } else {
        const exsitList = data.sourceList.filter((item) => item.name === data.name);
        if (exsitList.length) {
          const type = await VXETable.modal.confirm(
            t('r2rSimulation.tip.overWrite', { name: data.name })
          );
          type === 'confirm' ? onSave(exsitList[0].rawId) : reject();
        } else {
          onSave(null);
        }
      }
    } else {
      resolve();
      data.visible = false;
    }
  });
}

// Start simulation
const excludeRows = ['ALIAS', 'METHOD'];
async function onStart() {
  const headerParams = {
    log: 'Y',
    pageId: buttonIds.pageId,
    action: buttonIds.dataSource.start
  };
  const { validationColumns } = store.state;
  const executionKeyValue: string[] = [];
  const checkedList = xGrid.value.getCheckboxRecords();
  const dataList: any[] = checkedList.length ? checkedList : data.list;
  dataList.forEach((item) => {
    if (!excludeRows.includes(item.dataSet)) {
      let val = '';
      validationColumns.forEach((field) => {
        val += `${field}=${item[field]};`;
      });
      if (!executionKeyValue.includes(val)) executionKeyValue.push(val);
    }
  });
  const bodyParams = {
    modelGroupName: store.state.templateJson.modelGroup,
    executionKeyValue
  };
  const res = await startSimulation({ headerParams, bodyParams });
  if (res.status === 'SUCCESS') {
    if (res.data.status) {
      data.simulationId = res.data.simulationId;
      onSend();
    } else {
      showWarning('r2rSimulation.tip.startFail');
    }
  }
}

// Send simulation
type SendListType = {
  area: string;
  simulationId: number;
  methodName: string;
  contextList: string[];
  dataList: any[];
  headerList: string[][];
};
async function onSend() {
  const headerList: string[][] = [];
  let columns: string[] = [];
  JSON.parse(data.saveObj.columnSetting).forEach((item: VxeColumnProps) => {
    if (item.field !== 'dataSet') columns.push(item.field!);
  });
  headerList.push(columns);
  const { selectedMethod, methodList } = store.state.templateJson;
  const { contextList, noPassDataSet } = store.state;
  data.list.forEach((item, index) => {
    if (index < 2) {
      const arr: string[] = [];
      columns.forEach((col) => {
        const val = Array.isArray(item[col]) ? item[col][0] : item[col];
        arr.push(val);
      });
      headerList.push(arr);
    } else {
      let groupKey = '';
      contextList.forEach((con) => {
        if (con !== 'SUBSTRATE_ID') groupKey += item[con] || '';
      });
      item.groupKey = groupKey;
    }
    //stop后又start需要清除历史数据
    if (!data.actionIndex) {
      methodList.forEach(({ methodName }) => (item[`${methodName}_tid`] = undefined));
    }
  });
  let sendList: SendListType[] = [];
  const checkedList: any[] = xGrid.value.getCheckboxRecords();
  const _dataList: any[] = (
    checkedList.length ? intersectionBy(data.list, checkedList, 'dataSet') : data.list
  ).filter(({ dataSet }: any) => ![...excludeRows, ...noPassDataSet].includes(dataSet));
  const lotBaseList: Record<string, any[]> = groupBy(_dataList, 'groupKey');
  Object.keys(lotBaseList).forEach((key) => {
    selectedMethod.forEach(({ methodName, lotBase }, ind) => {
      if (lotBase) {
        sendList.push({
          area: area.value,
          simulationId: data.simulationId!,
          methodName,
          contextList,
          dataList: lotBaseList[key],
          headerList
        });
      } else {
        lotBaseList[key].forEach((item) => {
          sendList.push({
            area: area.value,
            simulationId: data.simulationId!,
            methodName,
            contextList,
            dataList: [item],
            headerList
          });
        });
      }
    });
  });
  if (!sendList.length) return showWarning(t('r2rSimulation.tip.noSimulate'));
  store.setSimulationStatus({
    isSimulated: false,
    actualData: [],
    isSimulating: true
  });
  setBtnStatus({ btnCode: 'start' });
  setBtnStatus({ btnCode: 'stop', status: true });
  setBtnStatus({ btnCode: 'pause', status: true });
  setBtnStatus({ btnCode: 'other' });
  sendTimer({ sendList, columns, contextList, _dataList });
  getTid(_dataList, lotBaseList, columns, contextList);
}

let interval: NodeJS.Timeout;
let timer: NodeJS.Timeout;
function sendTimer(params: {
  sendList: SendListType[];
  columns: string[];
  contextList: string[];
  _dataList: any[];
}) {
  const { sendList, columns, contextList, _dataList } = params;
  const { selectedMethod, intervalTime } = store.state.templateJson;
  interval = setTimeout(async () => {
    const bodyParams = sendList[data.actionIndex];
    const res = await sendSimulation({ bodyParams });
    if (res.status === 'SUCCESS') {
      if (res.data.status === 'success') {
        // const dataSetArr = bodyParams.dataList.map(v => v.dataSet as number);
        // data.list.forEach(item => {
        //     if (dataSetArr.includes(item.dataSet)) {
        //         item[`${bodyParams.methodName}_tid`] = res.data.tid;
        //     }
        // });
        data.actionIndex++;
        sendTimer(params);
      } else if (res.data.status === 'fail') {
        onPause();
        showWarning('r2rSimulation.tip.sendFail');
      } else if (res.data.status === 'reject') {
        onStop();
        showWarning('r2rSimulation.tip.sendReject');
      }
      if (data.actionIndex === sendList.length) {
        // simulation end
        clearTimeout(interval);
      }
    }
  }, intervalTime * 1000);
}

// get tid
async function getTid(
  simulationList: any[],
  mergeList: Record<string, any[]>,
  columns: string[],
  contextList: string[]
) {
  const { intervalTime, selectedMethod } = store.state.templateJson;
  const selectedMethodStr: string[] = [];
  const methodObj: Record<string, boolean> = {};
  selectedMethod.forEach(({ methodName, lotBase }) => {
    selectedMethodStr.push(methodName);
    methodObj[methodName] = lotBase;
  });
  timer = setInterval(async () => {
    const totalTidList: number[] = [];
    const res = await getSimulationTid();
    if (res.status === 'SUCCESS') {
      const _data: Record<string, number[]> = cloneDeep(res.data);
      Object.keys(_data).forEach((key) => {
        if (methodObj[key]) _data[key] = [];
      });
      Object.keys(mergeList).forEach((mergeKey, mergeIdx) => {
        Object.keys(res.data).forEach((methodName) => {
          if (methodObj[methodName]) {
            mergeList[mergeKey].forEach((item) => {
              _data[methodName].push(res.data[methodName][mergeIdx]);
            });
          }
        });
      });
      simulationList.forEach((item, index) => {
        Object.keys(_data).forEach((methodName) => {
          if (selectedMethodStr.includes(methodName)) {
            item[`${methodName}_tid`] = _data[methodName][index] || '';
          }
        });
      });
      Object.keys(_data).forEach((key) =>
        _data[key].forEach((item: number) => totalTidList.push(item))
      );
      if (simulationList.length * selectedMethod.length === totalTidList.length) {
        clearInterval(timer);
        setBtnStatus();
        const selectedArr = selectedMethod.map(({ methodName }) => methodName as string);
        const includeArr: string[] = differenceBy(columns, [...selectedArr, ...contextList]);
        let actualData: Record<string, string[]>[] = [];
        includeArr.forEach((key) => {
          let actual: Record<string, string[]> = {};
          simulationList.forEach((item) => {
            actual[key] = actual[key] || [];
            actual[key].push(item[key]);
          });
          actualData.push(actual);
        });
        data.actionIndex = 0;
        store.setSimulationStatus({
          isSimulated: true,
          actualData,
          isSimulating: false
        });
        const endRes = await endSimulation();
        if (endRes.status === 'SUCCESS' && res.data) successInfo('r2rSimulation.tip.sendSuccess');
      }
    }
  }, intervalTime * 1000);
}

// pause and stop
function onPause() {
  clearTimeout(interval);
  clearInterval(timer);
  setBtnStatus({ btnCode: 'pause' });
  setBtnStatus({ btnCode: 'stop', status: true });
  setBtnStatus({ btnCode: 'start', status: true });
}
function onStop() {
  clearTimeout(interval);
  clearInterval(timer);
  data.actionIndex = 0;
  data.simulationId = null;
  setBtnStatus({ btnCode: 'stop' });
  setBtnStatus({ btnCode: 'pause' });
  setBtnStatus({ btnCode: 'start', status: true });
  setBtnStatus({ btnCode: 'other', status: true });
  store.setSimulationStatus({
    isSimulating: false,
    isSimulated: false,
    actualData: []
  });
}

// show log viewer
function onShowLog(row: any, column: any) {
  if (isPermission(btnAuth.value, buttonIds.dataSource.loadLog)) {
    data.logRow = { tid: row[`${column.field}_tid`] };
    data.isShow = true;
  }
}

// open dataSource config modal
function openDataConfig() {
  const { area, modelGroup } = store.state.templateJson;
  if (!area)
    return showWarning(t('common.tip.selectName', { name: t('r2rSimulation.title.area') }));
  if (!modelGroup)
    return showWarning(t('common.tip.selectName', { name: t('r2rSimulation.title.modelGroup') }));
  data.dataVisible = true;
}

// 处理插槽复制粘贴问题
function onCellAreaPaste(params: any) {
  const { columns: columnList, selectedMethod } = store.state.templateJson;
  const { aliasOptions, methodOptions, isSimulating } = store.state;
  const { copyAreas, currentAreas } = params;
  const { rows, columns, data } = currentAreas;
  if (rows[0] > 1 || columns[0] < selectedMethod.length + 2 || isSimulating) return;
  const firstField = Object.keys(data[0])[0];
  const firstIndex = columnList.findIndex((item) => item.field === firstField);
  const tableList = xGrid.value.getTableData().fullData;
  Object.keys(copyAreas.data[0]).forEach((key, i) => {
    const field = columnList[firstIndex + i].field!;
    const value = copyAreas.data[0][key];
    const len = aliasOptions.filter((item: any) => item.value === value).length;
    const _len = methodOptions.filter((item: any) => item.value === value).length;
    if (rows[0] === 0 && len) {
      tableList[rows[0]][field] = copyAreas.data[0][key];
    }
    if (rows[0] === 1 && _len) {
      tableList[rows[0]][field] = copyAreas.data[0][key];
    }
  });
  data.list = tableList;
}

// 编辑后触发
function onEditClose() {
  const { isSimulating } = store.state;
  if (!isSimulating) {
    setBtnStatus();
    const dataList = delSimulationRes(xGrid.value.getTableData().fullData);
    store.setTempJson({
      ...store.state.templateJson,
      dataList
    });
  }
}
const noTableColumnData = computed(() => data.options.columns?.length || 0);
onUnmounted(() => {
  clearTimeout(interval);
  clearInterval(timer);
});

defineExpose({ initTable, getDataSource, setTableConfig });
</script>

<template>
  <div
    class="r2r-simulation-setting-right"
    :class="{
      'no-data-table': noTableColumnData <= 2
    }"
  >
    <vxe-grid
      ref="xGrid"
      class="common-table-grid-maximize-style"
      v-bind="data.options"
      :loading="data.gridLoading"
      :data="data.list"
      v-on="gridEvents"
      @cell-area-paste="onCellAreaPaste"
      @edit-closed="onEditClose"
    >
      <template #beforeTools>
        <span class="data-title">{{ t('r2rSimulation.title.data') }}</span>
        <vxe-select
          v-model="data.rawId"
          :option-props="{ label: 'name', value: 'rawId' }"
          clearable
          :has-button="isPermission(btnAuth, buttonIds.dataSource.add)"
          button-text="+ Add Data"
          :option-groups="data.groupOptions"
          :loading="data.loading"
          :disabled="store.state.isSimulating"
          @click="openDataConfig"
          @change="changeData"
        />
      </template>
      <template #edit-data-row="{ row, column, rowIndex }">
        <vxe-select
          v-if="!rowIndex"
          v-model="row[column.field]"
          :options="aliasOptions"
          :readonly="false"
          clearable
          transfer
          @change="(e) => onChange(e, row, column.field, rowIndex, xGrid)"
        />
        <vxe-select
          v-else-if="rowIndex === 1"
          v-model="row[column.field]"
          :options="isMultiSelect(column.field, xGrid) ? _methodOptions : methodOptionsNoAll"
          readonly
          multiple
          clearable
          transfer
          @change="(e) => onChange(e, row, column.field, rowIndex, xGrid)"
        />
        <span v-else>{{ row[column.field] }}</span>
      </template>
      <template #is-simulation="{ row, column, rowIndex }">
        <!-- <FileDoneOutlined
                    v-if="showSimulationIcon(column, row, rowIndex)"
                    :style="{ fontSize: '18px', cursor: 'pointer' }"
                    @click="onShowLog(row, column)"
                /> -->
        <span
          v-if="showSimulationIcon(column, row, rowIndex)"
          class="iconfont icon-file-done icon-normal-color iconfont-small"
          :style="{ cursor: 'pointer' }"
          @click="onShowLog(row, column)"
        ></span>
        <span v-else></span>
      </template>
    </vxe-grid>
  </div>
  <!-- data-source -->
  <data-source v-model:visible="data.dataVisible" @close-modal="getDataSource(data.area, true)" />
  <vxe-modal
    v-model="data.visible"
    title="r2rSimulation.btn.saveData"
    show-footer
    type="confirm"
    :height="180"
    :before-hide-method="beforeHideDataModal"
  >
    <vxe-form ref="dataForm" :data="data" :rules="dataFormRules">
      <vxe-form-item title="r2rSimulation.title.name" field="name">
        <vxe-select
          v-model="data.name"
          :options="data.sourceList"
          :option-props="{ label: 'name', value: 'name' }"
          :readonly="false"
          transfer
        />
      </vxe-form-item>
    </vxe-form>
    <template #footer>
      <div class="modal-footer">
        <p>
          <a-button
            type="primary"
            style="margin-right: 10px"
            @click="beforeHideDataModal({ type: 'confirm' })"
          >
            {{ t('common.btn.confirm') }}
          </a-button>
          <a-button class="btn" @click="beforeHideDataModal({ type: 'cancel' })">{{
            t('common.btn.cancel')
          }}</a-button>
        </p>
      </div>
    </template>
  </vxe-modal>
  <!-- execution log -->
  <execute-flow
    :show="data.isShow"
    :current-row="data.logRow"
    @hide-sidebar="() => (data.isShow = false)"
  />
</template>

<style scoped lang="less">
@import url('@/assets/style/variable.less');
.r2r-simulation-setting-right {
  width: 100%;
  height: 100%;
  .data-title {
    margin-right: @margin-xss;
    color: @text-sub-color;
    font-weight: bold;
  }
}
.no-data-table {
  :deep(.vxe-table--main-wrapper) {
    .vxe-table--header-wrapper {
      display: none;
    }
  }
}
</style>
