<template>
  <div>
    <ees-button-tip
      v-if="!loading"
      icon="#icon-btn-copy"
      :text="$t('common.btn.copyToClipboard')"
      @on-click="generateAndCopyToClipboard"
    />
    <div v-else class="overview-chart-btn-loading"></div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import domtoimage from 'dom-to-image';
import type { CopyChartType } from '../../interface';
import { EesButtonTip } from '@futurefab/ui-sdk-ees-basic';

const props = defineProps<{
  copyChartConfig: CopyChartType;
}>();

const loading = ref(false);

const generateCanvas = async (container: HTMLElement, title: string) => {
  try {
    // 使用 dom-to-image 生成 Blob
    const image = new Image();
    image.src = await domtoimage.toPng(container);

    return new Promise((resolve) => {
      // 当图像加载完成后再继续绘制
      image.onload = () => resolve(image);
    });
  } catch (error) {
    console.error('Failed to create canvas image:', error);
    throw error;
  }
};

const generateAndCopyToClipboard = async () => {
  if (loading.value) return;
  loading.value = true;

  try {
    const container = document.getElementById(props.copyChartConfig.id) as HTMLElement;
    const title = props.copyChartConfig.name;
    const image: any = await generateCanvas(container, title);

    // 创建一个新的 Canvas 对象
    const newCanvas = document.createElement('canvas');
    const titleFontSize = 14;
    const padding = 10;
    const originalHeight = image.height;
    const originalWidth = image.width;
    const newCanvasHeight = originalHeight + titleFontSize + padding * 2;
    const newCanvasWidth = originalWidth + padding * 2;

    newCanvas.width = newCanvasWidth;
    newCanvas.height = newCanvasHeight;
    const ctx = newCanvas.getContext('2d')!;

    // 绘制背景
    ctx.fillStyle = '#fff';
    ctx.fillRect(0, 0, newCanvasWidth, newCanvasHeight);

    // 绘制标题
    ctx.font = 'bold 14px 微软雅黑';
    ctx.fillStyle = '#000';
    let text = title;
    let lines = text.split(/\r\n|\r|\n/);
    let lineHeight = ctx.measureText('M').width * 1.2; // 测量字体高度
    let y = padding + titleFontSize / 2;
    for (let line of lines) {
      let textWidth = ctx.measureText(line).width;
      let x = (newCanvasWidth - textWidth) / 2;
      ctx.fillText(line, x, y);
      y += lineHeight;
    }

    // 绘制图像
    ctx.drawImage(image, padding, padding + titleFontSize, originalWidth, originalHeight);
    // 将画布转换为 Blob 并复制到剪贴板
    newCanvas.toBlob((blob: any) => {
      navigator?.clipboard?.write([new ClipboardItem({ 'image/png': blob })]);
    }, 'image/png');

    loading.value = false;
  } catch (error) {
    loading.value = false;
    console.error('Failed to copy to clipboard:', error);
  }
};
</script>

<style scoped lang="less">
@import url('@/assets/style/variable.less');
.overview-chart-btn-loading {
  display: inline-block;
  position: relative;
  width: 26px;
  height: 26px;
  margin-right: 4px;
  border-top: 0.5em solid @loading-default-color;
  border-right: 0.5em solid @loading-default-color;
  border-bottom: 0.5em solid @loading-default-color;
  border-left: 0.5em solid @loading-spinner-bg-color;
  transform: translateY(40%);
  animation: loader 1000ms infinite linear;
  transition: all 1000ms ease;
  z-index: 9999;
  border-radius: 50%;
  &:before,
  &:after {
    content: '';
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: @loading-spinner-bg-color;
    opacity: 0.6;
    position: absolute;
    top: 0;
    left: 0;
    animation: bounce 2s infinite ease-in-out;
  }

  &:after {
    animation-delay: -1s;
  }
}
@keyframes loader {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
    border-left: 0.5em solid var(--primary-color);
  }

  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
    border-left: 0.5em solid var(--primary-color);
  }
}
@keyframes bounce {
  0%,
  100% {
    transform: scale(0);
  }

  50% {
    transform: scale(1);
  }
}
</style>
