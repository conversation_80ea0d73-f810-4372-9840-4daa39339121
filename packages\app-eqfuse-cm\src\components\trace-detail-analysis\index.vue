<script lang="ts" setup>
import { computed, reactive, ref, nextTick, onUnmounted, toRaw } from 'vue';
import { paramOptions, removeRepeat, getHelpTip } from './config';
import ChartTab from './chart-tab.vue';
import type { TabListType, ChartParamType, ChartDataType } from './interface';
import { getNacosConfig, getTraceDataSubstrateList } from '@futurefab/ui-sdk-api';
import { t } from '@futurefab/vxe-table';
import { Splitpanes, Pane } from 'splitpanes';
import 'splitpanes/dist/splitpanes.css';
import { setHeaderSelectFilter, showWarning, EesNoData } from '@futurefab/ui-sdk-ees-basic';
import { EesGroupBottom, EesAnalysisChart } from '@futurefab/ui-sdk-ees-charts';
import { debounce, intersection } from 'lodash-es';
import { traceDetailChartInfo as buttonIds } from '@/log-config/index';
import { joinEqpModuleAlias, setParamColor } from '@/utils';
import { customColor } from '@/utils/custom-color';
import dayjs from 'dayjs';

const props = withDefaults(
  defineProps<{
    modelRawId: number | undefined;
    modelName: string;
    substrateId?: any;
  }>(),
  {
    modelRawId: undefined,
    modelName: '',
    substrateId: ''
  }
);
let paramGrid = ref();
const showChart = ref<boolean>(false);
let myChart = ref();

// tab 绑定值
let tabValue = reactive({
  byType: 'module',
  moduleId: '',
  modelId: ''
});
const listData = reactive({
  lotList: [] as any[],
  histories: [] as ChartParamType[],
  moduleList: [] as TabListType[],
  modelList: [] as TabListType[],
  chartSource: '' // chart 的类型，区分 eg chart、summary chart 和 list chart
});

let minSize = ref(0);
let params = reactive<{
  total: any;
  list: any[];
  checked: string[];
  multiChecked: string[];
  tableHight: number;
  isSingle: boolean;
  loading: boolean;
  max: number;
  groupMap: any;
}>({
  total: {},
  list: [],
  checked: [],
  multiChecked: [],
  tableHight: 100,
  isSingle: true,
  loading: false,
  max: 10,
  groupMap: undefined
});

const formatDate = (date: string) => dayjs(date).format('YYYY-MM-DD HH:mm:ss.SSS');
const fdcFaultRuleColor = ref('');

onUnmounted(() => {
  sessionStorage.removeItem('analysisChartParams');
  sessionStorage.removeItem('analysisLotList');
  window.removeEventListener('resize', () => onResized());
});

// const dealHeaderFilter = () => {
//     analysisDataSetRef.value && analysisDataSetRef.value.handleShow(true);
// };
// 获取入参
const getLotHistories = (list: any[]) => {
  console.log('listlist', list);

  let histories: ChartParamType[] = [];
  list.forEach((v: any) => {
    const { moduleId, lotId, substrateId, startTime, endTime, rawIds, tagRawIds } = v;
    histories.push({
      eqpModuleId: moduleId,
      lotId,
      substrateId,
      startDate: startTime,
      endDate: endTime,
      rawIds,
      tagRawIds
    });
  });
  return histories;
};

// 切换byeqp or bymodel
const handleTab = (val: TabListType) => {
  tabValue.byType = val.value;
  const subTab = tabValue.byType === 'module' ? tabValue.moduleId : tabValue.modelId;
  params.list = params?.total?.[tabValue.byType]?.[subTab] || [];
  params.list.forEach((v: any) => {
    if (params.checked.includes(v.paramAlias)) {
      nextTick(() => paramGrid.value?.setCheckboxRow(v, true));
    }
  });
};
// 获取paramList
const getParamArr = async (val: TabListType, isInit?: boolean) => {
  if (tabValue.byType === 'module') {
    tabValue.moduleId = val.value;
  } else {
    tabValue.modelId = val.value;
  }
  params.list = params.total[tabValue.byType][val.value] || [];
  if (!params.list.length) {
    params.checked = [];
    return;
  }
  if (isInit) {
    params.checked.push(params.list[0].paramAlias);
  }
  let specValue = params.checked[0];
  const obj = params.list.find((item) => item.paramAlias === specValue) || {};
  const isSpec = obj.cttSpecExist === 'Y' || obj.specExist === 'Y';
  curSpecValue.value = isSpec ? specValue : undefined;
  if (isInit) params.isSingle = params.checked.length <= 1;
  params.list.forEach((v: any) => {
    if (params.checked.includes(v.paramAlias)) {
      nextTick(() => paramGrid.value.setCheckboxRow(v, true));
    }
  });
  nextTick(() => {
    setHeaderSelectFilter({
      xGrid: paramGrid,
      tableData: toRaw(params.list),
      columnFieldList: ['priority', 'unit']
    });
    myChart.value.onChangeChartData({ paramList: params.checked });
    chartGroupBottom.value.onChangeChartData({ paramList: params.checked });
  });
};
let curSpecValue = ref<undefined | string>(undefined);
const changeSwitch = (e: boolean) => {
  if (e) {
    params.checked = [params.list[0].paramAlias];
    curSpecValue.value = specParamList.value[0];
    nextTick(() => {
      paramGrid.value.clearCheckboxRow();
      paramGrid.value.setCheckboxRow(params.list[0], true);
      // 重新获取Check项
      computeCheckedBasket();
      if (chartData.kind === 'trend') {
        console.log('listData.histories 1', listData.histories);

        myChart.value?.drawMyChart({
          chartParams: { lotHistories: listData.histories },
          paramAlias: params.checked,
          type: chartData.type,
          layout: chartData.layout,
          isSplitParam: chartData.isSplitParam,
          specValue: curSpecValue.value
        });
      }
      chartData.x = intersection(params.checked, chartData.x);
      chartData.y = intersection(params.checked, chartData.y);
      chartGroupBottom.value.onChangeChartData({ ...chartData, paramList: params.checked });
      myChart.value.onChangeChartData({ ...chartData, paramList: params.checked });
    });
  }
};

const checkParam = (e: any) => {
  if (params.isSingle) {
    //单选
    if (e.checked) params.checked = [e.row.paramAlias];
    paramGrid.value.clearCheckboxRow();
    paramGrid.value.setCheckboxRow(e.row, true);
    chartData.x = intersection(params.checked, chartData.x);
    chartData.y = intersection(params.checked, chartData.y);
  } else {
    //多选
    const checkedParam: any[] = paramGrid.value.getCheckboxRecords();
    if (e.checked) {
      // if (checkedParam.length > params.max) {
      //     showLimitParam();
      //     return false;
      // }
      checkedParam.forEach((row: any) => params.multiChecked.push(row.paramAlias));
      params.multiChecked = [...new Set(params.multiChecked)];
    } else {
      let curTotalParam: string[] = [],
        curCheckParam: string[] = [];
      if (!checkedParam.length) {
        params.list.forEach((row) => {
          if (params.multiChecked.includes(row.paramAlias)) {
            paramGrid.value.setCheckboxRow(row, true);
          }
        });
        return;
      }
      params.list.forEach((row) => curTotalParam.push(row.paramAlias));
      checkedParam.forEach((row) => curCheckParam.push(row.paramAlias));
      if (params.multiChecked.length > 1) {
        params.multiChecked = params.multiChecked.filter(
          (param) => curCheckParam.includes(param) || !curTotalParam.includes(param)
        );
      } else {
        paramGrid.value.setCheckboxRow(e.row, true);
      }
    }
    chartData.x = intersection(params.multiChecked, chartData.x);
    chartData.y = intersection(params.multiChecked, chartData.y);
    chartGroupBottom.value.onChangeChartData({ ...chartData, paramList: params.multiChecked });
    myChart.value.onChangeChartData({ ...chartData, paramList: params.multiChecked });
  }
  if (params.checked.length && params.isSingle) wsDrawChart(e);
};

// 防抖
const wsDrawChart = debounce((e: any) => {
  // 重新获取Check项
  computeCheckedBasket();
  if (params.isSingle) {
    if (e.checked) {
      nextTick(() => {
        curSpecValue.value = specParamList.value[0];
        if (chartData.kind === 'scatter') return;
        myChart.value.chartData.isSetLegendTree = true;

        console.log('listData.histories 2', listData.histories);

        myChart.value?.drawMyChart({
          chartParams: { lotHistories: listData.histories },
          paramAlias: params.checked,
          type: chartData.type,
          layout: chartData.layout,
          isSplitParam: chartData.isSplitParam,
          specValue: curSpecValue.value,
          chartKind: chartData.kind
        });
      });
    }
  } else {
    nextTick(() => {
      if (chartData.kind === 'scatter') {
        chartGroupBottom.value.onChangeChartData({
          paramList: params.checked,
          ...chartData
        });
        myChart.value.listData.paramList = params.checked;
        return false;
      }
      myChart.value.chartData.isSetLegendTree = true;
      if (!e.checked) {
        const specValue =
          curSpecValue.value && params.checked.includes(curSpecValue.value)
            ? curSpecValue.value
            : undefined;
        myChart.value.listData.multiUnselectLegendList = [];
        myChart.value.onShowSpec({ specValue, paramList: params.checked, isLoading: true });
      } else {
        console.log('listData.histories 3', listData.histories);

        myChart.value?.drawMyChart({
          chartParams: { lotHistories: listData.histories },
          paramAlias: params.checked,
          type: chartData.type,
          layout: chartData.layout,
          isSplitParam: chartData.isSplitParam,
          chartKind: chartData.kind,
          specValue: curSpecValue.value
        });
      }
    });
  }
  nextTick(() => {
    chartGroupBottom.value.onChangeChartData({ ...chartData, paramList: params.checked });
    myChart.value.onChangeChartData({ ...chartData, paramList: params.checked });
  });
}, 500);

// chart type
const chartData = reactive<ChartDataType>({
  type: 1,
  layout: 1,
  kind: 'trend',
  isSplitParam: false,
  x: [],
  y: [],
  options: []
});
let chartGroupBottom = ref();
const changeChartData = (param: ChartDataType) => {
  console.log('param===chart', param, params.checked);
  chartData.type = param.type;
  chartData.layout = param.layout;
  chartData.kind = param.kind;
  chartData.x = param.x;
  chartData.y = param.y;
  chartData.isSplitParam = param.isSplitParam;
  chartData.options = param.options;
  chartGroupBottom.value.onChangeChartData({ ...chartData, paramList: params.checked });
};
const changeBottomData = (param: ChartDataType) => {
  console.log('param===', param);
  chartData.type = param.type;
  chartData.layout = param.layout;
  chartData.kind = param.kind;
  chartData.x = param.x;
  chartData.y = param.y;
  chartData.isSplitParam = param.isSplitParam;
  chartData.options = param.options;
  myChart.value.onChangeChartData({ ...chartData, paramList: params.checked });
};

// drawChart
const disableBtn = computed(() => {
  const { x, y, kind } = chartData;
  return !params.checked.length || (kind === 'scatter' && (!x.length || !y.length));
});
const computeCheckedBasket = () => {
  const lotList = JSON.parse(sessionStorage.getItem('analysisLotList')!);
  listData.histories = getLotHistories(lotList);
};
const drawChart = () => {
  if (!params.isSingle && params.multiChecked.length) params.checked = params.multiChecked;
  if (params.checked.length > params.max) {
    return showWarning(
      t('common.tip.chartParamsMax', {
        count: params.max
      })
    );
  }
  const { type, layout, kind } = chartData;
  // 重新获取Check项
  computeCheckedBasket();
  if (kind === 'trend') {
    nextTick(() => {
      console.log('listData.histories 4', listData.histories);

      myChart.value?.drawMyChart({
        chartParams: { lotHistories: listData.histories },
        paramAlias: params.checked,
        type,
        layout,
        isSplitParam: chartData.isSplitParam,
        chartKind: kind,
        specValue: curSpecValue.value
      });
    });
  } else {
    const { x, y, kind, type, layout } = chartData;
    nextTick(() => {
      myChart.value?.wsDrawScatterChart({
        chartParams: { lotHistories: listData.histories },
        xList: x,
        yList: y,
        kind,
        type,
        layout
      });
    });
  }
};
const onResized = () => {
  myChart.value && myChart.value.resizeChart();
  onResizeRight(false);
};
// 画chart
const chartPageBasketDrawChart = async (list: any) => {
  let payload = JSON.parse(sessionStorage.getItem('analysisLotList')!);
  const _result: any[] = [];
  // if (payload.length === 0) {
  //     analysisDataSetRef.value && analysisDataSetRef.value.setAllRowCheck(false);
  // }
  const groupMap: Record<string, string> = {};
  payload &&
    payload.forEach(
      ({ moduleId, startTime, endTime, lotId, substrateId, rawIds, seriesGroup }: any) => {
        let _item = {
          startDate: startTime,
          endDate: endTime,
          eqpModuleId: moduleId,
          lotId: lotId,
          lotIds: [lotId],
          substrateIds: substrateId ? [substrateId] : [],
          rawIds
        };
        substrateId ? (groupMap[substrateId] = seriesGroup) : (groupMap[lotId] = seriesGroup);
        _result.push(_item);
      }
    );
  // 第二步
  const totalH = document.getElementById(buttonIds.pageId)!.clientHeight;
  minSize.value = Math.ceil((280 / totalH) * 100);

  onResizeRight(true);
  window.addEventListener('resize', () => onResized());
  params.loading = true;
  getNacosConfig({
    headerParams: {
      log: 'N'
    },
    bodyParams: ['traceDetailMaxParam', 'fdcFaultRuleColor']
  }).then((res: any) => {
    if (res.status === 'SUCCESS') {
      params.max = res.data?.traceDetailMaxParam || 10;
      fdcFaultRuleColor.value = res.data?.fdcFaultRuleColor ?? customColor.typeCd_W;
    }
  });
  params.groupMap = groupMap ? groupMap : undefined;
  if (_result) {
    _result.forEach((item: any) => {
      if (item.startDate) item.startDate = formatDate(item.startDate);
      if (item.endDate) item.endDate = formatDate(item.endDate);
      if (item.eventTime) item.eventTime = formatDate(item.eventTime);
    });
  }
  listData.chartSource = 'TRACE_DETAIL';
  getTraceDataSubstrateList({
    headerParams: {
      log: 'Y',
      page: buttonIds.pageId,
      action: 'search'
    },
    bodyParams: {
      source: 'TRACE_DETAIL',
      modelRawId: props.modelRawId,
      modelName: props.modelName,
      lotHistories: _result,
      groupMap: groupMap || ''
    }
  })
    .then(async (res: any) => {
      params.loading = false;
      listData.lotList = res.data.substrate || [];
      listData.lotList.forEach((item: any) => {
        listData.moduleList.push({
          value: item.moduleId,
          label: joinEqpModuleAlias(item.eqpId, item.moduleAlias)
        });
        listData.modelList.push({ value: item.eqpModel, label: item.eqpModel });
      });
      listData.moduleList = removeRepeat(listData.moduleList, 'value');
      listData.modelList = removeRepeat(listData.modelList, 'value');
      tabValue.moduleId = listData.moduleList[0].value;
      tabValue.modelId = listData.modelList[0].value;
      params.total = res.data.param || {};
      handleTab({ value: 'module', label: 'EQP' });
      getParamArr({ ...listData.moduleList[0] }, true);
      // 区分是否使用接口返回的数据，跳转chart的参数处理
      // 使用存储的 lot/substrate 数据
      const lotList = JSON.parse(sessionStorage.getItem('analysisLotList')!);
      listData.histories = getLotHistories(lotList);
      showChart.value = true;
      myChart.value?.drawMyChart({
        chartParams: { lotHistories: listData.histories },
        paramAlias: params.checked,
        type: chartData.type,
        layout: chartData.layout,
        isSplitParam: chartData.isSplitParam,
        specValue: curSpecValue.value
      });
    })
    .catch(() => (params.loading = false));
};

const onResizeRight = (isInit: boolean) => {
  nextTick(() => {
    let h1 = document.getElementById('param_list_top')!.clientHeight;
    let h2 = 40;
    let h = 0;
    if (isInit) {
      const totalH = document.getElementById(buttonIds.pageId)!.clientHeight;
      h = (totalH * (100 - minSize.value)) / 100;
      params.tableHight = h - (h1 + h2) - 70;
    } else {
      h = document.getElementById('param_list')!.clientHeight;
      params.tableHight = h - (h1 + h2) - 40;
    }
  });
};
const specParamList = computed(() => {
  const list = params.list.filter(
    (item) =>
      params.checked.includes(item.paramAlias) &&
      (item.cttSpecExist === 'Y' || item.specExist === 'Y')
  );
  let arr: string[] = [];
  list.forEach((v) => arr.push(v.paramAlias));

  console.log('====chart params====', arr);
  return arr;
});

defineExpose({ chartPageBasketDrawChart });
</script>
<template>
  <div :id="buttonIds.pageId" class="trace_detail_analysis">
    <Splitpanes class="default-theme" @resized="onResized">
      <Pane size="80" class="cardItem pane-border-radius-left">
        <div v-if="showChart" class="trace_detail_analysis_chart">
          <EesAnalysisChart
            ref="myChart"
            :source="'TRACE_DETAIL'"
            style="min-width: 850px; min-height: 200px"
            :param-alias="params.checked"
            :substrate-id="substrateId || ''"
            :spec-param-list="specParamList"
            :group-map="params.groupMap"
            :cur-series="params.groupMap ? 'time' : undefined"
            @change-chart-data="changeChartData"
            @change-spec-value="(val: string) => (curSpecValue = val)"
          />
        </div>
        <ees-no-data v-else></ees-no-data>
      </Pane>
      <Pane class="cardItem pane-border-radius-right">
        <div class="trace_detail_analysis_hanlers">
          <Splitpanes class="default-theme" horizontal @resized="onResizeRight(false)">
            <Pane class="cardItem" :size="65">
              <div
                id="param_list"
                v-isLoading="{
                  isShow: params.loading,
                  title: t('common.loading.text')
                }"
                class="param_list"
              >
                <div id="param_list_top" class="param_list_top">
                  <div class="top_left">
                    <span class="top_title">{{ t('common.title.paramList') }}</span>
                    <chart-tab
                      v-model:active-tab="tabValue.byType"
                      class="param_custom_tab"
                      :tab-list="[
                        {
                          label: 'EQP',
                          value: 'module'
                        },
                        {
                          label: 'Model',
                          value: 'model'
                        }
                      ]"
                      @handle-tab="(val: TabListType) => handleTab(val)"
                    />
                  </div>
                </div>
                <a-tabs
                  v-if="tabValue.byType === 'module'"
                  v-model:active-key="tabValue.moduleId"
                  class="param_list_tab"
                  type="card"
                  @change="(activeKey: string) => getParamArr({ value: activeKey, label: '' })"
                >
                  <a-tab-pane
                    v-for="item in listData.moduleList"
                    :key="item.value"
                    :tab="item.label"
                  ></a-tab-pane>
                </a-tabs>
                <a-tabs
                  v-else
                  v-model:active-key="tabValue.modelId"
                  class="param_list_tab"
                  type="card"
                  @change="(activeKey: string) => getParamArr({ value: activeKey, label: '' })"
                >
                  <a-tab-pane
                    v-for="item in listData.modelList"
                    :key="item.value"
                    :tab="item.label"
                  ></a-tab-pane>
                </a-tabs>
                <div class="my_checkbox">
                  <a-radio-group
                    v-model:value="params.isSingle"
                    name="radioGroup"
                    @change="changeSwitch"
                  >
                    <a-radio :value="true">single</a-radio>
                    <a-radio :value="false">multi</a-radio>
                  </a-radio-group>
                </div>
                <div style="height: calc(100% - 95px)">
                  <vxe-grid
                    ref="paramGrid"
                    v-bind="paramOptions"
                    :data="params.list"
                    style="height: 100%"
                    class="my-table"
                    :border-out-line="[0, 0, 1, 0]"
                    @checkbox-change="checkParam"
                  >
                    <template #param_slot="{ row }">
                      <span
                        v-if="fdcFaultRuleColor !== ''"
                        :style="`color: ${setParamColor(row, fdcFaultRuleColor)}`"
                        >{{ row.paramAlias }}</span
                      >
                    </template>
                    <template #paramAliasHelp="{ column }">
                      <div class="param_alias_help">
                        <span>{{ t(column.title) }}</span>
                        <vxe-tooltip
                          :enterable="true"
                          :use-h-t-m-l="true"
                          theme="light"
                          :content="getHelpTip()"
                        >
                          <div>
                            <svg
                              style="margin-left: 4px"
                              class="icon iconfont-header-gray iconfont-small"
                              aria-hidden="true"
                            >
                              <use xlink:href="#icon-help"></use>
                            </svg>
                          </div>
                        </vxe-tooltip>
                      </div>
                    </template>
                  </vxe-grid>
                </div></div
            ></Pane>
            <Pane
              class="cardItem bottom-panel-border pane-border-radius-bottom-right"
              :size="35"
              :min-size="35"
              ><div class="chart_type">
                <EesGroupBottom
                  ref="chartGroupBottom"
                  @change-chart-data="changeBottomData"
                />
                <div class="bottom_btns">
                  <a-button
                    :id="buttonIds.drawChart"
                    :disabled="disableBtn"
                    type="primary"
                    style="margin-right: 10px"
                    @click="drawChart"
                  >
                    {{ t('common.btn.drawChart') }}
                  </a-button>
                </div>
              </div></Pane
            >
          </Splitpanes>
        </div>
      </Pane>
    </Splitpanes>
  </div>
</template>
<style scoped lang="less">
@import '@/assets/style/variable.less';
.trace_detail_analysis {
  width: 100%;
  height: 100%;
  &_chart {
    height: 100%;
    width: 100%;
    // padding: @padding-basis + 2;
    box-sizing: border-box;
    overflow-y: auto;
  }
  &_hanlers {
    width: 100%;
    height: 100%;
    flex-shrink: 0;
    margin-left: @margin-basis;
    overflow-x: auto;
  }
  .param_list {
    :deep(.ant-tabs-nav) {
      margin: 0 !important;
    }
    :deep(.ant-tabs-tab-active) {
      font-weight: @font-weight-md;
    }
    width: 100%;
    height: 100%;
    color: @text-subtitle-color;
    // padding-left: 10px;
    &_top {
      display: flex;
      justify-content: space-between;
      margin: @margin-basis + 2 0;
      .top_left {
        display: flex;
        width: 100%;
        padding: 0 @padding-basis + 6;
        justify-content: space-between;
        // border: 1px solid @border-color;
        .top_title {
          font-weight: @font-weight-base;
          margin-right: @margin-lg;
          white-space: nowrap;
          font-size: @font-size-sm;
          color: @text-title-color;
        }
      }
      :deep(.ant-switch) {
        background-color: @primary-color;
        margin-right: 10px;
      }
    }
    &_center {
      margin-bottom: 10px;
    }
    &_btns {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: @margin-basis + 2 0;
      .btns_left {
        display: flex;
      }
    }
  }
  .chart_type {
    width: 100%;
    height: 100%;
    overflow: auto;
    padding: 0 (@padding-basis + 6);
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .bottom_btns {
      display: flex;
      justify-content: flex-end;
      align-items: center;
      // margin-top: 20px;
      height: 52px;
      min-height: 52px;
      .btn {
        margin-right: @margin-basis + 2;
      }
    }
  }
  .result-fixed {
    position: fixed;
    right: 50px;
    bottom: 300px;
    height: 45px;
    width: 45px;
    z-index: 10;
    .iconfont-box {
      display: flex;
      justify-content: center;
      align-items: center;

      cursor: pointer;
      height: 45px;
      width: 45px;
      // border: 1px solid @primary-color;
      border-radius: 50%;
      line-height: 45px;
      // text-align: center;
      color: @primary-color;
      font-size: @font-size-md - 2;
      background-color: @bg-panel-color;
      box-shadow: 0px 2px 24px 0px @shadow-color-3;
      .icon-a-Report3 {
        font-size: @font-size-lg;
      }

      i.iconfont {
        margin-right: 0;
      }
    }
  }
  :deep(.vxe-body--row.error_row) {
    color: @error-color;
  }
  :deep(.vxe-body--row.warning_row) {
    color: @warning-color;
  }
}
.my-table {
  :deep(.vxe-grid--toolbar-wrapper.border) {
    border-top: none !important;
  }
}
.trace_detail_analysis_hanlers {
  // padding: 14px;
  margin-left: 0px !important;
  height: calc(100% - 10px);
  .param_list_tab {
    :deep(.ant-tabs-nav) {
      background-color: @bg-page-color;
      border-top: 1px solid @border-color;
      &::before {
        border: none;
      }
      .ant-tabs-nav-list {
        border-right: none;
      }
      .ant-tabs-nav-more {
        border: none;
        border-left: 1px solid @border-color;
      }
    }
    :deep(.ant-tabs-tab) {
      padding: @padding-common @padding-inner !important;
      border-radius: 0 !important;
      border: none;
      border-right: 1px solid @border-color;
      .ant-tabs-tab-btn {
        text-shadow: none;
      }
    }
  }
}
.my_checkbox {
  height: 40px;
  line-height: 40px;
  border: 1px solid @border-color;
  padding: 0 @padding-basis + 6;
  border-bottom: none;
  border-left: none;
  border-right: none;
}
.param_list_top {
  // border: 1px solid @border-color;
  border-radius: 0 @border-radius-lg 0 0;
  border-bottom: none;
  // padding: 0 10px;
  margin: 0px !important;
  height: 52px;
  // line-height: 52px;
  display: flex;
  align-items: center;
}
</style>
