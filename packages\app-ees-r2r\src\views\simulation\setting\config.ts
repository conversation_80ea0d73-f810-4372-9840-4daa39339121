import {
  VXETable,
  type VxeColumnProps,
  type VxeFormPropTypes,
  type VxeGridProps,
  t
} from '@futurefab/vxe-table';
import { simulation as buttonIds } from '@/log-config';
import { type BtnAuth, isPermission } from '@/utils/permission';
import { filters, filterRender, filterMethod } from '@/utils/table-config';
import { isArray, intersection } from 'lodash-es';
import {
  getSimulationDataSource,
  getSimulationImportTable,
  getSimulationQueryTable
} from '@futurefab/ui-sdk-api';
import { patternLetter } from '../modal/config';
import { computed } from 'vue';
import { useR2RSimulationStore } from '@futurefab/ui-sdk-stores';
import { tableDefaultConfig } from '@futurefab/ui-sdk-ees-basic';
const store = useR2RSimulationStore();
const { showWarning, successInfo } = VXETable.tableFun;

export const methodOptions: VxeGridProps = tableDefaultConfig({
  height: 300,
  id: 'simulation-method',
  border: 'full',
  toolbarConfig: {
    tableName: 'r2rSimulation.title.method',
    import: false,
    export: false,
    refresh: false
  },
  editConfig: {
    showStatusBorder: false,
    showInsertStatus: false
  },
  columns: [
    {
      field: 'order',
      width: 40,
      align: 'center',
      title: 'r2rSimulation.title.order',
      fixed: 'left',
      params: {
        selectable: false
      },
      slots: {
        default: 'order'
      }
    },
    {
      field: 'rawId',
      type: 'checkbox',
      width: 40,
      align: 'center'
    },

    {
      field: 'methodName',
      minWidth: 80,
      title: 'common.field.name',
      filters
    },
    {
      field: 'lotBase',
      width: 160,
      title: 'r2rSimulation.field.lotBase',
      align: 'center',
      type: 'columnCheckbox',
      filters: []
    },
    {
      field: 'alias',
      width: 110,
      title: 'common.field.alias',
      filters,
      editRender: {
        name: '$input'
      }
    }
  ]
});

export const getTempOptions = (btnAuth: BtnAuth): VxeGridProps => {
  return tableDefaultConfig({
    id: 'simulation-template',
    borderOutLine: [1, 0, 0, 0],
    toolbarConfig: {
      tableName: 'r2rSimulation.title.tempList',
      import: false,
      export: isPermission(btnAuth, buttonIds.setting.exportTemp),
      tools: [
        ...VXETable.tableFun.getToolsButton([
          {
            name: 'common.btn.delete',
            icon: 'icon-btn-delete',
            visible: isPermission(btnAuth, buttonIds.setting.deleteTemp),
            id: buttonIds.setting.deleteTemp
          }
        ])
      ],
      slots: {
        beforeTools: 'beforeTools'
      }
    },
    exportConfig: {
      filename: 'R2RSimulation_TemplateList.xlsx',
      id: buttonIds.setting.exportTemp
    },
    columns: [
      {
        field: 'rawId',
        type: 'checkbox',
        width: 40,
        align: 'center'
      },
      {
        field: 'area',
        minWidth: 120,
        title: 'r2rSimulation.field.area',
        filters: []
      },
      {
        field: 'name',
        minWidth: 180,
        title: 'r2rSimulation.field.tempName',
        filters
      },
      {
        field: 'createBy',
        minWidth: 160,
        title: 'r2rSimulation.field.createdBy',
        filters
      },
      {
        field: 'createDtts',
        minWidth: 200,
        title: 'r2rSimulation.field.createdDtts',
        filters,
        filterRender,
        filterMethod: ({ option, row }: any) => filterMethod(option, row.createDtts)
      },
      {
        field: 'lastUpdateBy',
        minWidth: 160,
        title: 'r2rSimulation.field.updatedBy',
        filters
      },
      {
        field: 'lastUpdateDtts',
        minWidth: 200,
        title: 'r2rSimulation.field.updatedDtts',
        filters,
        filterRender,
        filterMethod: ({ option, row }: any) => filterMethod(option, row.lastUpdateDtts)
      }
    ]
  });
};

export const getColumnConfig = (columns: VxeColumnProps[]) => {
  const exportColumns: { field: string }[] = [];
  const selectColumns = store.state.templateJson.selectedMethod.map(
    (item) => item.methodName as string
  );
  columns.forEach((col) => {
    col.filterMethod = ({ option, row }: any) => filterMethod(option, row[col.field!]);
    if (![...selectColumns, 'dataSet'].includes(col.field!)) {
      exportColumns.push({ field: col.field! });
    }
  });
  return { columns, exportColumns };
};

export const getDataOptions = (btnAuth: BtnAuth, columns: VxeColumnProps[]): VxeGridProps => {
  const { validation, save, start, stop, pause } = buttonIds.dataSource;
  const options: VxeGridProps = tableDefaultConfig(
    {
      id: 'simulation-data-source-list',
      border: 'full',
      borderOutLine: [1, 0, 0, 0],
      toolbarConfig: {
        tableName: 'r2rSimulation.title.data',
        border: false,
        import: false,
        export: isPermission(btnAuth, buttonIds.dataSource.export),
        tools: [
          ...VXETable.tableFun.getToolsButton([
            {
              name: 'r2rSimulation.btn.saveData',
              icon: 'icon-btn-save',
              visible: isPermission(btnAuth, save),
              id: save
            },
            {
              name: 'common.btn.validation',
              icon: 'icon-btn-validation',
              visible: isPermission(btnAuth, validation),
              id: validation
            },
            {
              name: 'r2rSimulation.btn.start',
              icon: 'icon-btn-start',
              visible: false,
              id: start,
              code: 'start'
            },
            {
              name: 'r2rSimulation.btn.start',
              icon: 'icon-btn-start',
              visible: isPermission(btnAuth, start),
              id: start,
              code: 'start_gray'
            },
            {
              name: 'r2rSimulation.btn.stop',
              icon: 'icon-btn-stop',
              visible: false,
              id: stop
            },
            {
              name: 'r2rSimulation.btn.stop',
              icon: 'icon-btn-stop',
              visible: isPermission(btnAuth, stop),
              id: stop,
              code: 'stop_gray'
            },
            {
              name: 'r2rSimulation.btn.pause',
              icon: 'icon-btn-pause',
              visible: false,
              id: pause
            },
            {
              name: 'r2rSimulation.btn.pause',
              icon: 'icon-btn-pause',
              visible: isPermission(btnAuth, pause),
              id: pause,
              code: 'pause_gray'
            }
          ])
        ],
        slots: {
          beforeTools: 'beforeTools'
        }
      },
      exportConfig: {
        filename: 'R2RSimulation_DataSourceList.xlsx',
        id: buttonIds.dataSource.export,
        columns: getColumnConfig(columns).exportColumns
      },
      checkboxConfig: {
        visibleMethod: ({ row }: any) =>
          !['ALIAS', 'METHOD', ...store.state.noPassDataSet].includes(row.dataSet),
        checkMethod: () => !store.state.isSimulating
      },
      editConfig: {
        activeMethod: () => !store.state.isSimulating
      },
      columns: [
        {
          type: 'checkbox',
          width: 40,
          field: 'rawId'
        },
        ...getColumnConfig(columns).columns
      ]
    },
    false
  );
  options.toolbarConfig?.tools?.forEach((item) => {
    if (['start_gray', 'stop_gray', 'pause_gray'].includes(item.code!)) {
      item.disabled = true;
    }
  });
  return options;
};

export const tempFormRules: VxeFormPropTypes.Rules = {
  tempName: [
    {
      required: true,
      message: t('common.tip.selectName', {
        name: t('r2rSimulation.title.tempName')
      })
    },
    {
      pattern: patternLetter,
      message: t('r2rSimulation.tip.onlyLetter')
    }
  ]
};

export const dataFormRules: VxeFormPropTypes.Rules = {
  name: [
    {
      required: true,
      message: t('common.tip.inputName', {
        name: t('r2rSimulation.title.name')
      })
    },
    {
      pattern: patternLetter,
      message: t('r2rSimulation.tip.onlyLetter')
    }
  ]
};

export function getDataList(fieldList: string[], valueList: string[], dataSet: string) {
  const list: any[] = [];
  const obj: any = {};
  if (isArray(valueList) && valueList.length) {
    valueList.forEach((value, index) => {
      const field = fieldList[index];
      if (field) obj[field] = value;
      obj.dataSet = dataSet;
    });
    list.push(obj);
  }
  return list;
}

export function getColumns(list: string[]) {
  const columns: VxeColumnProps[] = [
    {
      title: 'DATASET',
      field: 'dataSet',
      width: 120,
      align: 'center',
      fixed: 'left'
    }
  ];
  list.forEach((field) =>
    columns.push({
      field,
      minWidth: field.length * 14 + 50,
      title: field,
      editRender: {
        enabled: field !== 'AREA'
      },
      slots: {
        edit: 'edit-data-row'
      },
      filters,
      filterRender,
      filterMethod: ({ option, row }: any) => filterMethod(option, row[field])
    } as VxeColumnProps)
  );
  return columns;
}

// 获取data-source list
type GetDataType = {
  conditions: any;
  dataSourceType: string;
  area: string;
  isApply?: boolean;
};
type ReturnValue = { columns: VxeColumnProps[]; tableList: any[] };
export function getDataSourceList({ conditions, dataSourceType, area, isApply }: GetDataType) {
  return new Promise<ReturnValue>(async (resolve) => {
    let res: any;
    const bodyParams = { ...conditions, area };
    if (dataSourceType === 'History') {
      res = await getSimulationDataSource({ bodyParams });
    } else if (dataSourceType === 'Query') {
      res = await getSimulationQueryTable({ bodyParams: { ...bodyParams, type: 'apply' } });
    } else {
      res = await getSimulationImportTable({ bodyParams });
    }
    const { headerList = [], dataList = [] } = res.data || {};
    (dataList as any[]).forEach((_, i) => (_.dataSet = i + 1));
    let columns: VxeColumnProps[] = [];
    let tableList: any[] = [];
    if (Array.isArray(headerList[0])) {
      columns = getColumns(headerList[0]);
      tableList = isApply
        ? [
            ...getDataList(headerList[0], headerList[1], 'ALIAS'),
            ...getDataList(headerList[0], headerList[2], 'METHOD'),
            ...dataList
          ]
        : dataList;
    }
    resolve({ columns, tableList });
  });
}

// 验证data-source
type ValidateData = {
  list: any[];
  isSave?: boolean;
  methodList?: string[];
  isDataConfig?: boolean;
};
export function onValidationData({ list, isSave, methodList, isDataConfig }: ValidateData) {
  return new Promise<{ result: boolean; noPassDataList?: any[] }>((resolve) => {
    const { trueValidateColumns, isBySub } = store.state;
    if (!list.length) {
      resolve({ result: false });
      return showWarning(t('r2rSimulation.tip.dataSourceEmpty'));
    }
    // 1.判断必填项是否存在
    const valList = Object.values(list[0]).filter(
      (val: any) => !['ALIAS', true, false, undefined, null, ''].includes(val)
    );
    const _validationColumns = [...trueValidateColumns];
    if (isBySub && !isDataConfig) _validationColumns.push(...['SUB-SUBSTRATEID', 'SUB-SLOTNO']);
    for (let i = 0; i < _validationColumns.length; i++) {
      if (!valList.includes(_validationColumns[i])) {
        resolve({ result: false });
        return showWarning(t('r2rSimulation.tip.aliasNoExist', { name: _validationColumns[i] }));
      }
    }
    // 2.若该列有Method且在左侧的勾选Method列表中，那么Alias也是必填
    for (let i = 0; i < Object.keys(list[1]).length; i++) {
      const key = Object.keys(list[1])[i];
      if (list[1][key] && list[1][key].length) {
        const intersectArr = intersection(methodList, list[1][key]);
        if (intersectArr.length && !list[0][key]) {
          resolve({ result: false });
          return showWarning(t('r2rSimulation.tip.aliasEmpty', { name: key }));
        }
      }
    }
    // 3.判断每个列不能重复
    for (let i = 0; i < valList.length; i++) {
      if (valList.indexOf(valList[i]) !== i) {
        return showWarning('r2rSimulation.tip.aliasRepeat');
      }
    }
    // 验证Method行
    // 1.验证必填列是否有值
    const requiredKey = Object.keys(list[0]).filter((key) =>
      _validationColumns.includes(list[0][key])
    );
    for (let i = 0; i < requiredKey.length; i++) {
      if (!list[1][requiredKey[i]] || !list[1][requiredKey[i]].length) {
        resolve({ result: false });
        return showWarning(t('r2rSimulation.tip.methodEmpty', { name: requiredKey[i] }));
      }
    }
    // 2.simulation时验证Method行的必填列是不是都含有勾选的的Method
    if (methodList && methodList.length) {
      const keyList = Object.keys(list[1]);
      for (let i = 0; i < keyList.length; i++) {
        const methodItem = list[1][keyList[i]];
        if (requiredKey.includes(keyList[i]) && Array.isArray(methodItem) && methodItem.length) {
          const intersectArr: string[] = intersection(methodList, methodItem); //勾选的method与列表的method交叉项
          if (!intersectArr || !intersectArr.length) {
            resolve({ result: false });
            return showWarning(t('r2rSimulation.tip.methodWrong', { name: keyList[i] }));
          }
        }
      }
    }
    // 验证实际数据必填项
    const dataList = list.slice(2);
    const noPassDataSet: number[] = [];
    const noPassDataList = dataList.filter((item) => {
      let flag = true;
      requiredKey.forEach((field) => {
        if (!item[field]) flag = false;
      });
      if (!flag) noPassDataSet.push(item.dataSet);
      return !flag;
    });
    store.setNoPassDataSet(noPassDataSet);
    if (!isSave) successInfo(t('common.tip.validateSuccess'));
    resolve({ result: true, noPassDataList });
  });
}

// 验证simulation
export async function onValidationTemp() {
  const { area, modelGroup, selectedMethod, intervalTime, dataList } = store.state.templateJson;
  return new Promise<{ result: boolean; noPassDataList?: any[] }>(async (resolve) => {
    if (!area) {
      resolve({ result: false });
      return showWarning(t('common.tip.selectName', { name: t('r2rSimulation.title.area') }));
    }
    if (!modelGroup) {
      resolve({ result: false });
      return showWarning(t('common.tip.selectName', { name: t('r2rSimulation.title.modelGroup') }));
    }
    if (!selectedMethod.length) {
      resolve({ result: false });
      return showWarning(t('r2rSimulation.tip.checkOne'));
    }
    if (!intervalTime) {
      resolve({ result: false });
      return showWarning(
        t('common.tip.inputName', { name: t('r2rSimulation.title.intervalTime') })
      );
    }
    const methodList: string[] = ['ALL', ...selectedMethod.map((item) => item.methodName)];
    const res = await onValidationData({ list: dataList, methodList });
    resolve(res);
  });
}

// 格式化Method行 字符串的话转成数组
export function formatTableData(list: any[], columns: VxeColumnProps[]) {
  for (const key in list[1]) {
    columns.forEach((item) => {
      if (item.field === key && item.field !== 'dataSet' && !Array.isArray(list[1][key])) {
        list[1][key] = list[1][key] ? list[1][key].split(',') : [];
      }
    });
  }
  return list;
}

// alias和method行的数据源
export const aliasOptions = computed(() => store.state.aliasOptions);
export const _methodOptions = computed(() => store.state.methodOptions);
export const methodOptionsNoAll = computed(() =>
  store.state.methodOptions.filter(({ value }) => value !== 'ALL')
);

// ALL和其他选项互斥
// 控制method行下拉单多选，Alias属于Alias列表则多选，否则单选切不能选ALL
export function isMultiSelect(field: string, xGrid: any) {
  const { fullData } = xGrid.getTableData();
  return !!aliasOptions.value.filter((item) => item.value === fullData[0][field]).length;
}
export function onChange(e: any, row: any, field: string, rowIndex: number, xGrid: any) {
  const val: string[] = row[field];
  if (rowIndex) {
    if (isMultiSelect(field, xGrid)) {
      if (val && val.length > 1 && val.includes('ALL')) {
        row[field] = e.$event.target.innerText === 'ALL' ? ['ALL'] : val.filter((v) => v !== 'ALL');
      }
    } else {
      row[field] = e.$event ? [e.$event.target.innerText] : [];
    }
  } else {
    const len = aliasOptions.value.filter(({ value }) => value === e.value).length;
    const { fullData } = xGrid.getTableData();
    const arr = fullData[1][field];
    if (!len && arr && (arr.length > 1 || arr.includes('ALL'))) {
      fullData[1][field] = [];
    }
  }
}
