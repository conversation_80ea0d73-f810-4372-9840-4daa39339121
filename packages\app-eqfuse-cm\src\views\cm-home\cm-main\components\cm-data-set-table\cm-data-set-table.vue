<!-- eslint-disable vue/no-mutating-props -->
<script setup lang="ts">
import { reactive, ref, nextTick, watch, shallowRef, computed } from 'vue';
import { GroupConfigVO } from '@/model/GroupConfigVO';
import { cloneDeep, debounce } from 'lodash-es';
import CmTreeTable from '@/components/cm-tree-table/index.vue';
import StatusNumberInfo from '@/views/cm-home/cm-main/components/cm-data-set-table/status-number-info/status-number-info.vue';
import TooltipTable from './reference-column/tooltip-table.vue';
import { getCount } from '@/utils';
import NoData from '@/views/cm-home/cm-main/add-dataset/common-grid/no-data.vue';
import DatasetHeader from '@/views/cm-home/cm-main/components/cm-data-set-table/reference-column/dataset-header.vue';
import ParameterSortAndSearch from '@/views/cm-home/cm-main/components/cm-data-set-table/parameter-sort-and-search/index.vue';
import SpecialTag from '@/views/cm-home/cm-main/components/special-tag/index.vue';
import { t } from '@futurefab/vxe-table';

let props: any = defineProps([
  'tableData',
  'columns',
  'commonGroupConfig',
  'selectedReferenceType',
  'useDataNormalization',
  'isHistory',
  'websocketParams'
]);
let emits = defineEmits([
  'update:tableData',
  'update:columns',
  'update:commonGroupConfig',
  'sendWebsocket',
  'showAddDataset',
  'getClickedRow',
  'nameColorChange'
]);
// const stepInfo = defineModel<any[]>('stepInfo');
const firstColWidth = defineModel<number>('firstColWidth', { default: 350 });
const tableLoading = ref(false);
const groupConfigTable = ref();
let d = reactive({
  tableTreeConfig: {
    transform: true,
    rowField: 'parentGroupId',
    parentField: 'groupId',
    expandRowKeys: [1],
    accordion: false,
    lazy: true,
    trigger: 'row'
  } as any,
  expandConfig: {
    expandAll: true,
    expandRowKeys: [1],
    accordion: false,
    lazy: true,
    visibleMethod(d: any) {
      if (d.rowIndex > 1) {
        return true;
      }
      return false;
    }
  }
});
// 每列数据的缓存
const columnsInfoTemp: any = shallowRef([]);
const cellClassName = (c: any) => {
  // 参数cell样式
  if (c.row.isAnalysisResultRow && c.columnIndex === 0 && !c.row.isGroup) {
    let hasWarning = false;
    for (const [groupConfigId, value] of c.row.allColInfo) {
      if (value?.unmatchingRecipeSteps && value.unmatchingRecipeSteps.length > 0) {
        return 'un-match-recipe';
      }
      if (value?.warningRecipeSteps && value.warningRecipeSteps.length > 0) {
        hasWarning = true;
      }
    }
    if (hasWarning) return 'warning-recipe';
    return 'col-red';
  }
  if (
    c.row.isAnalysisResultRow &&
    c.columnIndex != 0 &&
    !props.commonGroupConfig.isReferenceColumn(c.columnIndex - 1) &&
    c.row.isGroup
  ) {
    return 'no-padding';
  }
  // inActive和notSelected样式
  if (
    c.row.inActive ||
    c.row.notSelected ||
    (c.row.isGroup && c.columnIndex != 0 && c.row?.missingCount === c.row?.totalCount)
  ) {
    return 'no-padding gray-cell';
  }
  // 结果cell样式
  if (
    c.row.isAnalysisResultRow &&
    c.columnIndex != 0 &&
    !props.commonGroupConfig.isReferenceColumn(c.columnIndex - 1) &&
    !c.row.isGroup
  ) {
    const id = Number(columnsInfoTemp.value?.[c.columnIndex]?.id);
    const lineInfo = c.row.allColInfo.get(id);
    if (lineInfo?.unmatchingRecipeSteps.length > 0) return 'no-padding un-match-recipe-important';
    if (lineInfo?.warningRecipeSteps.length > 0) return 'no-padding warning-recipe-important';

    return 'no-padding no-padding-result';
  }
  return 'col-red';
};
const headerClass = (c: any) => {
  return c.col?.reference ? 'is-reference ' : '';
};
const cellClassEnhance = (c: any) => {
  return (c.col?.reference ? 'is-reference ' : '') + cellClassName(c);
};
let removeGroupConfig = (c: GroupConfigVO) => {
  let nowTableData = props.tableData?.map((e: { id: string; configColumns: any[] }) => {
    if (e.id == 'configColumns') {
      e.configColumns = e.configColumns.filter((gc) => gc.id != c.id);
      return e;
    } else {
      return e;
    }
  });
  let nowColumns = props.columns.filter((gc: { id: number | undefined }) => gc.id != c.id);
  props.commonGroupConfig.remove(c);
  emits('update:tableData', nowTableData);
  emits('update:columns', nowColumns);
  emits('update:commonGroupConfig', props.commonGroupConfig);
};
const cellClick = debounce((rowData: any) => {
  const { row, seq, columnIndex } = rowData;
  if ([null, undefined].includes(row.groupId)) return;

  // 设置点击的单元格状态
  handleCellClick(row.treeKey, columnIndex);

  emits(
    'sendWebsocket',
    { paramAliasList: [row?.parameterName] },
    { ...row, groupConfigId: props.columns?.[columnIndex]?.id, isClick: true }
  );
  emits('getClickedRow', rowData);
}, 600);
const tableInfo = shallowRef<any[]>([]);
const tableInfoTemp = shallowRef<any[]>([]);
const columnsInfo: any = shallowRef([]);
const treeData = ref<any[]>([]);
const updateInfo = (data: any) => {
  tableInfoTemp.value = data;
  if (searchByResult.value) {
    updateCategoryInfo(data);
  } else {
    updateCategoryInfo(sortByCategoryOrder(data));
  }
};
const updateCategoryInfo = (data: any) => {
  treeData.value = getTreeData(data);
  const row = treeData.value?.[0];
  nextTick(() => {
    // 更新图表数据
    emits('sendWebsocket', { paramAliasList: [row?.children?.[0]?.parameterName] }, row[0]);
  });
};
// 点击单元格变化其border
const clickedCell = ref<{ rowTreeKey: number; columnIndex: number } | null>(null);
const handleCellClick = (rowTreeKey: number, columnIndex: number) => {
  clickedCell.value = { rowTreeKey, columnIndex };
};
// 默认为searchByResult，false的时候变为sortByCategory
const searchByResult = ref<boolean>(true);
// 给每个分组的数据排序
const sortAndGroupByGroupId = (items: any) => {
  const grouped = items.reduce((acc: any, item: any) => {
    if (!acc[item.groupId]) {
      acc[item.groupId] = [];
    }
    acc[item.groupId].push(item);
    return acc;
  }, {});
  return Object.entries(grouped)
    .sort(
      (a, b) =>
        items.findIndex((x: any) => x.groupId === Number(a[0])) -
        items.findIndex((x: any) => x.groupId === Number(b[0]))
    )
    ?.flatMap(([_, group]: any) =>
      group.sort((a: any, b: any) => {
        // 首先处理 notSelected 状态，notSelected 为 true 的排在最后
        if (a.notSelected && !b.notSelected) return 1;
        if (!a.notSelected && b.notSelected) return -1;
        // 其次处理 inActive 状态，inActive 为 true 的排在倒数第二
        if (a.inActive && !b.inActive) return 1;
        if (!a.inActive && b.inActive) return -1;

        // 其他按照原来的排序逻辑
        return a.categoryOrder - b.categoryOrder;
      })
    );
};
const sortByCategoryOrder = (arr: any) => {
  // 按照group分组，每组单独排序
  let withCategoryOrder = sortAndGroupByGroupId(arr.filter((item: any) => 'categoryOrder' in item));
  let sortedArr = [];
  let withIndex = 0;
  for (let i = 0; i < arr.length; i++) {
    if ('categoryOrder' in arr[i]) {
      sortedArr.push(withCategoryOrder[withIndex++]);
    } else {
      sortedArr.push(arr[i]);
    }
  }

  return sortedArr;
};
const setSortByCategory = () => {
  const originData = tableInfoTemp.value?.length > 0 ? tableInfoTemp.value : tableInfo.value;
  if (searchByResult.value) {
    updateCategoryInfo(originData);
  } else {
    const newData = cloneDeep(originData);
    updateCategoryInfo(sortByCategoryOrder(newData));
  }
};
// 高亮第一行内容
watch(
  () => props.tableData,
  () => {
    // 设置默认展开的parentGroupId
    d.tableTreeConfig.expandRowKeys = [props.tableData?.[2]?.parentGroupId];
    d.expandConfig.expandRowKeys = [props.tableData?.[2]?.parentGroupId];
    columnsInfo.value = props.columns;
    columnsInfoTemp.value = cloneDeep(props.columns);
    tableInfo.value = cloneDeep(props.tableData);
    treeData.value = getTreeData(props.tableData);
    clickedCell.value = null;
  },
  { deep: true }
);
const headerHight = computed(() => {
  // 计算表头高度，表头为EQP、Chamber...
  return props.columns?.[0]?.length * 20 + 117;
});
const itemHeight = 24;
const categoryCheckData = shallowRef([]);
const groupCheckData = shallowRef([]);
const parameter = shallowRef([]);
const stepCheck = shallowRef([]);
const _columns = ref<any[]>([]);
const clearModalTemp = () => {
  categoryCheckData.value = [];
  groupCheckData.value = [];
  parameter.value = [];
  stepCheck.value = [];
  searchByResult.value = true;
  tableInfoTemp.value = [];
};
watch(
  () => props.columns,
  (v) => {
    _columns.value = v;
  }
);
const getAllGroupResult = (resultGroupInfos: any, id: number) => {
  return resultGroupInfos
    ?.filter((item: any) => item.id === id)
    ?.reduce((pre: any, next: any) => {
      return pre.concat(next.results);
    }, []);
};
const getMatchingRatio = (resultGroupInfos: any, id: number) => {
  let totalCount = 0;
  let matchingCount = 0;
  resultGroupInfos
    ?.filter((item: any) => item.id === id)
    ?.forEach((item: any) => {
      totalCount += item.totalCount;
      matchingCount += item.matchingCount;
    });
  return totalCount < 1 ? '0' : ((matchingCount / totalCount) * 100).toFixed(2);
};
function getTreeData(data: any[]) {
  const result: any[] = [];
  if (!data?.length || data?.length < 2) {
    return [];
  } else {
    cloneDeep(data.slice(2)).forEach((item: any) => {
      if (item.groupId === null || item.groupId === undefined) {
        result.push({ ...item, children: [], treeKey: item.parentGroupId, expand: false });
      } else {
        result
          .find((group) => group.parentGroupId === item.groupId)
          ?.children.push({ ...item, treeKey: item.gorupId + item.parameterName });
      }
    });
    return result;
  }
}
// 根据id和parameterName返回指定的组数组
const getSpecifyGroup = (resultGroupInfos: any, id: number, parameterName: string) => {
  return resultGroupInfos?.find(
    (item: any) => item.id === id && item.parameterName === parameterName
  );
};

// Screenshot
const triggerRowClick = async (rowData: any) => {
  if (!rowData || !groupConfigTable.value) return;

  groupConfigTable.value.cellClick(rowData, { visibleColIndex: 0 }, 0, false);
  await new Promise((resolve) => setTimeout(resolve, 300));
};

// 展开/收起所有有子节点的行
const expandOrFoldAll = (isExpand: boolean) => {
  groupConfigTable.value.expandOrFoldAll(isExpand);
};
// 设置SpecialTag的type和text
const setSpecialTag = (row: any) => {
  // 零飘
  if (row?.inActive) return { type: 'info', text: t('cm.label.inactive') };
  // 检查 row.results 是否存在
  if (!row?.results || !Array.isArray(row.results)) {
    return { type: 'success', text: t('cm.label.matching') };
  }
  // 循环 row.results
  for (const result of row.results) {
    // 检查 allColInfo 是否存在且为 Map 结构
    if (!result?.allColInfo || !(result.allColInfo instanceof Map)) {
      continue;
    }
    // 循环 allColInfo Map，先收集所有状态
    let hasWarning = false;
    for (const [groupConfigId, value] of result.allColInfo) {
      // 检查 unmatchingRecipeSteps 长度大于0，直接返回 error
      if (value?.unmatchingRecipeSteps && value.unmatchingRecipeSteps.length > 0) {
        return { type: 'error', text: t('cm.label.critical') };
      }
      // 检查 warningRecipeSteps 长度大于0，标记有警告
      if (value?.warningRecipeSteps && value.warningRecipeSteps.length > 0) {
        hasWarning = true;
      }
    }
    // 如果有警告，返回 warning
    if (hasWarning) return { type: 'warning', text: t('cm.label.warning') };
  }
  return { type: 'success', text: t('cm.label.matching') };
};
const nameColorChange = () => emits('nameColorChange');
defineExpose({
  clearModalTemp,
  triggerRowClick
});
</script>
<template>
  <div
    :key="tableInfo.length"
    v-isLoading="{
      isShow: tableLoading,
      hasButton: false,
      title: 'Loading...'
    }"
    class="container"
  >
    <CmTreeTable
      v-if="tableInfo?.length && tableInfo?.length >= 2"
      ref="groupConfigTable"
      class="cm-customer-table"
      v-model:firstColWidth="firstColWidth"
      :columns="_columns"
      :header-hight="headerHight"
      :item-height="itemHeight"
      :tree-data="treeData"
      :cell-class="cellClassEnhance"
      :header-class="headerClass"
      @cell-click="cellClick"
    >
      <template #header="{ col, index }">
        <template v-if="index == 0">
          <div class="first-column-box cm-header-first-row">
            <div class="dataset-text">
              Dataset<span v-if="useDataNormalization" class="applied">Norm</span>
            </div>
            <div class="cm-header-tag">
              <slot name="dataset-header-right" />
            </div>
          </div>
          <div class="params-label cm-lablel-color cm-header-two-row">
            <template v-for="(i, index) in col" :key="index">
              <div v-if="typeof i === 'string'" class="data-set-item">
                {{ i }}
              </div>
            </template>
          </div>
          <div class="cm-header-three-row cm-header-three-row-total">
            <ParameterSortAndSearch
              v-if="tableInfo.length >= 3"
              :columns="columns"
              :tableData="tableInfo"
              v-model:searchByResult="searchByResult"
              v-model:columnData="_columns"
              @setSortByCategory="setSortByCategory"
              @updateInfo="updateInfo"
            />
            <status-number-info
              v-if="tableInfo[1]?.showMatchingInfo"
              :matching-info="tableInfo[1].matchingInfo"
              @expandOrFoldAll="expandOrFoldAll"
            />
          </div>
        </template>
        <template v-else>
          <div class="cm-header-first-row">
            <DatasetHeader
              :selectedReferenceType="selectedReferenceType"
              :isHistory="isHistory"
              :columnsData="col"
              :tableInfo="tableInfo"
              :websocketParams="websocketParams"
              v-model:reference="col.reference"
              v-model:groupName="col.key"
              v-model:color="col.color"
              v-model:chartLegendColor="col.chartLegendColor"
              @removeGroupConfig="removeGroupConfig"
              @nameColorChange="nameColorChange"
            />
          </div>
          <div class="cm-header-two-row column-two-n">
            <div v-for="(alias, index) in columns[0]" :key="index" class="data-set-item">
              <a-tooltip placement="topLeft" :auto-adjust-overflow="true">
                <template #title>
                  {{ GroupConfigVO.getFieldValueByAlias(alias, col) }}
                </template>
                {{ GroupConfigVO.getFieldValueByAlias(alias, col) }}
              </a-tooltip>
            </div>
          </div>
          <template v-if="!col['reference'] && col.resultGroupInfo">
            <tooltip-table
              :parameter-name="'Unmatching Parameters'"
              :result-data="getAllGroupResult(col.resultGroupInfos, col.id)"
              :is-un-match="true"
              :width="800"
            >
              <template #tooltipBtn="{ clickEvent }">
                <div class="cm-header-three-row match-group-box" @click="clickEvent(true)">
                  <div class="cm-header-three-row-match-text">
                    <i class="iconfont icon-info" style="font-size: 16px" />
                    MATCH
                  </div>
                  <div class="cm-header-three-row-number">
                    <span class="cm-header-three-row-number-span-one">
                      {{ getMatchingRatio(col.resultGroupInfos, col.id) }}%
                    </span>
                    <span class="cm-header-three-row-number-span-two">
                      ({{ getCount(col.resultGroupInfos, 'matchingCount', col.id) }}/{{
                        getCount(col.resultGroupInfos, 'totalCount', col.id)
                      }})
                    </span>
                  </div>
                </div>
              </template>
            </tooltip-table>
          </template>
        </template>
      </template>
      <template #default="d">
        <div
          v-if="d.columnIndex != 0 && d.col['reference']"
          @click="handleCellClick(d.row.treeKey, d.columnIndex)"
        >
          REFERENCE
        </div>
        <div v-if="d.columnIndex != 0 && !d.col['reference'] && d.row.isGroup" style="width: 100%">
          <span class="dec-text">{{
            getSpecifyGroup(d.col.resultGroupInfos, d.col.id, d.row.parameterName)?.getMatchValue()
          }}</span>
          <div class="match-group">
            <div
              class="unmatch-recipe"
              :data-percentage="
                getSpecifyGroup(
                  d.col.resultGroupInfos,
                  d.col.id,
                  d.row.parameterName
                )?.getUnmatchingRatio()
              "
              :style="{
                flex: getSpecifyGroup(
                  d.col.resultGroupInfos,
                  d.col.id,
                  d.row.parameterName
                )?.getUnmatchingRatio()
              }"
            ></div>
            <div
              class="warning-recipe"
              :data-percentage="
                getSpecifyGroup(
                  d.col.resultGroupInfos,
                  d.col.id,
                  d.row.parameterName
                )?.getWarningRatio()
              "
              :style="{
                flex: getSpecifyGroup(
                  d.col.resultGroupInfos,
                  d.col.id,
                  d.row.parameterName
                )?.getWarningRatio()
              }"
            ></div>
            <div
              class="result"
              :data-percentage="
                getSpecifyGroup(
                  d.col.resultGroupInfos,
                  d.col.id,
                  d.row.parameterName
                )?.getMatchingRatio()
              "
              :style="{
                flex: getSpecifyGroup(
                  d.col.resultGroupInfos,
                  d.col.id,
                  d.row.parameterName
                )?.getMatchingRatio()
              }"
            ></div>
            <div
              class="missing-recipe"
              :data-percentage="
                getSpecifyGroup(
                  d.col.resultGroupInfos,
                  d.col.id,
                  d.row.parameterName
                )?.getMissingRatio()
              "
              :style="{
                flex: getSpecifyGroup(
                  d.col.resultGroupInfos,
                  d.col.id,
                  d.row.parameterName
                )?.getMissingRatio()
              }"
            ></div>
          </div>
        </div>
        <div
          v-if="d.columnIndex == 0"
          @click="handleCellClick(d.row.treeKey, d.columnIndex)"
          style="width: 100%"
        >
          <a-tooltip placement="topLeft" :destroy-tooltip-on-hide="true">
            <template #title>
              <span>{{ d.row.parameterName }}</span>
            </template>
            <span v-if="d.row?.categoryColor" class="parameter-text-box">
              <span
                :style="{
                  width: '12px',
                  height: '12px',
                  borderRadius: '2px',
                  backgroundColor: d.row?.categoryColor,
                  flex: 'none',
                  marginRight: '4px'
                }"
              ></span>
              <span :class="d.row?.notSelected ? 'parameter-name not-selected' : 'parameter-name'">
                {{ d.row.parameterName }}
              </span>
              <SpecialTag
                v-if="d.row?.inActive"
                :type="setSpecialTag(d.row).type"
                :text="setSpecialTag(d.row).text"
                :marginLeft="4"
              />
            </span>
            <span v-else class="parameter-text-box">
              <span :class="d.row.missingCount === d.row.totalCount ? 'not-selected' : ''">
                {{ d.row.parameterName }}
              </span>
              <SpecialTag
                v-if="d.row.missingCount !== d.row.totalCount"
                :type="setSpecialTag(d.row).type"
                :text="setSpecialTag(d.row).text"
                :marginLeft="4"
              />
            </span>
          </a-tooltip>
        </div>
        <tooltip-table
          v-if="d.columnIndex != 0 && !d.col['reference'] && !d.row.isGroup"
          :parameter-name="d.row?.parameterName"
          :result-data="d.row.allColInfo.get(Number(d.col?.id))"
          :width="500"
          :trigger="d.row?.notSelected || d.row?.inActive ? 'click' : 'hover'"
        >
          <template #tooltipBtn>
            <div
              :style="{
                height: '100%',
                width: '100%',
                margin: '0',
                border:
                  clickedCell?.rowTreeKey === d.row.treeKey &&
                  clickedCell?.columnIndex === d.columnIndex
                    ? `2px solid ${d.col.color}`
                    : ''
              }"
              @click="handleCellClick(d.row.treeKey, d.columnIndex)"
            >
              <span
                :class="
                  d.row.allColInfo?.get(Number(d.col?.id))?.isUnMatchingTop5
                    ? 'param-text param-text-special'
                    : 'param-text'
                "
                >{{ d.row.allColInfo?.get(Number(d.col?.id))?.recipeText }}
              </span>
            </div>
          </template>
        </tooltip-table>
      </template>
    </CmTreeTable>
    <NoData v-else :border="false" />
  </div>
</template>

<style lang="less" scoped>
@import url('@/assets/style/variable.less');
.container {
  height: 100%;
  overflow: hidden;
  border: 1px solid @border-color;
  border-radius: 4px;
}

.cm-customer-table {
  :deep(.vxe-table--main-wrapper) {
    overflow: auto;
  }

  :deep(.vxe-table--header-wrapper) {
    width: 100%;
    position: sticky;
  }

  :deep(.is-reference) {
    background-color: @primary-color-row-select;
  }

  :deep(.no-padding) {
    padding: 0 !important;
  }
  :deep(.gray-cell) {
    color: @bg-disabled-color;
    background-color: @bg-disabled-color;
  }
  // 不覆盖active样式，参数样式
  :deep(.warning-recipe) {
    cursor: pointer;
    color: @warning-color;
    background-color: @fcm-import-changed-bg;
  }
  :deep(.un-match-recipe) {
    cursor: pointer;
    color: @error-color;
    background-color: @fcm-import-deleted-bg;
  }
  :deep(.missing-recipe) {
    cursor: pointer;
    color: @bg-disabled-color;
    background-color: @bg-disabled-color;
  }
  // 覆盖active样式
  :deep(.warning-recipe-important) {
    cursor: pointer;
    background-color: @fcm-import-changed-bg !important;
  }
  :deep(.un-match-recipe-important) {
    cursor: pointer;
    background-color: @fcm-import-deleted-bg !important;
  }

  :deep(.no-padding-result) {
    background-color: @fcm-import-added-bg !important;
    cursor: pointer;
  }

  :deep(.column0-cls) {
    text-align: right;
    font-weight: bold;
    background-color: @bg-color !important;
  }

  :deep(.vxe-body--column) {
    padding: 0 !important;
  }

  :deep(.sticky-row) {
    position: sticky;
    top: 0;
    z-index: 2;
    background-color: @bg-color !important;
  }

  :deep(.not-highlight) {
    background-color: @bg-color !important;
  }

  :deep(.data-set-item) {
    line-height: 20px;
    height: 20px;
    overflow-x: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  :deep(.dataset-cell) {
    max-height: 200px !important;
  }
}

.params-label {
  border-top: 1px solid @border-color;
  border-bottom: 1px solid @border-color;

  .data-set-item {
    font-weight: 700;
    text-align: right;
  }
}

.dec-text {
  position: absolute;
  z-index: 1;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.result {
  background: @fcm-import-added-bg;
}

.first-column-box {
  display: flex;
  align-items: center;
  justify-content: space-between;
  align-items: center;
  font-weight: 700;
  padding: 0 0 3px 0;

  .applied {
    margin: 0 0 0 5px;
    padding: 0 5px;
    background: @warning-color-light;
    color: @warning-color;
    border: solid 1px @warning-color-special;
    font-weight: normal;
  }

  .data-trim-btn {
    &:hover {
      cursor: pointer;
      color: @primary-color;
    }
  }

  .cm-header-tag {
    display: inline-flex;
    flex-direction: row;
    white-space: nowrap;
    line-height: 24px;
  }
}

// new css
.dec-text {
  position: absolute;
  z-index: 1;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.param-text {
  color: @text-subtitle-color;
  position: absolute;
  z-index: 1;
  top: 50%;
  transform: translateY(-50%);
  left: 16px;
  width: 88%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.parameter-text-box {
  display: inline-flex;
  align-items: center;
  width: 100%;
  overflow: hidden;

  .parameter-name {
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    min-width: 0;
  }
  .not-selected {
    color: @text-disabled-color-1;
  }
}

.param-text-special {
  color: @fcm-import-deleted-text;
}

.result {
  background: @fcm-import-added-bg;
}

.match-group {
  width: calc(100% + 10px);
  height: 32px;
  margin: 0 -5px;
  padding: 0;
  display: flex;
  border-bottom: solid @border-color 1px;

  .result {
    background-color: @fcm-import-added-bg;
  }

  .unmatch-recipe {
    background-color: @fcm-import-deleted-bg;
  }

  .warning-recipe {
    background-color: @fcm-import-changed-bg;
  }
}

.cm-header-first-row {
  color: @bg-icon-active-color;
}
.cm-header-two-row {
  color: @text-sub-color;
  padding: 4px 10px;
}
.cm-header-three-row {
  color: @text-sub-color;
}

.cm-header-three-row-total {
  padding-left: 0;
}

.column-two-n {
  border-top: 1px solid @border-color;
  border-bottom: 1px solid @border-color;
}

.cm-header-first-row {
  padding: 8px 10px;
  font-size: 16px;
  font-weight: bold;
  line-height: 22px;
  text-align: left;

  .dataset-text {
    display: flex;
    align-items: center;
    color: @text-title-color;
    font-size: 16px;
  }
}

.cm-header-three-row {
  &-match-text {
    vertical-align: middle;
    line-height: 20px;
    font-size: 13px;
    font-weight: bold;
    margin-bottom: 6px;
  }

  &-number {
    &-span-one {
      margin-left: 17px;
      font-size: 16px;
      font-weight: 600;
      line-height: 18px;
      color: @success-color;
    }

    &-span-two {
      font-size: 16px;
      line-height: 18px;
      color: @success-color;
    }
  }
}
.match-group-box {
  padding: 10px;
  cursor: pointer;
}

.app_theme_dark {
  .cm-header-first-row {
    background-color: @bg-panel-color;
  }
}
</style>
