<script setup lang="ts">
import { CheckOutlined } from '@ant-design/icons-vue';
const props = withDefaults(
  defineProps<{
    curColor: string;
    type?: 'circle' | 'rectangle';
    showArrow?: boolean;
    disabled?: boolean;
    colorList?: string[];
    tooltipText?: string;
  }>(),
  {
    colorList: () => [
      '#0094C8',
      '#B487FF',
      '#7E9300',
      '#EEAC00',
      '#F06910',
      '#5F99F1',
      '#369B04',
      '#E05BE7',
      '#0A9E99',
      '#EE5D9A',
      '#0AB6F3',
      '#8B5DD6',
      '#97AF0A',
      '#B28104',
      '#F39150',
      '#327AF6',
      '#57C121',
      '#BD3CC4',
      '#34B6AA',
      '#D12970'
    ],
    type: 'rectangle',
    showArrow: false,
    disabled: false
  }
);

const emits = defineEmits(['selectColor']);

const selectColor = (c: string) => {
  emits('selectColor', c);
};
</script>
<template>
  <div class="color-select-popover">
    <a-popover
      v-if="!disabled"
      trigger="click"
      title="Select Color"
      placement="bottomLeft"
      :auto-adjust-overflow="true"
      :arrow="showArrow"
      :overlay-class-name="'color-select-popover'"
    >
      <template #content>
        <div class="color-select-box">
          <div
            v-for="(color, index) in colorList"
            :key="index"
            class="inner-color-cube"
            :style="{ background: color }"
            @click="selectColor(color)"
          >
            <CheckOutlined
              v-if="props.curColor == color"
              style="color: white; transform: scale(0.75)"
            />
          </div>
        </div>
      </template>
      <div
        v-if="type === 'rectangle'"
        class="outer-color-cube"
        :style="{ background: curColor }"
      ></div>
      <a-tooltip v-if="type === 'circle'">
        <template #title>
          <span>{{ tooltipText }}</span>
        </template>
        <div class="circle-box" v-if="type === 'circle'">
          <span
            class="color-circle"
            :style="{
              background: curColor
            }"
          ></span>
        </div>
      </a-tooltip>
    </a-popover>
    <div v-else class="outer-color-cube diabled-cube" :style="{ background: curColor }"></div>
  </div>
</template>
<style lang="less">
@import url('@/assets/style/variable.less');
.color-select-popover {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  .outer-color-cube {
    display: inline-block;
    width: 16px;
    height: 16px;
    border-radius: 2px;
    &:hover {
      cursor: pointer;
      transform: scale(1.05);
    }
    &.diabled-cube:hover {
      cursor: default;
      transform: none;
    }
  }
  .inner-color-cube {
    width: 18px;
    border-radius: 4px;
    height: 18px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;

    &:hover {
      cursor: pointer;
      transform: scale(1.05);
    }
  }
  .color-select-box {
    width: 234px;
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
  }
  .ant-popover-inner {
    width: 262px;
  }
  // 圆形
  .circle-box {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
    border-radius: 2px;
    cursor: pointer;
    &:hover {
      background: @bg-hover-2-color;
    }
    .color-circle {
      display: inline-flex;
      width: 8px;
      height: 8px;
      border-radius: 100%;
    }
  }
}
</style>
