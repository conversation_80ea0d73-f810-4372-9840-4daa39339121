import { Dayjs } from 'dayjs';
export interface ConditionData {
  startDate: string;
  endDate: string;
  area: string;
  eqp: string;
  method: string;
  timeAreaBind: Dayjs[];
  lotId: string;
  lotFlag: boolean;
  recipeId: string;
  recipeFlag: boolean;
}
export interface TableData {
  tid: string;
  area: string;
  eqpId: string;
  lotId: string;
  recipeId: string;
  methodName: string;
  message: string;
  eventDtts: string;
  errorYn: boolean;
  modelGroupWorkflow?: string;
  modelWorkflow?: string;
}

export interface ContextResult {
  label: string;
  isExclude: boolean;
  value: string;
}
