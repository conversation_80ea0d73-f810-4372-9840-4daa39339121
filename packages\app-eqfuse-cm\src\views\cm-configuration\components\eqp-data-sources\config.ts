import { type AddScheduleForm } from '@/utils/schedule';
import { t, VXETable } from '@futurefab/vxe-table';
import dayjs from 'dayjs';
import type { EqpDataTypeSourcesItem, EqpDataTypeSourcesSelectOptions } from './interface';
import { type Ref } from 'vue';
import { eqpDataSources as buttonIds } from '@/log-config';

export const getInitFormValue = ({
    isEdit,
    form,
    eqpId,
}: {
    isEdit: boolean;
    form: null | AddScheduleForm;
    eqpId?: string;
}): AddScheduleForm => {
    const now = dayjs();
    const startDate = dayjs()
        .startOf('hour')
        .add(now.minute() >= 30 ? 1 : 0.5, 'hour')
        .add(1, 'day');
    if (isEdit) {
        return {
            ...form,
            startDate: dayjs(form?.startDate),
            endDate: dayjs(form?.endDate),
        } as AddScheduleForm;
    } else {
        return {
            jobName: eqpId ? eqpId + '_DC' : null,
            startDate: startDate,
            recurrenceBy: 'hour',
            dayInterval: 1,
            hourInterval: 1,
            endType: 'noEnd',
            afterOccurrences: null,
            endDate: startDate.add(1, 'day'),
        };
    }
};

export const getEqpDataSourceConfig = (selectOption: EqpDataTypeSourcesSelectOptions) => {
    return VXETable.tableFun.tableDefaultConfig({
        toolbarConfig: {
            tableName: 'cm.title.eqpDataSources',
            import: false,
            export: false,
            refresh: true,
            border: true,
            tools: [
                ...VXETable.tableFun.getToolsButton([
                    {
                        id: buttonIds.add,
                        name: 'common.btn.add',
                        icon: 'icon-btn-add',
                        visible: false,
                    },
                    {
                        name: 'common.btn.save',
                        icon: 'icon-btn-save',
                        visible: false,
                        id: buttonIds.save,
                    },
                    {
                        id: buttonIds.delete,
                        name: 'common.btn.delete',
                        icon: 'icon-btn-delete',
                        visible: false,
                    },
                    {
                        id: buttonIds.schedule,
                        name: 'common.btn.schedule',
                        icon: 'icon-btn-schedule',
                        visible: false,
                    },
                ]),
            ],
            slots: {
                beforeTools: 'beforeTools',
            },
        },
        columns: [
            {
                type: 'checkbox',
            },
            {
                title: 'cm.field.eqp',
                field: 'eqpId',
                minWidth: 120,
                sortable: true,
                filters: [{ data: '' }],
                filterRender: { name: '$input' },
                editRender: { name: '$input' },
            },
            {
                title: 'cm.field.dataType',
                field: 'eqpDataTypeCd',
                minWidth: 120,
                sortable: true,
                filterRender: {},
                filters: selectOption.dataType,
                editRender: {
                    name: '$select',
                    options: selectOption.dataType,
                },
                formatter: ({ cellValue }: any) => {
                    const temp = selectOption.dataType.find(item => item.value === cellValue);
                    return temp?.label ?? cellValue;
                },
            },
            {
                title: 'cm.field.protocol',
                field: 'protocolCd',
                minWidth: 120,
                sortable: true,
                filters: selectOption.protocal,
                filterRender: {},
                editRender: {
                    name: '$select',
                    options: selectOption.protocal,
                },
                formatter: ({ cellValue }: any) => {
                    const temp = selectOption.protocal.find(item => item.value === cellValue);
                    return temp?.label ?? cellValue;
                },
            },
            {
                title: 'cm.field.ip',
                field: 'ipAddr',
                minWidth: 120,
                sortable: true,
                filters: [{ data: '' }],
                filterRender: { name: '$input' },
                editRender: { name: '$input' },
            },
            {
                title: 'cm.field.port',
                field: 'portNo',
                minWidth: 120,
                sortable: true,
                filters: [{ data: '' }],
                filterRender: { name: '$input' },
                editRender: {
                    name: '$input',
                    props: {
                        type: 'number',
                    },
                },
            },
            {
                title: 'cm.field.user',
                field: 'userName',
                minWidth: 120,
                sortable: true,
                filters: [{ data: '' }],
                filterRender: { name: '$input' },
                editRender: { name: '$input' },
            },
            {
                title: 'cm.field.password',
                field: 'userPass',
                minWidth: 120,
                sortable: true,
                filters: [{ data: '' }],
                filterRender: { name: '$input' },
                editRender: { name: '$input' },
            },
            {
                title: 'cm.field.remoteDirectory',
                field: 'remotePath',
                minWidth: 120,
                sortable: true,
                filters: [{ data: '' }],
                filterRender: { name: '$input' },
                editRender: { name: '$input' },
            },
            {
                title: 'cm.field.localDirectory',
                field: 'localPath',
                minWidth: 120,
                sortable: true,
                filters: [{ data: '' }],
                filterRender: { name: '$input' },
                editRender: { name: '$input' },
            },
            {
                title: 'cm.field.schedulerJob',
                field: 'jobName',
                minWidth: 120,
                sortable: true,
                filters: [{ data: '' }],
                filterRender: { name: '$input' },
            },
        ],
    });
};

export const getScheduleValidationMsg = (form: AddScheduleForm, isEdit?: boolean): string[] => {
    console.log(form, 'getValidationMsg');
    const result: string[] = [];
    if (!form.jobName) {
        result.push(t('common.tip.inputName', { name: t('cm.label.jobName') }));
    }
    if (!form.startDate) {
        result.push(t('common.tip.inputName', { name: t('cm.label.startDate') }));
    } else {
        if (!isEdit && form.startDate.$d.getTime() < new Date().getTime()) {
            result.push(t('common.tip.startDateIsPast'));
        }
    }
    if (form.recurrenceBy !== 'never') {
        if (form.recurrenceBy === 'day' && !form.dayInterval) {
            result.push(t('common.tip.inputName', { name: t('Day Interval') }));
        }
        if (form.recurrenceBy === 'hour' && !form.hourInterval) {
            result.push(t('common.tip.inputName', { name: t('Hour Interval') }));
        }
        if (form.endType === 'after' && !form.afterOccurrences) {
            result.push(t('common.tip.inputName', { name: 'Occurrences' }));
        }
        if (form.endType === 'endBy') {
            if (!form.endDate) {
                result.push(t('common.tip.inputName', { name: 'End Date' }));
            }
        }
    }
    return result;
};
const ipRegex =
    /^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$|^([0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$|^(([0-9a-fA-F]{1,4}:){0,6}[0-9a-fA-F]{1,4})?::(([0-9a-fA-F]{1,4}:){0,6}[0-9a-fA-F]{1,4})?$/;

export const eqpDataRules = (xGrid: Ref<any>) => {
    return {
        eqpId: [
            {
                required: true,
                message: t('common.tip.required', { name: t('cm.field.eqp') }),
            },
        ],
        eqpDataTypeCd: [
            {
                required: true,
                message: t('common.tip.required', { name: t('cm.field.dataType') }),
            },
            {
                validator({ row }: any) {
                    // 唯一校验   eqpId eqpDataTypeCd
                    const data = xGrid.value.getTableData().fullData;

                    const isRepeat = data.some((item: EqpDataTypeSourcesItem) => {
                        if (item === row) {
                            return false;
                        }
                        return item.eqpId == row.eqpId && item.eqpDataTypeCd == row.eqpDataTypeCd;
                    });
                    if (isRepeat) {
                        return new Error(t('cm.tips.eqpAndDataType'));
                    }
                },
            },
        ],
        protocolCd: [
            {
                required: true,
                message: t('common.tip.required', { name: t('cm.field.protocol') }),
            },
        ],
        ipAddr: [
            {
                required: true,
                message: t('common.tip.required', { name: t('cm.field.ip') }),
            },
            {
                validator({ row }: any) {
                    // 校验 ip 合法性
                    const validIp = ipRegex.test(row.ipAddr);
                    return !validIp && new Error(t('common.tip.ipNoValid'));
                },
            },
        ],
        userName: [
            {
                required: true,
                message: t('common.tip.required', { name: t('cm.field.user') }),
            },
        ],
        userPass: [
            {
                required: true,
                message: t('common.tip.required', { name: t('cm.field.password') }),
            },
        ],
        remotePath: [
            {
                required: true,
                message: t('common.tip.required', { name: t('cm.field.remoteDirectory') }),
            },
        ],
        localPath: [
            {
                required: true,
                message: t('common.tip.required', { name: t('cm.field.localDirectory') }),
            },
        ],
    };
};

export const eqpDataValidate = (
    validList: EqpDataTypeSourcesItem[],
    totalList: EqpDataTypeSourcesItem[],
) => {
    const uniMap: Record<string, EqpDataTypeSourcesItem> = {};
    const getRowKey = (row: EqpDataTypeSourcesItem) => [row.eqpId, row.eqpDataTypeCd].join('_');
    for (let i = 0; i < validList.length; i++) {
        const row = validList[i];
        // 校验必填项
        let requireName = '';
        if (!row.eqpId) {
            requireName = t('cm.field.eqp');
        } else if (!row.eqpDataTypeCd) {
            requireName = t('cm.field.dataType');
        } else if (!row.protocolCd) {
            requireName = t('cm.field.protocol');
        } else if (!row.ipAddr) {
            requireName = t('cm.field.ip');
        } else if (!row.userName) {
            requireName = t('cm.field.user');
        } else if (!row.userPass) {
            requireName = t('cm.field.password');
        } else if (!row.remotePath) {
            requireName = t('cm.field.remoteDirectory');
        } else if (!row.localPath) {
            requireName = t('cm.field.localDirectory');
        }
        if (requireName) {
            return { row, msg: t('common.tip.required', { name: requireName }) };
        }
        // 校验 ip 合法性
        const validIp = ipRegex.test(row.ipAddr);
        if (!validIp) {
            return { row, msg: t('common.tip.ipNoValid') };
        }
        // 唯一校验   eqpId eqpDataTypeCd
        const key = getRowKey(row);
        if (uniMap.hasOwnProperty(key) && uniMap[key] !== row) {
            return { row, msg: t('cm.tips.eqpAndDataType') };
        }
        uniMap[key] = row;
    }
    // 唯一校验
    for (let i = 0; i < totalList.length; i++) {
        const row = totalList[i];
        const key = getRowKey(row);
        if (uniMap.hasOwnProperty(key) && uniMap[key] !== row) {
            return { row: uniMap[key], msg: t('cm.tips.eqpAndDataType') };
        }
    }
};
