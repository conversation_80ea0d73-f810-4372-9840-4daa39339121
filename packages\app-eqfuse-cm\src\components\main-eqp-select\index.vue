<template>
  <a-select
    v-model:value="eqpValue"
    show-search
    allow-clear
    max-tag-count="responsive"
    :bordered="false"
    style="width: 180px"
    :default-active-first-option="false"
    :show-arrow="true"
    :options="options"
    :field-names="{ label: 'alias', value: 'rawId' }"
    :filter-option="filterOption('alias')"
    :not-found-content="$t('eesBasic.tips.noData')"
    :dropdown-style="{ zIndex: 99999 }"
    :disabled="disabled"
    @change="change"
  >
    <template #maxTagPlaceholder="omittedValues">
      <a-tooltip :title="omittedTooltip(omittedValues)" placement="bottom">
        <span style="font-weight: normal">+{{ omittedValues.length }}</span>
      </a-tooltip>
    </template>
  </a-select>
</template>
<script lang="ts" setup>
import { omittedTooltip } from '@/utils';
import { ref, watch } from 'vue';
import type { SelectProps } from '@futurefab/ant-design-vue';
const props = defineProps<{
  value: any;
  disabled: boolean;
  options: SelectProps['options'];
}>();
const emits = defineEmits(['update:value', 'change']);
const eqpValue = ref(null);
watch(
  () => props.value,
  (v) => {
    eqpValue.value = v;
  },
  { immediate: true }
);
const filterOption = (labelKey?: string) => (val: string, option: { [k: string]: string }) =>
  option[labelKey ?? 'label'].toLowerCase().includes(val.toLowerCase());

const change = (val: string, option: any) => {
  emits('update:value', val);
  emits('change', option);
};
</script>
