import dayjs from 'dayjs';

export const getEndTime = (
    timeFormat: string,
    executeOnce: boolean,
    form: any,
    startTime: any,
    startDay: number,
    interval: number,
    isDay: boolean,
    startHour: number,
    hour: number,
    startMinute: number,
) => {
    if (executeOnce) {
        return dayjs(startTime).add(0.5, 'hour').format(timeFormat);
    }
    if (form.endType === 'endBy') {
        return dayjs(form.endDate).format(timeFormat);
    } else if (form.endType === 'after') {
        // 次数
        const times = form.afterOccurrences as number;
        if (isDay) {
            const tempEndDay = dayjs(new Date(startTime)).endOf('month').date();
            // 首月能执行的次数
            const firstMonthTimes = Math.floor((tempEndDay - startDay) / interval) + 1;

            if (firstMonthTimes >= times) {
                return dayjs(new Date(startTime))
                    .add((times - 1) * interval, 'day')
                    .endOf('day')
                    .format(timeFormat);
            } else {
                let executedTimes = firstMonthTimes;
                let monthStart = dayjs(new Date(startTime)).endOf('month').add(1, 'second');
                let monthEnd = dayjs(new Date(startTime))
                    .endOf('month')
                    .add(1, 'second')
                    .endOf('month');
                while (true) {
                    // 从第二个月开始，每个月能执行的次数；
                    const temp =
                        Math.floor(
                            (monthEnd.date() - (startDay % interval || interval)) / interval,
                        ) + 1;
                    // 剩余次数
                    const tempRest = times - executedTimes;
                    if (temp >= tempRest) {
                        // 本月结束
                        return monthStart
                            .add((startDay % interval) + (tempRest - 1) * interval, 'day')
                            .endOf('day')
                            .format(timeFormat);
                    } else {
                        executedTimes += temp;
                        monthStart = monthEnd.add(1, 'second');
                        monthEnd = monthStart.endOf('month');
                    }
                }
            }
        } else {
            // 开始的当天剩余时间 (0-23)
            const startDayRestHour = 23 - startHour;
            // 开始当天剩余时间 最大能 执行的次数
            const startDayTimes = 1 + Math.floor(startDayRestHour / interval);
            if (startDayTimes > times) {
                return dayjs(
                    // + 0.5 为了避免时间刚好，而少执行一次
                    new Date(startTime.getTime() + ((times - 1) * interval + 0.5) * hour),
                ).format(timeFormat);
            } else if (startDayTimes === times) {
                return dayjs(new Date(startTime)).endOf('day').format(timeFormat);
            } else {
                let result = dayjs(new Date(startTime)).endOf('day');
                const rest = times - startDayTimes;
                // 从第二天开始每天 最多能 执行的次数
                const perDayTimes = Math.ceil((24 - (startHour % interval)) / interval);
                // 能整除的天数
                const addDay = Math.floor(rest / perDayTimes);
                result = result.add(addDay, 'day');
                const lastDayTimes = rest % perDayTimes;
                if (lastDayTimes) {
                    result = result
                        .add((startHour % interval) + interval * (lastDayTimes - 1), 'hour')
                        .add(startMinute + 1, 'minute');
                }
                return result.format(timeFormat);
            }
        }
    } else {
        return null;
    }
};
export const getTriggerTime = (
    executeOnce: boolean,
    startMinute: number,
    startSecond: number,
    startHour: number,
    startDay: number,
    startMonth: number,
    isDay: boolean,
    interval: number,
) => {
    if (executeOnce) {
        // 只执行一次
        return `${startSecond} ${startMinute} ${startHour} ${startDay} ${startMonth} ?`;
    } else {
        if (isDay) {
            // 以天为间隔
            return `${startSecond} ${startMinute} ${startHour} ${
                startDay % interval || interval
            }/${interval} * ?`;
        } else {
            return `${startSecond} ${startMinute} ${startHour % interval}/${interval} * * ?`;
        }
    }
};

export interface AddScheduleForm {
    jobName: null | string;
    startDate: any;
    recurrenceBy: 'never' | 'day' | 'hour';
    dayInterval: number;
    hourInterval: number;
    endType: 'noEnd' | 'after' | 'endBy';
    afterOccurrences: null | number;
    endDate: any;
    emailNotification?: boolean;
    notificationRecipients?: string[];
    [key: string]: any;
}
