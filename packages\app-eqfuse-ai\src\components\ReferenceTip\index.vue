<script lang="ts" setup>
import { computed, defineProps } from 'vue';
import { InfoCircleOutlined } from '@ant-design/icons-vue';
import { imgPrefix } from '@/assets/const';

let props = defineProps({
  chunk: {
    type: String,
    default: () => ''
  }
});

let chunkObj = computed(() => {
  if (!props.chunk) return {};
  try {
    return JSON.parse(props.chunk);
  } catch (e) {
    console.error('解析chunk失败', e);
    return {};
  }
});
</script>

<template>
  <!-- 外层原始弹窗 -->
  <a-popover
    :overlayInnerStyle="{
      padding: 0,
      width: '500px',
      height: '100%'
    }"
    :getPopupContainer="(triggerNode: HTMLElement) => triggerNode.parentNode"
  >
    <template #content>
      <div class="reference-content">
        <!-- 图片容器增加悬浮效果和大图弹窗 -->
        <a-popover placement="right" :overlayInnerStyle="{ padding: '10px' }" trigger="hover">
          <template #content>
            <!-- 大图显示区域 -->
            <div class="large-image-container">
              <img
                :src="imgPrefix + chunkObj.image_id"
                alt="大图预览"
                :style="{ maxWidth: '800px', maxHeight: '600px' }"
              />
            </div>
          </template>

          <!-- 缩略图区域（带悬浮效果） -->
          <div class="image-container">
            <img
              :src="imgPrefix + chunkObj.image_id"
              width="100px"
              height="100px"
              class="thumbnail"
              :alt="chunkObj.image_id ? '缩略图' : '无图片'"
            />
          </div>
        </a-popover>

        <div class="text-container" v-html="chunkObj.content"></div>
      </div>
    </template>

    <InfoCircleOutlined :style="{ color: 'rgba(0, 101, 255, 1)' }" />
  </a-popover>
</template>

<style lang="less" scoped>
.reference-content {
  display: flex;
  .image-container {
    width: 100px;
    height: 100px;
    cursor: zoom-in;

    .thumbnail {
      width: 100px;
      height: 100px;
      object-fit: cover;
      // 悬浮效果：轻微放大+阴影
      transition: all 0.3s ease;

      &:hover {
        transform: scale(1.05);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }
    }
  }

  .text-container {
    margin-left: 10px;
  }
}

// 大图容器样式
.large-image-container {
  display: flex;
  justify-content: center;
  align-items: center;
  background: #f5f5f5;
  border-radius: 4px;
}
</style>
