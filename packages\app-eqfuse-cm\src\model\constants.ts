export class Constants {
    static EQUIPMENT = `EQUIPMENT`;
    static CHAMBER = `CHAMBER`;
    static PROCESS = `PROCESS`;
    static PRODUCT = `PRODUCT`;
    static RECIPE = `RECIPE`;
    static STEP = `STEP`;
    static RECIPE_STEP = `RECIPE_STEP`;
    static TRACETIME = `TRACETIME`;
    static Parameter = `Parameter`;
    static LOT = `LOT`;
    static WAFER = `WAFER`;
    static From = `From`;
    static To = `To`;
    static Count = `Count`;
    static TimeFrame = `Time Frame`;
    static TraceTimeFormat = `YYYY/MM/DD HH:mm:ss.SSS`;
    static ToolAlias = `Tool`;
    static EqpAlias = `EQP`;
    static ChamberAlias = `Chamber`;
    static RouteAlias = `Route`;
    static RecipeAlias = `Recipe`;
    static ProductAlias = `Product`;
}
