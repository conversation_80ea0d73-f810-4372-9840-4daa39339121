import {
    PROJECTNA<PERSON>,
    <PERSON>ARCH,
    ADD,
    DELETE,
    SAVE,
    REFRE<PERSON>H,
    IMPORT,
    EXPORT,
    setButtons,
} from './constant';

export const cmMainInfo = {
    pageId: `${PROJECTNAME}cm-main`,
    ...setButtons(
        [
            ADD,
            DELETE,
            SAVE,
            SEARCH,
            EXPORT,
            'modify',
            'run',
            'csv-import',
            'db-import',
            'clear',
            'cancel',
        ],
        'main',
    ),
};
export const cmConfig = {
    pageId: `${PROJECTNAME}cm-config`,
    parameterCategorization: {
        pageId: `${PROJECTNAME}parameter-categorization`,
        ...setButtons([ADD, DELETE, 'modify', SEARCH, SAVE], 'parameter-categorization'),
    },
    config: {
        pageId: `${PROJECTNAME}config-config`,
        ...setButtons(['modify'], 'config-config'),
    },
};

export const cmTrimingInfo = {
    pageId: `${PROJECTNAME}data-trimming-rule`,
    ...setButtons([SEARCH, ADD, DELETE, SAVE], 'data-trimming-rule'),
};
export const stationParameterInfo = {
    pageId: `${PROJECTNAME}station-parameter`,
    ...setButtons([SEARCH, ADD, DELETE, SAVE, IMPORT], 'export-current-page', 'station-parameter'),
};

export const hardwareConfigInfo = {
    pageId: `${PROJECTNAME}hardware-config`,
    ...setButtons([SEARCH, ADD, DELETE, 'copy', 'modify'], 'hardware-config'),
};
export const metrologyConfigInfo = {
    pageId: `${PROJECTNAME}metrology-management`,
    ...setButtons(
        [SEARCH, IMPORT, 'export-origin-file', DELETE, 'virtual-item', 'trend-analysis'],
        'metrology-management',
    ),
};

export const scheduleManageInfo = {
    pageId: `${PROJECTNAME}schedule-management`,
    ...setButtons([ADD, SEARCH, SAVE, DELETE, 'modify'], 'schedule-management'),
};

export const eqpDataSources = {
    pageId: `${PROJECTNAME}eqp-data-srouces`,
    ...setButtons([ADD, DELETE, 'modify', 'schedule', SEARCH, SAVE], 'eqp-data-srouces'),
};

export const traceDetailChartInfo = {
    pageId: `${PROJECTNAME}trace-detail-list`,
    ...setButtons(['drawChart', 'getParamList'], 'trace-detail-analysis'),
};

// export const traceDetailChartInfo = {
//     pageId: `eqFuse-wafer-chart`,
//     ...setButtons(['search', 'draw']),
// };

export const summaryInfo = {
    pageId: `fdc-summary`,
    ...setButtons([SEARCH, 'draw', 'selectPoint']),
};
