import { VXETable, type VxeGridProps, t } from '@futurefab/vxe-table';
import { tableDefaultConfig } from '@futurefab/ui-sdk-ees-basic';
export const xConfig = tableDefaultConfig(
  {
    // showHeader: false,
    // border: true,
    // stripe: false,
    id: 'stateKey',
    toolbarConfig: {
      tableName: 'stateViewer.label.stateKey',
      import: false,
      export: false,
      refresh: false,
      custom: false,
      zoom: false,
      tools: [...VXETable.tableFun.getToolsButton([])]
    },
    columns: [
      {
        field: 'key',
        minWidth: 190,
        title: 'stateViewer.field.key',
        align: 'left'
      },
      {
        field: 'value',
        minWidth: 190,
        title: 'stateViewer.field.value',
        align: 'left'
      }
    ]
  } as VxeGridProps,
  false
);
export const dConfig = tableDefaultConfig(
  {
    // showHeader: false,
    border: true,
    // borderOutline: [0, 0, 0, 0],
    // stripe: false,
    id: 'stateReset',
    toolbarConfig: {
      tableName: 'stateViewer.label.stateItems',
      import: false,
      export: false,
      refresh: false,
      custom: false,
      zoom: false
      // border:false,
    },
    columns: [
      {
        field: 'actualItem',
        minWidth: 190,
        title: 'stateViewer.field.item',
        align: 'left'
      },
      {
        field: 'itemValue',
        minWidth: 190,
        title: 'stateViewer.field.value',
        align: 'left',
        editRender: {
          name: '$input'
        }
      }
    ]
  } as VxeGridProps,
  false
);
