<script lang="ts" setup>
import { getAnalysisList, getTraceDetailTableHeader } from '@futurefab/ui-sdk-api';
import { onMounted, reactive, ref } from 'vue';
import dayjs from 'dayjs';
import { options, setRowClassName } from './config';
import { dealFilterMethod, dealFilterRender, dealFilters } from '@/utils';
import { setHeaderSelectFilter, EesMultiTreeSearchCondition } from '@futurefab/ui-sdk-ees-basic';

const props = withDefaults(
  defineProps<{
    pageId: string;
  }>(),
  {
    pageId: ''
  }
);
const emits = defineEmits(['search']);
const multiSearchRef = ref();
const xGrid = ref();
let startTime: number | null = null,
  endTime: number | null = null,
  controller: AbortController | undefined = undefined,
  substrateList = [] as Record<string, any>[];
const timePop = (val: number[]) => {
  startTime = val[0];
  endTime = val[1];
};
const params = reactive({
  showLoading: false,
  tableData: [] as Record<string, any>[],
  options
});
const substrateColumns: any[] = [
  {
    title: 'vxe.no',
    type: 'seq',
    width: '48px',
    align: 'center',
    fixed: 'left'
  },
  {
    field: 'rawId',
    type: 'checkbox',
    title: 'vxe.sel',
    fixed: 'left',
    align: 'center',
    width: '50px',
    headerClassName: 'sel'
  }
];
const getTableHeader = () => {
  getTraceDetailTableHeader({ bodyParams: {} }).then((res: any) => {
    if (res.status == 'SUCCESS') {
      res.data.forEach((ff: any) => {
        const item: any = {
          field: ff.columnName,
          title: ff.titleName,
          minWidth: ff.titleLength,
          sortable: true,
          filters: dealFilters(ff.filterType),
          filterRender: dealFilterRender(ff.filterType),
          filterMethod: dealFilterMethod(ff.filterType, ff.columnName),
          align: ff.dataType === 'Number' ? 'right' : 'left',
          headerAlign: 'center'
        };
        substrateColumns.push(item);
        params.options.columns.push(item);
      });
      xGrid.value && xGrid.value.reloadColumn(params.options.columns);
    }
  });
};
let beforeSearchData: any = null;
const handleSearch = (type: string, data: Record<string, any>) => {
  if (type == 'reset') {
    multiSearchRef.value &&
      multiSearchRef.value.setInitialValue({
        locationID: [],
        areaID: [],
        startDate: dayjs().startOf('day'),
        endDate: dayjs()
      });
  } else if (type == 'search') {
    beforeSearchData = { ...data, startDate: startTime, endDate: endTime };
    emits('search', beforeSearchData);
    requestList(data);
  }
};
const requestList = (data: any) => {
  params.showLoading = true;
  controller = new AbortController();
  const startDate = dayjs(startTime).format('YYYY/MM/DD HH:mm:ss');
  const endDate = dayjs(endTime).format('YYYY/MM/DD HH:mm:ss');
  getAnalysisList({
    headerParams: {
      log: 'Y',
      page: props.pageId,
      action: 'get-substrate-list'
    },
    bodyParams: {
      signal: controller.signal,
      startDate,
      endDate,
      eqpRawIds: data.eqpRawId,
      locationRawId: data.locationID,
      areaRawId: data.areaID,
      modelRawId: data.modelID,
      ...data.contextResult,
      showNullLotId: false
    }
  }).then((res: { status: string; data: { lot: any[]; substrate: any[]; type: string } }) => {
    if (res.status === 'SUCCESS') {
      if (res.data) {
        substrateList = res.data.substrate ? res.data.substrate : [];
        params.options.columns = substrateColumns;
        params.tableData = substrateList;
        handleHeaderSelectFilter();
      }
    }
    params.showLoading = false;
  });
};
const handleHeaderSelectFilter = () => {
  setTimeout(() => {
    xGrid.value &&
      setHeaderSelectFilter({
        xGrid: xGrid,
        tableData: params.tableData,
        columnFieldList: [
          'eqpAlias',
          'moduleAlias',
          'slot',
          'recipeId',
          'lotType',
          'productId',
          'statusCd'
        ]
      });
  }, 500);
};
const handleLoadingCancel = () => {
  if (controller) {
    controller.abort();
  }
  params.showLoading = false;
};
const events = {
  toolbarToolClick: ({ code }: { code: string }) => {
    if (code == 'refresh') {
      requestList(beforeSearchData);
    }
  }
};
onMounted(() => {
  getTableHeader();
});
defineExpose({
  getCheckRecords: () => xGrid.value?.getCheckboxRecords() || [],
  clearCheck: async () => xGrid.value?.clearCheckboxRow()
});
</script>
<template>
  <div class="substrate-list">
    <div class="substrate-list-search">
      <EesMultiTreeSearchCondition
        ref="multiSearchRef"
        :tree-checkable="false"
        style="flex-wrap: wrap"
        :show-location="true"
        :show-area="true"
        :is-show-condition="false"
        :show-recent="false"
        :should-select-eqp="true"
        :page-id="props.pageId"
        @time-pop="timePop"
        @event-btn="(handleSearch as any)"
      />
    </div>

    <div
      v-isLoading="{
        isShow: params.showLoading,
        title: $t('common.loading.text'),
        hasButton: true,
        buttonEvent: handleLoadingCancel
      }"
      class="substrate-list-table"
    >
      <vxe-grid
        ref="xGrid"
        v-bind="params.options"
        :data="params.tableData"
        class="vxe-table-bottom-left-radius-xs vxe-table-bottom-right-radius-xs"
        :row-class-name="setRowClassName"
        v-on="events"
      >
      </vxe-grid>
    </div>
  </div>
</template>
<style lang="less" scoped>
@import '@/assets/style/variable.less';
.substrate-list {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  overflow: auto;
  &-search {
    flex-shrink: 0;
    margin-bottom: 8px;
  }
  &-table {
    flex: 1;
    :deep(.vxe-body--row.error_row) {
      .vxe-cell {
        color: @error-color;
      }
    }
    :deep(.vxe-body--row.warning_row) {
      .vxe-cell {
        color: @warning-color;
      }
    }
  }
}
</style>
