export interface EqpDataTypeSourcesItem {
    eqpDataSourceId: number;
    eqpId: string;
    eqpDataTypeCd: string;
    protocolCd: string;
    ipAddr: string;
    portNo: string;
    userName: string;
    userPass: string;
    remotePath: string;
    localPath: string;
    schdJobRawId: number;
    schdJobInfo: any;
}

export type EqpDataTypeSourcesSelectOptions = Record<
    'dataType' | 'protocal',
    { label: string; value: string }[]
>;

export interface LinkScheduleAndEqpDataSource {
    eqpId: number;
    schdJobRawId: number;
}
