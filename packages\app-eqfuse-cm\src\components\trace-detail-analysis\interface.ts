export interface TabListType {
    value: string;
    label: string;
    count?: number;
}

export interface ChartParamType {
    eqpModuleId: string;
    lotId: string;
    startDate: string;
    endDate: string;
    substrateId: string;
    rawIds: number[];
    tagRawIds: number[];
}

export type SeriesType = {
    color: string;
    symbol: string;
} & ChartParamType;

export type ColorType = {
    color: string;
    eqpId?: string;
    moduleAlias?: string;
    recipeId?: string;
    lotId?: string;
    substrateId?: string;
    productId?: string;
    slot?: string;
};

export interface ChartDataType {
    type: number;
    layout: number;
    kind: string;
    isSplitParam: boolean;
    x: string[];
    y: string[];
    options?: Array<{ label: string | number; value: string | number }>;
}

export interface TabItem {
    index: number;
    tab: {
        name?: string;
        icon?: string;
        count?: string;
        id?: string;
    };
}
