<script lang="ts" setup>
import { RUN_BY } from '@/constant/charts';
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useCmstore } from '@futurefab/ui-sdk-stores';
import FdcDbContent from './fdc-db-content.vue';
import EqpDbContent from './eqp-db-content.vue';
import CsvFileContent from './csv-file-content.vue';
import ProcessFileContent from './process-file-content.vue';
import { datasetGroupStore } from '@/views/cm-home/cm-main/add-dataset/dataset-group-store';
import { showConfirm } from '@futurefab/ui-sdk-ees-basic';
import { t } from '@futurefab/vxe-table';
import { getDataSourceConfig } from '@futurefab/ui-sdk-api';

const cmStore = useCmstore();
const isLocal = computed(() => cmStore.smartMachineLocalMode);

const runByModel = defineModel('runByModel');
const commonGroupConfig = defineModel<any>('commonGroupConfig');
const groupLoading = defineModel('groupLoading');
const runType = defineModel('runType');
const fileNames = defineModel('fileNames');
const datasetSelect = defineModel('datasetSelect');
const datasetSelectKey = defineModel('datasetSelectKey');

const oldRunByModel = ref(runByModel.value);

const dataSourceOptions = ref<any[]>([
  { label: 'FDC DB', value: 'FDC' },
  { label: 'EQP DB', value: 'EQP' },
  { label: 'CSV File', value: 'CSV' },
  { label: 'TXT File', value: 'TSV' }
]);
const emits = defineEmits(['handleRunByModelChange', 'setWaferData', 'clearMetrologyCol']);
const handleRunByModelChange = (value: string) => {
  emits('handleRunByModelChange', value);
};
const fdcDbContentRef = ref<any>(null);
const eqpDbContentRef = ref<any>(null);
const csvFileContentRef = ref<any>(null);
const processFileContentRef = ref<any>(null);
// 触发弹窗提示条件
const hasGroupingData = () => {
  return datasetGroupStore.checkData.size > 0;
};
const updateDataList = async () => {
  // 更新data list key
  datasetSelectKey.value = datasetSelect.value;
  // 更新记录的runType
  oldRunByModel.value = runByModel.value;
  switch (datasetSelect.value) {
    case 'FDC':
      runType.value = 'DATABASE';
      await fdcDbContentRef.value?.getDatabaseWaferData();
      break;
    case 'EQP':
      runType.value = 'HIS';
      await eqpDbContentRef.value?.getHisFileWaferData();
      break;
    case 'CSV':
      runType.value = 'CSV';
      await csvFileContentRef.value.getCsvFileWaferData();
      break;
    case 'TSV':
      runType.value = 'TSV';
      await processFileContentRef.value.getProcessFileWaferData();
      break;
  }
};
const handleViewDataset = async () => {
  // 如果有分组信息，就提示
  // 如果切换了Run By，需要清空分组数据
  if (runByModel.value !== oldRunByModel.value && hasGroupingData()) {
    showConfirm({ msg: t('cm.tips.changeRunBy') }).then(async (type: string) => {
      if (type !== 'confirm') return;
      // 清空分组数据
      clearGroupData(true);
      updateDataList();
    });
  } else {
    updateDataList();
  }
};
const setWaferData = (data: any) => {
  emits('setWaferData', data);
};
const setFileNames = (data: any) => {
  fileNames.value = data;
};
// 保存data source到localStorage
const saveDataSource = (dataSource: string) => {
  localStorage.setItem('DATASET_SELECT', dataSource);
};
// 切换Data Source
const handleChangeDatasetSelect = (value: any) => {
  // 如果有分组信息，就提示
  if (hasGroupingData()) {
    showConfirm({ msg: t('cm.tips.changeDataSource') }).then((type: string) => {
      if (type !== 'confirm') return;
      datasetSelect.value = value;
      clearGroupData(true);
      saveDataSource(value);
    });
  } else {
    datasetSelect.value = value;
    saveDataSource(value);
  }
};
// FDC DB/fdcDb
// 重置dataset-from下拉框内容
const resetCanGetOptions = () => {
  commonGroupConfig.value.attributeConfigs.forEach((e: any, i: number) => {
    e.checkAll = false;
    e.loadedData = false;
    e.value = [];
    // 清除数据，禁用选择框
    e.loadedData = false;
    e.options = [];
    if (i > 0) {
      e.disabled = true;
    }
  });
};
// 仅清空分组数据和Data List
const clearGroupData = (noInit?: boolean) => {
  datasetGroupStore.handleClear();
  if (!noInit) datasetGroupStore.handleBackupsClear();
  setWaferData({ data: [] });
  emits('clearMetrologyCol');
};
// 初始化弹窗
const clearModalData = (noInit?: boolean) => {
  // 弹窗初始化
  clearGroupData(noInit);
  eqpDbContentRef.value?.clearFileCheckList();
  if (!noInit) runByModel.value = 'wafer';
  if (!noInit)
    datasetSelect.value = localStorage.getItem('DATASET_SELECT') || (isLocal.value ? 'EQP' : 'FDC');

  // 清除数据，禁用选择框
  resetCanGetOptions();
};
// 手动更新dataset-form
const handleRefresh = () => {
  resetCanGetOptions();
  clearModalData(true);
};

// 滚动布局相关逻辑
const middleWrapperRef = ref<HTMLElement | null>(null);
const middleContentRef = ref<HTMLElement | null>(null);
let scrollListener: (() => void) | null = null;
let resizeObserver: ResizeObserver | null = null;

// 更新阴影逻辑
const updateShadows = () => {
  const content = middleContentRef.value;
  const wrapper = middleWrapperRef.value;
  if (!content || !wrapper) return;

  const { scrollTop, scrollHeight, clientHeight } = content;

  // 判断是否真的可滚动
  const canScroll = scrollHeight > clientHeight;

  // 根据滚动位置决定显示哪个阴影
  const hasShadowTop = canScroll && scrollTop > 0;
  const hasShadowBottom = canScroll && scrollTop + clientHeight < scrollHeight;

  wrapper.classList.toggle('has-shadow-top', hasShadowTop);
  wrapper.classList.toggle('has-shadow-bottom', hasShadowBottom);
};

const getDataSetSource = async () => {
  const res = await getDataSourceConfig({
    bodyParams: {}
  });
  if (res.status === 'SUCCESS') {
    dataSourceOptions.value = res.data?.filter((item: any) => !item.disabled);
  }
};
// 获取数据源label
const getDataSource = () => {
  return dataSourceOptions.value.find((item: any) => item.value === datasetSelect.value);
};

onMounted(() => {
  // 获取数据源配置
  getDataSetSource();
  // 滚动内容监听
  const content = middleContentRef.value;
  if (!content) return;
  content.addEventListener('scroll', updateShadows);
  scrollListener = updateShadows;
  resizeObserver = new ResizeObserver(() => {
    requestAnimationFrame(updateShadows);
  });
  resizeObserver.observe(content);
  window.addEventListener('resize', updateShadows);
  updateShadows();
});

onUnmounted(() => {
  const content = middleContentRef.value;
  if (content && scrollListener) {
    content.removeEventListener('scroll', scrollListener);
  }
  if (resizeObserver) {
    resizeObserver.disconnect();
  }
  window.removeEventListener('resize', updateShadows);
});

defineExpose({ clearModalData, getDataSource });
</script>

<template>
  <div class="dataset-modal-content-left-top">
    <!-- 数据源选项 -->
    <div class="dataset-modal-content-left-top-dataset-source-select">
      <span class="label">{{ $t('cm.label.dataSource') }}</span>
      <a-select
        class="select"
        :value="datasetSelect"
        :options="dataSourceOptions"
        @change="handleChangeDatasetSelect"
      />
    </div>
    <!-- 不同dataset选项内容 -->
    <div class="dataset-modal-content-left-top-dataset-content" ref="middleWrapperRef">
      <div class="middle-content" ref="middleContentRef">
        <!-- FDC DB/FDC -->
        <div v-if="datasetSelect === 'FDC'">
          <FdcDbContent
            ref="fdcDbContentRef"
            v-model:runByModel="runByModel"
            v-model:groupLoading="groupLoading"
            :common-group-config="commonGroupConfig"
            @handle-refresh="handleRefresh"
            @reset-can-get-options="resetCanGetOptions"
            @setWaferData="setWaferData"
          />
        </div>
        <!-- EQP DB/EQP -->
        <div v-if="datasetSelect === 'EQP'">
          <EqpDbContent
            ref="eqpDbContentRef"
            :selectType="'checkboxGroup'"
            v-model:runByModel="runByModel"
            v-model:groupLoading="groupLoading"
            @setWaferData="setWaferData"
          />
        </div>
        <!-- CSV File/CSV -->
        <div v-if="datasetSelect === 'CSV'" class="csv-file-content-box">
          <CsvFileContent
            ref="csvFileContentRef"
            v-model:runByModel="runByModel"
            v-model:groupLoading="groupLoading"
            @setWaferData="setWaferData"
            @setFileNames="setFileNames"
          />
        </div>
        <!-- Process File/TSV -->
        <div v-if="datasetSelect === 'TSV'" class="process-file-content-box">
          <ProcessFileContent
            ref="processFileContentRef"
            v-model:runByModel="runByModel"
            v-model:groupLoading="groupLoading"
            @setWaferData="setWaferData"
            @setFileNames="setFileNames"
          />
        </div>
      </div>
      <!-- 固定阴影 -->
      <div class="shadow-top"></div>
      <div class="shadow-bottom"></div>
    </div>
    <!-- RUN BY -->
    <div class="dataset-modal-content-left-top-run-by-select">
      <div class="select-box">
        <span class="label">{{ $t('cm.title.runBy') }}</span>
        <a-select
          v-model:value="runByModel"
          class="run-by-select"
          :options="RUN_BY"
          :show-arrow="true"
          @change="handleRunByModelChange"
        />
      </div>
      <a-button class="run-by-button" @click="handleViewDataset">
        {{ $t('common.btn.view') }}
      </a-button>
    </div>
  </div>
</template>

<style lang="less" scoped>
@import url('@/assets/style/variable.less');
.dataset-modal-content-left-top {
  height: 100%;
  padding: 14px 14px 4px 14px;
  border-bottom: 1px solid @border-color;
  overflow: scroll;
  display: flex;
  flex-direction: column;
  // 显示滚动条
  // &:hover {
  //   &::-webkit-scrollbar-thumb {
  //     background-color: @bg-scroll-color;
  //   }
  // }
  // &::-webkit-scrollbar-corner {
  //   background-color: @bg-scroll-color;
  // }
  &-dataset-source-select {
    display: flex;
    flex-direction: row;
    align-items: center;
    flex: 0 0 auto;
    margin-bottom: 10px;
    .label {
      min-width: 106px;
      width: max-content;
      color: @text-title-color;
      font-weight: bold;
    }
    .select {
      display: flex;
      flex: 1;
    }
  }
  &-dataset-content {
    max-height: calc(100% - 100px);
    min-height: 0;
    position: relative;
    overflow: hidden;

    .middle-content {
      height: 100%;
      width: 100%;
      overflow-y: scroll;

      .csv-file-content-box {
        display: flex;
        flex-direction: column;
        height: 100%;
      }
      .process-file-content-box {
        display: flex;
        flex-direction: column;
        height: 100%;
      }
    }

    .shadow-top {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 8px;
      background: linear-gradient(to bottom, @eq-scroll-top-shadow, transparent);
      pointer-events: none;
      z-index: 2;
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    .shadow-bottom {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 8px;
      background: linear-gradient(to top, @eq-scroll-bottom-shadow, transparent);
      pointer-events: none;
      z-index: 2;
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &.has-shadow-top .shadow-top {
      opacity: 1;
    }
    &.has-shadow-bottom .shadow-bottom {
      opacity: 1;
    }
  }
  &-run-by-select {
    display: flex;
    flex-direction: column;
    justify-content: space-evenly;
    align-items: end;
    flex: 0 0 auto;
    .select-box {
      display: flex;
      align-items: center;
      width: 100%;
      .label {
        font-weight: bold;
        color: @text-title-color;
        min-width: 106px;
      }
      .run-by-select {
        display: flex;
        flex: 1;
      }
    }
    .run-by-button {
      color: @color-default;
      background: @primary-color;
      margin-top: 10px;
      width: 60px;
    }
  }
}
</style>
