// traceChart图表展示数据的类型和对应的x,y轴分组
export const TRACE_CHART_X_AXIS_TYPE = [
  {
    label: 'Overlaid by Recipe Step and Sequence',
    value: 'step_count_slot',
    XYGroup: ['count_slot', 'VALUE']
  },
  { label: 'Overlaid by Sequence', value: 'wafer_count_slot', XYGroup: ['ROW_INDEX', 'VALUE'] },
  {
    label: 'Overlaid by Recipe Step and Time',
    value: 'step_time_slot',
    XYGroup: ['time_slot', 'VALUE']
  },
  {
    label: 'Overlaid by Process Time',
    value: 'wafer_time_slot',
    XYGroup: ['TIME_SLOT', 'VALUE']
  },
  { label: 'Date and Time', value: 'TIME', XYGroup: ['TIME', 'VALUE'] }
];
// chart图颜色
export const SERIES_COLORS = [
  'rgba(0, 127, 172)',
  'rgba(255,133,27,0.4)',
  'rgba(57,204,204,0.4)',
  'rgba(0,255,0)',
  'rgba(255,220,0,0.4)',
  'rgba(2,173,36)',
  'rgba(246,125,123)',
  'rgba(147,96,8)',
  'rgba(255,20,147,1)',
  'rgba(0,83,0)',
  'rgba(151,166,79)',
  'rgba(172,225,175,1)',
  'rgba(0,128,128,1)',
  'rgba(125,104,113)',
  'rgba(162,162,208,1)'
];
export const CHART_DEFAULT_COLORS = [
  '#00ff00',
  '#000080',
  '#cccc00',
  '#800080',
  '#ffa500',
  '#00ffff',
  '#dc143c',
  '#191970',
  '#4682b4',
  '#9400d3',
  '#f0e68c',
  '#1e90ff',
  '#00ff7f',
  '#ff1493',
  '#708090',
  '#fffacd',
  '#ee82ee',
  '#ffc0cb',
  '#48d1cc',
  '#adff2f',
  '#f08080',
  '#808080',
  '#ff69b4',
  '#cd5c5c',
  '#ffa07a',
  '#0000ff'
];
export const CHART_DEFAULT_NEGATIVE_COLORS = [
  '#ff00ff',
  '#ffff7f',
  '#3434ff',
  '#7fff7f',
  '#005aff',
  '#ff0000',
  '#23ebc3',
  '#e6e68f',
  '#b97d4b',
  '#6bff2c',
  '#0f1973',
  '#e16f00',
  '#ff0080',
  '#00eb6c',
  '#8f7f6f',
  '#000532',
  '#117d11',
  '#003f34',
  '#b72e33',
  '#5200d0',
  '#0f7f7f',
  '#7f7f7f',
  '#00964b',
  '#32a3a3',
  '#005f85',
  '#ffff00'
];
// Summary Chart算法
export const SUMMARY_CHART_STATE_TYPES = [
  'MIN',
  'MAX',
  'RANGE',
  'MEAN',
  'MEDIAN',
  'STDEV',
  'INDEX_MIN',
  'INDEX_MAX',
  'UPTREND',
  'DOWNTREND',
  'SLOPE',
  'POSITIVE_SUM',
  'NEGATIVE_SUM',
  'POSITIVE_MAX',
  'NEGATIVE_MAX',
  'POSITIVE_MAXSTART',
  'POSITIVE_MAXEND',
  'NEGATIVE_MAXSTART',
  'NEGATIVE_MAXEND',
  'L1_NORM',
  'COUNT',
  'DURATION',
  'DRIFT'
];
// Chart Constants
export const CHART_CONSTANT = {
  CHAMBER: `CHAMBER`,
  PROCESS: `PROCESS`,
  STEP: `STEP`,
  EQUIPMENT: `EQUIPMENT`,
  RECIPE_STEP: `RECIPE_STEP`,
  TRACE_TIME: `TRACE_TIME`,
  Parameter: `Parameter`,
  LOT: `LOT`,
  WAFER: `WAFER`,
  From: `From`,
  To: `To`,
  Count: `Count`,
  TimeFrame: `Time Frame`,
  TraceTimeFormat: `YYYY/MM/DD HH:mm:ss.SSS`,
  XAisTimeFormat: `MM/DD/YYYY HH:mm:ss.SSS`,
  TIME_SLOT_FORMAT: `HH:mm:ss.SS`,
  ProcessDuration: `HH:mm:ss.SSS`
};
// Data Source
export const WEB_DATA_SOURCES = [{ label: 'FDC DB', value: 'fdcDb' }];
export const DATA_SOURCES = [
  { label: 'FDC DB', value: 'fdcDb' },
  { label: 'EQP DB', value: 'eqpDb' }
];
// Run By
export const RUN_BY = [
  { label: 'Wafer', value: 'wafer' },
  { label: 'Loop', value: 'loop' },
  { label: 'Station', value: 'station' }
];
// Data Group
export const DATA_GROUP = [
  { label: 'A', value: 'A' },
  { label: 'B', value: 'B' },
  { label: 'C', value: 'C' },
  { label: 'D', value: 'D' },
  { label: 'E', value: 'E' },
  { label: 'F', value: 'F' },
  { label: 'G', value: 'G' },
  { label: 'H', value: 'H' },
  { label: 'I', value: 'I' },
  { label: 'J', value: 'J' },
  { label: 'K', value: 'K' },
  { label: 'L', value: 'L' },
  { label: 'M', value: 'M' },
  { label: 'N', value: 'N' },
  { label: 'O', value: 'O' },
  { label: 'P', value: 'P' },
  { label: 'Q', value: 'Q' },
  { label: 'R', value: 'R' },
  { label: 'S', value: 'S' },
  { label: 'T', value: 'T' },
  { label: 'U', value: 'U' },
  { label: 'V', value: 'V' },
  { label: 'W', value: 'W' },
  { label: 'X', value: 'X' },
  { label: 'Y', value: 'Y' },
  { label: 'Z', value: 'Z' }
];

export const CM_CHART_DEFUALT_FONT_SIZE = 12;
