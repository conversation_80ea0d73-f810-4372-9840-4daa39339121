<script setup lang="ts">
import { watch, ref } from 'vue';
import { type Measuredata } from '@/utils/spline-interpolate';
import Chart from '../chart.vue';
import type { NewMetrologyTableItem } from '@/views/metrology-management/interface';
import { getToolbox } from './index';
const props = defineProps<{
    data: NewMetrologyTableItem[];
    valueKeys: string[];
    summaryFuns: string[];
    summary: any[];
}>();
const options = ref<any>(null);

// const summaryFun = {
//     max: (values: number[]) => Math.max(...values),
//     min: (values: number[]) => Math.min(...values),
//     mean: (values: number[]) => mean(...values),
//     variance: (values: number[]) => variance(...values),
//     std: (values: number[]) => std(...values),
// };

const getMultiTrend = (
    data: Measuredata[],
    valueKeys: string[],
    summaryFuns: string[],
    summary: Measuredata[],
) => {
    const series: any[] = [];

    valueKeys.forEach(key => {
        summaryFuns.forEach(fun => {
            const temp: any = {
                name: `${fun}-${key}`,
                type: 'line',
                data: [],
            };
            summary.forEach((summaryItem, sumIndex) => {
                const vIndex = summaryItem.header.findIndex(item => item === key);
                const rIndex = summaryItem.data.findIndex(item => item[0] === fun);
                temp.data.push({value: summaryItem.data[rIndex][vIndex], fun, key, index: sumIndex});
            });
            series.push(temp);
        });
    });

    return {
        grid: {
            left: 80,
            top: 30,
            right: 20,
            bottom: 30,
        },
        xAxis: {
            show: true,
            type: 'category',
            data: new Array(data.length).fill(null).map((item, index) => index + 1),
            splitArea: {
                show: true,
            },
        },
        legend: {
            show: true,
            type: 'scroll',
        },
        yAxis: {
            show: true,
            scale: true,
            splitArea: {
                show: false,
            },
        },
        toolbox: getToolbox(),
        tooltip: {
            formatter: (event: any) => {
                if (event?.data.key) {
                    const info = event.data;
                    const measureData = props.data[info.index];
                    return [
                        `Value: ${info.value}`,
                        `Parameter: ${info.key}`,
                        `Func: ${info.fun}`,
                        `Lot: ${measureData.lotId}`,
                        `Measure Time: ${measureData.measureTime}`,
                    ].join('<br/>');
                }
            },
            appendToBody: true, 
        },
        series,
    };
};

watch(
    [() => props.data, () => props.valueKeys, () => props.summaryFuns],
    v => {
        if (props.data && props.valueKeys && props.summaryFuns) {
            options.value = getMultiTrend(
                props.data.map(item => item.rowDatas),
                props.valueKeys,
                props.summaryFuns,
                props.summary,
            );
        }
    },
    { immediate: true },
);
</script>
<template>
    <div class="common-chart-container">
        <Chart :options="options" />
    </div>
</template>
<style scoped lang="less">
.common-chart-container {
    width: 100%;
    height: 100%;
}
</style>
