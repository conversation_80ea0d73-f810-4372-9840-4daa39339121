<script setup lang="ts">
import { onActivated, onMounted, reactive } from 'vue';
import { VxeGridProps, VXETable } from '@futurefab/vxe-table';
import {
  attributesConfigsUpdateService,
  getAttributesConfigs,
  getConfigByCodeService,
  getLicenseStatusService,
  putConfigService
} from '@futurefab/ui-sdk-api';
import { Splitpanes, Pane } from 'splitpanes';
import { cmConfig as buttonIds } from '@/log-config';
import { message } from '@futurefab/ant-design-vue';

let tableRecords: any = [];
const gridOptions = reactive<typeof VxeGridProps>(
  VXETable.tableFun.tableDefaultConfig({
    toolbarConfig: {
      import: false,
      export: false,
      refresh: false,
      custom: false
    },
    columns: [
      { field: 'alias', title: 'Attribute', sortable: true },
      {
        field: 'multiSelection',
        title: 'Multi Selection',
        slots: { default: 'multiSelection' }
      }
    ],
    border: true
  })
);
let d = reactive<any>({
  expiration: '2220-10-31',
  availableTools: 'Unlimited Tools',
  CMConfigs: {},
  // useSamplingForTraceChart: false,
  license: {
    expirationDate: null,
    availableTools: null,
    isUnlimitedTime: false,
    isStatusUnknown: true,
    isUnlimitedTools: false
  },
  tableRecords
});
// const configSet =;
onActivated(() => {
  console.log('激活configurantion');
});
onMounted(() => {
  getLicenseStatusService({
    bodyParams: {},
    headerParams: {
      log: 'Y',
      page: buttonIds.pageId,
      action: 'get-license-status'
    }
  }).then((res: any) => {
    d.license = res.data;
  });
  getConfigByCodeService({
    bodyParams: { code: 'CMConfig' },
    headerParams: {
      log: 'Y',
      page: buttonIds.pageId,
      action: 'get-config-by-code'
    }
  }).then((res: any) => {
    console.log('CMConfig:', res);
    d.CMConfigs = res.data;
    Object.keys(d.CMConfigs).forEach((k) => {
      d.CMConfigs[k].oldValue = d.CMConfigs[k].val01;
    });
    // d.useSamplingForTraceChart = res.data.val01 == 1;
    // console.log('d.useSamplingForTraceChart:', d.useSamplingForTraceChart);
  });
  getTableRecords();
});
let getTableRecords = () => {
  getAttributesConfigs({
    bodyParams: {},
    headerParams: {
      log: 'Y',
      page: buttonIds.pageId,
      action: buttonIds.parameterCategorization.search
    }
  }).then((res: { data: any }) => {
    console.log('res:', res);
    d.tableRecords = res.data;
  });
};
const putConfig = (c: any) => {
  if (c.oldValue != c.val01) {
    let cfg: any = { code: c.code, rawId: c.rawId, val01: c.val01 };
    putConfigService({
      bodyParams: cfg,
      showResultMsg: true,
      headerParams: {
        log: 'Y',
        page: buttonIds.pageId,
        action: 'put-config'
      }
    }).then((res: any) => {
      if ('SUCCESS' == res.status) {
        c.oldValue = c.val01;
      }
    });
  }
};
</script>
<template>
  <div class="cm-configuration b-radius">
    <splitpanes class="default-theme" horizontal>
      <Pane size="50">
        <splitpanes>
          <pane>
            <div class="right-card-margin left-right-card">
              <a-card size="small" style="height: 100%">
                <template #title>
                  <div class="card-title-text">Performance Configurations</div>
                </template>
                <a-space direction="vertical" style="gap: 16px !important">
                  <div v-for="(c, i) in d.CMConfigs" :key="i">
                    <a-space>
                      <div class="cm-configuration-config-label">
                        {{ c.description }}
                      </div>
                      <a-input-number
                        v-model:value="c.val01"
                        :min="0"
                        style="width: 150px"
                        @blur="putConfig(c)"
                      />
                      <span class="cm-configuration-config-unit">{{ c.val02 }}</span>
                    </a-space>
                  </div>
                </a-space>
              </a-card>
            </div>
          </pane>
        </splitpanes>
      </Pane>
      <pane>
        <a-card size="small" class="bottom-card-margin cm-configuration-table">
          <template #title>
            <div class="card-title-text">Attribute Configurations</div>
          </template>
          <vxe-grid :data="d.tableRecords" v-bind="gridOptions" class="table-b-radius">
            <template #multiSelection="{ row }">
              <a-switch
                v-model:checked="row.multiSelection"
                :disabled="row.isDescriptor"
                @change="
                  attributesConfigsUpdateService({
                    bodyParams: row,
                    showResultMsg: true,
                    headerParams: {
                      log: 'Y',
                      page: buttonIds.pageId,
                      action: 'attributes-configs-update'
                    }
                  }).then((res: any) => {
                    if ('SUCCESS' == res.status) {
                      message.success($t('common.tip.operationSuccess'));
                    } else {
                      message.error(res.msg);
                    }
                  })
                "
              ></a-switch>
            </template>
          </vxe-grid>
        </a-card>
      </pane>
    </splitpanes>
  </div>
</template>
<style lang="less" scoped>
@import url('@/assets/style/variable.less');
.cm-configuration {
  height: 100%;
  width: 100%;
  overflow: hidden;
  :deep(.splitpanes, ),
  :deep(.splitpanes__pane) {
    // align-items: baseline !important;
    background-color: transparent !important;
  }
  :deep(.ant-card-head) {
    height: 42px;
    padding-left: 14px;
  }
  :deep(.ant-card-body) {
    padding-left: 14px;
  }
  .cm-configuration-table {
    border-radius: 0 0 16px 16px;
    border: 0px;
    overflow: hidden;
    :deep(.ant-card-head) {
      border-radius: 0;
      border: 1px solid @border-color;
    }
    :deep(.ant-card-body) {
      padding: 0;
      height: calc(100% - 41px);
    }
  }

  .left-right-card {
    flex: 1;
    height: 100%;
  }

  .left-card-margin {
    padding: 0 4px 0 0;
  }

  .card-title-text {
    font-size: 16px;
    font-weight: bold;
    color: @text-title-color;
  }

  .bottom-card-margin {
    width: 100%;
    top: 0;
    height: 100%;
  }
  .cm-configuration-config-label {
    width: 317px;
    font-size: 14px;
    font-weight: bold;
    color: @text-sub-color;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .cm-configuration-config-unit {
    font-size: 14px;
    font-weight: normal;
    color: @text-sub-color;
  }
  .left-right-card {
    :deep(.ant-card) {
      border-radius: 0;
      border: 1px solid @border-color;
      .ant-card-head {
        border-radius: 0;
        border-bottom: 1px solid @border-color;
      }
      .ant-card-body {
        border-radius: 0;
      }
    }
  }
}
</style>
