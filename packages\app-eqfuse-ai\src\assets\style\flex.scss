.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-between {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.flex-around {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
}

.flex-start {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
}

.flex-end {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
}

.flex-column-start {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.flex-column-end {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.flex-item {
  flex: 1;
}

.flex-item-fixed {
  flex: 0 0 auto;
}

.flex-item-auto {
  flex: 1 1 auto;
}

.flex-spacer {
  flex: 1 1 0; // 占用剩余所有空间
}
