<script setup>
import { ref, onMounted, onUnmounted, shallowRef, computed, nextTick, watch } from 'vue';
import * as pdfjsLib from 'pdfjs-dist/build/pdf';
import { LoadingOutlined } from '@ant-design/icons-vue';
import { h } from 'vue';
const indicator = h(LoadingOutlined, {
  style: {
    fontSize: '48px'
  },
  spin: true
});

let loading = ref(false);
const getWorkerSrc = () => {
  if (import.meta.env.PROD) {
    return '/assets/js/pdf.worker.min.js';
  }
  return new URL('pdfjs-dist/build/pdf.worker.min.js', import.meta.url).href;
};

pdfjsLib.GlobalWorkerOptions.workerSrc = getWorkerSrc();
import { CloseOutlined } from '@ant-design/icons-vue';
import { defineEmits } from 'vue';
import { message } from '@futurefab/ant-design-vue';

const props = defineProps({
  src: {
    type: String,
    required: true
  },
  pageGap: {
    type: Number,
    default: 20
  }
});

const container = ref(null);
const pagesContainer = ref(null);
const pageRefs = ref({});
const thumbnailRefs = ref({});

const pdfDoc = shallowRef(null);
const totalPages = ref(0);
const scale = ref(1.0);
const isFullscreen = ref(false);
const scrollPosition = ref(0);
const pagePositions = ref([]);
const pageHeights = ref([]);
const containerHeight = ref(0);
let isThumbnailsShow = ref(false); // 默认关闭缩略图
const handleThumbnailsShow = () => {
  isThumbnailsShow.value = !isThumbnailsShow.value;
  // 切换缩略图显示状态后重新计算页面位置
  nextTick(() => {
    updatePagePositions();
  });
};

// 计算当前可见页
const currentVisiblePage = computed(() => {
  if (!pagePositions.value.length || !containerHeight.value || !pagesContainer.value) return 1;

  // 获取可视区域顶部和底部位置
  const viewportTop = scrollPosition.value;
  const viewportBottom = scrollPosition.value + containerHeight.value;

  // 找出在可视区域内占比最大的页面
  let maxVisibleRatio = 0;
  let currentPage = 1;

  for (let i = 0; i < pagePositions.value.length; i++) {
    const pageNum = i + 1; // 页面编号(1-based)
    const pageTop = pagePositions.value[i];
    const pageBottom = pageTop + (pageRefs.value[pageNum]?.height || 0);
    const pageHeight = pageBottom - pageTop;

    // 计算页面与视口重叠部分
    const overlapStart = Math.max(viewportTop, pageTop);
    const overlapEnd = Math.min(viewportBottom, pageBottom);
    const overlapHeight = Math.max(0, overlapEnd - overlapStart);

    // 计算页面在视口中的可见比例
    const visibleRatio = pageHeight > 0 ? overlapHeight / pageHeight : 0;

    // 如果当前页面可见比例更大，则更新当前页
    if (visibleRatio > maxVisibleRatio) {
      maxVisibleRatio = visibleRatio;
      currentPage = pageNum;
    }
  }

  return currentPage;
});

// 初始化 PDF
const initPDF = async () => {
  try {
    loading.value = true;
    const loadingTask = pdfjsLib.getDocument({
      url: props.src,
      cMapUrl: '/cmaps/',
      cMapPacked: true
    });

    pdfDoc.value = await loadingTask.promise;
    totalPages.value = pdfDoc.value.numPages;
    loading.value = false;
    await renderAllPages();
    await renderThumbnails();

    // 初始化页面高度数组 - 确保无论缩略图是否显示都执行
    updatePagePositions();
  } catch (error) {
    loading.value = false;
    message.error('PDF加载失败');
    console.error('PDF加载失败:', error);
  }
};

// 更新所有页面位置信息
const updatePagePositions = async () => {
  await nextTick(); // 等待DOM更新

  if (!pagesContainer.value) return;

  const newPositions = [];
  let accumulatedHeight = 0;

  // 计算每页的绝对位置（相对于容器顶部）
  for (let i = 1; i <= totalPages.value; i++) {
    const canvas = pageRefs.value[i];
    if (canvas) {
      newPositions.push(accumulatedHeight);
      accumulatedHeight += canvas.height + props.pageGap;
    }
  }

  pagePositions.value = newPositions;
  containerHeight.value = pagesContainer.value.clientHeight;

  // 强制更新滚动位置以触发重新计算
  if (pagesContainer.value) {
    scrollPosition.value = pagesContainer.value.scrollTop;
  }
};

// 渲染所有页面
const renderAllPages = async () => {
  if (!pdfDoc.value) return;

  for (let i = 1; i <= totalPages.value; i++) {
    await renderPage(i);
  }
  // 确保渲染完成后更新页面位置
  updatePagePositions();
};

// 渲染单个页面
const renderPage = async (pageNum) => {
  const page = await pdfDoc.value.getPage(pageNum);
  const viewport = page.getViewport({ scale: scale.value });

  const canvas = pageRefs.value[pageNum];
  if (!canvas) return;

  const context = canvas.getContext('2d', { willReadFrequently: true });
  canvas.width = viewport.width;
  canvas.height = viewport.height;

  await page.render({
    canvasContext: context,
    viewport: viewport
  }).promise;
};

// 渲染缩略图
const renderThumbnails = async () => {
  if (!pdfDoc.value) return;

  for (let i = 1; i <= totalPages.value; i++) {
    const page = await pdfDoc.value.getPage(i);
    const thumbnailViewport = page.getViewport({ scale: 0.15 });

    const canvas = thumbnailRefs.value[i];
    if (!canvas) continue;

    const context = canvas.getContext('2d', { willReadFrequently: true });
    canvas.width = 78;
    canvas.height = 110;

    await page.render({
      canvasContext: context,
      viewport: thumbnailViewport
    }).promise;
  }
};

// 设置页面引用
const setPageRef = (el, pageNum) => {
  if (el) {
    pageRefs.value[pageNum] = el;
    // 当页面元素加载完成后更新位置信息
    nextTick(() => {
      updatePagePositions();
    });
  }
};

// 设置缩略图引用
const setThumbnailRef = (el, pageNum) => {
  if (el) {
    thumbnailRefs.value[pageNum] = el;
  }
};

// 滚动到指定页
const scrollToPage = (pageNum) => {
  if (pageNum < 1 || pageNum > totalPages.value) return;

  // 如果位置信息还未准备好，等待并重试
  if (!pagePositions.value.length) {
    const timer = setTimeout(() => {
      scrollToPage(pageNum);
      clearTimeout(timer);
    }, 100);
    return;
  }

  const targetPosition = pagePositions.value[pageNum - 1] - 20;
  pagesContainer.value.scrollTo({
    top: targetPosition,
    behavior: 'smooth'
  });
};

// 处理滚动事件
const handleScroll = () => {
  if (!pagesContainer.value) return;
  scrollPosition.value = pagesContainer.value.scrollTop;
};

// 缩放控制
const zoomIn = async () => {
  if (scale.value < 3) {
    const currentPageBeforeZoom = currentVisiblePage.value;
    scale.value += 0.25;
    await renderAllPages();
    scrollToPage(currentPageBeforeZoom);
  } else {
    message.warning('已到最大缩放比例');
  }
};

const zoomOut = async () => {
  if (scale.value > 0.5) {
    const currentPageBeforeZoom = currentVisiblePage.value;
    scale.value -= 0.25;
    await renderAllPages();
    scrollToPage(currentPageBeforeZoom);
  } else {
    message.warning('已到最小缩放比例');
  }
};

// 全屏切换
const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    container.value.requestFullscreen().catch((err) => {
      console.error('全屏失败:', err);
    });
    isFullscreen.value = true;
  } else {
    document.exitFullscreen();
    isFullscreen.value = false;
  }
};

// 更新容器高度
const updateContainerHeight = () => {
  if (pagesContainer.value) {
    containerHeight.value = pagesContainer.value.clientHeight;
    // 容器高度变化时重新计算页面位置
    updatePagePositions();
  }
};

// 监听页面渲染完成和尺寸变化
watch([totalPages, scale], () => {
  nextTick(() => {
    updatePagePositions();
  });
});

onMounted(() => {
  initPDF();
  window.addEventListener('resize', updateContainerHeight);
  if (pagesContainer.value) {
    pagesContainer.value.addEventListener('scroll', handleScroll);
  }
});

onUnmounted(() => {
  window.removeEventListener('resize', updateContainerHeight);
  if (pagesContainer.value) {
    pagesContainer.value.removeEventListener('scroll', handleScroll);
  }
  if (pdfDoc.value) {
    pdfDoc.value.destroy();
  }
});

const emit = defineEmits(['close']);
const close = () => {
  emit('close');
};
</script>
<template>
  <div class="pdf-viewer-container" ref="container">
    <!-- 顶部工具栏 -->
    <div class="pdf-toolbar flex-center">
      <a-tooltip v-if="!isThumbnailsShow" placement="top">
        <template #title>展开目录</template>
        <div class="toolbar-item">
          <span class="iconfont icon-expand-config" @click="handleThumbnailsShow()"></span>
        </div>
      </a-tooltip>

      <a-tooltip v-if="isThumbnailsShow" placement="top">
        <template #title>收起目录</template>
        <div class="toolbar-item">
          <span
            class="iconfont icon-fold-config"
            style="cursor: pointer"
            @click="handleThumbnailsShow()"
          ></span>
        </div>
      </a-tooltip>
      <div class="page-info">{{ currentVisiblePage }} / {{ totalPages }}</div>
      <a-tooltip placement="top">
        <template #title>{{ isFullscreen ? '取消全屏' : '全屏展示文档' }}</template>
        <div class="toolbar-item full-screen">
          <span
            class="iconfont"
            :class="isFullscreen ? 'icon-btn-screen-full' : 'icon-btn-screen-reduction'"
            @click="toggleFullscreen"
          ></span>
        </div>
      </a-tooltip>

      <a-tooltip placement="top">
        <template #title>放大文档 </template>
        <div class="toolbar-item"><SvgIcon @click="zoomIn" name="zoom-in" /></div>
      </a-tooltip>

      <a-tooltip placement="top">
        <template #title>缩小文档 </template>
        <div class="toolbar-item zoom-out">
          <SvgIcon @click="zoomOut" name="zoom-out" />
        </div>
      </a-tooltip>
      <close-outlined class="close-outlined" @click="close" />
    </div>

    <div class="pdf-content">
      <a-spin
        v-if="loading"
        :indicator="indicator"
        tip="Loading..."
        class="spin-container flex-column flex-center"
      />
      <!-- 左侧缩略图 -->
      <div
        v-show="!loading"
        class="pdf-thumbnails"
        :class="{ 'pdf-thumbnails-visible': isThumbnailsShow }"
      >
        <div
          v-for="pageNum in totalPages"
          :key="pageNum"
          class="thumbnail"
          :class="{ active: pageNum === currentVisiblePage }"
          @click="scrollToPage(pageNum)"
        >
          <canvas :ref="(el) => setThumbnailRef(el, pageNum)" style="height: 110px" />
        </div>
      </div>

      <!-- 右侧主内容 - 多页容器 -->
      <div v-show="!loading" class="pdf-pages-container" ref="pagesContainer">
        <div
          v-for="pageNum in totalPages"
          :key="'page-' + pageNum"
          class="pdf-page-wrapper"
          :style="{ marginBottom: pageGap + 'px' }"
        >
          <canvas :ref="(el) => setPageRef(el, pageNum)" class="pdf-page-canvas" />
        </div>
      </div>
    </div>
  </div>
</template>
<style scoped lang="less">
@import url('@/assets/style/variable.less');
.pdf-viewer-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  z-index: 10;
  background-color: #f0f0f0;
  overflow: hidden;
  border-left: 1px solid #e2e4e7;
}

.pdf-toolbar {
  padding: 10px;
  background-color: #fff;
  display: flex;
  align-items: center;
  z-index: 10;
  font-size: 12px;
  color: #132035;
  .toolbar-item {
    cursor: pointer;
    position: relative;
    top: 1px;
  }
  .toolbar-item:hover {
    background: #fafafa;
  }
}

.page-info {
  margin: 0 10px;
}

.full-screen {
  margin-left: 20px;
  margin-right: 20px;
  &::before {
    content: ' ';
    display: inline-block;
    width: 1px;
    height: 16px;
    background: #e2e4e7;
    position: relative;
    left: -10px;
    top: 2px;
  }
  &::after {
    content: ' ';
    display: inline-block;
    width: 1px;
    height: 16px;
    background: #e2e4e7;
    position: relative;
    left: 10px;
    top: 2px;
  }
}

.zoom-out {
  margin-left: 10px;
}
.close-outlined {
  cursor: pointer;
  color: #4e5969;
  margin-left: auto;
}
.pdf-content {
  display: flex;
  flex: 1;
  overflow: hidden;
  position: relative;
  .spin-container {
    height: 100%;
    width: 100%;
  }
}

.pdf-thumbnails {
  width: 0;
  overflow-y: auto;
  scrollbar-width: none;
  background-color: #e0e0e0;
  padding: 14px;
  transform: translateX(0);
  opacity: 0;
  pointer-events: none;

  transition:
    width 0.3s ease-in-out,
    opacity 0.3s ease-in-out;

  &.pdf-thumbnails-visible {
    width: 106px;
    opacity: 1;
    pointer-events: auto;
  }

  &:not(.pdf-thumbnails-visible) {
    padding: 0;
  }
}

.thumbnail {
  margin-bottom: 10px;
  cursor: pointer;
  border: 2px solid transparent;
}

.thumbnail.active {
  border-color: @primary-color;
}

.thumbnail canvas {
  width: 100%;
  height: auto;
  display: block;
}

.pdf-pages-container {
  flex: 1;
  overflow-y: auto;
  padding: 0 40px;
  transition: padding-left 0.3s ease-in-out;
}

.pdf-page-wrapper {
  display: flex;
  justify-content: center;
  background-color: white;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
}

.pdf-page-canvas {
  display: block;
  max-width: 100%;
  height: auto;
}
</style>
