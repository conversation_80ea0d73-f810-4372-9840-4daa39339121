import { VXETable, VxeGridProps, t } from '@futurefab/vxe-table';
export default VXETable.tableFun.tableDefaultConfig({
    id: 'fdc_summary_table',
    columns: [
        {
            field: 'rawId',
            type: 'checkbox',
            width: '40px',
            headerClassName: 'show',
        },
        {
            field: 'param',
            title: 'common.field.paramAlias',
            sortable: true,
            minWidth: '200px',
            filters: [{ data: '' }],
            filterRender: {
                name: '$input',
            },
        },
        {
            field: 'dataType',
            title: 'summary.dataTypeName',
            sortable: true,
            filters: [{ data: '' }],
            minWidth: '200px',
            filterMethod: ({ value, cellValue }: any) => {
                return value == cellValue || cellValue.indexOf(value) > -1;
            },
        },
        {
            field: 'priority',
            title: 'common.field.priority',
            sortable: true,
            filters: [{ data: '' }],
            minWidth: '150px',
            filterMethod: ({ value, cellValue }: any) => {
                return value == cellValue || cellValue.indexOf(value) > -1;
            },
        },
        {
            field: 'unit',
            title: 'common.field.unit',
            sortable: true,

            minWidth: '150px',
            filters: [{ data: '' }],
            filterRender: {
                name: '$input',
            },
        },
        {
            field: 'category',
            title: 'common.field.category',
            sortable: true,

            minWidth: '150px',
            filters: [{ data: '' }],
            filterRender: {
                name: '$input',
            },
        },
    ],
    cellClassName: ({
        row,
        column,
    }: {
        row: { specExist: boolean };
        column: { field: string };
    }) => {
        return row.specExist && column.field == 'param' ? 'summary-param-spec-exist' : '';
    },
    toolbarConfig: {
        tableName: 'common.title.paramList',
        import: false,
        export: false,
        custom: false,
        refresh: false,
        border: false,
    },
    checkboxConfig: {
        showHeader: false,
    },
});

export const sumParamAliasConfig = VXETable.tableFun.tableDefaultConfig({
    id: 'fdc_summary_filter_table',
    columns: [
        {
            field: 'sumParamModuleId',
            type: 'checkbox',
            width: '40px',
            align: 'center',
        },
        {
            field: 'eqpModuleId',
            title: 'common.field.moduleAlias',
            sortable: true,
            minWidth: '150px',
            filters: [],
        },
        {
            field: 'sumParamAlias',
            title: 'summary.sumParamAlias',
            sortable: true,

            minWidth: '200px',
            filters: [{ data: '' }],
            filterRender: {
                name: '$input',
            },
        },
        {
            field: 'sumType',
            title: 'summary.sumType',
            sortable: true,
            minWidth: '150px',
            filters: [],
        },
        {
            field: 'sumCategory',
            title: 'summary.sumCategory',
            sortable: true,
            minWidth: '150px',
            filters: [],
        },
        {
            title: 'common.field.step',
            minWidth: '60px',
            align: 'center',
            slots: {
                default: 'stepFilter',
            },
        },
    ],
    cellClassName: ({ row, column }: { row: { specExist: string }; column: { field: string } }) => {
        return row.specExist === 'Y' && column.field == 'sumParamAlias'
            ? 'summary-param-spec-exist'
            : '';
    },
    toolbarConfig: {
        tableName: 'Summary Parameter List',
        import: false,
        export: false,
        custom: false,
        refresh: false,
    },
    // maxHeight: 500,
    rowConfig: {
        keyField: 'sumParamModuleId',
    },
});
export const stepOptions = VXETable.tableFun.tableDefaultConfig({
    rowId: 'code',
    columns: [
        {
            field: 'code',
            type: 'checkbox',
            width: '40px',
            align: 'center',
        },
        {
            field: 'name',
            title: 'common.field.step',
            minWidth: '60px',
            align: 'center',
        },
    ],
    toolbarConfig: {
        import: false,
        export: false,
        custom: false,
        refresh: false,
        border: false,
    },
    checkboxConfig: {
        checkAll: true,
    },
});
