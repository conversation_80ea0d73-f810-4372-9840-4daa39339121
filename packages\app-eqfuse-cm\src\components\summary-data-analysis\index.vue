<script lang="ts" setup>
import { onMounted, onUnmounted, ref, nextTick, onBeforeMount, shallowReactive } from 'vue';
import tableConfig, {
  sumParamAliasConfig,
  stepOptions
} from '@/components/summary-data-analysis/config';
import {
  getSummaryParameterList,
  getSumParam,
  getNacosConfig,
  getSummaryConditionEqp
} from '@futurefab/ui-sdk-api';
import { t } from '@futurefab/vxe-table';
import { summaryInfo as buttonIds } from '@/log-config/index';
import { Splitpanes, Pane } from 'splitpanes';
import 'splitpanes/dist/splitpanes.css';
import {
  showInfo,
  showWarning,
  showError,
  FFSocket,
  EesButtonTip
} from '@futurefab/ui-sdk-ees-basic';
import type { SearchConditionTreeInputData, UnknownData, ContextResult } from '@/types/common';
import type {
  ButtonAuthority,
  Parameter,
  StepList
} from '@/components/summary-data-analysis/interface';
import ChartItem from '@/components/summary-data-analysis/components/chart/index.vue';
import { getToday, handleData } from '@/components/summary-data-analysis/tools';
import Worker from '@/workers/summary.worker';
import { useCurrentInstance } from '@/utils/use-current-instance';
import { setHeaderSelectFilter, getPermissionButton } from '@futurefab/ui-sdk-ees-basic';
import { useBaseStore } from '@futurefab/ui-sdk-stores';
import { LOADINGVALUECOUNT, LOADINGVALUETIME } from '@/utils/constant';
import { urlQueryParse } from '@/utils';
import dayjs from 'dayjs';
import useWindowResize from '@/hooks/window-resize';

const baseStore = useBaseStore();
const socket = new FFSocket({
  socketUrl: baseStore.socketUrl,
  enableGzip: true
});

const { proxy } = useCurrentInstance();
let myWorker = null as any;
let myCount = 0;
let chartData = {};
let count = 0;
let time = 0;
let timer: any = null;
const xGird = ref();
const xChart = ref();
const xStepGird = ref();
const multiSearch = ref();
const xSumParamAlias = ref();
const params = shallowReactive({
  paneSize: 25,
  options: tableConfig,
  resultList: [] as Parameter[],
  buttonIds,
  //是否分图标展示
  checkedSplitParam: false,
  //是否显示chart上的标记点
  checkedShowPoint: true,
  stepList: [] as StepList[],
  checkedStepList: [] as StepList[],
  modelName: '',
  searchCondition: '',
  startDate: Number(getToday().start),
  endDate: Number(getToday().end),
  //图表未梳理的所有数据
  rightParamList: [] as string[],
  sumModuleNameList: [] as string[],
  specParamList: [] as string[],
  //draw按钮是否可用
  ableDrawBtn: true,
  //用来判断是否取消了websocket请求
  isCancel: false,
  //附加条件 画chart用
  contextResult: {} as ContextResult,
  paramAliasList: [] as string[],
  isShowFilter: false,
  sumParamAliasList: [] as any[],
  visible: false,
  selectedPointLimit: 0,
  maxExportLimit: 0,
  checkedSumParamAlias: [] as any[],
  loadingType: 'all',
  loadingValue: 'all' as string | number,
  buttonAuthority: {} as ButtonAuthority,
  currentSpec: null as null | string,
  categoryList: [] as { code: string; name: string }[],
  copyChartFullTitle: false,
  eqpIdList: [] as string[]
});
const loadingParams = shallowReactive({
  isShow: false,
  hasButton: true,
  title: t('common.loading.text')
});
const searchConditionWrapperRef = ref();
const { searchConditionHeight, bindResize, getSearchConditionHeight } = useWindowResize();
nextTick(() => {
  bindResize(searchConditionWrapperRef.value);
});
const handleCollapsedSearchCondition = () => {
  getSearchConditionHeight();
  setTimeout(() => {
    xChart.value && xChart.value.resize();
  }, 50);
};

const socketHeader = {
  log: 'Y',
  application: 'wees-fdc-service',
  from: 'WEB' + new Date().getTime(),
  token: window.localStorage.getItem('token') as string,
  bsId: 'ViewSummaryChartDataService',
  heartBeat: 'N',
  params: {
    time: new Date().getTime()
  },
  page: buttonIds.pageId,
  action: buttonIds.draw
};
const drawingChart = (title?: string) => {
  loadingParams.title = t(title || 'common.loading.drawChart');
  loadingParams.isShow = true;
  loadingParams.hasButton = false;
};
const resetLoadingParams = () => {
  loadingParams.isShow = false;
  loadingParams.hasButton = true;
  loadingParams.title = t('common.loading.text');
};
const requestSumParam = (values: SearchConditionTreeInputData, type?: string) => {
  if (!values.modelID || values.eqpRawId?.length == 0) return false;
  params.options.loading = true;
  getSummaryParameterList({
    headerParams: {
      log: 'Y',
      page: buttonIds.pageId,
      action: buttonIds.search
    },
    bodyParams: {
      eqpModel: values.modelID,
      eqpRawIdList: values.eqpRawId
    }
  }).then((result: any) => {
    params.options.loading = false;
    if (result.status == 'SUCCESS') {
      params.resultList = result.data as Parameter[];
      if (type && type == 'link') {
        const parameter = urlQueryParse('parameter');
        const parameteObj = parameter && parameter !== '{}' ? JSON.parse(parameter) : null;
        if (parameteObj && parameteObj.eqpParam) {
          params.resultList.forEach((item: { param: string }) => {
            if (item.param === parameteObj.eqpParam) {
              nextTick(() => {
                xGird.value && xGird.value.setCheckboxRow(item, true);
              });
            }
          });
        }
      }
      //列filter设置
      xGird.value &&
        setHeaderSelectFilter({
          xGrid: xGird,
          columnFieldList: ['priority', 'dataType'],
          tableData: params.resultList,
          splitType: ','
        });
    }
  });
};
//获取chart 数据
const requestSummaryChartData = (checkedList: string[]) => {
  if (multiSearch.value) {
    params.contextResult = multiSearch.value.getContext();
    const searchCondition = JSON.parse(params.searchCondition);
    searchCondition.contextResult = params.contextResult;
    // searchCondition.startDate = params.startDate;
    // searchCondition.endDate = params.endDate;
    params.searchCondition = JSON.stringify(searchCondition);
  }
  const stepList =
    params.checkedStepList.length == params.stepList.length ? [] : params.checkedStepList;
  let stepStrList = [] as string[];
  if (stepList.length > 0) {
    stepStrList = stepList.map((item: StepList) => item.name);
  }

  let headerBody = {
    header: socketHeader,
    body: {
      ...getRequestData(),
      stepList: stepStrList,
      sumParamModuleIdList: checkedList
    }
  };
  console.time('loading data');
  socket.send(headerBody);
};
//获取 searchcondition的日期
const getDate = (value: UnknownData) => {
  params.startDate = value[0];
  params.endDate = value[1];
};
//获取nacos的配置信息
const getChartConfig = () => {
  getNacosConfig({
    headerParams: {
      log: 'N'
    },
    bodyParams: [
      'summaryPointLimit',
      'summaryMaxExport',
      'summaryLoadingType',
      'summaryLoadingValue',
      'copyChartFullTitle'
    ]
  }).then((res: any) => {
    if (res.status === 'SUCCESS') {
      if (res.data && Object.keys(res.data).length > 0) {
        params.selectedPointLimit =
          res.data.summaryPointLimit == 0 ? 100 : Number(res.data.summaryPointLimit);
        params.maxExportLimit =
          res.data.summaryMaxExport == 0 ? 30000 : Number(res.data.summaryMaxExport);
        params.loadingType = res.data.summaryLoadingType ? res.data.summaryLoadingType : 'all';
        params.loadingValue =
          params.loadingType == 'all' ? 'all' : Number(res.data.summaryLoadingValue);
        if (params.loadingType == 'count' && Number(params.loadingValue) < LOADINGVALUECOUNT) {
          params.loadingValue = LOADINGVALUECOUNT;
        }
        if (params.loadingType == 'time' && Number(params.loadingValue) < LOADINGVALUETIME) {
          params.loadingValue = LOADINGVALUETIME;
        }
        params.copyChartFullTitle = res.data.copyChartFullTitle.toLowerCase() === 'true';
      } else {
        params.selectedPointLimit = 100;
        params.maxExportLimit = 30000;
        params.loadingType = 'all';
        params.loadingValue = 'all';
        params.copyChartFullTitle = false;
      }
    }
  });
};
//pane
const resize = ($event: UnknownData) => {
  params.paneSize = $event[0].size;
  xChart.value && xChart.value.resize();
};
//search
const doSearch = (argus: string, value: SearchConditionTreeInputData) => {
  if (argus && argus == 'reset') return false;
  nextTick(() => {
    params.modelName = value.modelName as string;
    params.contextResult = value.contextResult as ContextResult;
    const obj: any = value;
    // obj.startDate = params.startDate;
    // obj.endDate = params.endDate;
    params.searchCondition = JSON.stringify(obj);
    xSumParamAlias.value && xSumParamAlias.value.clearFilter();
    params.checkedSumParamAlias = [];
    xSumParamAlias.value && xSumParamAlias.value.setAllCheckboxRow(false);
    params.checkedStepList = [];
    params.checkedShowPoint = true;
    params.checkedSplitParam = false;
    requestSumParam(value, argus);
    if (argus && argus == 'link') {
      const parameter = urlQueryParse('parameter');
      const parameterObj = parameter && parameter !== '{}' ? JSON.parse(parameter) : null;

      if (parameterObj && parameterObj.categoryCode) {
        const categoryObj: { code: string; name: string } = params.categoryList.filter(
          (item: { code: string; name: string }) => item.code === parameterObj.categoryCode
        )[0];
        if (!categoryObj || !categoryObj.name || !parameterObj.name || !parameterObj.moduleId)
          return false;
        resetLoadingParams();
        loadingParams.isShow = true;
        params.rightParamList = [];
        params.sumModuleNameList = [];
        params.specParamList = [];
        params.checkedStepList = [];
        const checkedSumParamAlias = [] as any[];
        const sumParamModuleIdList: any[] = [];
        params.currentSpec = parameterObj.name ? parameterObj.name : null;
        params.specParamList.push(parameterObj.name);
        const arr = parameterObj.moduleId.split(':');
        const eqp = arr[1].split('/')[0];
        params.eqpIdList = eqp ? [eqp] : [];
        const sumParamModuleId = `[${categoryObj.name}]${parameterObj.name}^${parameterObj.moduleId}`;
        sumParamModuleIdList.push(sumParamModuleId);
        checkedSumParamAlias.push({
          specExist: 'Y',
          sumParamAlias: parameterObj.name,
          sumParamModuleId
        });
        params.checkedSumParamAlias = checkedSumParamAlias;
        resetChart();
        params.ableDrawBtn = false;
        if (params.loadingType == 'all') {
          //开启webworker
          openWorker();
        } else {
          count = 0;
          myCount = Number(params.loadingValue);
          if (timer) {
            clearInterval(timer);
            time = 0;
            timer = null;
          }
          chartData = {};
        }
        if (multiSearch.value) {
          params.contextResult = multiSearch.value.getContext();
          const searchCondition = JSON.parse(params.searchCondition);
          searchCondition.contextResult = params.contextResult;
          // searchCondition.startDate = params.startDate;
          // searchCondition.endDate = params.endDate;
          params.searchCondition = JSON.stringify(searchCondition);
        }
        let headerBody = {
          header: socketHeader,
          body: {
            ...getRequestData(),
            stepList: [],
            sumParamModuleIdList
          }
        };
        console.time('loading data');
        socket.send(headerBody);
      }
    }
  });
};
// 展示filter step
const filterStep = () => {
  params.visible = true;
  nextTick(() => {
    if (xStepGird.value) {
      if (params.checkedStepList.length == 0) {
        xStepGird.value.setAllCheckboxRow(true);
      } else {
        xStepGird.value.setCheckboxRow(params.checkedStepList, true);
      }
    }
  });
};
//处理在弹出filter step层时，不关闭summary chart setting层
const handleShowFilter = ({ type }: { type: string }): any => {
  params.checkedSumParamAlias = xSumParamAlias.value && xSumParamAlias.value.getCheckboxRecords();
  if (type == 'confirm' && params.checkedSumParamAlias.length == 0) {
    return new Error('');
  }
};
//filter step ok按钮
const handleOk = () => {
  params.checkedStepList = xStepGird.value && xStepGird.value.getCheckboxRecords();
  handleStepCancel();
};
//filter step cancel按钮
const handleStepCancel = () => {
  params.visible = false;
};
//重置chartData和其他信息
const resetChart = () => {
  params.ableDrawBtn = true;
  params.isCancel = false;
};
//请求数据的参数对象
const getRequestData = () => {
  const { location, modelId, eqpRawId, startDate, endDate } = JSON.parse(params.searchCondition);

  console.log('JSON Time', JSON.parse(params.searchCondition), params.startDate, params.endDate);

  return {
    startDate: startDate,
    endDate: endDate,
    // lotId: params.contextResult.lotId,
    // lotIdsFlag: params.contextResult.lotIdsFlag,
    // product: params.contextResult.product,
    // productIdsFlag: params.contextResult.productIdsFlag,
    // recipeId: params.contextResult.recipeId,
    // recipeIdsFlag: params.contextResult.recipeIdsFlag,
    // substrateId: params.contextResult.substrateId,
    // substrateIdsFlag: params.contextResult.substrateIdsFlag,
    eqpModel: modelId,
    eqpRawIdList: eqpRawId,
    paramAliasList: params.paramAliasList
  };
};
//获取summary parameter list
const requestSumParamAlias = () => {
  getSumParam({
    headerParams: {
      log: 'Y',
      page: buttonIds.pageId,
      action: buttonIds.draw
    },
    bodyParams: {
      ...getRequestData()
    }
  }).then((result: any) => {
    params.options.loading = false;
    if (result.status == 'SUCCESS') {
      params.sumParamAliasList = (result.data as any).moduleSumParamList as any[];
      params.stepList = (result.data as any).stepFilter.map((item: string) => {
        const obj: StepList = { code: '', name: '' };
        obj.code = item;
        obj.name = item;
        return obj;
      });
      params.isShowFilter = true;
      nextTick(() => {
        params.sumParamAliasList.forEach((item: { sumParamModuleId: string }) => {
          const isExist = params.checkedSumParamAlias.filter(
            (alias: { sumParamModuleId: string }) =>
              alias.sumParamModuleId === item.sumParamModuleId
          );
          if (isExist.length > 0) {
            xSumParamAlias.value && xSumParamAlias.value.setCheckboxRow(item, true);
          }
        });
        // xSumParamAlias.value &&
        //     xSumParamAlias.value.setCheckboxRow(params.checkedSumParamAlias, true);
        setTimeout(() => {
          if (xSumParamAlias.value) {
            //列filter设置
            const filterList = xSumParamAlias.value.getCheckedFilters();
            setHeaderSelectFilter({
              xGrid: xSumParamAlias,
              columnFieldList: ['eqpModuleId', 'sumType', 'sumCategory'],
              tableData: params.sumParamAliasList,
              filterList
            });
          }
        }, 100);
      });
    }
  });
};
//summary chart setting cancel按钮
const handleCancel = () => {
  params.isShowFilter = false;
  params.checkedSumParamAlias = xSumParamAlias.value && xSumParamAlias.value.getCheckboxRecords();
  if (params.sumParamAliasList.length == 0) {
    resetChart();
    resetLoadingParams();
    xChart.value && xChart.value.clearChart();
  }
};
//summary chart setting ok按钮
const handleDrawChart = () => {
  if (params.sumParamAliasList.length == 0) {
    handleCancel();
    return false;
  }
  params.checkedSumParamAlias = xSumParamAlias.value && xSumParamAlias.value.getCheckboxRecords();
  if (params.checkedSumParamAlias.length == 0) {
    showWarning('summary.tips.sumParamRequired');
    return false;
  }
  if (socket.status != 'open') {
    showWarning('common.tip.socketPending');
    return false;
  }
  params.isShowFilter = false;
  resetLoadingParams();
  loadingParams.isShow = true;
  params.rightParamList = [];
  params.sumModuleNameList = [];
  params.specParamList = [];
  params.eqpIdList = [];
  params.currentSpec = null;
  params.checkedSumParamAlias.forEach(
    (item: { specExist: string; sumParamAlias: string; eqpId: string }) => {
      if (item.specExist == 'Y') {
        params.specParamList.push(item.sumParamAlias);
      }
      if (!params.eqpIdList.includes(item.eqpId)) {
        params.eqpIdList.push(item.eqpId);
      }
    }
  );
  resetChart();
  params.ableDrawBtn = false;
  if (params.loadingType == 'all') {
    //开启webworker
    openWorker();
  } else {
    count = 0;
    myCount = Number(params.loadingValue);
    if (timer) {
      clearInterval(timer);
      time = 0;
      timer = null;
    }
    chartData = {};
  }
  const list = params.checkedSumParamAlias.map((item: { sumParamModuleId: string }) => {
    return item.sumParamModuleId;
  });
  requestSummaryChartData(list);
};
//点击左侧 draw 按钮
const drawChart = () => {
  if (!params.startDate || !params.endDate) {
    showWarning('common.tip.emptyDate');
    return false;
  }
  const checkedList = xGird.value && xGird.value.getCheckboxRecords(true);
  if (checkedList.length > 0) {
    const paramAliasList = [] as string[];
    checkedList.forEach((element: Parameter) => {
      paramAliasList.push(element.param);
    });
    params.paramAliasList = paramAliasList;
    requestSumParamAlias();
  } else {
    if (checkedList.length == 0) {
      showWarning('common.tip.parameterRequired');
      return false;
    }
  }
};
//取消websocket 请求
const cancelRequest = () => {
  params.isCancel = true;
  params.ableDrawBtn = true;
  if (params.loadingType == 'all') {
    myWorker && myWorker.postMessage('end');
  } else {
    handleCountDraw();
  }
  loadingParams.title = t('common.loading.drawChart');
  loadingParams.hasButton = false;
  loadingParams.isShow = true;
  socket.send({
    header: {
      ...socketHeader,
      subscribeFlag: 'unsubscribe'
    },
    body: {}
  });
  // setChartData();
};
// 配置webworker
const openWorker = () => {
  if (!myWorker) {
    const isDev = process.env.NODE_ENV === 'development';
    if (isDev) {
      myWorker = new Worker();
    } else {
      myWorker = new Worker(`${baseStore.portalUrl}/summary.worker.js`, {
        type: 'module'
      });
    }
  }
  myWorker.onmessage = (e: any) => {
    const { rightParamList, sumModuleNameList, totalData, totalCount } = e.data;
    myWorker.terminate();
    myWorker = null;
    if (totalData.length == 0) {
      if (!params.isCancel) {
        showInfo('common.tip.noData');
      }
      resetChart();
      resetLoadingParams();
      xChart.value && xChart.value.clearChart();
      return false;
    }
    xChart.value &&
      xChart.value.getData({
        data: totalData,
        sumModuleNameList: sumModuleNameList,
        rightParamList: rightParamList,
        count: totalCount,
        isEnd: true,
        specParams: params.specParamList,
        currentSpec: params.currentSpec,
        eqpIdList: params.eqpIdList
      });
  };
};
//table event的处理函数
const events = (type: string) => {
  // let grid = xGird;
  // if (type == 'filterSum') {
  //     grid = xSumParamAlias;
  // } else if (type == 'step') {
  //     grid = xStepGird;
  // }
  // return {
  //     checkboxChange: ({ $event, selectedData, checked, _rowIndex }: UnknownData) => {
  //         VXETable.tableFun.handleCheckboxAreaChange({
  //             selectedData,
  //             xGird: grid,
  //             checked,
  //             rowIndex: _rowIndex,
  //             $event,
  //         });
  //     },
  // };
};
const handleCountDraw = () => {
  const data = Object.values(chartData);
  if (timer) {
    clearInterval(timer);
    time = 0;
  }
  if (data.length == 0) {
    if (!params.isCancel) {
      showInfo('common.tip.noData');
    }
    resetChart();
    resetLoadingParams();
    xChart.value && xChart.value.clearChart();
    return false;
  }
  xChart.value &&
    xChart.value.getData({
      data,
      sumModuleNameList: params.sumModuleNameList,
      rightParamList: params.rightParamList,
      specParams: params.specParamList,
      count,
      isEnd: true,
      currentSpec: params.currentSpec,
      eqpIdList: params.eqpIdList
    });
};
const urlSetSearchCondition = () => {
  const _searchUrl = urlQueryParse('parameter') || '{}';
  if (_searchUrl && _searchUrl !== '{}') {
    const _searchUrlObj = JSON.parse(_searchUrl);
    if (_searchUrlObj.moduleId) {
      getSummaryConditionEqp({
        bodyParams: {
          moduleId: _searchUrlObj.moduleId
        }
      }).then((result: any) => {
        if (result.status == 'SUCCESS' && result.data && Object.keys(result.data).length) {
          const {
            areaRawId,
            locationRawId,
            modelRawId,
            rawId,
            sumCategoryCodeList,
            alarmTimeRange
          } = result.data;
          const obj: {
            areaID: number;
            locationID: number;
            modelID: number;
            eqpRawId: number[];
            startDate?: number;
            endDate?: number;
          } = {
            areaID: areaRawId,
            locationID: locationRawId,
            modelID: modelRawId,
            eqpRawId: [rawId]
          };
          if (_searchUrlObj.alarmTime) {
            const dateTime = dayjs(_searchUrlObj.alarmTime, 'YYYY-MM-DD HH:mm:ss.SSS');
            if (dateTime.isValid()) {
              const startTime = dateTime.subtract(alarmTimeRange ? alarmTimeRange : 12, 'hour');
              const startDate = startTime.valueOf();
              const endTime = dateTime.add(alarmTimeRange ? alarmTimeRange : 12, 'hour');
              const endDate = endTime.valueOf();
              obj.startDate = startDate;
              obj.endDate = endDate;
            }
          }
          multiSearch.value &&
            multiSearch.value.setInitialValue({
              ...obj,
              callback: (result: any) => {
                if (result.eqpRawId && result.eqpRawId.length > 0) {
                  params.categoryList =
                    sumCategoryCodeList && Array.isArray(sumCategoryCodeList)
                      ? sumCategoryCodeList
                      : [];
                  doSearch('link', result);
                }
              }
            });
        }
      });
    }
  }
};
onMounted(() => {
  urlSetSearchCondition();
  getChartConfig();
  //收取后端推送的数据
  const getChartData = (data: any) => {
    if (Object.keys(data).length == 0) return false;
    const value: any = Object.values(data)[0];
    const keys = Object.keys(value);
    loadingParams.title =
      keys.length > 1 ? `${value['paramInfo']['percentage']}` : loadingParams.title;
    if (!value['paramInfo']) return false;
    //传送消息
    if (params.loadingType == 'all') {
      myWorker && myWorker.postMessage(JSON.stringify(data));
    } else {
      const { totalData, totalCount, rightParamList, sumModuleNameList } = handleData({
        data,
        totalData: chartData,
        totalCount: count,
        rightParamList: params.rightParamList,
        sumModuleNameList: params.sumModuleNameList
      });
      chartData = totalData;
      count = totalCount;
      params.rightParamList = rightParamList;
      params.sumModuleNameList = sumModuleNameList;
      if (params.loadingType == 'count' && count > 0 && count >= myCount) {
        myCount += Number(params.loadingValue);
        xChart.value &&
          xChart.value.getData({
            data: Object.values(chartData),
            sumModuleNameList: params.sumModuleNameList,
            rightParamList: params.rightParamList,
            specParams: params.specParamList,
            count,
            currentSpec: params.currentSpec,
            eqpIdList: params.eqpIdList
          });
      }
      if (params.loadingType == 'time' && time > 0 && time > Number(params.loadingValue)) {
        time = 0;
        xChart.value &&
          xChart.value.getData({
            data: Object.values(chartData),
            sumModuleNameList: params.sumModuleNameList,
            rightParamList: params.rightParamList,
            specParams: params.specParamList,
            count,
            currentSpec: params.currentSpec,
            eqpIdList: params.eqpIdList
          });
      }
    }
  };
  //websokcet接收信息的处理函数
  const receiveMessage = (message: any) => {
    if (message.body == 'connect success!') {
      errorFun('', '0%');
      console.log('链接成功');
    } else if (message.body.status === 'start') {
      setInterval(() => {
        ++time;
      }, 1000);
      console.log('开始');
    } else if (message.body.status === 'end') {
      console.timeEnd('loading data');
      params.ableDrawBtn = true;
      loadingParams.title = t('common.loading.drawChart');
      loadingParams.hasButton = false;
      if (params.loadingType == 'all') {
        myWorker && myWorker.postMessage('end');
      } else {
        handleCountDraw();
      }
    } else if (message.header.bsId && message.header.bsId === '200002') {
      resetChart();
      resetLoadingParams();
      window.location.href = `${baseStore.portalUrl}/login`;
      return false;
    } else if (message.header.bsId && message.header.bsId === '200003') {
      resetChart();
      resetLoadingParams();
      showError(message.body);
      return false;
    } else {
      if (!params.isCancel) {
        getChartData(message.body);
      }
    }
  };
  //websokcet 异常处理函数
  const errorFun = (content: string, title?: string) => {
    if (params.ableDrawBtn) return false;
    content != '' && showWarning(content);
    params.ableDrawBtn = true;
    loadingParams.title = title || t('common.loading.drawChart');
    loadingParams.hasButton = false;
    if (params.loadingType == 'all') {
      myWorker && myWorker.postMessage('end');
    } else {
      handleCountDraw();
    }
  };
  //初始化websokcet
  socket.init(null, receiveMessage, errorFun);
});
onMounted(async () => {
  params.buttonAuthority = (await getPermissionButton('/efdc/summary')) as ButtonAuthority;
});
onUnmounted(() => {
  //关闭websokcet连接
  socket.close();
  if (timer) {
    clearInterval(timer);
    timer = null;
  }
});

defineExpose({ doSearch });
</script>
<template>
  <div :id="params.buttonIds.pageId" class="fdc_summary_wrapper">
    <Splitpanes
      class="fdc_summary_container default-theme"
      :style="{
        height: `calc(100% - ${searchConditionHeight}px)`
      }"
      @resize="resize"
    >
      <Pane
        class="fdc_summary_container-left-pane cardItem pane-border-radius-bottom-left"
        :size="params.paneSize"
      >
        <div class="fdc_summary_container-left">
          <vxe-grid
            ref="xGird"
            v-bind="params.options"
            :data="params.resultList"
            class="vxe-table-bottom-left-radius-lg"
            v-on="events('filter')"
          >
            <template #top>
              <div class="table_top_wrapper">
                <div class="table_top_table-name">{{ params.modelName }}</div>
                <div class="table_top_button">
                  <EesButtonTip
                    :id="buttonIds.draw"
                    icon="#icon-btn-chart-draw"
                    :text="t('eesCharts.commonBtn.drawChart')"
                    :disabled="!params.ableDrawBtn"
                    :is-border="true"
                    class="table_top_button-doChart"
                    @on-click="drawChart"
                  >
                  </EesButtonTip>
                </div>
              </div>
            </template>
          </vxe-grid>
        </div>
      </Pane>

      <Pane
        style="overflow: auto"
        :size="100 - params.paneSize"
        class="fdc_summary_container-right-pane pane-border-radius-bottom-right"
      >
        <div
          v-is-loading="{
            ...loadingParams,
            buttonEvent: cancelRequest
          }"
          class="fdc_summary_container-right cardItem"
        >
          <chart-item
            ref="xChart"
            :start-date="params.startDate"
            :end-date="params.endDate"
            :search-condition="params.searchCondition"
            :is-split="params.checkedSplitParam"
            :is-show-point="params.checkedShowPoint"
            :selected-point-limit="params.selectedPointLimit"
            :max-export-limit="params.maxExportLimit"
            :loading-type="params.loadingType"
            :is-show-copy-confirm="params.copyChartFullTitle"
            :button-authority="params.buttonAuthority"
            @show-loading="drawingChart"
            @hide-loading="resetLoadingParams"
          ></chart-item>
        </div>
      </Pane>
    </Splitpanes>
  </div>
  <vxe-modal
    v-model="params.isShowFilter"
    class-name="fdc_summary_chart_setting common_modal_hide_titleIcon"
    show-zoom
    show-close
    show-footer
    resize
    width="880"
    height="500"
    :z-index="999"
    transfer
    type="confirm"
    :title="$t('Summary Chart Setting')"
    :cancel-button-text="$t('eesCharts.Cancel')"
    :confirm-button-text="$t('eesCharts.OK')"
    confirm-button-type="primary"
    :before-hide-method="handleShowFilter"
    @confirm="handleDrawChart"
    @cancel="handleCancel"
    @close="handleCancel"
  >
    <div class="fdc_summary_chart_item">
      <span class="common_table_top_title">{{ $t('common.title.chart') }}</span>
      <div>
        <a-checkbox v-model:checked="params.checkedSplitParam">{{
          $t('eesCharts.commonBtn.splitParam')
        }}</a-checkbox>
      </div>
      <div>
        <a-checkbox v-model:checked="params.checkedShowPoint">{{
          $t('Show Chart Point')
        }}</a-checkbox>
      </div>
    </div>
    <div style="height: calc(100% - 36px)">
      <vxe-grid
        ref="xSumParamAlias"
        class="vxe-toolbar-top-left-radius-xs vxe-toolbar-top-right-radius-xs vxe-table-bottom-left-radius-xs vxe-table-bottom-right-radius-xs"
        v-bind="sumParamAliasConfig"
        style="height: 100%"
        :data="params.sumParamAliasList"
        v-on="events('filterSum')"
      >
        <template #stepFilter="{ row }">
          <span
            v-if="row.sumCategory == 'STEP' && params.stepList.length > 0"
            :class="`iconfont icon-filter filter_type_category-step ${
              params.checkedStepList.length > 0 &&
              params.checkedStepList.length != params.stepList.length
                ? 'highlight'
                : ''
            }`"
            @click="filterStep"
          ></span>
        </template>
      </vxe-grid>
    </div>
  </vxe-modal>
  <vxe-modal
    v-model="params.visible"
    class-name="common_modal_hide_titleIcon"
    :title="$t('eesCharts.commonBtn.detail')"
    show-zoom
    show-close
    show-footer
    resize
    width="400"
    height="500"
    :z-index="1000"
    transfer
    type="confirm"
    :cancel-button-text="$t('eesCharts.Cancel')"
    :confirm-button-text="$t('eesCharts.OK')"
    confirm-button-type="primary"
    @confirm="handleOk"
    @cancel="handleStepCancel"
    @close="handleStepCancel"
  >
    <vxe-grid
      ref="xStepGird"
      style="height: 100%"
      class="vxe-table-top-left-radius-xs vxe-table-top-right-radius-xs vxe-table-bottom-left-radius-xs vxe-table-bottom-right-radius-xs"
      :data="params.stepList"
      v-bind="stepOptions"
      v-on="events('step')"
    ></vxe-grid>
  </vxe-modal>
</template>
<style lang="less" scoped>
@import url('@/assets/style/variable.less');
:deep(.summary-param-spec-exist) {
  .vxe-cell--label {
    color: @primary-color;
  }
}
.fdc_summary_wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  .fdc_summary_search-condition {
    width: 100%;
    flex-shrink: 0;
  }
  .fdc_summary_container {
    width: 100%;
    flex-grow: 1;
    display: flex;
    padding-top: 8px;
    .fdc_summary_container-left-pane {
      width: 300px;
      height: 100%;
      flex-shrink: 0;
      box-sizing: border-box;
      overflow: auto;
      justify-content: flex-start;
    }
    .fdc_summary_container-left {
      height: 100%;
      width: 100%;
      min-width: 280px;
      :deep(.is--maximize) {
        z-index: 103 !important;
      }
      :deep(.vxe-toolbar) {
        padding: 0;
        height: 52px;
        padding: 0 @padding-basis + 6;
        .vxe-table-name-text {
          padding: 0;
        }
      }
      .table_top_wrapper {
        display: flex;
        justify-content: space-between;
        align-items: center;
        height: 28px;
        padding: 0 @padding-basis + 6;
        padding-bottom: @padding-basis;
        .table_top_table-name {
          color: @text-other-color2;
          font-weight: bold;
          color: @text-title-color;
          font-size: @font-size-basis;
        }
        .table_top_tab {
          width: 40%;
          :deep(.ff-basic-tabChange-horizontal) {
            padding: 0 10px;
            li {
              span {
                padding: 0 (@padding-basis + 2);
              }
            }
          }
        }
        .table_top_button {
          position: absolute;
          right: 14px;
          top: -41px;
          display: flex;
          align-items: center;
          color: @text-subtitle-color;
        }
      }
    }
    .fdc_summary_container-right-pane {
      box-sizing: border-box;
      background: @primary-color-other3;
      border-radius: 0 0 @border-radius-lg 0;
      border: 1px solid @border-color;
    }
    .fdc_summary_container-right {
      height: 100%;
      width: 100%;
      :deep(.ff-loading) {
        z-index: 102;
      }
    }
  }
}
.fdc_summary_chart_setting {
  .filter_type_category-step {
    font-size: 12px;
    margin-left: 6px;
    cursor: pointer;
    &.highlight {
      color: @primary-color;
    }
  }
  .fdc_summary_chart_item {
    display: flex;
    height: 22px;
    margin-bottom: @margin-basis + 6;
    // padding-left: @padding-lg;
    align-items: center;
    .common_table_top_title {
      font-size: @font-size-basis + 2;
      font-weight: @font-weight-lg;
      margin-right: @margin-basis;
      color: @text-title-color;
    }
  }
}
</style>
