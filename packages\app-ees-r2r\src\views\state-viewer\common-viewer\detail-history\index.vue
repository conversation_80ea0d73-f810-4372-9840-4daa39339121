<script lang="ts" setup>
import { ref, reactive, onBeforeMount, onMounted, inject, computed } from 'vue';
import {
  getStateViewerDetailHistory,
  exportStateViewerDetailHistory,
  getUserStateViewerDetailHistory,
  exportUserStateViewerDetailHistory,
  apiConfig
} from '@futurefab/ui-sdk-api';
import { stateViewer as buttonIds } from '@/log-config/index';
import { xConfig, dGetConfig } from './config';
import {
  Column,
  t,
  type VxeGridDefines,
  VXETable,
  type VxeTablePropTypes
} from '@futurefab/vxe-table';
import {
  setHeaderSelectFilter,
  showWarning,
  exportToExcel,
  EesSidebar,
  EesCustomTime,
  EesTabs,
  EesNoData
} from '@futurefab/ui-sdk-ees-basic';
import Chart from '../Chart/index.vue';
import dayjs from 'dayjs';
import { filterMethod } from '@/utils/tools';
import { boolFilters } from '@/utils/table-config';
import type { GetButtonAuthority } from '@/views/setup-data-manager/interface';
// import mock from 'C:/Projects/mock/stateViewer.json';

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  selected: {
    type: Object,
    default: {}
  },
  selectedState: {
    type: Object,
    default: {}
  },
  selectedCols: {
    type: Object,
    default: {}
  },
  type: String
});
const getButtonAuthority = inject('getButtonAuthority');
const buttonAuthority = computed(() =>
  (getButtonAuthority as GetButtonAuthority)().buttonAuthority()
);
const emits = defineEmits(['hideSidebar']);
const handleClick = async () => {
  emits('hideSidebar');
};
const xChart = ref();
const refTime = ref();
const dataParams = reactive<any>({
  startDate: dayjs().subtract(29, 'day').startOf('day'),
  endDate: dayjs().endOf('day'),
  timeAreaResult: null,
  timeConfigVal: '',
  timeIsConfig: '',
  timeAreaBind: [dayjs().subtract(29, 'day').startOf('day'), dayjs().endOf('day')],
  tabIndex: 0
});
const timePop = (val: [number, number]) => {
  // console.log('timePop', val);
  dataParams.timeAreaResult = val;
  // dataParams.timeAreaBind = val; // recursive
  dataParams.startDate = dayjs(val[0]).format('YYYY-MM-DD HH:mm:ss');
  dataParams.endDate = dayjs(val[1]).format('YYYY-MM-DD HH:mm:ss');
};
const timeStartEndChange = (val: [number, number]) => {
  console.log('timeStartEndChange', val);
  dataParams.timeAreaBind = val;
};
const timeTypeChange = ({ isConfig, val }: any) => {
  dataParams.timeConfigVal = val;
  dataParams.timeIsConfig = isConfig;
};
const xGrid = ref();
const xTable = reactive({
  options: xConfig,
  data: [] as any[],
  height: 100
});
const dGrid = ref();
const dTable = reactive({
  options: dGetConfig(props.type),
  data: [] as any[],
  loading: false,
  totalPage: 0,
  currentPage: 1,
  pageSize: 20
});
const checkedKeys = reactive<any>({});
const inputData = reactive<any>({
  stateName: '',
  stateItems: [],
  stateKey: [],
  rawId: 'STATE_TRX_RAWID'
  // rawId: props.type === 'user' ? 'stateTrxRawId' : 'stateExtRawId',
});
const loadingParams = reactive({
  isShow: false,
  hasButton: true,
  title: t('loading')
});
const drawingChart = (title?: string) => {
  loadingParams.title = t(title || 'common.loading.drawChart');
  loadingParams.isShow = true;
  loadingParams.hasButton = false;
  // console.log('drawingChart', loadingParams);
};
const resetLoadingParams = () => {
  loadingParams.isShow = false;
  loadingParams.hasButton = true;
  loadingParams.title = t('loading');
  // console.log('resetLoadingParams', loadingParams);
};
const items = reactive({
  options: [] as any,
  checked: [] as any,
  // checkedKeys: [] as any,
  // resData: {} as any,
  hasChartData: false,
  chartData: [] as any[],
  resData: {
    // ...mock?.detailHist,
    // chartData: mock?.detailHist?.chartData?.map((v: any) => {
    //     return {
    //         itemName: v.itemName,
    //         rawId: v[inputData.rawId],
    //         stateKeyValue: v.stateKeyValue,
    //         // seriesValue: v.data.map((e: any) => Math.random()),
    //         // seriesValue: v.data.map((e: any) => (Math.random() > 0.5 ? Math.random() : null)),
    //         // seriesValue: v.data.map((e: any) => parseFloat(e && e.replace(/\D/g, '')) || null),
    //         // seriesValue: v.data.map((e: any) => (e && parseFloat(e)) || null),
    //         seriesValue: v.data,
    //         timestamp: mock?.detailHist?.date,
    //         paramInfo: { legend: `${v.itemName}^${v.stateKeyValue || v[inputData.rawId]}` },
    //     };
    // }),
  } as any
});
const refill = () => {
  // console.log('selectedState', props.selectedState);
  inputData.stateName = props.selectedState?.stateName || props.selectedState?.title;
  // console.log(inputData);
  // console.log('selected', props.selected);
  // console.log('selectedCols', props.selectedCols);
  // items.options = items.checked = Object.keys(props.selected[0]?.stateValueMap);
  // options 需要使用 STATE VALUE ACTUAL(props.type === 'user')
  if (props.type === 'user') {
    items.options = items.checked = props.selectedCols['STATE VALUE ACTUAL'];
  } else {
    items.options = items.checked = props.selectedCols['STATE VALUE'];
  }
  // xTable.data = props.selected as any;
  // const keys = Object.keys(props.selected[0]?.stateKeyMap || {});
  const stateKeys = props.selected?.map((v: any) => {
    return {
      rawId: v[inputData.rawId],
      ...Object.fromEntries(
        props.selectedCols['STATE KEY'].map((e: any, index: number) => {
          // console.log(e, v[e]);
          // stateKeys 也需要使用 STATE KEY ACTUAL
          if (props.type === 'user') {
            return [props.selectedCols['STATE KEY ACTUAL'][index], v[e]];
          } else {
            return [e, v[e]];
          }
        })
      )
      // ...v,
      // ...v.stateKeyMap,
    };
  });
  // console.log('stateKeys', stateKeys);
  // row to column
  const columns: any = {};
  for (const row of stateKeys) {
    for (const key in row) {
      if (key !== 'rawId') {
        if (!(key in columns)) {
          columns[key] = { key };
        }
        columns[key][row.rawId] = row[key];
      }
    }
  }
  // console.log(columns);
  xTable.data = Object.values(columns);
  // xTable.data = props.selected?.map((v: any) => v.stateKeyMap);
  const keys = ['key', ...stateKeys.map((v: any) => v.rawId)];
  xTable.height = (xTable.data.length > 3 ? 3 : xTable.data.length) * 34.5 + 40.5;
  // console.log('row to col', stateKeys, xTable.data, keys, xTable.height);
  keys.forEach((k: any) => {
    checkedKeys[k] = true;
  });
  const cols: any[] = keys.map((v: any) => {
    return {
      field: v,
      title: v,
      sortable: false,
      minWidth: 180,
      // filters: [{ data: '' }],
      // filterMethod,
      align: 'center',
      slots: {
        header: 'headerCheck'
      }
    };
  });
  xTable.options.columns = cols;
  // items.checkedKeys = xTable.data.map((v: any) => v.stateKeyValue);
  // setTimeout(() => {
  //     xTable.data.forEach((v: any) => {
  //         xGrid.value?.setCheckboxRow(v, true);
  //     });
  // }, 100);
  // console.log('keyCols', cols);
};
const setColumn = async (resData: any) => {
  if (!resData.column) return;
  let cols: any = [];
  Object.entries(resData.column).forEach((item: any) => {
    // console.log('resCols', item);
    if (!['STATE KEY', 'STATE VALUE', 'STATE KEY ALIAS', 'STATE VALUE ALIAS'].includes(item[0])) {
      const minWidth = item[1].length * 16 > 100 ? item[1].length * 16 : 100;
      cols.push({
        field: item[1],
        title: item[0],
        // fixed: index ? '' : 'left',
        sortable: true,
        minWidth,
        filters: item[1] === 'RESET_YN' ? boolFilters : [],
        filterMethod,
        align: item[1] === 'RESET_YN' ? 'center' : 'left',
        type: item[1] === 'RESET_YN' ? 'columnCheckbox-default' : ''
      });
    } else {
      if (!['STATE KEY ALIAS', 'STATE VALUE ALIAS'].includes(item[0])) {
        cols.push({
          title: item[0],
          minWidth: 180,
          children: item[1].map((v: any, index: number) => {
            const minWidth = v.length * 16 > 100 ? v.length * 16 : 100;
            // 增加 displayName，后端字段的field需要处理
            let stateKeyValueAlias = '';
            if (item[0] == 'STATE KEY') {
              stateKeyValueAlias = 'STATE KEY ALIAS';
            } else if (item[0] == 'STATE VALUE') {
              stateKeyValueAlias = 'STATE VALUE ALIAS';
            }
            const stateKeyValueAliasColumn = resData.column[stateKeyValueAlias];
            console.log('setColumn----', stateKeyValueAliasColumn);
            return {
              field: v,
              title: (stateKeyValueAliasColumn && stateKeyValueAliasColumn[index]) || v,
              sortable: true,
              minWidth,
              filters: [],
              filterMethod
            };
          })
        });
      }
    }
  });
  dTable.options.columns = [...dTable.options.columns!.slice(0, 1), ...cols];
  // console.log('setColumn', cols);
  // xGrid.value?.reloadColumn(cols);
  setTimeout(() => {
    const columnFieldList = [
      ...Object.values(resData.column),
      ...resData.column['STATE KEY'],
      ...resData.column['STATE VALUE']
    ];
    // const columnFieldList = ['actionModeCd', ...resCols.key, ...resCols.value];
    dGrid.value &&
      setHeaderSelectFilter({
        xGrid: dGrid,
        columnFieldList,
        tableData: dTable.data
      });
  }, 100);
};
const setTable = () => {
  // console.log('setTable-items.resData', items.resData);
  if (!items.resData?.table?.length) showWarning('common.tip.noData');
  if (!items.resData?.column) return;
  if (!dTable.data.length) setColumn(items?.resData);
  dTable.totalPage = items?.resData?.count;
  dTable.data = [];
  dTable.data = items?.resData?.table || [];
  // ?.filter(
  //     (e: any) => items.checked.length && checkedKeys[e.STATE_TRX_RAWID]
  //     // (e: any) => items.checked.some((item: string) => e[item]) && checkedKeys[e.STATE_TRX_RAWID],
  // )
  // items?.resData?.table?.map((v: any) => {
  //     return {
  //         ...v,
  //         ...v.stateKeyMap,
  //         ...v.stateValueMap,
  //     };
  // }) || [];
};
const setChart = () => {
  // const data = mock.chartData;
  // console.log('setChart-items.resData?.chartData', items.chartData);
  /* const hasSome = items.resData?.chartData?.some((v: any) =>
        v?.seriesValue?.some((e: any) => e && !/^-?\d+(\.\d+)?$/g.test(e)),
    ); */
  // if (hasSome) return showWarning('common.tip.drawNumeric');
  // console.log('checkedKeys', items.checked, checkedKeys);
  const data = items.chartData?.filter(
    // (e: any) => items.checked.includes(e.itemName),
    // TODO chart 这里的filter，需要使用
    (e: any) => items.checked.includes(e.itemName) && checkedKeys[e.rawId]
    // (e: any) => items.checked.includes(e.itemName) && checkedKeys[e.stateKeyValue || e.rawId],
  );
  const rightParamList: any = [...new Set(data?.map((v: any) => v.itemName))];
  // console.log('setChart', data);
  items.hasChartData = data?.length > 0;
  data && xChart.value && xChart.value.getData({ data, rightParamList, count: 2, isEnd: true });
};
const getHistory = async (type: 'chart' | 'table') => {
  /* chart不传分页 */
  const startDate = dayjs(dataParams.startDate);
  const endDate = dayjs(dataParams.endDate);
  type === 'table' ? (dTable.loading = true) : drawingChart();
  const res = await (props.type === 'user'
    ? getUserStateViewerDetailHistory({
        headerParams: {
          log: 'Y',
          page: buttonIds.pageId,
          action: buttonIds.user.history
        },
        bodyParams: {
          type,
          limit: type === 'table' ? dTable.pageSize : undefined,
          page: type === 'table' ? dTable.currentPage : undefined,
          startDate: startDate.format('YYYY-MM-DD HH:mm:ss'),
          endDate: endDate.format('YYYY-MM-DD HH:mm:ss'),
          stateTrxRawIds: props.selected?.map((e: any) => e[inputData.rawId]),
          stateName: props.selectedState?.title
        }
      }).finally(() => {
        type === 'table' ? (dTable.loading = false) : resetLoadingParams();
      })
    : getStateViewerDetailHistory({
        headerParams: {
          log: 'Y',
          page: buttonIds.pageId,
          action: buttonIds.system.history
        },
        bodyParams: {
          type,
          limit: type === 'table' ? dTable.pageSize : undefined,
          page: type === 'table' ? dTable.currentPage : undefined,
          startDate: startDate.format('YYYY-MM-DD HH:mm:ss'),
          endDate: endDate.format('YYYY-MM-DD HH:mm:ss'),
          stateTrxRawIds: props.selected?.map((e: any) => e[inputData.rawId]),
          modelName: props.selectedState?.modelName,
          stateName: props.selectedState?.stateName
        }
      }).finally(() => {
        type === 'table' ? (dTable.loading = false) : resetLoadingParams();
      }));
  if (res.status !== 'SUCCESS') return;
  const resData = res?.data;
  if (type === 'table') {
    items.resData = resData;
    setTable();
  } else {
    items.chartData = resData?.chartData?.map((v: any) => {
      const keyEntries = Object.entries(v).filter((item: any) => {
        // console.log(item[0], inputData.rawId);
        return !['itemName', 'data', 'stateTrxRawId'].includes(item[0]);
      });
      let actualAlias = v.itemName;
      if (props.type === 'user') {
        const actualIndex = props.selectedCols['STATE VALUE'].indexOf(v.itemName);
        actualAlias = props.selectedCols['STATE VALUE ACTUAL'][actualIndex];
      }
      return {
        itemName: actualAlias,
        // stateKeyValue: v.stateKeyValue,
        keyEntries,
        // rawId: v[inputData.rawId],
        rawId: v.stateTrxRawId,
        // seriesValue: v.data.map((e: any) => Math.random()),
        // seriesValue: v.data.map((e: any) => (Math.random() > 0.5 ? Math.random() : null)),
        // seriesValue: v.data.map((e: any) => parseFloat(e && e.replace(/\D/g, '')) || null),
        // seriesValue: v.data.map((e: any) => (e && parseFloat(e)) || null),
        seriesValue: v.data,
        timestamp: resData?.date,
        paramInfo: {
          legend: `${actualAlias}^${keyEntries
            // .map((e: any) => `${e[0]}=${e[1]}`)
            .map((e: any) => e[1])
            .join('^')}`
        }
      };
    });
    setChart();
  }
};
const exportMethod = async () => {
  if (!dTable.data.length) return showWarning(t(`common.tip.exportCommonTip`));
  const startDate = dayjs(dataParams.startDate);
  const endDate = dayjs(dataParams.endDate);
  const res = await (props.type === 'user'
    ? exportUserStateViewerDetailHistory({
        headerParams: {
          log: 'Y',
          page: buttonIds.pageId,
          action: buttonIds.user.export
        },
        bodyParams: {
          exportFileName: `StateDetailHistory - ${inputData.stateName}`,
          startDate: startDate.format('YYYY-MM-DD HH:mm:ss'),
          endDate: endDate.format('YYYY-MM-DD HH:mm:ss'),
          stateTrxRawIds: props.selected?.map((e: any) => e[inputData.rawId]),
          stateName: props.selectedState?.title
        }
      })
    : exportStateViewerDetailHistory({
        headerParams: {
          log: 'Y',
          page: buttonIds.pageId,
          action: buttonIds.system.export
        },
        bodyParams: {
          exportFileName: `StateDetailHistory - ${inputData.stateName}`,
          startDate: startDate.format('YYYY-MM-DD HH:mm:ss'),
          endDate: endDate.format('YYYY-MM-DD HH:mm:ss'),
          stateTrxRawIds: props.selected?.map((e: any) => e[inputData.rawId]),
          modelName: props.selectedState?.modelName,
          stateName: props.selectedState?.stateName
        }
      }));
  if (res.status !== 'SUCCESS') return;
  exportToExcel(res.data);
};
const gridEvents = {
  toolbarToolClick: ({ code, tool }: any) => {
    // console.log(code, tool);
    const { menuClick, row } = tool;
    switch (code) {
      case 'exportData':
        exportMethod();
        break;
    }
  },
  pageChange: ({ currentPage, pageSize }: any) => {
    // console.log('pageChange', currentPage, pageSize);
    dTable.currentPage = currentPage;
    dTable.pageSize = pageSize;
    getHistory('table');
  }
};
const onSearch = () => {
  const startDate = dayjs(dataParams.startDate);
  const endDate = dayjs(dataParams.endDate);
  if (endDate.diff(startDate, 'days') > 30)
    return showWarning(t('common.tip.rangeDays', { n: 30 }));
  getHistory('chart');
  dTable.totalPage = 0;
  dTable.currentPage = 1;
  dTable.pageSize = 20;
  getHistory('table');
};
const handleChange = ({ index }: { index: number }) => {
  dataParams.tabIndex = index;
  if (!index) setChart();
};
const handleHeaderCheck = ({ checked, field }: any) => {
  console.log('handleHeaderCheck', checked, field);
  checkedKeys[field] = checked;
  setChart();
};
onBeforeMount(() => {
  refill();
  // setTimeout(() => {
  //     setChartTable();
  // }, 100);
});
onMounted(() => {
  setTimeout(() => {
    dGrid.value &&
      dGrid.value.setVisibleTools({
        authorityList: buttonAuthority.value.buttonList,
        flag: buttonAuthority.value.allButtonFlag
      });
  }, 300);
});
</script>

<template>
  <ees-sidebar v-if="show" @clickbutton="handleClick">
    <template #sidebar_content>
      <div class="detail-history">
        <a-form
          ref="xFormRef"
          class="detail-history-form"
          auto-complete="off"
          :colon="false"
          label-align="left"
        >
          <a-form-item :label="t('stateViewer.label.stateName')">
            <span class="span-text">{{ inputData.stateName }}</span>
          </a-form-item>
          <a-form-item :label="t('common.title.date')">
            <span class="search-row">
              <ees-custom-time
                ref="refTime"
                :bordered="true"
                :time-area="dataParams.timeAreaBind"
                :api-config="apiConfig"
                @time-pop="timePop"
                @timeStartEndChange="timeStartEndChange"
                @time-type-change="timeTypeChange"
              ></ees-custom-time>
              <a-button type="primary" class="search-btn" @click="onSearch">
                {{ t('common.btn.search') }}
              </a-button>
            </span>
          </a-form-item>
          <a-form-item :label="t('stateViewer.label.stateItems')">
            <a-checkbox-group
              v-model:value="items.checked"
              :options="items.options"
              @change="setChart"
            />
          </a-form-item>
          <a-form-item :label="t('stateViewer.label.stateKey')" class="none-item">
            <vxe-grid
              ref="xGrid"
              class="x-grid vxe-table-top-left-radius-xs vxe-table-top-right-radius-xs vxe-table-bottom-left-radius-xs vxe-table-bottom-right-radius-xs"
              :toolbar-config-border="false"
              v-bind="xTable.options"
              :data="xTable.data"
              :style="`height: ${xTable.height}px`"
            >
              <template #headerCheck="{ column }">
                <vxe-checkbox
                  v-if="column.field !== 'key'"
                  v-model="checkedKeys[column.field]"
                  @change="({ checked }) => handleHeaderCheck({ checked, field: column.field })"
                />
                <!-- {{ column.title }} -->
              </template>
            </vxe-grid>
          </a-form-item>
        </a-form>
        <div class="detail-history-content">
          <ees-tabs
            ref="xTabChange"
            :need-border-radius-array="[true, true, false, false]"
            :border-outline="[1, 1, 0, 1]"
            :border-radius="4"
            direction="horizontal"
            is-large-font
            v-model:active-item="dataParams.tabIndex"
            :tab-list="[
              { name: 'common.title.trendChart', id: 'trend-chart' },
              { name: 'common.title.dataView', id: 'data-view' }
            ]"
            @change-tab="handleChange"
          ></ees-tabs>
          <div
            v-show="!dataParams.tabIndex"
            v-isLoading="{ ...loadingParams }"
            class="tab-content trend-chart"
          >
            <Chart
              v-show="items.hasChartData"
              ref="xChart"
              :export-name="`StateDetailHistory_Chart - ${inputData.stateName}`"
              @showLoading="drawingChart"
              @hideLoading="resetLoadingParams"
            />
            <div v-show="!items.hasChartData" class="no-data-title">
              <ees-no-data />
            </div>
          </div>
          <div v-show="dataParams.tabIndex" class="tab-content data-view">
            <vxe-grid
              ref="dGrid"
              class="vxe-grid-pager-border-xs"
              v-bind="dTable.options"
              :show-header="dTable.data?.length > 0"
              :data="dTable.data"
              :loading="dTable.loading"
              :export-config="{
                filename: `StateDetailHistory - ${inputData.stateName}.xlsx`,
                id: buttonIds[props.type as string].export
              }"
              :pager-config="{
                layouts: ['Total', 'PrevPage', 'JumpNumber', 'NextPage', 'Sizes', 'FullJump'],
                align: 'right',
                pageSizes: [10, 15, 20, 25, 100],
                pageSize: dTable.pageSize,
                total: dTable.totalPage,
                currentPage: dTable.currentPage
              }"
              v-on="gridEvents"
            >
            </vxe-grid>
          </div>
        </div>
      </div>
    </template>
  </ees-sidebar>
</template>

<style lang="less" scoped>
@import url('@/assets/style/variable.less');
@ns: detail-history;

.@{ns} {
  height: 100%;

  :deep(.title-time) {
    display: none;
  }

  display: flex;
  flex-direction: column;

  &-form {
    border: 1px solid @border-color;
    padding: @padding-sm + 2px;
    background: @bg-group-color;
    border-radius: @border-radius-xs;
    position: relative;
  }

  &-content {
    flex-grow: 1;
    margin-top: @margin-xs;
    overflow: auto;
    display: flex;
    flex-direction: column;
    border-radius: @border-radius-xs @border-radius-xs 0 0;

    .tab-content {
      flex-grow: 1;
      width: 100%;
      height: calc(100% - 46px);
      border: 1px solid @border-color;
      border-top:none;
      border-radius: 0 0 @border-radius-xs @border-radius-xs;
      :deep(.vxe-grid--toolbar-wrapper){
        &.border{
          border-top: none;
        }
      }
    }
    .data-view {
      border: none;
    }
  }

  .span-text {
    color: @text-subtitle-color;
  }

  :deep(.xchart-marker) {
    white-space: normal;
  }

  :deep(.ant-form-item) {
    margin-bottom: @margin-sm - 2px;
    &.none-item {
      margin-bottom: 0;
    }
    .ant-form-item-label {
      label {
        width: 82px;
      }
    }
    .ant-row {
      align-items: baseline;
    }
  }
  .x-grid {
    border-radius: @border-radius-xs;
    overflow: hidden;
  }

  .search-row {
    display: flex;
  }

  .search-btn {
    width: 70px;
    margin-left: @margin-btn;
  }
  .no-data-title {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    pointer-events: auto;
    border-radius: 0 0 @border-radius-xs @border-radius-xs;
    background: @bg-block-color;
  }
  :deep(.ees-tabChange-horizontal-text) {
    border-radius: @border-radius-xs @border-radius-xs 0 0 !important;
  }
  :deep(.vxe-grid--pager-wrapper) {
    background-color: transparent;
  }
  :deep(.ees-tabChange-wrapper-horizontal .ees-tabChange-horizontal-text li .tab-name) {
    line-height: 22px;
    font-weight: @font-weight-md; //700;
  }
}
</style>
