import { useBaseStore, useCmstore } from '@futurefab/ui-sdk-stores';
const baseStore = useBaseStore();
import { showWarning } from '@futurefab/ui-sdk-ees-basic';
type SocketStatus = 'pending' | 'open' | 'reconnecting' | 'serverError' | 'close';
const application = 'wees-cm-service'; // 当前用户信息，后台配置
const from = 'WEB';
const connectURL =
    `${
        !baseStore.socketUrl ? window.sessionStorage.getItem('socketUrl') : baseStore.socketUrl
    }` + '/websocket';
// const connectURL = 'ws://192.168.0.35:18888/websocket';
interface RequestData {
    header: any;
    body: any;
}
interface Socket {
    uuid: string | null;
    websocket: any;
    status: SocketStatus;
    connectURL: string;
    hearbeat_timer: any;
    hearbeat_interval: number;
    is_reonnect: boolean;
    ronnect_number: number;
    reconnect_timer: any;
    reconnect_interval: number;
    pageKey?: string;
    reconnectCurrent: number;
    isClickBtn: boolean;
    init: (
        requestList: Array<RequestData> | RequestData | null,
        receiveMessage: Function | null,
        errorFun?: Function,
    ) => any;
    receive: (message: any) => void;
    heartbeat: () => void;
    send: (data: any, callback?: any) => void;
    close: () => void;
    reconnect: (receiveMessage: Function | null, errorFun?: Function) => void;
}

export const socket: Socket = {
    uuid: null,
    websocket: null,
    pageKey: '',
    // socket 状态
    status: 'pending',
    connectURL: '',
    // 心跳timer
    hearbeat_timer: null,
    // 心跳发送频率
    hearbeat_interval: 1000 * 30,
    // 是否自动重连
    is_reonnect: true,
    // 网络错误提示此时
    ronnect_number: 0,
    // 重连timer
    reconnect_timer: null,
    // 重连频率, 断开后5秒开始重连
    reconnect_interval: 5 * 1000,
    reconnectCurrent: 1,
    isClickBtn: false,
    init: (
        requestList: Array<RequestData> | RequestData | null,
        receiveMessage: Function | null,
        errorFun?: Function,
    ) => {
        socket.status = 'reconnecting';
        socket.is_reonnect = true;
        if (!('WebSocket' in window)) {
            showWarning('common.tip.noSocket');
            return null;
        }
        // 已经创建过连接不再重复创建
        // if (socket.websocket) {
        //     return socket.websocket;
        // }
        // const token = '88888888';
        const token = localStorage.getItem('token'); // 获取验证信息，后台配置
        socket.connectURL = `${connectURL}?application=${application}&token=${token}&from=${from}`;
        socket.websocket = null;
        console.log(connectURL);
        console.log(socket.connectURL);
        socket.websocket = new WebSocket(socket.connectURL);
        socket.websocket.onmessage = (e: any) => {
            const param = JSON.parse(e.data);
            if (param?.header.bsId === '200000') {
                socket.uuid = param.header.params.uuid;
            }
            //
            if (receiveMessage && socket.is_reonnect) {
                socket.reconnectCurrent = 0;
                receiveMessage(param);
            }
            if (socket.reconnect_timer) {
                socket.reconnectCurrent = 0;
                clearTimeout(socket.reconnect_timer);
            }
        };

        socket.websocket.onclose = (e: any) => {
            console.log('onclose');
            console.log(socket.pageKey);
            clearInterval(socket.hearbeat_timer);
            console.log(e);
            // 需要重新连接
            if (socket.is_reonnect) {
                clearTimeout(socket.reconnect_timer);
                console.log('onClosetoRecon');
                socket.reconnect_timer = setTimeout(() => {
                    socket.reconnectCurrent++;
                    socket.reconnect(receiveMessage, errorFun);
                }, socket.reconnect_interval);
            }
        };

        // 连接成功
        socket.websocket.onopen = function () {
            socket.status = 'open';
            console.log('ws onopen');
            console.log(socket.websocket);
            clearTimeout(socket.reconnect_timer);
            if (requestList && Array.isArray(requestList) && requestList.length > 0) {
                requestList.forEach(item => {
                    socket.send(item);
                });
            } else if (requestList && Object.keys(requestList).length > 0) {
                socket.send(requestList);
            }

            // 开启心跳
            socket.heartbeat();
        };
        // 连接发生错误
        socket.websocket.onerror = function (err: any) {
            if (!socket.is_reonnect) return;
            socket.status = 'serverError';
            console.log('连接发生错误', err);
            if (navigator.onLine) {
                if (socket.isClickBtn || socket.reconnectCurrent == 1) {
                    errorFun && errorFun('common.tip.connectError');
                }
            } else {
                if (socket.reconnectCurrent == 1) {
                    errorFun && errorFun('vxe.httpError.error8');
                }
            }
        };
    },

    send: (data, callback = null) => {
        // const token = '88888888';
        const token = localStorage.getItem('token'); // 获取验证信息，后台配置
        const params = {
            header: {
                from,
                token,
                ...data.header,
                params: {
                    uuid: socket.uuid,
                },
            },
            body: Array.isArray(data.body)
                ? data.body
                : {
                      ...data.body,
                  },
        };
        socket.isClickBtn = true;
        // 开启状态直接发送
        if (socket.websocket.readyState === socket.websocket.OPEN) {
            socket.websocket.send(JSON.stringify(params));
            if (callback) {
                callback();
            }
            // 正在开启状态，则等待1s后重新调用
        } else {
            clearInterval(socket.hearbeat_timer);
            console.log('正在开启状态，则等待1s后重新调用', socket.status);
            socket.ronnect_number++;
        }
    },

    receive: (message: any) => {
        let params = JSON.parse(message.data).data;
        params = JSON.parse(params);
        return params;
    },

    heartbeat: () => {
        if (socket.hearbeat_timer) {
            clearInterval(socket.hearbeat_timer);
        }
        socket.hearbeat_timer = setInterval(() => {
            const data = {
                header: {
                    heartBeat: 'Y',
                },
            };
            socket.send(data);
        }, socket.hearbeat_interval);
    },

    close: () => {
        clearInterval(socket.hearbeat_timer);
        clearTimeout(socket.reconnect_timer);
        socket.is_reonnect = false;
        console.log('oldsocket close');
        socket.status = 'close';
        socket.websocket.close();
    },

    /**
     * 重新连接
     */
    reconnect: (receiveMessage: Function | null, errorFun?: Function) => {
        if (!socket.is_reonnect) return;
        console.log('reconnect', socket.reconnectCurrent);
        if (socket.websocket && !socket.is_reonnect) {
            socket.close();
        }
        if (socket.reconnectCurrent == 1) {
            errorFun && errorFun('common.tip.dataError');
        }
        socket.isClickBtn = false;
        socket.init(null, receiveMessage, errorFun);
        socket.status = 'reconnecting';
    },
};
