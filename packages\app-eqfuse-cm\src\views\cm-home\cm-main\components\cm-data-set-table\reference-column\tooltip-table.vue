<script setup lang="ts">
import { ref, watchEffect } from 'vue';
import { VXETable } from '@futurefab/vxe-table';
import type {
  ParameterLineType,
  ParameterLinesType,
  ParameterTableLine,
  GroupDataType,
  PercentageInfo
} from './interface';
import { CloseOutlined } from '@ant-design/icons-vue';

interface Props {
  parameterName: string;
  resultData: ParameterLinesType<ParameterLineType>[] | GroupDataType;
  width: number;
  isUnMatch?: boolean;
  placement?: string;
  trigger?: string;
}
const props = withDefaults(defineProps<Props>(), {
  parameterName: '',
  resultData: (): ParameterLinesType<ParameterLineType>[] | GroupDataType => [],
  isUnMatch: false,
  placement: 'topLeft',
  trigger: 'click'
});
const xGrid = ref(null);
const getOption = () => {
  const option = VXETable.tableFun.tableDefaultConfig({
    columns: [
      {
        field: 'recipeStepId',
        title: 'cm.field.recipeStep',
        minWidth: 70,
        sortable: false,
        filters: false
      },
      {
        field: 'matchingCountTotal',
        title: 'cm.field.matchingCount',
        minWidth: 70,
        sortable: false,
        filters: false
      },
      {
        field: 'matchingPercentage',
        title: 'cm.field.matchingPercentage',
        minWidth: 70,
        sortable: false,
        filters: false
      }
    ]
  });
  option.columns.shift();
  if (props?.isUnMatch) {
    option.columns.unshift({
      field: 'parameterName',
      title: 'cm.field.parameter',
      minWidth: 150,
      sortable: false,
      filters: false
    });
  }
  return option;
};
const open = ref<boolean>(false);
const gridData = ref<ParameterTableLine[]>([]);
// step
const unMatchSteps = ref<string[]>([]);
const warningSteps = ref<string[]>([]);
const missingSteps = ref<string[]>([]);
// 参数
const unMatchParameters = ref<string[]>([]);
const warningParameters = ref<string[]>([]);
const missingParameters = ref<string[]>([]);
const rowClassName = (rowInfo: any) => {
  const row = rowInfo.row;
  if (props?.isUnMatch) {
    // 所有不match的parameter
    if (unMatchParameters.value?.includes(row.parameterName)) {
      if (row?.isUnMatchingTop5) {
        return 'un-match';
      }
      return 'un-match-default';
    }
    if (warningParameters.value?.includes(row.parameterName)) {
      return 'warning';
    }
    if (missingParameters.value?.includes(row.parameterName)) {
      return 'missing';
    }
    return 'match';
  } else {
    // 所有不match的step
    if (unMatchSteps.value?.includes(row.recipeStepId)) {
      return 'un-match';
    }
    if (warningSteps.value?.includes(row.recipeStepId)) {
      return 'warning';
    }
    if (missingSteps.value?.includes(row.recipeStepId)) {
      return 'missing';
    }
    return 'match';
  }
};
const sortByCriteria = (array: any) => {
  array?.sort((a: any, b: any) => {
    // 首先处理 totalCount === 0 的情况（miss 排在最后）
    if (a.totalCount === 0 && b.totalCount !== 0) {
      return 1;
    }
    if (b.totalCount === 0 && a.totalCount !== 0) {
      return -1;
    }

    // 比较 matchingPercentage
    if (a.matchingPercentage < b.matchingPercentage) {
      return -1;
    }
    if (a.matchingPercentage > b.matchingPercentage) {
      return 1;
    }

    // 如果 matchingPercentage 相等，则比较 recipeStepId
    if (Number(a.recipeStepId) < Number(b.recipeStepId)) {
      return -1;
    }
    if (Number(a.recipeStepId) > Number(b.recipeStepId)) {
      return 1;
    }

    // 如果两个条件都相等，则保持原顺序
    return 0;
  });
  return array;
};
const handleTooltipData = () => {
  if (props?.isUnMatch) {
    // 选出所有的unMatch
    const groupLineData = (props.resultData as ParameterLinesType<ParameterLineType>[])?.filter(
      (item: ParameterLineType) =>
        (item.unmatchingRecipeSteps && item.unmatchingRecipeSteps.length > 0) ||
        (item.warningRecipeSteps && item.warningRecipeSteps.length > 0)
    );
    gridData.value = groupLineData.map((item: ParameterLineType) => {
      let unMatchLine: PercentageInfo = {} as PercentageInfo;
      if (item.unmatchingRecipeSteps && item.unmatchingRecipeSteps.length > 0) {
        const lineData = item.unmatchingRecipeSteps[0];
        lineData.matchingCountTotal = lineData.matchingCount + '/' + lineData.totalCount;
        unMatchLine = lineData;
        unMatchParameters.value.push(item.parameterName);
      } else if (item.warningRecipeSteps && item.warningRecipeSteps.length > 0) {
        const lineData = item.warningRecipeSteps[0];
        lineData.matchingCountTotal = lineData.matchingCount + '/' + lineData.totalCount;
        unMatchLine = lineData;
        warningParameters.value.push(item.parameterName);
      } else if (item.missingRecipeSteps && item.missingRecipeSteps.length > 0) {
        const lineData = item.missingRecipeSteps[0];
        lineData.matchingCountTotal = lineData.matchingCount + '/' + lineData.totalCount;
        unMatchLine = lineData;
        missingParameters.value.push(item.parameterName);
      }
      unMatchLine.isUnMatchingTop5 = item.isUnMatchingTop5;
      return {
        ...unMatchLine,
        parameterName: item.parameterName
      };
    });
    // 排序
    gridData.value = sortByCriteria(gridData.value);
  } else {
    const groupLineData = props.resultData as GroupDataType;
    gridData.value = groupLineData?.recipeStepSummaryItems?.map((item: any) => {
      item.matchingCountTotal = item.matchingCount + '/' + item.totalCount;
      return item;
    });
    unMatchSteps.value = groupLineData?.unmatchingRecipeSteps?.map(
      (item: any) => item.recipeStepId
    );
    warningSteps.value = groupLineData?.warningRecipeSteps?.map((item: any) => item.recipeStepId);
    missingSteps.value = groupLineData?.missingRecipeSteps?.map((item: any) => item.recipeStepId);
    // 排序
    gridData.value = sortByCriteria(gridData.value);
  }
};

const handleOpen = (value: boolean) => {
  open.value = value;
};

watchEffect(() => handleTooltipData());
</script>

<template>
  <a-tooltip
    v-model:open="open"
    :placement="props.placement"
    :trigger="props.trigger"
    :destroy-tooltip-on-hide="true"
    :auto-adjust-overflow="true"
    :overlay-style="{ maxWidth: `${props.width}px`, zIndex: 13 }"
    :color="`var(--bg-panel-color)`"
    :mouse-enter-delay="0.6"
  >
    <template #title>
      <div class="tooltip-table-warp">
        <div class="title">
          <p>{{ parameterName }}</p>
          <CloseOutlined @click="handleOpen(false)" />
        </div>
        <div class="table-warp">
          <vxe-grid
            :id="parameterName"
            ref="xGrid"
            :height="'auto'"
            :max-height="gridData?.length > 0 ? 260 : NaN"
            v-bind="getOption()"
            :data="gridData"
            :row-class-name="rowClassName"
          />
        </div>
      </div>
    </template>

    <slot name="tooltipBtn" :click-event="handleOpen"></slot>
  </a-tooltip>
</template>

<style lang="less" scoped>
@import url('@/assets/style/variable.less');
.tooltip-table-warp {
  min-width: 300px;
  padding: 8px 6px;
  .title {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    vertical-align: middle;
    padding: 0 0 10px 0;
    width: 100%;
    font-weight: 600;
    color: @text-title-color;
  }
  .table-warp {
    min-height: 260px;
    :deep(.vxe-body--column) {
      height: 36px !important;
    }
    :deep(.match) {
      color: @text-title-color;
      padding: 0 !important;
      cursor: pointer;
      background-color: @fcm-import-added-bg;
    }
    :deep(.un-match-default) {
      padding: 0 !important;
      cursor: pointer;
      background-color: @fcm-import-deleted-bg;
    }
    :deep(.un-match) {
      color: @fcm-import-deleted-text;
      padding: 0 !important;
      cursor: pointer;
      background-color: @fcm-import-deleted-bg;
    }
    :deep(.warning) {
      color: @text-title-color;
      padding: 0 !important;
      cursor: pointer;
      background-color: @fcm-import-changed-bg;
    }
    :deep(.missing) {
      color: @text-title-color;
      padding: 0 !important;
      cursor: pointer;
      background-color: @bg-disabled-color;
    }
  }
}
</style>
