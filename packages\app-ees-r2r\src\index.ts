import VueComponent from './app.vue';
import VXETable from '@futurefab/vxe-table';
import Antd from '@futurefab/ant-design-vue';
// import '@/assets/style/pie-chart-tooltip.less';
// import '@futurefab/ui-sdk-ees-basic/dist/style.css';
import VMdPreview from '@kangc/v-md-editor/lib/preview';
import '@kangc/v-md-editor/lib/style/preview.css';
import githubTheme from '@kangc/v-md-editor/lib/theme/vuepress.js';
import '@kangc/v-md-editor/lib/theme/style/vuepress.css';
import '@/assets/style/common.less';

VMdPreview.use(githubTheme);

export default VueComponent;
export const plugins = [VXETable, Antd];
