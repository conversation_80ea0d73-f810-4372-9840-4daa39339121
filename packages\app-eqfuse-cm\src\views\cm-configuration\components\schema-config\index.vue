<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';
import { t, VXETable } from '@futurefab/vxe-table';
import {
  addDefaultMeta,
  deleteSchemaMeta,
  getSchemaMetaList,
  saveSchemaMeta
} from '@futurefab/ui-sdk-api';
import { cmMainInfo as buttonIds } from '@/log-config';
import { showConfirm, showError, successInfo } from '@futurefab/ui-sdk-ees-basic';

let xGrid = ref();
// 数组第一个下标表示是否是表单类型：1表示switch开关，2表示输入框，3表示数字输入框
// ，第二个表示描述文本
const metaColumnsDes = {
  sort: [3, '排序数字'],
  csvIndex: [3, 'CSV中列的下标'],
  columnName: [2, '列的名称'],
  dbTableColumnName: [2, 'SqLite表中列的名称'],
  eesTableColumnName: [2, 'EES表中列的名称'],
  name: [2, '列的别名'],
  timeFormat: [2, '时间格式'],
  time: [1, '表示时间的列'],
  chamber: [1, '表示Chamber的列'],
  hide: [1, '解析Wafer列表时表头是否隐藏'],
  loop: [1, '表示Context中的loop'],
  loopNo: [1, '表示Loop的属性名称的列'],
  loopStep: [1, '表示Step-Loop的复合列'],
  recipe: [1, '表示Recipe的列'],
  tool: [1, '表示Tool的列'],
  waferKey: [1, '表示Wafer的列'],
  trimming: [1, '属于修剪的列']
};
let options = VXETable.tableFun.tableDefaultConfig({
  toolbarConfig: {
    tableName: 'SchemaConfig',
    import: false,
    export: false,
    refresh: true,
    tools: [
      ...VXETable.tableFun.getToolsButton([
        {
          id: buttonIds.add,
          name: 'common.btn.add',
          icon: 'icon-btn-add',
          visible: true
        },
        {
          id: buttonIds.delete,
          name: 'common.btn.delete',
          icon: 'icon-btn-delete',
          visible: true
        }
      ])
    ]
  },
  columns: [
    {
      type: 'checkbox'
    },
    {
      field: 'schemaMetaId',
      title: 'schemaMetaId',
      minWidth: 120,
      sortable: true
    },
    {
      field: 'metaCategoryName',
      title: 'metaCategoryName',
      minWidth: 120,
      sortable: true,
      filterRender: { name: '$input' },
      editRender: {
        name: '$select',
        events: {
          change: (p: any) => {
            saveMetaColumn(p.row, true);
          }
        },
        immediate: true,
        options: [
          { label: 'context', value: 'context' },
          { label: 'parameter', value: 'parameter' },
          { label: 'attribute', value: 'attribute' },
          { label: 'descriptor', value: 'descriptor' }
        ]
      },
      filters: [{ data: '' }]
    },
    {
      field: 'metaKeyName',
      title: 'metaKeyName',
      minWidth: 260,
      editRender: {
        name: '$input',
        events: {
          blur: (p: any) => {
            saveMetaColumn(p.row, true);
          }
        },
        immediate: true
      },
      sortable: true
    },
    {
      field: 'sort',
      title: '排序',
      minWidth: 120,
      editRender: {
        name: '$input',
        events: {
          blur: (p: any) => {
            saveMetaColumn(p.row, true);
          }
        },
        immediate: true
      },
      sortable: true
    },
    {
      field: 'csvIndex',
      title: 'csv列下标',
      minWidth: 120,
      editRender: {
        name: '$input',
        events: {
          blur: (p: any) => {
            saveMetaColumn(p.row, true);
          }
        },
        immediate: true
      },
      sortable: true
    },
    {
      field: 'metaValueJson',
      title: 'metaValueJson',
      slots: { default: 'metaValueJson' },
      minWidth: 120,
      sortable: true
    },
    {
      field: 'createDtts',
      title: 'createDtts',
      minWidth: 120,
      sortable: true
    },
    {
      field: 'createBy',
      title: 'createBy',
      minWidth: 120,
      sortable: true
    }
  ]
});
let d = reactive({
  data: []
});
let add = () => {
  addDefaultMeta({
    bodyParams: {},
    headerParams: {
      log: 'Y',
      page: buttonIds.pageId,
      action: 'add-default-meta'
    }
  }).then((res: any) => {
    if (res.data) {
      getData();
      successInfo(res.msg == null ? 'common.tip.operationSuccess' : res.msg, false);
    } else {
      showError(res.msg == null ? 'common.tip.operationFail' : res.msg, false);
    }
  });
};

let getData = () => {
  console.log('触发了');
  getSchemaMetaList({
    bodyParams: {},
    headerParams: {
      log: 'Y',
      page: buttonIds.pageId,
      action: 'get-schema-meta-list'
    }
  }).then((res: { data: any }) => {
    if (res.data) {
      d.data = res.data;
    }
    console.log('d.data', d.data);
  });
};
let dealDelete = (checkboxRecords: any[]) => {
  // 二次确认弹窗
  showConfirm({
    msg: t('common.tip.deleteConfirm', { total: checkboxRecords.length as any })
  }).then((type: string) => {
    if (type !== 'confirm') return;
    console.log('checkboxRecords:', checkboxRecords);
    deleteSchemaMeta({
      bodyParams: checkboxRecords.map((e: { schemaMetaId: any }) => e.schemaMetaId),
      headerParams: {
        log: 'Y',
        page: buttonIds.pageId,
        action: 'delete-default-meta'
      }
    }).then((res: any) => {
      getData();
    });
  });
};
onMounted(() => {
  getData();
});

const events = {
  toolbarToolClick: ({ code }: { code: string }) => {
    const checkboxRecords = xGrid.value && xGrid.value.getCheckboxRecords();
    xGrid.value?.clearValidate();
    switch (code) {
      case 'refresh':
        getData();
        break;
      case 'add':
        add();
        break;
      case 'delete':
        dealDelete(checkboxRecords);
        break;
    }
  }
};
let saveMetaColumn = (row: any, changeMetaValueJson: boolean) => {
  if (changeMetaValueJson) {
    row.metaValueJson.csvIndex = row.csvIndex;
    row.metaValueJson.sort = row.sort;
  } else {
    row.csvIndex = row.metaValueJson.csvIndex;
    row.sort = row.metaValueJson.sort;
  }
  console.log('row', row);
  saveSchemaMeta({
    bodyParams: row,
    headerParams: {
      log: 'Y',
      page: buttonIds.pageId,
      action: 'save-schema-meta'
    }
  }).then((res: any) => {
    if (res.data) {
      successInfo(res.msg == null ? 'common.tip.operationSuccess' : res.msg, false);
    }
  });
};
</script>
<template>
  <div style="display: flex; height: 100%; flex-direction: column">
    <vxe-grid
      style="flex: 1"
      v-bind="options"
      ref="xGrid"
      :data="d.data"
      v-on="events"
      :edit-config="{
        trigger: 'dblclick',
        mode: 'cell',
        showStatus: false,
        showStatusBorder: true
      }"
    >
      <template #metaValueJson="{ row }">
        <a-popover trigger="click">
          <template #content>
            <a-space direction="vertical">
              <a-space v-for="i in Object.keys(metaColumnsDes)" :key="i">
                <div class="meta-column-label-width">
                  {{ (metaColumnsDes as any)[i][1] }}
                </div>
                <a-switch
                  v-if="(metaColumnsDes as any)[i][0] == 1"
                  v-model:checked="row.metaValueJson[i]"
                  checked-children="是"
                  un-checked-children="否"
                />
                <a-input
                  v-if="(metaColumnsDes as any)[i][0] == 2"
                  size="small"
                  v-model:value="row.metaValueJson[i]"
                />
                <a-input-number
                  v-if="(metaColumnsDes as any)[i][0] == 3"
                  v-model:value="row.metaValueJson[i]"
                  size="small"
                />
              </a-space>
              <div style="text-align: right">
                <a-button size="small" type="primary" @click="saveMetaColumn(row, false)"
                  >保存</a-button
                >
              </div>
            </a-space>
          </template>
          <span>
            <i :class="['iconfont icon-btn-setting']"></i>
          </span>
        </a-popover>
      </template>
    </vxe-grid>
  </div>
</template>
<style scoped lang="less">
.meta-column-label-width {
  width: 170px;
  text-align: right;
}
</style>
