:root {
    /* 主色颜色值 */
    --primary-color-1: #e6f7ff;
    --primary-font-color-3: #1d2129;
    --on-primary-color-1: #262626;
    --on-primary-color-2: #ffffff;

    /**  主色调背景颜色  **/
    --body-bg-color: #e7e8eb;
    --bg-color: #ffffff;

    /**  已选筛选条件  **/
    --tag-br-color: #d1d1d1;

    /* search框颜色 */
    --input-error-font-color: #ff4d4f;
    --input-br-color: #d9d9d9;
    --input-bg-color: #ffffff;
    --input-disabled-bg-color: #f5f5f5;
    --input-font-color: #000000;
    --font-placeholder-color: #98a0c0;
    --select-multiple-bg-color: #f5f5f5; //多选已选项背景色

    /**  表格颜色  **/
    --th-bg-color: #e8e9ec;
    --th-font-color: #000000;
    --td-font-color: #606266;
    --td-br-color: #e8eaec;
    --tr-even-bg-color: #ffffff; //表格tr偶数背景色

    /* 弹框  **/
    --model-bg-color: #ffffff; //弹框背景颜色

    /**  checked框颜色  **/
    --check-default-br-color: #d9d9d9; //check默认边框

    /**  边框色 */
    --br-default-color: #e1e6ed;
    --br-default-color-1: #f0f0f0;

    /**  card块颜色  **/
    --card-default-br-color: #d8d8d8;

    /* 高亮背景 */
    --active-bg-color: rgb(47 152 249 / 50%);
}
