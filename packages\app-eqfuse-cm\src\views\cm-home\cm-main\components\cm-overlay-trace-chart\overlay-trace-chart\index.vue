<script setup lang="ts">
import { type ChartOptions, XChartVue, XChart, type AlignedData } from '@xchart/vue';
import { ref, shallowRef, reactive, watch, onUnmounted } from 'vue';
import { useChartDomain } from '@futurefab/ui-sdk-ees-charts';
import { useSelfFullScreen } from '@/utils/tools';
import { formatToXChart } from '@/utils';
import { getTraceOptions, setNormalization } from '../../cm-result-chart/trace-chart/config';
import { GroupConfigVO } from '@/model/GroupConfigVO';
import { cloneDeep } from 'lodash-es';
import NoData from '@/views/cm-home/cm-main/add-dataset/common-grid/no-data.vue';
import { TRACE_CHART_X_AXIS_TYPE } from '@/constant/charts';
import { debounce } from 'lodash-es';

interface Props {
  groupId: string[];
  chartData: any;
  groupData: any;
  selectWaferData: any[];
  parameterLegend: any[];
  parameterAndColor: any[];
  commonGroupConfigVO: GroupConfigVO;
  xAxisValue: string;
  normalization: boolean;
  type?: string;
  isDeleteBlank?: boolean;
  isShowRightY?: boolean;
  showRightYValue?: string[];
  filterStep: string[];
  stepInfo: any[];
  // Y轴范围设置相关props
  customYAxisRange?: {
    left?: { yMin?: number; yMax?: number };
    right?: { yMin?: number; yMax?: number };
  };
}

// Y轴双击事件相关接口
interface YAxisDoubleClickEvent {
  (chart: any, axes: any): void;
}

const props = withDefaults(defineProps<Props>(), {
  chartData: null,
  groupData: null,
  selectWaferData: () => [],
  parameterLegend: () => [],
  parameterAndColor: () => [],
  commonGroupConfigVO: () => new GroupConfigVO(),
  xAxisValue: 'step_count_slot',
  normalization: false,
  type: 'Overlay',
  isDeleteBlank: false,
  isShowRightY: false,
  showRightYValue: () => [],
  filterStep: () => [],
  stepInfo: () => [],
  customYAxisRange: () => ({})
});

// 定义emit事件
const emit = defineEmits<{
  (e: 'y-axis-double-click', axes: any): void;
}>();

// ==================== 响应式状态 ====================
const showModel = defineModel('showModel');
let xChartDOM = shallowRef<XChart>();
const oneChartData = reactive({
  xOption: {} as ChartOptions,
  data: [] as AlignedData,
  seriesColor: [] as string[]
});
const threeChatsDiv = ref<HTMLDivElement>();
const traceRef = ref();
const chartLoading = ref(false);

// ==================== 全屏相关 ====================
const { isFullscreen, toggle } = useSelfFullScreen(traceRef);

// ==================== 图表配置 ====================
const copyChartConfig = {
  id: 'overlay-trace-chart' + Math.random(),
  name: 'overlay-trace-chart'
};

// ==================== 工具函数 ====================
const getChartSize = () => {
  let width = 0;
  let height = 0;

  if (isFullscreen.value) {
    width = traceRef.value && traceRef.value.offsetWidth;
    height = traceRef.value && traceRef.value.offsetHeight - 104;
  } else {
    width = threeChatsDiv.value?.offsetWidth || 0;
    height = threeChatsDiv.value?.offsetHeight ? threeChatsDiv.value?.offsetHeight - 40 : 0;
  }

  return { width, height };
};

// ==================== Y轴双击事件处理 ====================
/**
 * 处理Y轴双击事件
 * @param chart 图表实例
 * @param axes 轴信息
 */
const onYAxisDoubleClick = (chart: any, axes: any) => {
  // 触发emit事件，通知父组件处理Y轴双击
  emit('y-axis-double-click', axes);
};

// ==================== 图表初始化 ====================
const initChart = debounce(async () => {
  chartLoading.value = true;
  try {
    // 先销毁旧的图表实例
    if (xChartDOM.value && typeof xChartDOM.value.destroy === 'function') {
      try {
        xChartDOM.value.destroy();
      } catch (error) {
        console.error('销毁overlay图表实例失败:', error);
      }
    }

    let selectWaferLoop = [];
    if (props.commonGroupConfigVO?.isLoop) {
      selectWaferLoop = props.selectWaferData?.map((item: any) => item.loopNo);
    } else {
      selectWaferLoop = props.selectWaferData?.map((item: any) => item.waferId);
    }
    // 先过滤出可见的legend项
    const visibleLegends = props.parameterLegend.filter((item) => item.show !== false);

    const groupConfigColor = props?.parameterAndColor?.map(({ id, name, chartLegendColor }) => ({
      id,
      name,
      chartLegendColor
    }));

    const cloneDeepChartData = cloneDeep(props.chartData);

    const { width, height } = getChartSize();

    let normalizationValue: any[] = [];
    if (props.normalization) {
      normalizationValue = await setNormalization(
        cloneDeepChartData,
        { type: 'Group', filterKeys: [] },
        visibleLegends
      );
    }

    const { xChartData, seriesColor, xAxisMinMax, yAxisMinMax, tooltipData, newChartData, xMap } =
      await formatToXChart({
        initChartData: cloneDeepChartData,
        xyKey: TRACE_CHART_X_AXIS_TYPE.find(({ value }) => value === props.xAxisValue)?.XYGroup || [
          'count_slot',
          'VALUE'
        ],
        groupConfigColor,
        normalizationValue,
        filterInfo: visibleLegends.map((item: any) => item.name || item),
        chartLegendConfig: null,
        customLegendItems: visibleLegends,
        overlay: true,
        selectWaferLoop,
        groupId: props.groupId,
        commonGroupConfigVO: props.commonGroupConfigVO,
        isDeleteBlank: props.isDeleteBlank,
        filterStep: props.filterStep.length === props.stepInfo?.length ? [] : props.filterStep
      });

    oneChartData.data = xChartData;
    oneChartData.seriesColor = seriesColor;

    // 计算自定义Y轴范围
    let customYAxisMinMax: [number | null, number| null] | null = null;
    if (
      props.customYAxisRange?.left?.yMin !== undefined ||
      props.customYAxisRange?.left?.yMax !== undefined
    ) {
      customYAxisMinMax = [
        props.customYAxisRange.left.yMin ?? yAxisMinMax[0],
        props.customYAxisRange.left.yMax ?? yAxisMinMax[1]
      ];
    }

    // 计算右侧Y轴自定义范围
    let customRightYRange: [number, number] | undefined;
    if (props.isShowRightY && props.showRightYValue && props.showRightYValue.length > 0) {
      // 只有在有自定义范围时才传递customRightYRange
      if (
        props.customYAxisRange?.right?.yMin !== undefined ||
        props.customYAxisRange?.right?.yMax !== undefined
      ) {
        // 使用完整的原始数据计算右侧Y轴基础范围
        const rightYData = cloneDeepChartData
          .filter((item: any) => props.showRightYValue.includes(item.paramAlias))
          .flatMap((item: any) => item.VALUE || []);

        if (rightYData.length > 0) {
          const rightYMinMax = [Math.min(...rightYData), Math.max(...rightYData)];
          const baseRightYRange: [number, number] = [rightYMinMax[0], rightYMinMax[1]];

          // 应用自定义范围
          customRightYRange = [
            props.customYAxisRange.right.yMin ?? baseRightYRange[0],
            props.customYAxisRange.right.yMax ?? baseRightYRange[1]
          ];
        }
      }
    }

    oneChartData.xOption = await getTraceOptions({
      contextIndex: {},
      chartData: oneChartData,
      model: props.xAxisValue,
      groupData: props?.groupData,
      currentRowInfo: [],
      xChartDOM,
      width,
      height,
      originData: newChartData,
      xAxisMinMax,
      yAxisMinMax: yAxisMinMax,
      tooltipData,
      commonGroupConfigVO: props.commonGroupConfigVO,
      overlay: true,
      isDeleteBlank: props.isDeleteBlank,
      xMap,
      isShowRightY: props.isShowRightY,
      showRightYValue: props.showRightYValue,
      filterStep: props.filterStep.length === props.stepInfo?.length ? [] : props.filterStep,
      rightYAxisData: cloneDeepChartData,
      customRightYRange: customRightYRange,
      customLeftYRange: customYAxisMinMax
    });

    oneChartData.xOption.hooks!.setSeries = [
      (chart, i, opts) => {
        if (!opts.show) {
          useChartDomain(chart, 'markData', true);
        }
      }
    ];

    oneChartData.xOption.hooks!.draw = [
      () => {
        useChartDomain(xChartDOM.value as any, 'markData', true);
      }
    ];

    // 添加Y轴双击事件
    oneChartData.xOption.hooks!.axisDoubleClick = [
      (chart: any, axes: any) => {
        onYAxisDoubleClick(chart, axes);
      }
    ];
  } catch (error) {
    console.error('初始化overlay图表失败:', error);
  } finally {
    // 等待图表渲染完成后结束 loading
    await new Promise((resolve) => setTimeout(resolve, 100));
    chartLoading.value = false;
  }
}, 300);

// ==================== 监听器 ====================
watch(
  [
    () => props.normalization,
    () => props.xAxisValue,
    () => props.chartData,
    () => JSON.stringify(props.parameterLegend),
    () => props.selectWaferData,
    () => props.isDeleteBlank,
    () => props.customYAxisRange,
    showModel
  ],
  () => {
    if (showModel.value) {
      if (props.chartData?.length > 0) {
        chartLoading.value = true;
        initChart();
      } else {
        oneChartData.data = [];
      }
    }
  },
  { immediate: true, flush: 'post' }
);

// ==================== 生命周期 ====================
onUnmounted(() => {
  if (xChartDOM.value && typeof xChartDOM.value.destroy === 'function') {
    try {
      xChartDOM.value.destroy();
    } catch (error) {
      console.error('组件卸载时销毁overlay图表实例失败:', error);
    }
  }
});

defineExpose({
  initChart
});
</script>

<template>
  <div ref="threeChatsDiv" class="overlay-chart">
    <!-- Loading 状态 -->
    <div
      v-if="chartLoading"
      class="chart-loading"
      style="
        display: flex;
        align-items: center;
        justify-content: center;
        flex: 1;
        width: 100%;
        height: 100%;
        background: #f5f5f5;
      "
    >
      <a-spin size="large" />
    </div>

    <!-- 图表内容 -->
    <div v-else-if="oneChartData.data.length > 0 && props.parameterAndColor.length > 0">
      <div :id="copyChartConfig.id" class="xchart-draw">
        <XChartVue
          :options="oneChartData.xOption"
          :data="oneChartData.data"
          @create="(chart: any) => (xChartDOM = chart)"
        />
      </div>
    </div>

    <!-- 无数据状态 -->
    <div v-else class="no-data"><NoData :border="false"></NoData></div>
  </div>
</template>

<style lang="less" scoped>
@import url('@/assets/style/variable.less');
.overlay-chart {
  width: 100%;
  height: 100%;
  display: flex;
  border: 1px solid @border-color;

  .factor-select {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .no-data {
    display: flex;
    flex: 1;
    justify-content: center;
    align-items: center;
    font-size: 16px;
    font-weight: 700;
    color: @td-font-color;
    text-align: center;
  }
}
</style>
