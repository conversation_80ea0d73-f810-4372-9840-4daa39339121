import type { Ref } from 'vue';
import { onMounted, onUnmounted } from 'vue';

export function useMedia(target: Ref<boolean>, query = '(max-width: 940px)') {
  let mql: MediaQueryList;

  const handler = (e: MediaQueryListEvent) => {
    if (e.matches) {
      // 宽度 ≤ 940px
      target.value = false;
    }
    // > 940px 时不做任何事，保持外部原值
  };

  onMounted(() => {
    mql = window.matchMedia(query);
    if (mql.matches) handler({ matches: true } as MediaQueryListEvent);
    mql.addEventListener('change', handler);
  });

  onUnmounted(() => {
    mql?.removeEventListener('change', handler);
  });
}
