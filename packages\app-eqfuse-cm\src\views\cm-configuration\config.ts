import { t } from '@futurefab/vxe-table';
import type { TrimFormMap, TrimType } from './interface';

export const trimFormMap: TrimFormMap = {
    startTime: (type: TrimType) => ({
        key: 'startTime',
        label: t('cm.label.' + (type === 'TIME' ? 'startTime' : 'maxTime')),
        fristK: 'start',
        secondK: 'time',
        unit: 'ms',
    }),
    startCount: (type: TrimType) => ({
        key: 'startCount',
        label: t('cm.label.' + (type === 'COUNT' ? 'startPoint' : 'maxCount')),
        fristK: 'start',
        secondK: 'count',
    }),
    startPercent: (type: TrimType) => ({
        key: 'startPercent',
        label: t('cm.label.' + (type === 'PERCENT' ? 'startPercent' : 'maxPercent')),
        max: 100,
        precision: 2,
        fristK: 'start',
        secondK: 'percent',
        unit: '%',
    }),
    endTime: (type: TrimType) => ({
        key: 'endTime',
        label: t('cm.label.' + (type === 'TIME' ? 'endTime' : 'maxTime')),
        fristK: 'end',
        secondK: 'time',
        unit: 'ms',
    }),
    endCount: (type: TrimType) => ({
        key: 'endCount',
        label: t('cm.label.' + (type === 'COUNT' ? 'endPoint' : 'maxCount')),
        fristK: 'end',
        secondK: 'count',
    }),
    endPercent: (type: TrimType) => ({
        key: 'endPercent',
        label: t('cm.label.' + (type === 'PERCENT' ? 'endPercent' : 'maxPercent')),
        max: 100,
        precision: 2,
        fristK: 'end',
        secondK: 'percent',
        unit: '%',
    }),
};

export const timeArr = [
    trimFormMap.startTime,
    trimFormMap.startCount,
    trimFormMap.startPercent,
    trimFormMap.endTime,
    trimFormMap.endCount,
    trimFormMap.endPercent,
].map(item => item('TIME'));

export const countArr = [
    trimFormMap.startCount,
    trimFormMap.startTime,
    trimFormMap.startPercent,
    trimFormMap.endCount,
    trimFormMap.endTime,
    trimFormMap.endPercent,
].map(item => item('COUNT'));

export const percentArr = [
    trimFormMap.startPercent,
    trimFormMap.startTime,
    trimFormMap.startCount,
    trimFormMap.endPercent,
    trimFormMap.endTime,
    trimFormMap.endCount,
].map(item => item('PERCENT'));
