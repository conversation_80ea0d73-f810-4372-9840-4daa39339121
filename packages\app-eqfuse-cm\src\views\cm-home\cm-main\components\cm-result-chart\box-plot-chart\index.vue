<script lang="ts" setup>
import Chart from '@/components/chart.vue';
import type { BoxplotDataItem, CmFilterObject, DataSetMap } from '../interface';
import { ref, type ShallowRef, watch, shallowRef, type Ref, inject, nextTick, computed } from 'vue';
import * as echarts from '@xdatav/echarts';
import { formatterYSM } from '@/utils';
import { useSelfFullScreen } from '@/utils/tools';
import { uniqueId, throttle } from 'lodash-es';
import { Marker } from '@xchart/marker';
import viewDataTable from '../components/view-data-table/index.vue';
import { getBoxPlotFormatData, getViewDataTableOptions, getFilterFieldList } from './config';
import { GroupConfigVO } from '@/model/GroupConfigVO';
import ShowLegend from '../components/show-legend/index.vue';
import type { LegendTree } from '../interface';
import { getLegendData } from '../components/show-legend/config';
// import CopyChart from '../components/copy-chart/index.vue';
import { getChartAxisRange } from '@/utils';
import { useBaseStore } from '@futurefab/ui-sdk-stores';
import { EesOverviewChart } from '@futurefab/ui-sdk-ees-charts';
import { EesButtonTip } from '@futurefab/ui-sdk-ees-basic';
import { useCmChartLegendStore } from '@futurefab/ui-sdk-stores';
import { useGroupLegend } from '../useGroupLegend';
import CmGroupLegend from '@/components/cm-group-legend/index.vue';
import CmChartCollapse from '@/components/cm-chart-collapse/index.vue';
import CmChartCopy from '@/components/cm-chart-copy/index.vue';
import dayjs from 'dayjs';
import { CM_CHART_DEFUALT_FONT_SIZE } from '@/constant/charts';
const legendStroe = useCmChartLegendStore();
const baseStore = useBaseStore();

const props = withDefaults(
  defineProps<{
    activeStep: string;
    chartData: any;
    groupData: any;
    commonGroupConfigVO: GroupConfigVO;
    ids: number[];
    contextIndex: any;
    dataSetMap?: DataSetMap;
    copyChartTitle?: string;
    metaData: any;
    tableColumns?: any[];
  }>(),
  {
    activeStep: '',
    chartData: () => [],
    groupData: () => [],
    commonGroupConfigVO: () => new GroupConfigVO(),
    ids: () => [],
    contextIndex: {},
    dataSetMap: () => ({
      1: {
        show: true,
        color: 'blue'
      },
      2: {
        show: true,
        color: 'red'
      }
    }),
    copyChartTitle: '',
    metaData: () => ({})
  }
);
const legendRef = ref();
const groupColumns = computed(() =>
  props.tableColumns?.length ? props.tableColumns.slice(1) : []
);
const getLineColor = () => {
  return baseStore.theme === 'light' ? '#ECEEF0' : '#556070';
};
const getLabelColor = () => {
  return baseStore.theme === 'light' ? '#4e5969' : '#e2e4e7';
};
const chartBoxWidth = defineModel<number>('chartBoxWidth');
const chartBoxHeight = defineModel<number>('chartBoxHeight');
const boxRef = ref();
const chartBoxRef = ref();
const viewDataRef = ref();
const { isFullscreen, toggle } = useSelfFullScreen(boxRef);
const uid = uniqueId('cm-boxplot');
const isMultiToolTip = ref(false);
const marker: ShallowRef<Marker | null> = shallowRef(null);
const legendInfo = ref<LegendTree[]>([]);
// const groupConfigIds = ref<number[]>([]);
const filterInfo = ref<CmFilterObject>({ type: 'Group', filterKeys: [] });
const filterChart = (info: any) => {
  filterInfo.value = info;
  initLengend();
  nextTick(dealOptions);
};
const isDeleteBlank = ref(true);
const open = defineModel<boolean>('open', { default: true });
const { customLegendItems, setGroupConfig, initLengend } = useGroupLegend(filterInfo);
const createMarker = () => {
  if (!marker.value) {
    marker.value = new Marker({
      wrapper: chartBoxRef.value as unknown as HTMLDivElement,
      closable: true,
      closeByRightClick: false,
      canMoveOutside: true,
      copyable: true
    });
  }
};

function clearMarker() {
  if (marker.value) {
    marker.value.clear();
  }
}

const getOptions = (seriesItemData = [] as any[], pointSeriesData = [] as any[]) => {
  // 'upper: Q3 + 1.5 * IQR \nlower: Q1 - 1.5 * IQR'
  let xAxisData: any[] = [];
  if (isDeleteBlank.value) {
    // 将时间去重后排序
    const timeArr = [...new Set(seriesItemData.map((item) => item.startTime))].sort(
      (a: string, b: string) => {
        return new Date(a).getTime() - new Date(b).getTime();
      }
    );

    seriesItemData = seriesItemData.sort((a, b) => {
      return new Date(a.value[0]).getTime() - new Date(b.value[0]).getTime();
    });
    seriesItemData.forEach((item) => {
      item.value.shift();
    });
    if (pointSeriesData.length) {
      const timeToIndex: any = {};
      timeArr.forEach((item, index) => {
        timeToIndex[item] = index;
      });

      pointSeriesData.forEach((item) => {
        item.value[0] = timeToIndex[item.value[0]];
      });
    }
    // 格式化时间
    xAxisData = timeArr.map((item) => dayjs(item).format('YYYY/MM/DD\nHH:mm'));
  }
  const fontSize = legendStroe.chartLegendConfig?.coordinateFontSize || CM_CHART_DEFUALT_FONT_SIZE;
  return {
    animation: false,
    grid: { left: 20, right: 30, top: 10, bottom: 0, containLabel: true },
    legend: { show: false },
    series: [
      {
        type: 'boxplot',
        boxWidth: [5, 15],
        data: seriesItemData,
        tooltip: {
          formatter: dealMarkerString
        }
      },
      {
        name: 'outlier',
        type: 'scatter',
        data: pointSeriesData,
        tooltip: {
          formatter: dealMarkerString
        }
      }
    ],
    tooltip: {
      show: false,
      appendToBody: true,
      borderColor: '#fff',
      textStyle: {
        fontSize: 12,
        color: '#666'
      },
      trigger: 'item'
    },
    xAxis: {
      // type: 'category',
      type: isDeleteBlank.value ? 'category' : 'time',
      data: isDeleteBlank.value ? xAxisData : undefined,
      axisLabel: {
        color: getLabelColor(),
        formatter: isDeleteBlank.value ? undefined : '{yyyy}/{MM}/{dd}\n{HH}:{mm}',
        hideOverlap: true,
        fontFamily: 'Nunito Sans',
        fontSize: fontSize
      },
      position: 'bottom',
      boundaryGap: ['3%', '3%'],
      axisLine: {
        onZero: false,
        show: true,
        lineStyle: { width: 2, color: getLineColor() }
      },
      axisTick: {
        show: true,
        lineStyle: { width: 2, color: getLineColor() }
      }
    },
    yAxis: {
      axisLabel: {
        color: getLabelColor(),
        hideOverlap: true,
        formatter: (v: any) => formatterYSM(v, 4),
        fontSize: fontSize
      },
      axisLine: {
        show: true,
        lineStyle: { width: 2, color: getLineColor() }
      },
      splitLine: {
        show: false
      },
      axisTick: {
        show: true,
        lineStyle: { width: 2, color: getLineColor() }
      },
      scale: true,
      name: '',
      min: ({ min, max }: any) => {
        return getChartAxisRange(min, max, 0.1)[0];
      },
      max: ({ min, max }: any) => {
        return getChartAxisRange(min, max, 0.1)[1];
      }
    },
    toolbox: {
      show: true,
      showTitle: false,
      feature: {
        dataZoom: {
          enableBrushBack: true
        },
        brush: {
          type: ['rect']
        }
      },
      iconStyle: {
        opacity: 0
      }
    }
  } as echarts.EChartsCoreOption;
};
const options = ref(getOptions());
const formatData = ref<BoxplotDataItem[]>([]);
watch(
  [() => baseStore.theme, chartBoxWidth, chartBoxHeight, () => legendStroe.chartLegendConfig],
  () => {
    dealOptions();
  },
  {
    immediate: true
  }
);
watch(
  [() => props.groupData, () => props.chartData, () => props.activeStep],
  () => {
    nextTick(dealOptions);
  },
  { immediate: true }
);
watch(
  [() => groupColumns.value],
  () => {
    setGroupConfig(groupColumns.value);
    filterInfo.value = { type: 'Group', filterKeys: [] };
    legendInfo.value = getLegendData(groupColumns.value, filterInfo, props.metaData, props.ids);
    dealOptions();
  },
  { immediate: true }
);

function dealOptions() {
  const groupConfigColor = groupColumns.value.reduce((acc: any, item: any) => {
    acc[item.id] = {
      show: true,
      color: item?.chartLegendColor
    };
    return acc;
  }, {});
  const dataSetList = Object.entries(groupConfigColor).filter(
    ([dataSet, dataSetMapItem]: any) => dataSetMapItem.show
  );
  // 数据转换
  const { chartTableData, allChartData } = getBoxPlotFormatData(
    props.groupData,
    props.chartData,
    props.commonGroupConfigVO,
    props.activeStep,
    filterInfo.value,
    customLegendItems.value,
    groupColumns.value
  );
  formatData.value = chartTableData;
  const seriesItemData: any = [];
  const pointSeriesData: any = [];
  formatData.value.forEach((row, index) => {
    const dataSetMapItem: any = dataSetList.find((item) => Number(item?.[0]) == row.dataSet);
    if (!dataSetMapItem) return;
    const color = dataSetMapItem[1].color;
    seriesItemData.push({
      ...row,
      value: [row.startTime, row.min, row.q1, row.median, row.q3, row.max],
      itemStyle: {
        borderColor: color,
        color: 'transparent'
      },
      id: 'cm-boxplot-item' + index
    });
    if (row?.pointValue) {
      row?.pointValue?.forEach((item: any) => {
        pointSeriesData.push({
          ...row,
          value: item,
          itemStyle: {
            // borderColor: dataSetMapItem[1].color,
            color
          },
          symbolSize: 5,
          id: 'cm-boxplot-scatter' + index
        });
      });
    }
  });
  options.value = getOptions(seriesItemData, pointSeriesData);
  clearMarker();
}
function dealMarkerString(event: { data?: BoxplotDataItem }) {
  // console.log('event.data 1', event?.data);
  if (event?.data?.dataSet) {
    const temp = event.data as BoxplotDataItem;
    const tips = [
      `EQP : ${temp.eqp}`,
      `Chamber : ${temp.chamber}`,
      `Lot : ${temp.lot}`,
      `Wafer : ${temp.wafer}`,
      `DateTime : ${temp.startTime}`
    ];
    if (temp?.value?.length > 2) {
      tips.push(
        ...[
          `Min : ${temp.min}`,
          `Max : ${temp.max}`,
          `Range : ${temp.range}`,
          `Average : ${temp.avg}`,
          `Median : ${temp.median}`,
          `Stdev : ${temp.stdev}`
        ]
      );
    } else {
      tips.push(`Value : ${temp.value?.[1]}`);
    }
    if (props.commonGroupConfigVO?.isLoop) {
      tips.splice(4, 0, `Loop : ${temp.loopNo}`);
    } else if (props.commonGroupConfigVO?.stationCompare) {
      tips.splice(4, 0, `Station : ${temp?.stationName}`);
    }
    return (
      `<p style="margin: 0;font-size: 16px;font-weight: 600">Data Details</p>` + tips.join('<br/>')
    );
  }
}

const chartClick = (event: any) => {
  if (event.componentType === 'markLine') {
    return;
  }
  // if (isMultiToolTip.value && event?.event) {
  if (event?.event) {
    clearMarker();
    createMarker();
    const ele = document.getElementById(uid + '-content');
    if (ele) {
      const x = event.event.offsetX + ele.offsetLeft;
      const y = event.event.offsetY + ele.offsetTop;
      // const key = event.data?.id;
      let markerString = dealMarkerString(event);
      marker.value!.addMark({
        x,
        y,
        // key,
        data: [
          {
            // color: event.color,
            label: '',
            value: markerString!
          }
        ]
      });
    }
  }
};
const viewData = () => {
  viewDataRef.value.show();
};

const legendChange = () => {
  dealOptions();
};
const onDeleteBlank = () => {
  isDeleteBlank.value = !isDeleteBlank.value;
  dealOptions();
};

const chartTitle = computed(() => `${props.chartData?.[0]?.paramAlias} Step:${props.activeStep}`);
watch(
  () => props.ids,
  () => {
    filterInfo.value = { type: 'Group', filterKeys: props.ids };
    options.value = getOptions();
    initLengend();
    legendRef.value.setFilterGroup(props.ids);
  }
);
</script>
<template>
  <div :id="uid" ref="boxRef" :class="['box-plot-chart', open ? 'open' : 'close']">
    <CmChartCollapse :is-full-screen="isFullscreen" v-model:open="open">
      <template #header>
        <div class="box-plot-chart-head">
          <div class="title-box">
            <p>{{ $t('cm.title.boxplotChart') }}</p>
            <vxe-tooltip
              :content="$t(!isFullscreen ? 'common.title.zoomIn' : 'common.title.zoomOut')"
              :use-h-t-m-l="true"
              theme="light"
              ><i
                class="iconfont chart_icon_icon zoom-icon"
                :class="isFullscreen ? 'icon-screen-reduction' : 'icon-screen-full'"
                style="color: var(--text-hint-color)"
                @click="toggle"
              ></i
            ></vxe-tooltip>
          </div>
          <div class="icon-box">
            <show-legend
              ref="legendRef"
              :legend-info="legendInfo"
              :metaData="metaData"
              @filter-chart="filterChart"
            />
            <!-- <EesOverviewChart
              :chart-id-list="[uid + '-content']"
              :chart-name-list="[props.copyChartTitle || props.title]"
              type="copy"
            /> -->
            <EesButtonTip
              :margin-right="10"
              :is-border="true"
              icon="#icon-btn-blank-delete"
              :text="$t('eesCharts.commonBtn.deleteBlank')"
              :is-active="isDeleteBlank"
              @on-click="onDeleteBlank"
            />
            <ees-button-tip
              :marginRight="0"
              :is-border="true"
              icon="#icon-btn-view-data"
              :text="$t('common.title.viewData')"
              @click="viewData"
            />
            <slot name="afterIcon"></slot>
          </div>
        </div>
      </template>
      <template #content>
        <div class="box-diagram" id="cm-boxplot-chart-container">
          <CmChartCopy>
            <CmGroupLegend
              :legend="customLegendItems"
              :title="chartTitle"
              @legendChange="legendChange"
            >
              <template #chart>
                <div ref="chartBoxRef" :id="uid + '-content'" class="chart-box">
                  <Chart
                    v-if="open"
                    id="cm-boxplot-chart"
                    :options="options"
                    @chart-click="chartClick($event)"
                    @chart-resize="clearMarker()"
                    @chart-datazoom="clearMarker()"
                    @chart-legendselectchanged="clearMarker()"
                  ></Chart>
                </div>
              </template>
            </CmGroupLegend>
          </CmChartCopy>
        </div>
      </template>
    </CmChartCollapse>
  </div>
  <Teleport to="body">
    <view-data-table
      ref="viewDataRef"
      :data="formatData"
      :options="getViewDataTableOptions()"
      :column-field-list="getFilterFieldList()"
    ></view-data-table>
  </Teleport>
</template>
<style lang="less" scoped>
@import url('@/assets/style/variable.less');
.box-plot-chart {
  height: 100%;
  width: 100%;
  background-color: @bg-block-color;
  position: relative;
  padding: 0;
  border-radius: 4px;
  border-top: 1px solid @border-color;
  &.close {
    border-bottom: 1px solid @border-color;
  }
  &-head {
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 40px;
    justify-content: space-between;
    .title-box {
      display: flex;
      align-items: center;
      line-height: 40px;
      p {
        font-size: 16px;
        font-weight: bold;
        line-height: 22px;
        flex-shrink: 0;
        max-width: 200px;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        margin: 0;
        color: @text-title-color;
      }
      .zoom-icon {
        margin: 0 0 0 5px;
        color: @text-title-color;
        width: 20px;
        height: 20px;
        display: inline-flex;
        justify-content: center;
        align-items: center;
        &:hover {
          cursor: pointer;
          color: @text-weak-text;
          background-color: @bg-hover-2-color;
        }
      }
      .title-like-icon {
        display: inline-block;
        background: @primary-color;
        width: 7px;
        height: 14px;
        border-radius: 0 7px 7px 0;
        line-height: 40px;
        margin-right: 8px;
      }
    }

    .icon-box {
      display: flex;
      align-items: center;
    }
  }
  .box-diagram {
    height: 100%;
    width: 100%;
    .chart-box {
      height: 100%;
      width: 100%;
    }
  }
}
</style>
