<script setup lang="ts">
import { ref, onMounted, onUnmounted, reactive, watch } from 'vue';
import { OrbitControls } from 'three/addons/controls/OrbitControls.js';
// import Stats from 'stats.js';
import {
    Scene,
    Color,
    OrthographicCamera,
    WebGLRenderer,
    AmbientLight,
    DirectionalLight,
    AxesHelper,
} from 'three';
import { PlaneGeometry, MeshStandardMaterial, DoubleSide, Mesh, ConeGeometry } from 'three';
import { getCenterPointer, getThreeQuadData, setNotch } from './index';
import {
    BufferGeometry,
    Float32BufferAttribute,
    MeshPhongMaterial,
    LineSegments,
    LineBasicMaterial,
} from 'three';
import type { Measuredata } from '@/utils/spline-interpolate';
import { visualMapColors } from '@/utils/custom-color';
import type { Specification } from '@/views/metrology-management/interface';
import { useResizeObserver } from '@vueuse/core';
const props = withDefaults(
    defineProps<{
        measuredata?: Measuredata[] | Measuredata;
        padding: number; // wafer周围的网格
        gridSize: number; // 横纵显示多少个点
        valueKey: string;
        size: number; // css 样式的宽高有关
        specification?: Specification;
        offset?: number; // 向上的偏移量
        scale?: number; // 归一化后的缩放比
    }>(),
    {
        measuredata: () => ({} as any),
        gridSize: 100,
        padding: 5,
        size: 300,
        offset: 5,
        scale: 15,
        specification: () => ({} as any),
    },
);
// 创建合并几何体（填充四边形）
function createSolidGeometry(data: any) {
    const geometry = new BufferGeometry();
    const positions: number[] = [];
    const colors: any[] = [];
    const indices: number[] = [];
    let vertexIndex = 0;
    data.forEach((quad: any) => {
        const color = new Color(quad.color);

        // 每个四边形的4个顶点
        for (let i = 0; i < 4; i++) {
            positions.push(...quad.points[i]);
            colors.push(color.r, color.g, color.b);
        }

        // 添加两个三角形（6个索引）组成四边形
        indices.push(
            vertexIndex,
            vertexIndex + 1,
            vertexIndex + 2,
            vertexIndex,
            vertexIndex + 2,
            vertexIndex + 3,
        );

        vertexIndex += 4;
    });

    // 设置几何体属性
    geometry.setAttribute('position', new Float32BufferAttribute(positions, 3));
    geometry.setAttribute('color', new Float32BufferAttribute(colors, 3));
    geometry.setIndex(indices);

    // 计算法线
    geometry.computeVertexNormals();

    return geometry;
}

// 创建边框几何体（线框）
function createWireframeGeometry(data: any) {
    const geometry = new BufferGeometry();
    const positions: number[] = [];

    data.forEach((quad: any) => {
        const points = quad.points;

        // 每个四边形添加4条边（每条边2个点）
        // 边1: p0 -> p1
        positions.push(...points[0], ...points[1]);
        // 边2: p1 -> p2
        positions.push(...points[1], ...points[2]);
        // 边3: p2 -> p3
        positions.push(...points[2], ...points[3]);
        // 边4: p3 -> p0
        positions.push(...points[3], ...points[0]);
    });

    geometry.setAttribute('position', new Float32BufferAttribute(positions, 3));
    return geometry;
}
let renderKey = 0;
const sceneContainer = ref();
const sceneRef = ref();
// const perfStats = reactive({
//     fps: 0,
//     quads: 0,
//     drawCalls: 0,
// });
let scene: any,
    camera: any,
    renderer: any,
    controls: any; // 相机类型改为OrthographicCamera
// 添加正交相机尺寸参数
const orthoSize = (props.gridSize / 2 + props.padding) * 1.1; // 正交投影范围大小
const init = () => {
    cleanup();
    const temp = Array.isArray(props.measuredata) ? props.measuredata : [props.measuredata];
    const data = getThreeQuadData(
        temp,
        props.gridSize,
        visualMapColors,
        props.padding,
        props.valueKey,
        props.specification,
        props.offset,
        props.scale,
    );
    const width = sceneContainer.value.clientWidth;
    const height = sceneContainer.value.clientHeight;
    const aspect = width / height;
    // const stats = new Stats();
    // stats.showPanel(0);
    scene = new Scene();
    scene.background = new Color('#fff'); // 背景保持白色

    // 创建正交相机 (替换透视相机)
    camera = new OrthographicCamera(
        -orthoSize * aspect, // left
        orthoSize * aspect, // right
        orthoSize, // top
        -orthoSize, // bottom
        0.1, // near
        1000, // far
    );
    /**
     * wafer的中心点坐标 （x, y, 0）
     */
    // const center = getCenterPointer(props.specification, props.gridSize) as [
    //     number,
    //     number,
    //     number,
    // ];
    const center = [0, 0, 0] as [number, number, number]; // wafer的中心点 一直保持到 图中的原点
    camera.position.set(center[0], center[1], props.gridSize / 2 + props.padding + 3); // 调整相机位置以适配正交视图
    camera.lookAt(center[0], center[1], center[2]);

    renderer = new WebGLRenderer({
        antialias: true,
        alpha: true,
        powerPreference: 'high-performance',
        canvas: sceneRef.value,
    });
    renderer.shadowMap.enabled = false;
    renderer.setSize(width, height);
    renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));

    controls = new OrbitControls(camera, renderer.domElement) as any;
    controls!.enableDamping = false;
    controls!.dampingFactor = 0.05;

    // ====== 增强光照 ======
    // 增强环境光强度
    const ambientLight = new AmbientLight(0xffffff, 1.2); // 强度从0.6增加到1.2
    scene.add(ambientLight);

    // 创建第一盏方向光 - 位置与相机相同
    const directionalLight = new DirectionalLight(0xffffff, 1.5);
    directionalLight.position.copy(camera.position); // 位置设置为相机位置
    directionalLight.castShadow = false;
    scene.add(directionalLight);
    // 添加第二盏方向光作为补充光源
    const directionalLight2 = new DirectionalLight(0xffffff, 0.8);
    directionalLight2.position.set(
        center[0] + props.gridSize,
        center[1],
        props.gridSize / 2 + props.padding + 3,
    );
    scene.add(directionalLight2);
    // 添加第二盏方向光作为补充光源
    const directionalLight3 = new DirectionalLight(0xffffff, 0.8);
    directionalLight3.position.set(
        center[0] - props.gridSize,
        center[1],
        props.gridSize / 2 + props.padding + 3,
    );
    scene.add(directionalLight2);
    // 显示坐标轴
    // scene.add(new AxesHelper(20));

    // 创建填充四边形（实体）
    const solidGeometry = createSolidGeometry(data);
    // 调整材质属性增加亮度
    const solidMaterial = new MeshPhongMaterial({
        vertexColors: true,
        side: DoubleSide,
        shininess: 60, // 降低高光集中度
        wireframe: false,
    });

    const solidMesh = new Mesh(solidGeometry, solidMaterial);
    solidMesh.castShadow = false;
    solidMesh.receiveShadow = false;
    scene.add(solidMesh);

    // 创建边框（线框）
    const wireframeGeometry = createWireframeGeometry(data);
    const wireframeMaterial = new LineBasicMaterial({
        color: 'gray', // 使用更深的线条颜色以增加对比度
        linewidth: 1,
        opacity: 0.08,
        transparent: true,
    });
    const wireframeMesh = new LineSegments(wireframeGeometry, wireframeMaterial);
    scene.add(wireframeMesh);

    // notch
    const notchHight = 5;
    const notch = new ConeGeometry(notchHight, props.padding, 3);
    const notch_material = new MeshStandardMaterial({
        color: 0xff3366,
        side: DoubleSide,
        emissiveIntensity: 0.9, // 中等发光强度
        envMapIntensity: 1.5, // 增强环境贴图反射强度
    });
    const notchM = new Mesh(notch, notch_material);
    scene.add(notchM);
    setNotch(notchM, center, props.specification, notchHight, props.gridSize, props.offset);
    // 地板
    const floorSize = props.gridSize + 2 * props.padding;
    const floorGeometry = new PlaneGeometry(floorSize, floorSize);
    const floorMaterial = new MeshStandardMaterial({
        color: 'white', // 使用更亮的灰色
        side: DoubleSide,
        opacity: 1,
        transparent: false,
        roughness: 0.1, // 降低粗糙度增加反光
        metalness: 0.3, // 轻微金属感
    });

    const floor = new Mesh(floorGeometry, floorMaterial);
    floor.castShadow = false;
    floor.receiveShadow = false;
    floor.position.z = -0.1;
    floor.position.x = center[0] - 1;
    floor.position.y = center[1] - 1;
    scene.add(floor);
};
// let lastTime = 0;
// let frameCount = 0;
function animate(k: number) {
    if (k !== renderKey) {
        return;
    }
    requestAnimationFrame(() => animate(k));
    // frameCount++;
    // if (time >= lastTime + 1000) {
    //     perfStats.fps = Math.round((frameCount * 1000) / (time - lastTime));
    //     frameCount = 0;
    //     lastTime = time;
    // }

    if (controls) controls.update();
    renderer?.render(scene!, camera!);
}

function onResize() {
    const width = sceneContainer.value.clientWidth;
    const height = sceneContainer.value.clientHeight;
    const aspect = width / height;

    // 更新正交相机参数
    camera!.left = -orthoSize * aspect;
    camera!.right = orthoSize * aspect;
    camera!.top = orthoSize;
    camera!.bottom = -orthoSize;
    camera!.updateProjectionMatrix();

    renderer!.setSize(width, height);
    console.log(width, height, 'onWindowResize');
}
useResizeObserver(sceneContainer, onResize);
// 添加资源释放函数
function disposeSceneResources(scene: typeof Scene) {
    scene.traverse((object: any) => {
        if (object instanceof Mesh) {
            // 释放几何体
            if (object.geometry) {
                object.geometry.dispose();
            }
            // 释放材质
            if (object.material) {
                if (Array.isArray(object.material)) {
                    object.material.forEach((m: any) => m.dispose());
                } else {
                    object.material.dispose();
                }
            }
        }
    });

    // 清空场景
    while (scene.children.length > 0) {
        scene.remove(scene.children[0]);
    }
}
function cleanup() {
    if (controls) {
        controls.dispose();
        controls = null;
    }

    if (renderer) {
        renderer.dispose();
        renderer = null;
    }

    if (scene) {
        disposeSceneResources(scene);
        scene = null;
    }

    camera = null;
}
watch(
    [
        () => props.measuredata,
        () => props.padding,
        () => props.gridSize,
        () => props.valueKey,
        () => props.size,
        () => props.specification,
        () => props.offset,
        () => props.scale,
    ],
    () => {
        renderKey++;
        setTimeout(() => {
            init();
            animate(renderKey);
        }, 100);
    },
);
onMounted(() => {
    init();
    animate(renderKey);
});

onUnmounted(() => {
    renderKey++;
    cleanup();
});
</script>

<template>
    <div
        ref="sceneContainer"
        :style="{ height: size + 'px', width: size + 'px' }"
        class="scene-container"
    >
        <canvas ref="sceneRef" class="scene-target"></canvas>
    </div>
</template>

<style scoped lang="less">
.scene-container {
    display: inline-block;
    position: relative;
    overflow: hidden;
    .scene-target {
        height: 100%;
        width: 100%;
    }
}
</style>
