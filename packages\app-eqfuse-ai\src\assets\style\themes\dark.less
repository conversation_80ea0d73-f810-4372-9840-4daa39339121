.app_theme_dark {
    --primary-color-1: #4c3f5c;
    --primary-font-color-3: rgb(255 255 255 / 87%);
    --on-primary-color-1: #ffffffde;
    --on-primary-color-2: #000000;

    /**  主色调背景颜色  **/
    --body-bg-color: #121212;
    --bg-color: #1d1d1d;

    /**  已选筛选条件  **/
    --tag-br-color: rgb(255 255 255 / 12%);

    /* search框颜色 */
    --input-error-font-color: #cf6679;
    --input-br-color: #7a7a7a;
    --input-bg-color: linear-gradient(0deg, rgb(255 255 255 / 14%), rgb(255 255 255 / 14%)), #121212;
    --input-disabled-bg-color: linear-gradient(0deg, #121212, #121212),
        linear-gradient(0deg, rgb(255 255 255 / 14%), rgb(255 255 255 / 14%));
    --input-font-color: #acacac;
    --font-placeholder-color: #98a0c0;
    --select-multiple-bg-color: #2e2e2e;

    /**  表格颜色  **/
    --th-bg-color: #343434;
    --th-font-color: #bab2ba;
    --td-font-color: #acacac;
    --table-br-color: #383838;
    --td-br-color: #383838;
    --tr-even-bg-color: #1e1e1e;

    /* 弹框  **/
    --model-bg-color: #1d1d1d;

    /**  checked框颜色  **/
    --check-default-br-color: #979797;

    /**  边框色 */
    --br-default-color: #2e2e2e;
    --br-default-color-1: #f0f0f0;

    /**  card块颜色  **/
    --card-default-br-color: #7a7a7a;

    /* 高亮背景 */
    --active-bg-color: rgb(148 99 203 / 40%);
}
